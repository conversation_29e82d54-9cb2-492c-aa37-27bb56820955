import React, { useEffect, useState, useRef } from 'react';
import { StyleSheet, View, Alert, Image, Text, Animated, Dimensions } from 'react-native';
import { useRouter } from 'expo-router';
import { useDispatch, useSelector } from 'react-redux';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { useRouteContext } from '@/context/RouteContext';

// Import components
import ButtonLinear from '@/components/Buttons/ButtonLinear';
import Container from '@/components/Layout/Container';
import Loader from '@/components/Loader/Loader';
import OrganizationSelector from '@/components/OrganizationSelector';

// Import other dependencies
import { Colors } from '@/constants';
import Constants from '@/constants/Const';
import { setCurrentOrgId, setCurrentOrgDetails } from '@/services/actions/organizationActions';
import { apiPostWithToken } from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import { createShadow } from '@/utils/shadowStyles';
import scale from '@/utils/scale';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function OrganizationScreen() {
  const dispatch = useDispatch();
  const router = useRouter();
  const [loader, setLoader] = useState(false);
  const { setHasSelectedOrganization } = useRouteContext();

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(30)).current;

  const caregiverId = useSelector(
    (state: any) => state?.loginReducer?.data?.userId,
  );

  // Define the organization item type
  interface OrganizationItem {
    id: number | string;
    name: string;
    displayName?: string;
  }

  const [organizationList, setOrganizationList] = useState<OrganizationItem[]>([]);
  const [selectedOrganization, setSelectedOrganization] = useState<OrganizationItem>({
    id: 0,
    name: 'Select organization',
    displayName: 'Select organization',
  });

  // Start animations when component mounts
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  const getOrganizationList = async () => {
    setLoader(true);
    try {
      const response = await apiPostWithToken(
        {
          userId: '' + caregiverId,
        },
        URLS.caregiverUrl + 'getOrganization',
      );

      if (response?.status == 200) {
        setLoader(false);
        let pList = response?.data?.data || [];

        let arr: OrganizationItem[] = [];
        if (pList && pList.length > 0) {
          pList.forEach((p: any) => {
            let obj: OrganizationItem = {
              id: parseInt(p.groupId),
              name: p.groupName,
              displayName: p.groupName,
            };
            arr.push(obj);
          });
          setOrganizationList(arr);
        }
      } else {
        setLoader(false);
        const errorMessage = response?.response?.data?.responseMessage
          ? response?.response?.data?.responseMessage
          : response.message === 'Network Error'
          ? 'Network error. Please check your data connection.'
          : response.message;
        Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
      }
    } catch (error) {
      setLoader(false);
      Alert.alert('Error', 'An unexpected error occurred while fetching organizations. Please try again.', [{text: 'Dismiss'}]);
    }
  };

  useEffect(() => {
    getOrganizationList();
  }, []);

  const handleContinue = async () => {
    if (selectedOrganization.id === 0) {
      Alert.alert('Error', 'You must select an organization', [{text: 'OK'}]);
    } else {
      // Store both organization ID and details
      dispatch(setCurrentOrgId(selectedOrganization.id.toString()));
      dispatch(setCurrentOrgDetails({
        id: selectedOrganization.id.toString(),
        name: selectedOrganization.name
      }));

      // Set that organization has been selected to enable notifications
      setHasSelectedOrganization(true);

      try {
        // Navigate directly to the tabs
        router.replace('/(tabs)');
      } catch (error) {
        // Fallback navigation if the tabs route fails
        Alert.alert('Navigation Error', 'There was an error navigating to the dashboard. Please try again.', [
          {text: 'OK'}
        ]);
      }
    }
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: Colors.Snow }} edges={['left', 'right']}>
      <StatusBar style="dark" />
      <Container style={styles.container}>
        <Loader modalVisible={loader} />

        <Animated.View
          style={[
            styles.contentContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateY: translateY }]
            }
          ]}
        >
          {/* Logo and Header section */}
          <View style={styles.headerSection}>
            <View style={styles.logoContainer}>
              <Image
                source={require('@/assets/images/logo_1.png')}
                style={styles.logo}
                resizeMode="contain"
              />
            </View>
            <Text style={styles.welcomeText}>Welcome back!</Text>
            <Text style={styles.subText}>
              Please select your organization to continue
            </Text>
          </View>

          {/* Card containing organization selector */}
          <View style={styles.selectorCard}>
            <OrganizationSelector
              value={selectedOrganization}
              onChange={setSelectedOrganization}
              items={organizationList}
              label="Organization"
              placeholder="Select organization"
            />

            <ButtonLinear
              title="Continue"
              onPress={handleContinue}
              disabled={selectedOrganization.id === 0}
              loader={loader}
              style={styles.continueButton}
            />
          </View>

          {/* Additional info section */}
          <View style={styles.infoSection}>
            <Text style={styles.infoText}>
              Need help? Contact your administrator
            </Text>
          </View>
        </Animated.View>
      </Container>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.Snow,
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerSection: {
    marginBottom: 40,
    alignItems: 'center',
  },
  logoContainer: {
    marginBottom: 32,
    alignItems: 'center',
  },
  logo: {
    height: 60,
    width: SCREEN_WIDTH * 0.6,
  },
  welcomeText: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.DarkJungleGreen,
    marginBottom: 8,
    textAlign: 'center',
  },
  subText: {
    fontSize: 16,
    color: Colors.GrayBlue,
    textAlign: 'center',
    maxWidth: '80%',
  },
  selectorCard: {
    backgroundColor: Colors.White,
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 450,
    ...createShadow(3),
  },
  continueButton: {
    marginTop: 24,
  },
  infoSection: {
    marginTop: 24,
    alignItems: 'center',
  },
  infoText: {
    fontSize: 14,
    color: Colors.GrayBlue,
  },
});
