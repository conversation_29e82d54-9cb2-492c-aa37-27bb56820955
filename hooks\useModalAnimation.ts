import { useState, useEffect } from 'react';
import { Dimensions, Platform } from 'react-native';
import { useSharedValue, withSpring, runOnJS, useAnimatedReaction } from 'react-native-reanimated';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

const ANIMATION_CONFIG = {
  damping: Platform.OS === 'android' ? 25 : 18, // Higher damping on Android for more stability
  mass: 1,
  stiffness: Platform.OS === 'android' ? 120 : 100, // Higher stiffness on Android
  overshootClamping: Platform.OS === 'android', // Prevent overshoot on Android
  restDisplacementThreshold: 0.01,
  restSpeedThreshold: 0.01,
};

const useModalAnimation = () => {
  const [visible, setVisible] = useState(false);
  const transY = useSharedValue(0);
  // Replace mutable ref with shared value to fix Reanimated warnings
  const isClosing = useSharedValue(false);

  // Track animation completion to reliably update visibility state
  useAnimatedReaction(
    () => transY.value,
    (current, previous) => {
      // If we're closing and animation is near complete
      if (isClosing.value && current < 10 && previous !== null) {
        runOnJS(setVisible)(false);
        // Update the shared value properly in the worklet
        isClosing.value = false;
      }
    },
    // No need to include isClosing in dependencies as it's a shared value
    []
  );

  const open = () => {
    // Update the shared value on JS thread
    isClosing.value = false;
    setVisible(true);
    // Animate the modal up - use more stable animation config
    // For Android, use a slightly different target value to ensure proper positioning
    const targetHeight = Platform.OS === 'android' ? SCREEN_HEIGHT * 0.98 : SCREEN_HEIGHT;
    transY.value = withSpring(targetHeight, ANIMATION_CONFIG);
  };

  const close = () => {
    // Update the shared value on JS thread
    isClosing.value = true;
    // Animate the modal down with improved animation config
    // For Android, use a slightly faster animation to ensure it completes before keyboard issues can occur
    const config = Platform.OS === 'android'
      ? { ...ANIMATION_CONFIG, damping: 30, stiffness: 150 }
      : ANIMATION_CONFIG;
    transY.value = withSpring(0, config);
    // Visibility is now handled by the animation completion in useAnimatedReaction
  };

  // Ensure cleanup on unmount
  useEffect(() => {
    return () => {
      transY.value = 0;
      isClosing.value = false;
    };
  }, []);

  return {
    open,
    close,
    transY,
    visible,
  };
};

export default useModalAnimation;
