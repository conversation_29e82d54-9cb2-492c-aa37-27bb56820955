import React, {useState} from 'react';
import {Dimensions, Text, View} from 'react-native';
import {LineChart} from 'react-native-chart-kit';
import Svg, {Rect, Text as TextSVG} from 'react-native-svg';
import {Colors} from '@/constants';
import Theme from '@/constants/Theme';

interface GraphProps {
  measuredDates: any[];
  measuredValues: any[];
  measuredValues2: any[];
  measuredValues3: any[];
  title: string;
  min: number;
  max: number;
  cMin: number;
  cMax: number;
}

const VitalGraph = (props: GraphProps) => {
  // Ensure props are defined with default values
  const safeProps = {
    measuredDates: props?.measuredDates || [],
    measuredValues: props?.measuredValues || [],
    measuredValues2: props?.measuredValues2 || [],
    measuredValues3: props?.measuredValues3 || [],
    title: props?.title || '',
    min: props?.min || 0,
    max: props?.max || 100,
    cMin: props?.cMin || 0,
    cMax: props?.cMax || 100,
  };
  
  let [tooltipPos, setTooltipPos] = useState({
    x: 0,
    y: 0,
    visible: false,
    value: 0,
  });

  const chartConfig = {
    backgroundGradientFrom: '#FFFFFF',
    backgroundGradientFromOpacity: 0,
    backgroundGradientTo: '#FFFFFF',
    backgroundGradientToOpacity: 0,
    color: (opacity = 1) => `rgba(0,0,0,${opacity})`, // Fixed format - no space after comma
    labelColor: (opacity = 1) => `rgba(0,0,0,${opacity})`, // Fixed format - no space after comma
    strokeWidth: 2,
    barPercentage: 2,
    decimalPlaces: 0,
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: '#FFF',
    },
    propsForLabels: {
      fontFamily: 'OpenSans-Regular',
      fontSize: 16,
    },
    fillShadowGradient: 'skyblue',
    fillShadowGradientOpacity: 0.5,
    useShadowColorFromDataset: true,
    propsForBackgroundLines: {
      strokeDasharray: '', // solid background lines with no dashes
    },
  };

  const dataObject: any[] = [];
  dataObject.push({
    data: safeProps.measuredValues,
    strokeWidth: 4,
    color: (opacity = 1) => `rgba(0,0,255,${opacity})`, // Fixed format - no space after comma, opacity clamped to valid range
  });

  if (safeProps.measuredValues2.length > 0) {
    dataObject.push({
      data: safeProps.measuredValues2,
      strokeWidth: 3,
      color: (opacity = 1) => `rgba(255,165,0,${opacity})`, // Fixed format - no space after comma
    });
  }

  if (safeProps.measuredValues3.length > 0) {
    dataObject.push({
      data: safeProps.measuredValues3,
      strokeWidth: 2,
      color: (opacity = 1) => `rgba(125,255,0,${opacity})`, // Fixed format - no space after comma
    });
  }

  return (
    <View>
      <View
        style={{
          marginVertical: 20,
          marginHorizontal: 20,
          borderRadius: 10,
          ...Theme.flexRowSpace,
        }}>
        {safeProps.title === 'Blood Pressure' || safeProps.title === 'Blood Sugar' ? (
          <>
            {safeProps.title === 'Blood Pressure' ? (
              <>
                <Text
                  style={{
                    color: 'rgb(0,0,255)',
                    fontFamily: 'OpenSans-Medium',
                    fontSize: 16,
                  }}>
                  {'\u2B24\tSystolic '}
                </Text>
                <Text
                  style={{
                    color: 'rgb(255,165,0)',
                    fontFamily: 'OpenSans-Medium',
                    fontSize: 16,
                  }}>
                  {'\u2B24\tDiastolic'}
                </Text>
              </>
            ) : (
              <>
                <Text
                  style={{
                    color: 'rgb(0,0,255)',
                    fontFamily: 'OpenSans-Medium',
                    fontSize: 16,
                  }}>
                  {'\u2B24\tFasting '}
                </Text>
                <Text
                  style={{
                    color: 'rgb(255,165,0)',
                    fontFamily: 'OpenSans-Medium',
                    fontSize: 16,
                  }}>
                  {'\u2B24\tRandom'}
                </Text>
              </>
            )}
          </>
        ) : (
          <>
            <Text
              style={{
                color: 'rgb(0,0,255)',
                fontFamily: 'OpenSans-Medium',
                fontSize: 16,
              }}>
              {'\u2B24'} {safeProps.title ?? ''}
            </Text>
          </>
        )}
      </View>

      <LineChart
        data={{
          labels: safeProps.measuredDates,
          datasets: dataObject,
        }}
        width={Dimensions.get('window').width * 0.9}
        height={220}
        yAxisInterval={1}
        chartConfig={chartConfig}
        verticalLabelRotation={0}
        bezier
        style={{
          marginVertical: 25,
          marginHorizontal: -20,
          borderRadius: 10,
        }}
        decorator={() => {
          return tooltipPos.visible ? (
            <View>
              <Svg>
                <Rect
                  x={tooltipPos.x - 15}
                  y={tooltipPos.y + 10}
                  width="40"
                  height="30"
                  fill="black"
                />
                <TextSVG
                  x={tooltipPos.x + 5}
                  y={tooltipPos.y + 30}
                  fill="white"
                  fontSize="16"
                  fontWeight="bold"
                  textAnchor="middle">
                  {tooltipPos.value ? tooltipPos.value : 0}
                </TextSVG>
              </Svg>
            </View>
          ) : null;
        }}
        onDataPointClick={data => {
          let isSamePoint = tooltipPos.x === data.x && tooltipPos.y === data.y;

          isSamePoint
            ? setTooltipPos(previousState => {
                return {
                  ...previousState,
                  value: data.value,
                  visible: !previousState.visible,
                };
              })
            : setTooltipPos({
                x: data.x,
                value: data.value,
                y: data.y,
                visible: true,
              });
        }}
        getDotColor={dataPoint => {
          if (dataPoint != null) {
            if (dataPoint > safeProps.max || dataPoint < safeProps.min) {
              return Colors.pastelRed;
            } else {
              return Colors.pastelGreen;
            }
          } else {
            return Colors.InnearColor;
          }
        }}
      />
    </View>
  );
};

export default VitalGraph;
