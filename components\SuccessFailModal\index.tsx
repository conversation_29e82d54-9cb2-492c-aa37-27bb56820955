import React, {useCallback, useEffect, useState} from 'react';
import {Image, StyleSheet, View, TouchableOpacity, Animated} from 'react-native';
import {Colors} from '@/constants';
import Theme from '@/constants/Theme';
import ButtonLinear from '../Buttons/ButtonLinear';
import Layout from '../Layout/Layout';
import Text from '../Text';
import {LinearGradient} from 'expo-linear-gradient';

interface SuccessFailAlertProps {
  close: () => void;
  open: () => void;
  primaryText: string;
  showPrimaryButton: boolean;
  secondaryText: string;
  showSecondaryButton: boolean;
  message: string;
  successOrFail: boolean;
}

const SuccessFailAlert = (props: SuccessFailAlertProps) => {
  // Animation values
  const [fadeAnim] = useState(new Animated.Value(0));
  const [scaleAnim] = useState(new Animated.Value(0.9));

  // Start animations when component mounts
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  // Handle confirm action
  const handleConfirm = useCallback(() => {
    // Animate out before closing
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 0.9,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      })
    ]).start(() => {
      props.open();
    });
  }, [props, fadeAnim, scaleAnim]);

  // Handle cancel action
  const handleCancel = useCallback(() => {
    // Animate out before closing
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 0.9,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      })
    ]).start(() => {
      props.close();
    });
  }, [props, fadeAnim, scaleAnim]);

  return (
    <Animated.View
      style={[
        styles.container,
        { opacity: fadeAnim }
      ]}
    >
      <TouchableOpacity
        style={styles.backdrop}
        activeOpacity={1}
        onPress={handleCancel}
      />
      <Animated.View
        style={[
          styles.modalContent,
          {
            transform: [{ scale: scaleAnim }],
          }
        ]}
      >
        <LinearGradient
          colors={[Colors.White, Colors.WhiteSmoke]}
          style={styles.gradientBackground}
        >
          <View style={styles.header}>
            <Text center bold size={18} lineHeight={24} color={Colors.DarkJungleGreen}>
              {props.successOrFail ? 'Success' : 'Warning'}
            </Text>
          </View>

          <View style={styles.body}>
            {props.successOrFail ? (
              <Image
                source={require('images/ic_accept_3x.png')}
                style={styles.icon}
              />
            ) : (
              <Image
                source={require('images/img_warning.png')}
                style={styles.icon}
              />
            )}

            {props.message ? (
              <Text
                center
                size={16}
                lineHeight={24}
                marginTop={16}
                color={Colors.DarkJungleGreen}
              >
                {props.message}
              </Text>
            ) : null}
          </View>

          <View style={styles.buttonContainer}>
            {props.primaryText && props.showPrimaryButton ? (
              <ButtonLinear
                styleButton={styles.primaryButton}
                style={{ marginTop: 0 }}
                title={props.primaryText}
                onPress={handleConfirm}
                colors={[Colors.TealBlue, Colors.TealBlue]}
              />
            ) : null}

            {props.secondaryText && props.showSecondaryButton ? (
              <ButtonLinear
                white
                styleButton={styles.secondaryButton}
                style={{ marginTop: 0 }}
                title={props.secondaryText}
                onPress={handleCancel}
              />
            ) : null}
          </View>
        </LinearGradient>
      </Animated.View>
    </Animated.View>
  );
};

export default SuccessFailAlert;

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    backgroundColor: Colors.Black68,
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
  },
  modalContent: {
    borderRadius: 16,
    overflow: 'hidden',
    ...Theme.shadow,
  },
  gradientBackground: {
    width: '100%',
    borderRadius: 16,
  },
  header: {
    paddingTop: 24,
    paddingBottom: 16,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: Colors.WhiteSmoke,
  },
  body: {
    padding: 24,
    alignItems: 'center',
  },
  icon: {
    width: 60,
    height: 60,
    alignSelf: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: 16,
    paddingTop: 0,
    justifyContent: 'space-between',
  },
  primaryButton: {
    flex: 1,
    marginRight: 8,
  },
  secondaryButton: {
    flex: 1,
    marginLeft: 8,
  },
});
