const Routes = {
  Splash: 'Splash',
  MainTab: 'MainTab',
  HomeStack: 'HomeStack',
  Chat: 'Chat',
  //Onboading
  OnBoarding: 'OnBoarding',
  //Log In & Sign Up
  Login: 'Login',
  ForgetPassword: 'ForgetPassword',
  RecoveryPassword: 'RecoveryPassword',
  ChangePasswordSuccessful: 'ChangePasswordSuccessful',
  SignUp: 'SignUp',
  VerifyPhoneNumber: 'VerifyPhoneNumber',
  SignUpSuccessful: 'SignUpSuccessful',
  BasicInformation: 'BasicInformation',
  WorkProfile: 'WorkProfile',
  OtherInformation: 'OtherInformation',
  SentVerifySuccessful: 'SentVerifySuccessful',
  //Home & Schedule
  Home: 'Home',
  Notification: 'Notification',
  Schedule: 'Schedule',
  //Next Consults
  Consult: 'Consult',
  ConsultDetail: 'ConsultDetail',
  //Screens
  CallReportProblem: 'CallReportProblem',
  SelectAddress: 'SelectAddress',
  VideoCallConnected: 'VideoCallConnected',
  EndCall: 'EndCall',
  PastConsultDetail: 'PastConsultDetail',
  WriteAnAnswer: 'WriteAnAnswer',
  ShareDoctor: 'ShareDoctor',
  //Past Consults
  PastConsultsFilter: 'PastConsultsFilter',
  //Private Care - Video Call'
  VideoCall: 'VideoCall',
  //Private Care - Live Chat
  PrivateCareLiveChat: 'PrivateCareLiveChat',
  ShareMedication: 'ShareMedication',
  //Free Consults
  FreeConsults: 'FreeConsults',
  FreeConsultDetail: 'FreeConsultDetail',
  // Search
  Search: 'Search',
  RecentSearch: 'RecentSearch',
  SearchResult: 'SearchResult',
  DoctorFilter: 'DoctorFilter',
  //Search Specials
  SearchSpecialDoctor: 'SearchSpecialDoctor',
  SearchDoctorResults: 'SearchDoctorResults',
  ConditionsAndSymptoms: 'ConditionsAndSymptoms',
  Medications: 'Medications',
  MedicationDetail: 'MedicationDetail',
  SearchSpecialHospital: 'SearchSpecialHospital',
  HospitalDetail: 'HospitalDetail',
  //Patients Management
  //
  //Search
  //
  //Search - Specials
  //
  //Private Chat
  ChatStack: 'ChatStack',
  PrivateChatStack: 'PrivateChatStack',
  PrivateChat: 'PrivateChat',
  ChatDetail: 'ChatDetail',
  //Health Feed
  HealthFeed: 'HealthFeed',
  HealthFeedStack: 'HealthFeedStack',
  TipDetail: 'TipDetail',
  TopicDetail: 'TopicDetail',
  TopicDetailConditions: 'TopicDetailConditions',
  //My Work
  UserProfileStack: 'UserProfileStack',
  //My Work - Finance Report
  UserProfile: 'UserProfile',
  FinanceReport: 'FinanceReport',
  IncomeReport: 'IncomeReport',
  WithdrawHistory: 'WithdrawHistory',
  //My Work - Topics
  MyWorkTopics: 'MyWorkTopics',
  CreateByMe: 'CreateByMe',
  FollowingTopics: 'FollowingTopics',
  TopicDetails: 'TopicDetails',
  TopicCreate: 'TopicCreate',
  TopicAddCondition: 'TopicAddCondition',
  //My Work - Health Guide & Tips
  HealthGuide: 'HealthGuide',
  HealthGuideDetail: 'HealthGuideDetail',
  HealthGuideSaved: 'HealthGuideSaved',
  HealthGuideSavedDetail: 'HealthGuideSavedDetail',
  HealthGuideCreate: 'HealthGuideCreate',
  HealthTips: 'HealthTips',
  HealthTipsCreate: 'HealthTipsCreate',
  //My Work - Library
  MyWorkLibrary: 'MyWorkLibrary',
  MyWorkNetwork: 'MyWorkNetwork',
  WorkProfileManagement: 'WorkProfileManagement',
  ServicesManagement: 'ServicesManagement',
  SendMessage: 'SendMessage',
  OnlineAppointment: 'OnlineAppointment',
  SetWorkingDay: 'SetWorkingDay',
  SetWorkingTime: 'SetWorkingTime',
  //Account
  AccountStack: 'AccountStack',
  Account: 'Account',
  BankDeposit: 'BankDeposit',
  BankDepositAddNew: 'BankDepositAddNew',
  AccountInviteExpert: 'AccountInviteExpert',
  AlertsStack: 'AlertsStack',

  PatientAlerts: 'PatientAlerts',
  PatientsStack: 'PatientsStack',
  Patients: 'Patients',

  PatientDetails: 'PatientDetails',
  PatientVitalReports: 'PatientVitalReports',
  MedicationPage: 'MedicationPage',
  PatientMapView: 'PatientMapView',

  UpdateProfile: 'UpdateProfile',
  ChangePassword: 'ChangePassword',
  CustomAlerts: 'CustomAlerts',

  VitalReportItem: 'VitalReportItem',
  VitalRepors: 'VitalRepors',

  AddMedication: 'AddMedication',

  PatientVideoCall: 'PatientVideoCall',

  CCMReports: 'CCMReports',
  CarePlanDetails: 'CarePlanDetails',
  BillingInfo: 'BillingInfo',
  Validation: 'Validation',

  EditTask: 'EditTask',

  Organization: 'Organization',
  PasswordExpire: 'PasswordExpire',
};

// Export Routes for backward compatibility
export { Routes };

export default Routes;
