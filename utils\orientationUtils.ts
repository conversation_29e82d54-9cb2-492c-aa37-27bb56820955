import { ViewStyle, TextStyle, ImageStyle } from 'react-native';
import { 
  isTablet, 
  isLargeTablet, 
  getOrientation, 
  spacing, 
  typography,
  iconSizes,
  buttonDimensions,
  tabletLayout 
} from './responsive';

export type Orientation = 'portrait' | 'landscape';
export type DeviceType = 'phone' | 'tablet' | 'large-tablet';

/**
 * Get orientation-specific spacing values
 */
export const getOrientationSpacing = (orientation?: Orientation) => {
  const currentOrientation = orientation || getOrientation();
  
  if (isLargeTablet()) {
    return {
      xs: currentOrientation === 'landscape' ? spacing.sm : spacing.xs,
      sm: currentOrientation === 'landscape' ? spacing.md : spacing.sm,
      md: currentOrientation === 'landscape' ? spacing.lg : spacing.md,
      lg: currentOrientation === 'landscape' ? spacing.xl : spacing.lg,
      xl: currentOrientation === 'landscape' ? spacing.xxl : spacing.xl,
    };
  }
  
  if (isTablet()) {
    return {
      xs: currentOrientation === 'landscape' ? spacing.sm : spacing.xs,
      sm: currentOrientation === 'landscape' ? spacing.md : spacing.sm,
      md: currentOrientation === 'landscape' ? spacing.lg : spacing.md,
      lg: currentOrientation === 'landscape' ? spacing.xl : spacing.lg,
      xl: currentOrientation === 'landscape' ? spacing.xl : spacing.lg,
    };
  }
  
  return spacing;
};

/**
 * Get orientation-specific typography values
 */
export const getOrientationTypography = (orientation?: Orientation) => {
  const currentOrientation = orientation || getOrientation();
  
  if (!isTablet()) {
    return typography;
  }
  
  // For tablets, slightly adjust typography based on orientation
  const multiplier = currentOrientation === 'landscape' ? 1.1 : 1;
  
  return {
    h1: {
      ...typography.h1,
      fontSize: Math.round(typography.h1.fontSize * multiplier),
      lineHeight: Math.round(typography.h1.lineHeight * multiplier),
    },
    h2: {
      ...typography.h2,
      fontSize: Math.round(typography.h2.fontSize * multiplier),
      lineHeight: Math.round(typography.h2.lineHeight * multiplier),
    },
    h3: {
      ...typography.h3,
      fontSize: Math.round(typography.h3.fontSize * multiplier),
      lineHeight: Math.round(typography.h3.lineHeight * multiplier),
    },
    body: {
      ...typography.body,
      fontSize: Math.round(typography.body.fontSize * multiplier),
      lineHeight: Math.round(typography.body.lineHeight * multiplier),
    },
    bodySmall: {
      ...typography.bodySmall,
      fontSize: Math.round(typography.bodySmall.fontSize * multiplier),
      lineHeight: Math.round(typography.bodySmall.lineHeight * multiplier),
    },
    caption: {
      ...typography.caption,
      fontSize: Math.round(typography.caption.fontSize * multiplier),
      lineHeight: Math.round(typography.caption.lineHeight * multiplier),
    },
  };
};

/**
 * Get orientation-specific layout configurations
 */
export const getOrientationLayout = (orientation?: Orientation) => {
  const currentOrientation = orientation || getOrientation();
  
  return {
    // Container padding
    containerPadding: {
      horizontal: isLargeTablet() 
        ? (currentOrientation === 'landscape' ? spacing.xxl : spacing.xl)
        : isTablet() 
        ? (currentOrientation === 'landscape' ? spacing.xl : spacing.lg)
        : spacing.md,
      vertical: isTablet() 
        ? (currentOrientation === 'landscape' ? spacing.lg : spacing.md)
        : spacing.sm,
    },
    
    // Grid columns
    gridColumns: isLargeTablet()
      ? (currentOrientation === 'landscape' ? 4 : 3)
      : isTablet()
      ? (currentOrientation === 'landscape' ? 3 : 2)
      : 1,
    
    // Card dimensions
    cardSpacing: isTablet() 
      ? (currentOrientation === 'landscape' ? spacing.lg : spacing.md)
      : spacing.sm,
    
    // Button sizing
    buttonHeight: isTablet() 
      ? (currentOrientation === 'landscape' ? buttonDimensions.height + 4 : buttonDimensions.height)
      : buttonDimensions.height,
    
    // Icon sizing
    iconScale: isTablet() 
      ? (currentOrientation === 'landscape' ? 1.2 : 1.1)
      : 1,
  };
};

/**
 * Create orientation-aware styles
 */
export const createOrientationStyles = <T extends ViewStyle | TextStyle | ImageStyle>(
  portraitStyles: T,
  landscapeStyles: T,
  orientation?: Orientation
): T => {
  const currentOrientation = orientation || getOrientation();
  return currentOrientation === 'landscape' ? landscapeStyles : portraitStyles;
};

/**
 * Get master-detail layout configuration based on orientation
 */
export const getMasterDetailConfig = (orientation?: Orientation) => {
  const currentOrientation = orientation || getOrientation();
  
  if (!isTablet()) {
    return {
      showMaster: false,
      showDetail: true,
      masterWidth: 0,
      detailWidth: '100%' as const,
      direction: 'column' as const,
    };
  }
  
  if (currentOrientation === 'landscape') {
    return {
      showMaster: true,
      showDetail: true,
      masterWidth: tabletLayout.sidebarWidth,
      detailWidth: `${(1 - tabletLayout.masterDetailSplit.master) * 100}%` as const,
      direction: 'row' as const,
    };
  }
  
  // Portrait tablet - stack vertically or use modal for master
  return {
    showMaster: false, // Will be shown as modal or drawer
    showDetail: true,
    masterWidth: 0,
    detailWidth: '100%' as const,
    direction: 'column' as const,
  };
};

/**
 * Get orientation-specific animation configurations
 */
export const getOrientationAnimations = (orientation?: Orientation) => {
  const currentOrientation = orientation || getOrientation();
  
  return {
    // Transition duration for orientation changes
    orientationTransition: isTablet() ? 300 : 200,
    
    // Scale animations
    scaleIn: currentOrientation === 'landscape' && isTablet() ? 1.02 : 1.01,
    
    // Slide animations
    slideDistance: currentOrientation === 'landscape' && isTablet() ? 20 : 15,
    
    // Fade animations
    fadeOpacity: {
      hidden: 0,
      visible: 1,
      semi: currentOrientation === 'landscape' && isTablet() ? 0.8 : 0.7,
    },
  };
};

/**
 * Utility to preserve state during orientation changes
 */
export const preserveStateOnOrientationChange = <T>(
  state: T,
  callback: (newState: T) => void
) => {
  // This can be used to maintain component state during orientation changes
  // The callback will be called with the preserved state after orientation change
  setTimeout(() => {
    callback(state);
  }, 100); // Small delay to ensure orientation change is complete
};

/**
 * Get safe area adjustments for different orientations
 */
export const getOrientationSafeArea = (orientation?: Orientation) => {
  const currentOrientation = orientation || getOrientation();
  
  return {
    // Additional padding for landscape mode on tablets
    additionalPadding: {
      horizontal: currentOrientation === 'landscape' && isTablet() ? spacing.md : 0,
      vertical: currentOrientation === 'landscape' && isTablet() ? spacing.sm : 0,
    },
    
    // Content insets
    contentInsets: {
      top: currentOrientation === 'landscape' ? spacing.sm : 0,
      bottom: currentOrientation === 'landscape' ? spacing.sm : 0,
      left: currentOrientation === 'landscape' && isTablet() ? spacing.md : 0,
      right: currentOrientation === 'landscape' && isTablet() ? spacing.md : 0,
    },
  };
};

export default {
  getOrientationSpacing,
  getOrientationTypography,
  getOrientationLayout,
  createOrientationStyles,
  getMasterDetailConfig,
  getOrientationAnimations,
  preserveStateOnOrientationChange,
  getOrientationSafeArea,
};