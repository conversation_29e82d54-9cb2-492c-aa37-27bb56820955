import Text from "@/components/Text";
import React from "react";
import { StyleSheet, View, Dimensions } from "react-native";

interface VitalRangeBarProps {
  currentValue: number;
  min?: number;
  max?: number;
  cMin?: number;
  cMax?: number;
  unit: string;
  title: string;
}

const VitalRangeBar = ({
  currentValue,
  min,
  max,
  cMin,
  cMax,
  unit,
  title
}: VitalRangeBarProps) => {
  // If no range data is available, don't render the bar
  if (min === undefined || max === undefined) {
    return null;
  }

  // Calculate the full range (including critical ranges if available)
  const minRange = cMin !== undefined ? Math.min(cMin, min) : min;
  const maxRange = cMax !== undefined ? Math.max(cMax, max) : max;
  
  // Add some padding to the range for better visualization
  const rangePadding = (maxRange - minRange) * 0.1;
  const visualMin = minRange - rangePadding;
  const visualMax = maxRange + rangePadding;
  const totalRange = visualMax - visualMin;

  // Calculate positions as percentages
  const getPosition = (value: number) => {
    return Math.max(0, Math.min(100, ((value - visualMin) / totalRange) * 100));
  };

  const currentPosition = getPosition(currentValue);
  const normalStart = getPosition(min);
  const normalEnd = getPosition(max);
  const criticalLowEnd = cMin !== undefined ? getPosition(cMin) : 0;
  const criticalHighStart = cMax !== undefined ? getPosition(cMax) : 100;

  // Determine current value status
  const getValueStatus = () => {
    if (cMin !== undefined && currentValue < cMin) return 'critical-low';
    if (cMax !== undefined && currentValue > cMax) return 'critical-high';
    if (currentValue < min || currentValue > max) return 'abnormal';
    return 'normal';
  };

  const valueStatus = getValueStatus();

  // Get colors based on status
  const getIndicatorColor = () => {
    switch (valueStatus) {
      case 'critical-low':
      case 'critical-high':
        return '#E53E3E'; // Red for critical
      case 'abnormal':
        return '#D69E2E'; // Orange for abnormal
      case 'normal':
        return '#38A169'; // Green for normal
      default:
        return '#A0AEC0'; // Gray for unknown
    }
  };

  // Calculate middle value
  const middleValue = (min + max) / 2;
  const middlePosition = getPosition(middleValue);

  // Calculate responsive width for alignment calculations
  const getContainerWidth = () => {
    const screenWidth = Dimensions.get('window').width;
    const cardPadding = 32; // 16px card padding on each side
    const rangePadding = 50; // 25px VitalRangeBar padding on each side
    const safetyMargin = 20; // Additional margin for edge elements
    const totalPadding = cardPadding + rangePadding + safetyMargin;
    return Math.max(150, screenWidth - totalPadding); // Conservative width to prevent clipping
  };

  // Calculate precise alignment for arrows - center them exactly on normal range boundaries
  const getMinArrowAlignment = () => {
    // Center the MIN arrow exactly at the start of the normal range with bounds checking
    const containerWidth = 24;
    const centerOffset = containerWidth / 2;
    const actualWidth = getContainerWidth();
    const leftPosition = (normalStart / 100) * actualWidth;
    
    // Ensure it doesn't go off the left edge
    const minBound = -leftPosition + 2; // 2px margin from left edge
    const centered = -centerOffset; // Normal centering
    
    return Math.max(minBound, centered);
  };

  const getMaxArrowAlignment = () => {
    // Center the MAX arrow exactly at the end of the normal range with bounds checking
    const containerWidth = 24;
    const centerOffset = containerWidth / 2;
    const actualWidth = getContainerWidth();
    const rightPosition = (normalEnd / 100) * actualWidth;
    
    // Ensure it doesn't go off the right edge
    const maxBound = actualWidth - rightPosition - containerWidth + 2; // 2px margin from right edge
    const centered = -centerOffset; // Normal centering
    
    return Math.min(maxBound, centered);
  };

  const getCurrentValueAlignment = () => {
    return -21;
  };

  return (
    <View style={styles.container}>
      <View style={styles.referenceRangeContainer}>
        <Text size={11} bold={true} color="#4A5568" style={styles.referenceRangeText}>Reference Range</Text>
      </View>
      {/* Visual Bar */}
      <View style={styles.barContainer}>
        {/* Background Track */}
        <View style={styles.backgroundTrack} />
        
        {/* Critical Low Range */}
        {cMin !== undefined && (
          <View
            style={[
              styles.criticalRange,
              {
                left: '0%',
                width: `${criticalLowEnd}%`,
                backgroundColor: '#E53E3E', // Full red color
              }
            ]}
          />
        )}
        
        {/* Normal Range */}
        <View
          style={[
            styles.normalRange,
            {
              left: `${normalStart}%`,
              width: `${normalEnd - normalStart}%`,
              backgroundColor: '#38A169', // Full green color
            }
          ]}
        />
        
        {/* Critical High Range */}
        {cMax !== undefined && (
          <View
            style={[
              styles.criticalRange,
              {
                left: `${criticalHighStart}%`,
                width: `${100 - criticalHighStart}%`,
                backgroundColor: '#E53E3E', // Full red color
              }
            ]}
          />
        )}
        
        {/* Min Arrow Indicator */}
        <View style={[
          styles.minArrowIndicator, 
          { 
            left: `${normalStart}%`,
            transform: [{ translateX: getMinArrowAlignment() }]
          }
        ]}>
          <Text size={10} bold color="#4A5568" style={styles.minMaxLabel}>
            MIN
          </Text>
          <Text size={12} bold color="#38A169" style={styles.minValueLabel}>
            {min}
          </Text>
          <View style={styles.arrowDown}>
            <View style={styles.arrowTriangle} />
          </View>
        </View>
        
        {/* Max Arrow Indicator */}
        <View style={[
          styles.maxArrowIndicator, 
          { 
            left: `${normalEnd}%`,
            transform: [{ translateX: getMaxArrowAlignment() }]
          }
        ]}>
          <Text size={10} bold color="#4A5568" style={styles.minMaxLabel}>
            MAX
          </Text>
          <Text size={12} bold color="#E53E3E" style={styles.maxValueLabel}>
            {max}
          </Text>
          <View style={styles.arrowDown}>
            <View style={styles.arrowTriangle} />
          </View>
        </View>
        
        {/* Current Value Indicator */}
        <View
          style={[
            styles.currentIndicator,
            {
              left: `${currentPosition}%`,
              backgroundColor: getIndicatorColor(),
            }
          ]}
        />
        
        {/* Current Value Label - Right beside the indicator */}
        <View 
          style={[
            styles.currentValueIndicator,
            { 
              left: `${currentPosition}%`,
              transform: [{ translateX: getCurrentValueAlignment() }]
            }
          ]}
        >
          <Text size={12} bold color={getIndicatorColor()} style={styles.currentValueText}>
            {currentValue}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#FFFFFF", 
    paddingHorizontal: 25, // Add padding to prevent edge clipping
    paddingTop: 28, // Reduced since no reference range text
    paddingBottom: 8,
    marginBottom: 8,
    width: '100%',
    overflow: 'hidden', // Keep contained within card
  },
  
  barContainer: {
    height: 16, // Reduced height for compact design
    position: 'relative',
    marginBottom: 24, // Reduced space for current value label
    width: '100%',
    minWidth: 180, // Further reduced minimum width to prevent clipping
    maxWidth: '100%', // Limit maximum width to leave room for edge elements
  },
  
  backgroundTrack: {
    position: 'absolute',
    width: '100%',
    height: 6, // Reduced height for compact design
    backgroundColor: '#F1F5F9',
    borderRadius: 3,
    top: 5,
  },
  
  normalRange: {
    position: 'absolute',
    height: 6, // Reduced height
    borderRadius: 3,
    top: 5,
    borderWidth: 1,
    borderColor: '#9AE6B4',
  },
  
  criticalRange: {
    position: 'absolute',
    height: 6, // Reduced height
    borderRadius: 3,
    top: 5,
    borderWidth: 1,
    borderColor: '#FC8181',
  },
  
  currentIndicator: {
    position: 'absolute',
    width: 3, 
    height: 16, 
    borderRadius: 1.5,
    top: 0,
    transform: [{ translateX: -1.5 }], 
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
  },
  
  // Current value indicator positioned below the bar
  currentValueIndicator: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    top: 20,
    width: 42, 
  },
  
  currentValueText: {
    textAlign: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    fontSize: 10, 
    fontWeight: 'bold',
    minWidth: 42, 
    maxWidth: 42, 
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 2,
  },
  
 
    // Separate arrow indicators for precise alignment control
    minArrowIndicator: {
      position: 'absolute',
      alignItems: 'center',
      top: -37, 
    },
    
    maxArrowIndicator: {
      position: 'absolute',
      alignItems: 'center',
      top: -37, 
    },
  
  arrowDown: {
    alignItems: 'center',
    marginTop: 3,
  },
  
  arrowTriangle: {
    width: 0,
    height: 0,
    borderLeftWidth: 4, 
    borderRightWidth: 4,
    borderTopWidth: 7,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: '#000000', 
  },
  
  minMaxLabel: {
    textAlign: 'center',
    fontSize: 10,
    fontWeight: 'bold',
    color: '#4A5568',
    marginBottom: 2,
  },
  
  minValueLabel: {
    textAlign: 'center',
    minWidth: 24, 
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 6, 
    paddingVertical: 2,
    borderRadius: 3,
    borderWidth: 1,
    borderColor: '#38A169',
    marginBottom: 2,
    fontSize: 10, 
    flexShrink: 0,
  },
  
  maxValueLabel: {
    textAlign: 'center',
    minWidth: 24, 
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 6, 
    paddingVertical: 2,
    borderRadius: 3,
    borderWidth: 1,
    borderColor: '#E53E3E',
    marginBottom: 2,
    fontSize: 10, 
    flexShrink: 0,
  },
  
  referenceRangeContainer: {
    marginBottom: 10,
  },
  
  referenceRangeText: {
    textAlign: 'center',
    fontSize: 11,
    fontWeight: 'bold',
    color: '#4A5568',
  },

});

export default VitalRangeBar; 