import {
  StackActions,
  useIsFocused,
  useNavigation,
} from '@react-navigation/native';
import React, { memo, useCallback, useEffect } from 'react';
import { 
  BackHandler, 
  Image, 
  StyleSheet, 
  View,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import ButtonLinear from '@/components/Buttons/ButtonLinear';
import Text from '@/components/Text';
import { Colors, Routes } from '@/constants';
import scale from '@/utils/scale';

const { width } = Dimensions.get('window');

const ChangePasswordSuccessful = memo(() => {
  const navigation = useNavigation() as any;

  const onGoToLogin = useCallback(() => {
    navigation.dispatch(StackActions.replace(Routes.Login, {}));
  }, [navigation]);

  const isFocused = useIsFocused();
  useEffect(() => {
    const exitApp = () => {
      BackHandler.exitApp();
      return true;
    };
    if (isFocused) {
      BackHandler.addEventListener('hardwareBackPress', exitApp);
    }
    return () => {
      BackHandler.removeEventListener('hardwareBackPress', exitApp);
    };
  }, [isFocused]);

  return (
    <SafeAreaView style={styles.container}>
      {/* Background Gradient */}
      <LinearGradient
        colors={['#f8fffe', '#e8f8f7', '#ffffff']}
        style={styles.background}
      />

      <View style={styles.contentContainer}>
        {/* Success Icon */}
        <View style={styles.iconContainer}>
          <View style={styles.successIconWrapper}>
            <Image
              source={require('@/assets/images/ic_accept.png')}
              style={styles.successIcon}
              resizeMode="contain"
            />
          </View>
        </View>

        {/* Success Content */}
        <View style={styles.textContainer}>
          <Text style={styles.title}>Password Changed!</Text>
          <Text style={styles.subtitle}>
            Your password has been successfully updated. You can now use your new password to sign in to your account.
          </Text>
        </View>

        {/* Action Button */}
        <View style={styles.buttonContainer}>
          <ButtonLinear
            title="Continue to Sign In"
            style={styles.buttonLinear}
            onPress={onGoToLogin}
          />
        </View>

        {/* Footer */}
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>
            Keep your password secure and don't share it with anyone
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
});

export default ChangePasswordSuccessful;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.White,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  iconContainer: {
    marginBottom: 40,
  },
  successIconWrapper: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: Colors.Malachite,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.Malachite,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  successIcon: {
    width: 60,
    height: 60,
    tintColor: Colors.White,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 48,
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    color: Colors.DarkJungleGreen,
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: Colors.GrayBlue,
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 16,
  },
  buttonContainer: {
    width: '100%',
    marginBottom: 32,
  },
  buttonLinear: {
    height: 56,
    borderRadius: 16,
    shadowColor: Colors.TiffanyBlue,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  footerContainer: {
    paddingHorizontal: 24,
  },
  footerText: {
    fontSize: 14,
    color: Colors.GrayBlue,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
