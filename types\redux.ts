/**
 * Redux state type definitions
 */

// Define the shape of the patients state
export interface PatientsState {
  patients: Array<{
    id: number;
    name: string;
    [key: string]: any;
  }>;
  loading: boolean;
  error: string | null;
}

// Define the shape of the login state
export interface LoginState {
  data: any;
  isLoggedIn: boolean;
  isLoggingIn: boolean;
  msg: string;
}

// Define the shape of the current patient ID state
export interface CurrentPatientIdState {
  patientId: string | null;
}

// Define the shape of the root state
export interface RootState {
  patientsListReducer: PatientsState;
  loginReducer: LoginState;
  currentPatientIdReducer: CurrentPatientIdState;
  [key: string]: any; // Allow for other reducers
}
