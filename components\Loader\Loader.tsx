import React from 'react';
import {ActivityIndicator, View} from 'react-native';
import {Colors} from '@/constants';

const Loader = (props: any) => {
  return props.modalVisible ? (
    <View
      style={{
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10,
        position: 'absolute',
        backgroundColor: 'rgba(0,0,0,0.5)',
      }}>
      <View
        style={{
          height: 70,
          width: 70,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: Colors.BlueCrayola,
          borderRadius: 5,
        }}>
        <ActivityIndicator size="large" color={'white'} />
      </View>
    </View>
  ) : (
    <View />
  );
};

export default Loader;
