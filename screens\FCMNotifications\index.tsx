import React, { useEffect, useState, useCallback } from 'react';
import {
  StyleSheet,
  View,
  FlatList,
  RefreshControl,
  StatusBar,
  TouchableOpacity,
  Animated,
  Dimensions,
  Alert,
  ActivityIndicator
} from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import Container from '@/components/Layout/Container';
import NavigationHeader from '@/components/NavigationHeader';
import Text from '@/components/Text';
import Loader from '@/components/Loader/Loader';
import { Colors } from '@/constants';
import { useIsFocused } from '@react-navigation/native';
import { useRouter } from 'expo-router';
import { useSelector, useDispatch } from 'react-redux';
import { clearNotification } from '@/services/actions/clearNotificationActions';
import { fetchUnreadMessages } from '@/services/actions/unreadMessagesActions';
import { useAlertsCount } from '@/context/AlertsCountContext';

const { width: screenWidth } = Dimensions.get('window');

// Unread message interface based on API response
interface UnreadMessage {
  patientName: string;
  patientId: string;
}

// API response interface
interface UnreadMessagesResponse {
  data: UnreadMessage[];
  success: boolean;
}

const FCMNotificationsScreen: React.FC = () => {
  const [unreadMessages, setUnreadMessages] = useState<UnreadMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [clearingNotifications, setClearingNotifications] = useState<Set<string>>(new Set());
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const slideAnim = React.useRef(new Animated.Value(50)).current;
  
  const isFocused = useIsFocused();
  const router = useRouter();
  const dispatch = useDispatch();

  // Get user data from Redux
  const userId = useSelector((state: any) => state?.loginReducer?.data?.userId);
  const orgId = useSelector((state: any) => state?.currentOrgIdReducer?.orgId);

  // Get alerts count context to refresh after clearing notifications
  const { refreshAlertsCount } = useAlertsCount();

  // API call to fetch unread messages using centralized action
  const fetchUnreadMessagesData = useCallback(async () => {
    const startTime = Date.now();
    
    if (!userId || !orgId) {
      setLoading(false);
      setRefreshing(false);
      return;
    }

    try {
      
      const requestParams = {
        userId: userId.toString(),
        orgId: orgId.toString()
      };
      
      // Use the centralized action
      const response = await (dispatch as any)(fetchUnreadMessages(requestParams));

      if (!response || !response.success) {
        setUnreadMessages([]);
      } else {
        const messagesData = response.data || [];
        
        setUnreadMessages(messagesData);
      }

      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
      });
      
      const endTime = Date.now();
      
    } catch (error) {
      setUnreadMessages([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [userId, orgId, unreadMessages.length, dispatch]);

  // Refresh handler
  const handleRefresh = () => {
    
    setRefreshing(true);
    fetchUnreadMessagesData();
  };

  // Load unread messages when screen is focused
  useEffect(() => {
    if (isFocused) {
      fetchUnreadMessagesData();
    }
  }, [isFocused, fetchUnreadMessagesData]);

  // Handle unread message press (clear notification then navigate to chat)
  const handleMessagePress = async (message: UnreadMessage) => {
    
    // Check if already clearing this notification
    if (clearingNotifications.has(message.patientId)) {
      return;
    }

    // Add to clearing set
    setClearingNotifications(prev => new Set(prev).add(message.patientId));
    
    try {
      // Call clear notification API using Redux action
      const success = await (dispatch as any)(clearNotification(message.patientId));
      
      if (success) {
        
        // Remove from unread messages list immediately for better UX
        setUnreadMessages(prev => prev.filter(msg => msg.patientId !== message.patientId));
        
        // Refresh alerts count to update the notification bell
        await refreshAlertsCount();
        
        // Also refresh the unread messages list to ensure consistency with server
        setTimeout(() => {
          fetchUnreadMessagesData();
        }, 500); // Small delay to ensure server has processed the clear request
        
        router.push(`/patient/chat/${message.patientId}`);
      } else {
        Alert.alert(
          'Error',
          'Failed to clear notification. Please try again.',
          [
            {
              text: 'OK',
              onPress: () => {
                // Still navigate to chat even if clear failed
                router.push(`/patient/chat/${message.patientId}`);
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error('FCMNotificationsScreen: Error during clear notification:', error);
      Alert.alert(
        'Error',
        'An error occurred while clearing the notification. You can still access the chat.',
        [
          {
            text: 'OK',
            onPress: () => {
              // Still navigate to chat even if clear failed
              console.log('FCMNotificationsScreen: Navigating to chat despite error...');
              router.push(`/patient/chat/${message.patientId}`);
            }
          }
        ]
      );
    } finally {
      // Remove from clearing set
      setClearingNotifications(prev => {
        const newSet = new Set(prev);
        newSet.delete(message.patientId);
        return newSet;
      });
    }
  };

  // Render unread message item
  const renderUnreadMessageItem = ({ item, index }: { item: UnreadMessage; index: number }) => {

    const isClearing = clearingNotifications.has(item.patientId);

    return (
      <Animated.View
        style={[
          styles.messageCardWrapper,
          { opacity: fadeAnim }
        ]}
      >
        <TouchableOpacity
          style={[
            styles.messageCard,
            isClearing && styles.messageCardClearing
          ]}
          onPress={() => handleMessagePress(item)}
          activeOpacity={isClearing ? 1 : 0.9}
          delayPressIn={50}
          disabled={isClearing}
        >
          {/* Unread indicator bar */}
          <View style={styles.unreadIndicatorBar} />
          
          <View style={styles.messageContent}>
            {/* Message icon */}
            <View style={styles.iconContainer}>
              <Ionicons
                name="chatbubble-outline"
                size={22}
                color={Colors.TealBlue}
              />
            </View>

            {/* Content */}
            <View style={styles.textContent}>
              <View style={styles.headerRow}>
                <View style={styles.titleContainer}>
                  <Text style={styles.title} numberOfLines={1}>
                    {item.patientName}
                  </Text>
                  <View style={styles.unreadDot} />
                </View>
                <Text style={styles.timestamp}>
                  New message
                </Text>
              </View>
              
              <Text style={styles.body} numberOfLines={2}>
                You have unread messages from this patient
              </Text>
              
              {/* Type badge and action hint */}
              <View style={styles.footerRow}>
                <View style={styles.badgeContainer}>
                  <View style={styles.typeBadge}>
                    <Text style={styles.typeText}>
                      Chat Message
                    </Text>
                  </View>
                </View>
                <Text style={styles.actionHint}>
                  {isClearing ? 'Clearing...' : 'Tap to open chat'}
                </Text>
              </View>
            </View>

            {/* Arrow icon or loading spinner */}
            <View style={styles.actions}>
              {isClearing ? (
                <ActivityIndicator size="small" color={Colors.TealBlue} />
              ) : (
                <Ionicons 
                  name="chevron-forward-outline" 
                  size={20} 
                  color={Colors.GrayBlue} 
                />
              )}
            </View>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  // Render empty state
  const renderEmptyState = () => (
    <Animated.View
      style={[
        styles.emptyContainer,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
      <View style={styles.emptyIconContainer}>
        <Ionicons
          name="chatbubble-outline"
          size={80}
          color={Colors.GrayBlue}
        />
      </View>
      <Text style={styles.emptyTitle}>No Unread Messages</Text>
      <Text style={styles.emptySubtitle}>
        You're all caught up! No patients have sent you unread messages.
      </Text>
      <View style={styles.emptyInfoCard}>
        <View style={styles.infoRow}>
          <Ionicons name="refresh-outline" size={16} color={Colors.TealBlue} />
          <Text style={styles.infoText}>Pull down to refresh</Text>
        </View>
        <View style={styles.infoRow}>
          <Ionicons name="chatbubbles-outline" size={16} color={Colors.TealBlue} />
          <Text style={styles.infoText}>New messages will appear here</Text>
        </View>
      </View>
    </Animated.View>
  );

  // Render header
  const renderHeader = () => {
    const unreadCount = unreadMessages.length;
    
    return (
      <View style={styles.header}>
        <View style={styles.headerTitleContainer}>
          <Text style={styles.headerTitle}>Unread Messages</Text>
          {unreadCount > 0 && (
            <View style={styles.unreadBadge}>
              <Text style={styles.unreadBadgeText}>{unreadCount}</Text>
            </View>
          )}
        </View>
        
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={handleRefresh}
          hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
        >
          <Ionicons 
            name="refresh-outline" 
            size={20} 
            color={Colors.TealBlue} 
          />
        </TouchableOpacity>
      </View>
    );
  };

  if (loading && !refreshing) {
    return (
      <Container>
        <StatusBar backgroundColor={Colors.White} barStyle="dark-content" />
        <NavigationHeader 
          title="Unread Messages" 
          showBackButton={true}
          showLogo={false}
          showRightLogo={true}
        />
        <View style={styles.loadingContainer}>
          <Loader />
          <Text style={styles.loadingText}>Loading unread messages...</Text>
        </View>
      </Container>
    );
  }

  return (
    <Container>
      <StatusBar backgroundColor={Colors.White} barStyle="dark-content" />
      <NavigationHeader 
        title="Notifications" 
        showBackButton={true}
        showLogo={false}
        showRightLogo={true}
      />
      
      <View style={styles.container}>
        {renderHeader()}
        
        <FlatList
          data={unreadMessages}
          renderItem={renderUnreadMessageItem}
          keyExtractor={(item, index) => `${item.patientId}-${index}`}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[Colors.TealBlue]}
              tintColor={Colors.TealBlue}
            />
          }
          contentContainerStyle={[
            styles.listContainer,
            unreadMessages.length === 0 && styles.emptyListContainer,
          ]}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.White,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.White,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.GrayBlue,
    fontFamily: 'DMSans-Regular',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.White,
    borderBottomWidth: 1,
    borderBottomColor: Colors.Platinum,
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.DarkJungleGreen,
    fontFamily: 'DMSans-Bold',
  },
  unreadBadge: {
    backgroundColor: Colors.Red,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginLeft: 8,
    minWidth: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  unreadBadgeText: {
    color: Colors.White,
    fontSize: 12,
    fontWeight: 'bold',
    fontFamily: 'DMSans-Bold',
  },
  refreshButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: Colors.lightBlue,
  },
  listContainer: {
    padding: 16,
  },
  emptyListContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  messageCardWrapper: {
    marginBottom: 12,
  },
  messageCard: {
    backgroundColor: Colors.White,
    borderRadius: 12,
    shadowColor: Colors.Black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: Colors.lightBlue,
  },
  messageCardClearing: {
    opacity: 0.6,
    backgroundColor: Colors.Platinum,
  },
  unreadIndicatorBar: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    width: 4,
    backgroundColor: Colors.TealBlue,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
  },
  messageContent: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  iconContainer: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.lightBlue,
    borderColor: Colors.TealBlue + '30',
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContent: {
    flex: 1,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.DarkJungleGreen,
    fontFamily: 'DMSans-Bold',
    marginRight: 8,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.Red,
  },
  timestamp: {
    fontSize: 12,
    color: Colors.TealBlue,
    fontFamily: 'DMSans-Regular',
    fontWeight: '600',
  },
  body: {
    fontSize: 14,
    color: Colors.GrayBlue,
    fontFamily: 'DMSans-Regular',
    lineHeight: 20,
    marginBottom: 8,
  },
  footerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  badgeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeBadge: {
    backgroundColor: Colors.TealBlue + '15',
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  typeText: {
    fontSize: 10,
    color: Colors.TealBlue,
    fontFamily: 'DMSans-Regular',
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  actionHint: {
    fontSize: 12,
    color: Colors.GrayBlue,
    fontFamily: 'DMSans-Regular',
    fontStyle: 'italic',
  },
  actions: {
    marginLeft: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 20,
  },
  emptyIconContainer: {
    marginBottom: 20,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.DarkJungleGreen,
    marginBottom: 8,
    fontFamily: 'DMSans-Bold',
  },
  emptySubtitle: {
    fontSize: 16,
    color: Colors.GrayBlue,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
    fontFamily: 'DMSans-Regular',
  },
  emptyInfoCard: {
    backgroundColor: Colors.lightBlue,
    borderRadius: 12,
    padding: 16,
    width: '100%',
    maxWidth: 280,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: Colors.DarkJungleGreen,
    marginLeft: 8,
    fontFamily: 'DMSans-Regular',
  },
});

export default FCMNotificationsScreen;
