import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import {
  registerForPushNotificationsAsync,
  registerDeviceWithBackend,
  registerNotificationListeners,
  removeNotificationListeners
} from '../../utils/notificationHelper';
import { isRunningInExpoGo } from '../../utils/environmentHelper';
import { apiPostWithToken } from '../apis/apiManager';
import URLS from '../config/config';

/**
 * Configuration options for notification registration
 */
export interface NotificationRegistrationOptions {
  /** User or caregiver ID to register with */
  userId: string;
  /** Whether to show logs (default: false) */
  showLogs?: boolean;
  /** Callback for token reception */
  onTokenReceived?: (token: string | null) => void;
  /** Callback for registration success */
  onRegistrationSuccess?: (response: any) => void;
  /** Callback for registration failure */
  onRegistrationFailure?: (error: any) => void;
}

/**
 * Service for handling application notifications
 */
class NotificationService {
  private static instance: NotificationService;
  private listeners: {
    notification?: Notifications.Subscription;
    response?: Notifications.Subscription;
  } = {};

  /**
   * Get the singleton instance of NotificationService
   */
  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();

      // Configure notification handling for Expo
      Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: false,
        }),
      });
    }
    return NotificationService.instance;
  }

  /**
   * Initialize notification system and register the device
   *
   * @param options Registration options
   * @returns Promise resolving to registration success or error
   */
  public async registerForNotifications(options: NotificationRegistrationOptions): Promise<any> {
    const { userId, showLogs = false, onTokenReceived, onRegistrationSuccess, onRegistrationFailure } = options;

    try {
      if (showLogs) console.log('NotificationService: Starting registration process');

      // First, check and request notification permissions
      const permissionStatus = await this.requestPermissions();

      if (showLogs) console.log('NotificationService: Permission status:', permissionStatus);

      if (permissionStatus.status !== 'granted') {
        if (showLogs) console.log('NotificationService: Permissions not granted');
        if (onRegistrationFailure) {
          onRegistrationFailure(new Error('Notification permissions not granted'));
        }
        return { error: 'Notification permissions not granted' };
      }

      // Register for push notifications
      const token = await registerForPushNotificationsAsync();

      if (showLogs) console.log('NotificationService: Token received:', token);

      // Token received
      if (onTokenReceived) {
        onTokenReceived(token);
      }

      if (!token) {
        if (showLogs) console.log('NotificationService: Failed to get token');
        throw new Error('Failed to get push notification token');
      }

      // For iOS, ensure the token is properly formatted
      let formattedToken = token;
      if (Platform.OS === 'ios' && typeof token === 'string') {
        // Remove any non-alphanumeric characters that might cause issues
        formattedToken = token.replace(/[^a-zA-Z0-9]/g, '');
        if (showLogs) console.log('NotificationService: iOS formatted token:', formattedToken);
      }

      // Register device with backend
      const deviceType = Platform.OS;

      if (showLogs) console.log(`NotificationService: Registering ${deviceType} device with backend`);

      const response = await apiPostWithToken(
        {
          deviceType,
          nurseId: userId,
          registerId: formattedToken,
        },
        URLS.FCMURL
      );

      // Device registered
      if (showLogs) console.log('NotificationService: Device registered successfully:', response);

      if (onRegistrationSuccess) {
        onRegistrationSuccess(response);
      }

      return response;
    } catch (error) {
      // Error handling
      if (showLogs) console.error('NotificationService: Registration error:', error);

      if (onRegistrationFailure) {
        onRegistrationFailure(error);
      }

      return { error };
    }
  }

  /**
   * Set up notification listeners
   *
   * @param onNotificationReceived Callback when notification is received
   * @param onNotificationResponse Callback when user interacts with notification
   */
  public setupListeners(
    onNotificationReceived?: (notification: Notifications.Notification) => void,
    onNotificationResponse?: (response: Notifications.NotificationResponse) => void
  ): void {
    // Clean up any existing listeners
    this.removeListeners();

    // Set up new listeners
    this.listeners.notification = Notifications.addNotificationReceivedListener(notification => {
      if (onNotificationReceived) {
        onNotificationReceived(notification);
      }
    });

    this.listeners.response = Notifications.addNotificationResponseReceivedListener(response => {
      if (onNotificationResponse) {
        onNotificationResponse(response);
      }
    });
  }

  /**
   * Remove all notification listeners
   */
  public removeListeners(): void {
    if (this.listeners.notification) {
      Notifications.removeNotificationSubscription(this.listeners.notification);
    }

    if (this.listeners.response) {
      Notifications.removeNotificationSubscription(this.listeners.response);
    }

    this.listeners = {};
  }

  /**
   * Schedule a local notification
   *
   * @param title Notification title
   * @param body Notification body
   * @param data Additional data to include
   * @param trigger When to show the notification (default: immediately)
   * @returns Promise with the notification identifier
   */
  public async scheduleLocalNotification(
    title: string,
    body: string,
    data?: Record<string, any>,
    trigger?: Notifications.NotificationTriggerInput
  ): Promise<string> {
    return await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data: data || {},
      },
      trigger: trigger || null, // null means show immediately
    });
  }

  /**
   * Cancel a specific scheduled notification
   *
   * @param notificationId The ID of the notification to cancel
   */
  public async cancelNotification(notificationId: string): Promise<void> {
    await Notifications.cancelScheduledNotificationAsync(notificationId);
  }

  /**
   * Cancel all scheduled notifications
   */
  public async cancelAllNotifications(): Promise<void> {
    await Notifications.cancelAllScheduledNotificationsAsync();
  }

  /**
   * Get all pending notification requests
   *
   * @returns Array of pending notification requests
   */
  public async getPendingNotifications(): Promise<Notifications.NotificationRequest[]> {
    return await Notifications.getAllScheduledNotificationsAsync();
  }

  /**
   * Check and request notification permissions
   *
   * @returns Promise with permission status
   */
  public async requestPermissions(): Promise<Notifications.NotificationPermissionsStatus> {
    console.log('NotificationService: Checking existing permissions');
    const existingPermissions = await Notifications.getPermissionsAsync();

    console.log('NotificationService: Existing permission status:', existingPermissions.status);
    console.log('NotificationService: Full permission details:', JSON.stringify(existingPermissions, null, 2));

    if (existingPermissions.status === 'granted') {
      return existingPermissions;
    }

    console.log('NotificationService: Requesting permissions');

    // For iOS, add additional logging to debug permission issues
    if (Platform.OS === 'ios') {
      console.log('NotificationService: iOS - About to request permissions with allowProvisional: false');
    }

    // Request permissions with platform-specific options
    const permissionResult = await Notifications.requestPermissionsAsync({
      android: {
        allowSound: true,
        allowAlert: true,
        allowBadge: true,
      },
      ios: {
        allowSound: true,
        allowAlert: true,
        allowBadge: true,
        // Remove allowProvisional to force the permission dialog to appear
        allowProvisional: false,
      },
    });

    console.log('NotificationService: Permission request result:', JSON.stringify(permissionResult, null, 2));
    
    return permissionResult;
  }

  /**
   * Debug function to test notification permissions and functionality
   * Call this function to manually test notification setup
   * 
   * @returns Promise with debug information
   */
  public async debugNotificationPermissions(): Promise<any> {
    console.log('=== NotificationService Debug Start ===');
    
    try {
      // Check device type
      console.log('Platform:', Platform.OS);
      console.log('Is physical device:', Device.isDevice);
      
      // Check current permissions
      const currentPermissions = await Notifications.getPermissionsAsync();
      console.log('Current permissions:', JSON.stringify(currentPermissions, null, 2));
      
      // Request permissions if not granted
      if (currentPermissions.status !== 'granted') {
        console.log('Requesting permissions...');
        const newPermissions = await this.requestPermissions();
        console.log('New permissions:', JSON.stringify(newPermissions, null, 2));
        
        if (newPermissions.status !== 'granted') {
          console.log('❌ Permissions not granted!');
          return {
            success: false,
            error: 'Permissions not granted',
            permissions: newPermissions
          };
        }
      }
      
      // Try to get push token
      console.log('Attempting to get push token...');
      const token = await registerForPushNotificationsAsync();
      console.log('Push token:', token);
      
      if (!token) {
        console.log('❌ Failed to get push token!');
        return {
          success: false,
          error: 'Failed to get push token',
          permissions: currentPermissions
        };
      }
      
      // Try to schedule a test notification
      console.log('Scheduling test notification...');
      const notificationId = await this.scheduleLocalNotification(
        'Test Notification',
        'This is a test notification to verify the setup is working correctly.',
        { test: true },
        null // Show immediately
      );
      
      console.log('Test notification scheduled with ID:', notificationId);
      console.log('✅ Notification setup appears to be working!');
      
      return {
        success: true,
        token,
        permissions: currentPermissions,
        testNotificationId: notificationId
      };
      
    } catch (error: any) {
      console.error('❌ Debug function error:', error);
      return {
        success: false,
        error: error?.message || 'Unknown error',
        stack: error?.stack
      };
    } finally {
      console.log('=== NotificationService Debug End ===');
    }
  }
}

export default NotificationService;
