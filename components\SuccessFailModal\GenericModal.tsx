import React, { memo, useCallback } from 'react';
import { StyleSheet, View, TouchableOpacity, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '@/constants';
import Theme from '@/constants/Theme';

import Text from '../Text';
import { LinearGradient } from 'expo-linear-gradient';

interface GenericModalProps {
  close: () => void;
  open: () => void;
  message: string;
  title?: string;
  confirmText?: string;
  cancelText?: string;
  icon?: React.ReactNode;
}

const GenericModal = memo((props: GenericModalProps) => {
  // Animation values
  const [fadeAnim] = React.useState(new Animated.Value(0));
  const [scaleAnim] = React.useState(new Animated.Value(0.9));

  // Start animations when component mounts
  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  // Handle confirm action
  const handleConfirm = useCallback(() => {
    // Animate out before closing
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 0.9,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      })
    ]).start(() => {
      props.open();
    });
  }, [props]);

  // Handle cancel action
  const handleCancel = useCallback(() => {
    // Animate out before closing
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 0.9,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      })
    ]).start(() => {
      props.close();
    });
  }, [props]);

  return (
    <Animated.View
      style={[
        styles.container,
        { opacity: fadeAnim }
      ]}
    >
      <TouchableOpacity
        style={styles.backdrop}
        activeOpacity={1}
        onPress={handleCancel}
      />
      <Animated.View
        style={[
          styles.modalContent,
          {
            transform: [{ scale: scaleAnim }],
          }
        ]}
      >
        <LinearGradient
          colors={[Colors.White, Colors.WhiteSmoke]}
          style={styles.gradientBackground}
        >
          <View style={styles.header}>
            <Text center bold size={18} lineHeight={24} color={Colors.TealBlue}>
              {props.title || 'Confirm Action'}
            </Text>
          </View>

          <View style={styles.body}>
            {props.icon || <Ionicons name="alert-circle-outline" size={60} color={Colors.TealBlue} style={styles.warningIcon} />}
            <Text center size={16} lineHeight={24} marginTop={16} color={Colors.DarkJungleGreen}>
              {props.message}
            </Text>
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleCancel}
              activeOpacity={0.7}
            >
              <Text style={styles.cancelButtonText}>{props.cancelText || 'Cancel'}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.confirmButton}
              onPress={handleConfirm}
              activeOpacity={0.7}
            >
              <Text style={styles.confirmButtonText}>{props.confirmText || 'Confirm'}</Text>
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </Animated.View>
    </Animated.View>
  );
});

export default GenericModal;

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    backgroundColor: Colors.Black68,
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
  },
  modalContent: {
    borderRadius: 16,
    overflow: 'hidden',
    ...Theme.shadow,
  },
  gradientBackground: {
    width: '100%',
    borderRadius: 16,
  },
  header: {
    paddingTop: 24,
    paddingBottom: 16,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: Colors.WhiteSmoke,
  },
  body: {
    padding: 24,
    alignItems: 'center',
  },
  warningIcon: {
    width: 60,
    height: 60,
    alignSelf: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: 16,
    paddingTop: 0,
    justifyContent: 'space-between',
  },
  confirmButton: {
    flex: 1,
    marginLeft: 8,
    height: 50,
    borderRadius: 8,
    backgroundColor: Colors.TealBlue,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmButtonText: {
    color: Colors.White,
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
    height: 50,
    borderRadius: 8,
    backgroundColor: Colors.White,
    borderWidth: 1,
    borderColor: Colors.TealBlue,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButtonText: {
    color: Colors.TealBlue,
    fontSize: 16,
    fontWeight: '600',
  },
});
