import React from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import VitalsReportScreen from '@/screens/PatientDetails/VitalsReport';

export default function VitalsReportRoute() {
  // Create a safe props object with default values for any required properties
  const safeProps = {
    // Add any default props needed for VitalsReportScreen
    // This ensures the component won't crash if it expects certain props
  };

  return (
    // Explicitly exclude the top and bottom edges to prevent double padding with the header and remove bottom safe area
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      <VitalsReportScreen {...safeProps} />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});