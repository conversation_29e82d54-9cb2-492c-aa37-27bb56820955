import {
  FETCH_ALERTS_LIST,
  FETCH_ALERTS_LIST_FAILURE,
  FETCH_ALERTS_LIST_SUCCESS,
} from '../actions/type';

const initialState = {
  data: [],
  isFetching: false,
  msg: '',
};

export default function alertsListReducer(
  state = initialState,
  action: {type: any; data: any; msg: any},
) {
  switch (action.type) {
    case FETCH_ALERTS_LIST:
      return {...state, isFetching: true, data: ''};
    case FETCH_ALERTS_LIST_SUCCESS:
      return {
        ...state,
        isFetching: false,
        data: action.data,
      };
    case FETCH_ALERTS_LIST_FAILURE:
      return {
        ...state,
        isFetching: false,
        msg: action.msg,
      };

    default:
      return state;
  }
}
