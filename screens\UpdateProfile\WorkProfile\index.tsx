import {useNavigation} from '@react-navigation/native';
import React, {memo, useCallback, useState} from 'react';
import {
  Image,
  Modal,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import ButtonChangeCode from '@/components/ButtonChangeCode';
import ButtonLinear from '@/components/Buttons/ButtonLinear';
import HeaderButton from '@/components/HeaderButton';
import InputApp from '@/components/InputApp';
import Container from '@/components/Layout/Container';
import Layout from '@/components/Layout/Layout';
import ModalSlideBottom from '@/components/ModalSlideBottom';
import ModalChangePhoneCode from '@/components/SignUp/ModalChangePhoneCode';
import Text from '@/components/Text';
import TextInput from '@/components/TextInput';
import TagItem from '@/components/WorkProfile/TagItem';
import {Colors, Routes} from '@/constants';
import {phonesAreaCodes} from '@/constants/Data';
import {useTheme} from '@/constants/Theme';
import useModalAnimation from '@/hooks/useModalAnimation';
import Theme from '@/constants/Theme';
import {TcodeArea} from '@/types/codeArea';
import scale from '@/utils/scale';
interface WorkProfileProps {}

const specialities = [
  'Allergy & Immunology',
  'Cardiology',
  'Cardiology',
  'Cardiology',
  'Cardiology',
];
const insurancePlans = [
  'Aetna',
  'AmeriHealth',
  'BlueCross BlueShield',
  'Cigna',
];

const languages = ['English', 'Spanish', 'French'];

const WorkProfile = memo((props: WorkProfileProps) => {
  const {theme} = useTheme();
  const [hospitalName, setHospitalName] = useState(
    'New York Downtown Hospital',
  );
  const {navigate} = useNavigation();
  const [address, setAddress] = useState('');
  const [code, setCode] = useState(phonesAreaCodes[0]);
  const [phoneNumber, setPhoneNumber] = useState('************');
  const [yearExperience, setYearExperience] = useState('23');
  const onChangeCode = useCallback((item: TcodeArea) => {
    setCode(item);
    close();
  }, []);
  const onGoToOtherInfo = useCallback(() => {
    navigate(Routes.OtherInformation);
  }, [navigate]);

  const {visible, open, close, transY} = useModalAnimation();

  return (
    <Container style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={Theme.updateProfileContentScrollStyle}>
        <HeaderButton />
        <Text size={13} lineHeight={16} bold marginTop={32}>
          Step 2 of 3
        </Text>
        <Text size={24} lineHeight={28} bold marginTop={16}>
          Your Work Profile
        </Text>
        <Text size={13} lineHeight={22} marginTop={16}>
          Your information will be share with our Medical Expert team who will
          verify your identity.
        </Text>
        <InputApp
          title={'Specialities'}
          value={'Select'}
          marginTop={scale(40)}
          isShowIconLeft
          iconLeft={
            <Image
              source={require('images/ic_search_normal.png')}
              style={styles.iconSearch}
            />
          }
        />
        <View style={styles.spec}>
          {specialities.map((i, index) => (
            <TagItem title={i} key={index.toString()} />
          ))}
        </View>
        <Text size={15} lineHeight={18} bold marginTop={32}>
          Primary Hospital/Clinic
        </Text>
        <InputApp
          title={'Hospital/Clinic Name'}
          marginTop={24}
          value={hospitalName}
          onChangeText={setHospitalName}
        />
        <InputApp
          title={'Hospital/Clinic Name'}
          marginTop={24}
          value={address}
        />
        <Text size={13} lineHeight={16} semibold marginTop={24}>
          Office Phone Number
        </Text>

        <View style={styles.phoneView}>
          <ButtonChangeCode codeArea={code} onPress={open} />
          <TextInput
            value={phoneNumber}
            onChangeText={setPhoneNumber}
            style={styles.phoneNumber}
          />
        </View>
        <Text size={13} lineHeight={16} semibold marginTop={24}>
          Accepted Insurance Plans
        </Text>
        <Layout
          style={[styles.insurancePlans, {borderColor: theme.innearColor}]}>
          {insurancePlans.map((i, index) => (
            <Text size={15} lineHeight={24} semibold key={index.toString()}>
              {i}
            </Text>
          ))}
        </Layout>

        <TouchableOpacity style={styles.buttonAdd}>
          <Image
            source={require('images/ic_add_16.png')}
            style={styles.iconRight}
          />
          <Text size={13} lineHeight={16} color={Colors.GrayBlue} bold>
            Add More Hospital/Clinic
          </Text>
        </TouchableOpacity>
        <View style={styles.bottomContent}>
          <InputApp
            title={'Year Experience'}
            value={yearExperience}
            onChangeText={setYearExperience}
          />
          <InputApp
            title={'Language'}
            value={'Select'}
            iconLeft={
              <Image
                source={require('images/ic_search_normal.png')}
                style={styles.iconSearch}
              />
            }
            isShowIconLeft
          />
        </View>
        <View style={styles.specLanguage}>
          {languages.map((i, index) => (
            <TagItem title={i} key={index.toString()} />
          ))}
        </View>
        <ButtonLinear
          white
          title={'Continue'}
          children={
            <Image
              source={require('images/ic_next.png')}
              style={styles.buttonChildren}
            />
          }
          onPress={onGoToOtherInfo}
        />
      </ScrollView>
      <Modal
        visible={visible}
        onRequestClose={close}
        transparent
        animationType={'none'}>
        <ModalSlideBottom onClose={close} transY={transY}>
          <ModalChangePhoneCode onChangeCode={onChangeCode} />
        </ModalSlideBottom>
      </Modal>
    </Container>
  );
});

export default WorkProfile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  phoneView: {
    ...Theme.flexRow,
    marginTop: 4,
  },
  phoneNumber: {
    marginLeft: 8,
    flex: 1,
  },
  iconSearch: {
    ...Theme.icons,
    tintColor: Colors.DodgerBlue,
  },
  spec: {
    marginTop: 8,
    ...Theme.flexRow,
    flexWrap: 'wrap',
    paddingBottom: 32,
    borderBottomColor: Colors.TealBlue,
    borderBottomWidth: 1,
  },
  specLanguage: {
    marginTop: 8,
    ...Theme.flexRow,
    flexWrap: 'wrap',
    paddingBottom: 52,
  },
  insurancePlans: {
    borderRadius: 8,
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginTop: 4,
  },
  buttonChildren: {
    ...Theme.icons,
    marginLeft: 8,
  },
  buttonAdd: {
    marginTop: 24,
    borderWidth: 1,
    borderColor: Colors.Platinum,
    height: 36,
    ...Theme.center,
    borderRadius: 8,
    ...Theme.flexDirection,
  },
  iconRight: {
    width: 16,
    height: 16,
    marginRight: 8,
  },
  bottomContent: {
    paddingTop: 32,
    borderTopColor: Colors.TealBlue,
    borderTopWidth: 1,
    marginTop: 32,
  },
});
