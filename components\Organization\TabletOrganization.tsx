import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  TextInput,
} from 'react-native';
import TabletAwareContainer from '@/components/Layout/TabletAwareContainer';
import AdaptiveLayout from '@/components/Layout/AdaptiveLayout';
import ResponsiveGrid from '@/components/Layout/ResponsiveGrid';
import AccessibleText from '@/components/Accessibility/AccessibleText';
import AccessibleButton from '@/components/Accessibility/AccessibleButton';
import { isTablet, isLargeTablet, spacing } from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';

interface Organization {
  id: string;
  name: string;
  type: 'hospital' | 'clinic' | 'practice' | 'health_system';
  address: string;
  phone: string;
  email: string;
  website?: string;
  description: string;
  memberCount: number;
  isActive: boolean;
  logo?: string;
}

interface TabletOrganizationProps {
  organizations?: Organization[];
  selectedOrganization?: Organization;
  onOrganizationSelect?: (organization: Organization) => void;
  onContinue?: () => void;
  loading?: boolean;
}

/**
 * Tablet-optimized Organization selection screen component
 */
const TabletOrganization: React.FC<TabletOrganizationProps> = ({
  organizations = [],
  selectedOrganization,
  onOrganizationSelect,
  onContinue,
  loading = false,
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  // Sample organizations data
  const sampleOrganizations: Organization[] = [
    {
      id: '1',
      name: 'Metro General Hospital',
      type: 'hospital',
      address: '123 Healthcare Ave, Medical City, MC 12345',
      phone: '+****************',
      email: '<EMAIL>',
      website: 'www.metrogeneral.com',
      description: 'Leading healthcare provider with comprehensive medical services and specialized care units.',
      memberCount: 1250,
      isActive: true,
    },
    {
      id: '2',
      name: 'Sunrise Medical Clinic',
      type: 'clinic',
      address: '456 Wellness St, Health Town, HT 67890',
      phone: '+****************',
      email: '<EMAIL>',
      description: 'Community-focused medical clinic providing primary care and preventive health services.',
      memberCount: 85,
      isActive: true,
    },
    {
      id: '3',
      name: 'CardioVascular Associates',
      type: 'practice',
      address: '789 Heart Lane, Cardio City, CC 11111',
      phone: '+****************',
      email: '<EMAIL>',
      website: 'www.cvassociates.com',
      description: 'Specialized cardiovascular practice with expert cardiologists and advanced cardiac care.',
      memberCount: 45,
      isActive: true,
    },
    {
      id: '4',
      name: 'Regional Health System',
      type: 'health_system',
      address: '321 System Blvd, Regional City, RC 22222',
      phone: '+****************',
      email: '<EMAIL>',
      website: 'www.regionalhealthsystem.com',
      description: 'Integrated health system serving multiple communities with hospitals, clinics, and specialty centers.',
      memberCount: 3500,
      isActive: true,
    },
  ];

  const orgsToShow = organizations.length > 0 ? organizations : sampleOrganizations;

  const filteredOrganizations = orgsToShow.filter(org =>
    org.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    org.type.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getTypeIcon = (type: Organization['type']) => {
    switch (type) {
      case 'hospital':
        return '🏥';
      case 'clinic':
        return '🏢';
      case 'practice':
        return '👨‍⚕️';
      case 'health_system':
        return '🏛️';
      default:
        return '🏢';
    }
  };

  const getTypeLabel = (type: Organization['type']) => {
    switch (type) {
      case 'hospital':
        return 'Hospital';
      case 'clinic':
        return 'Clinic';
      case 'practice':
        return 'Practice';
      case 'health_system':
        return 'Health System';
      default:
        return 'Organization';
    }
  };

  const renderOrganizationCard = ({ item }: { item: Organization }) => (
    <TouchableOpacity
      style={[
        styles.organizationCard,
        selectedOrganization?.id === item.id && styles.selectedCard,
      ]}
      onPress={() => onOrganizationSelect?.(item)}
      accessible={true}
      accessibilityLabel={`Select ${item.name}`}
      accessibilityRole="button"
    >
      <View style={styles.cardHeader}>
        <View style={styles.orgType}>
          <AccessibleText variant="body" style={styles.typeIcon}>
            {getTypeIcon(item.type)}
          </AccessibleText>
          <AccessibleText variant="caption" style={styles.typeLabel}>
            {getTypeLabel(item.type)}
          </AccessibleText>
        </View>
        {selectedOrganization?.id === item.id && (
          <View style={styles.selectedIndicator}>
            <AccessibleText variant="body" style={styles.checkmark}>
              ✓
            </AccessibleText>
          </View>
        )}
      </View>

      <View style={styles.cardContent}>
        <AccessibleText variant="h3" style={styles.orgName}>
          {item.name}
        </AccessibleText>
        <AccessibleText variant="bodySmall" style={styles.orgDescription}>
          {item.description}
        </AccessibleText>
        
        <View style={styles.orgDetails}>
          <AccessibleText variant="caption" style={styles.orgAddress}>
            📍 {item.address}
          </AccessibleText>
          <AccessibleText variant="caption" style={styles.orgPhone}>
            📞 {item.phone}
          </AccessibleText>
          <AccessibleText variant="caption" style={styles.orgEmail}>
            ✉️ {item.email}
          </AccessibleText>
        </View>
      </View>

      <View style={styles.cardFooter}>
        <AccessibleText variant="caption" style={styles.memberCount}>
          {item.memberCount} members
        </AccessibleText>
        <View style={[styles.statusBadge, { backgroundColor: item.isActive ? CoreColors.TealBlue : CoreColors.SlateGray }]}>
          <AccessibleText variant="caption" style={styles.statusText}>
            {item.isActive ? 'ACTIVE' : 'INACTIVE'}
          </AccessibleText>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderSearchAndFilter = () => (
    <View style={styles.searchContainer}>
      <AccessibleText variant="h3" style={styles.searchTitle}>
        Find Your Organization
      </AccessibleText>
      <View style={styles.searchInputContainer}>
        <AccessibleText variant="body" style={styles.searchIcon}>
          🔍
        </AccessibleText>
        <TextInput
          style={styles.searchInput}
          placeholder="Search organizations..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          accessible={true}
          accessibilityLabel="Search organizations"
        />
      </View>
    </View>
  );

  const renderOrganizationsList = () => (
    <View style={styles.organizationsContainer}>
      <View style={styles.listHeader}>
        <AccessibleText variant="h2" style={styles.listTitle}>
          Available Organizations ({filteredOrganizations.length})
        </AccessibleText>
      </View>

      <ResponsiveGrid
        data={filteredOrganizations}
        renderItem={renderOrganizationCard}
        numColumns={isLargeTablet() ? 2 : 1}
        spacing={spacing.md}
      />
    </View>
  );

  const renderSelectedOrganization = () => {
    if (!selectedOrganization) {
      return (
        <View style={styles.selectionEmpty}>
          <AccessibleText variant="body" style={styles.emptyText}>
            Select an organization to continue
          </AccessibleText>
        </View>
      );
    }

    return (
      <ScrollView style={styles.selectionDetail} showsVerticalScrollIndicator={false}>
        <AccessibleText variant="h2" style={styles.detailTitle}>
          Selected Organization
        </AccessibleText>

        <View style={styles.detailCard}>
          <View style={styles.detailHeader}>
            <AccessibleText variant="body" style={styles.detailTypeIcon}>
              {getTypeIcon(selectedOrganization.type)}
            </AccessibleText>
            <View style={styles.detailTypeInfo}>
              <AccessibleText variant="h3" style={styles.detailOrgName}>
                {selectedOrganization.name}
              </AccessibleText>
              <AccessibleText variant="bodySmall" style={styles.detailOrgType}>
                {getTypeLabel(selectedOrganization.type)}
              </AccessibleText>
            </View>
          </View>

          <AccessibleText variant="body" style={styles.detailDescription}>
            {selectedOrganization.description}
          </AccessibleText>

          <View style={styles.detailSection}>
            <AccessibleText variant="h3" style={styles.detailSectionTitle}>
              Contact Information
            </AccessibleText>
            <View style={styles.detailRow}>
              <AccessibleText variant="bodySmall" style={styles.detailLabel}>
                Address:
              </AccessibleText>
              <AccessibleText variant="body" style={styles.detailValue}>
                {selectedOrganization.address}
              </AccessibleText>
            </View>
            <View style={styles.detailRow}>
              <AccessibleText variant="bodySmall" style={styles.detailLabel}>
                Phone:
              </AccessibleText>
              <AccessibleText variant="body" style={styles.detailValue}>
                {selectedOrganization.phone}
              </AccessibleText>
            </View>
            <View style={styles.detailRow}>
              <AccessibleText variant="bodySmall" style={styles.detailLabel}>
                Email:
              </AccessibleText>
              <AccessibleText variant="body" style={styles.detailValue}>
                {selectedOrganization.email}
              </AccessibleText>
            </View>
            {selectedOrganization.website && (
              <View style={styles.detailRow}>
                <AccessibleText variant="bodySmall" style={styles.detailLabel}>
                  Website:
                </AccessibleText>
                <AccessibleText variant="body" style={styles.detailValue}>
                  {selectedOrganization.website}
                </AccessibleText>
              </View>
            )}
          </View>

          <View style={styles.detailSection}>
            <AccessibleText variant="h3" style={styles.detailSectionTitle}>
              Organization Details
            </AccessibleText>
            <View style={styles.detailRow}>
              <AccessibleText variant="bodySmall" style={styles.detailLabel}>
                Members:
              </AccessibleText>
              <AccessibleText variant="body" style={styles.detailValue}>
                {selectedOrganization.memberCount}
              </AccessibleText>
            </View>
            <View style={styles.detailRow}>
              <AccessibleText variant="bodySmall" style={styles.detailLabel}>
                Status:
              </AccessibleText>
              <View style={[styles.statusBadge, { backgroundColor: selectedOrganization.isActive ? CoreColors.TealBlue : CoreColors.SlateGray }]}>
                <AccessibleText variant="caption" style={styles.statusText}>
                  {selectedOrganization.isActive ? 'ACTIVE' : 'INACTIVE'}
                </AccessibleText>
              </View>
            </View>
          </View>
        </View>

        <AccessibleButton
          title="Continue with this Organization"
          onPress={onContinue}
          loading={loading}
          disabled={loading}
          style={styles.continueButton}
          accessibilityLabel="Continue with selected organization"
        />
      </ScrollView>
    );
  };

  return (
    <TabletAwareContainer style={styles.container}>
      <View style={styles.header}>
        <AccessibleText variant="h1" style={styles.title}>
          Select Organization
        </AccessibleText>
        <AccessibleText variant="body" style={styles.subtitle}>
          Choose your healthcare organization to get started
        </AccessibleText>
      </View>

      <AdaptiveLayout
        mobileLayout={() => (
          <ScrollView style={styles.mobileLayout}>
            {renderSearchAndFilter()}
            {renderOrganizationsList()}
            {selectedOrganization && (
              <View style={styles.mobileSelection}>
                {renderSelectedOrganization()}
              </View>
            )}
          </ScrollView>
        )}
        tabletLayout={() => (
          <View style={styles.tabletLayout}>
            <View style={styles.leftPanel}>
              {renderSearchAndFilter()}
              {renderOrganizationsList()}
            </View>
            <View style={styles.rightPanel}>
              {renderSelectedOrganization()}
            </View>
          </View>
        )}
      />
    </TabletAwareContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
    paddingHorizontal: spacing.lg,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    color: CoreColors.DarkJungleGreen,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  subtitle: {
    color: CoreColors.SlateGray,
    textAlign: 'center',
  },
  mobileLayout: {
    flex: 1,
  },
  tabletLayout: {
    flex: 1,
    flexDirection: 'row',
    padding: spacing.md,
  },
  leftPanel: {
    flex: 1,
    marginRight: spacing.md,
  },
  rightPanel: {
    width: isLargeTablet() ? 450 : 350,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: spacing.lg,
  },
  searchContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: isTablet() ? 12 : 8,
    padding: spacing.lg,
    marginBottom: spacing.md,
  },
  searchTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.md,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchIcon: {
    marginRight: spacing.sm,
    fontSize: 16,
  },
  searchInput: {
    flex: 1,
    paddingVertical: spacing.md,
    fontSize: isTablet() ? 16 : 14,
    color: CoreColors.DarkJungleGreen,
  },
  organizationsContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: isTablet() ? 12 : 8,
    padding: spacing.lg,
    flex: 1,
  },
  listHeader: {
    marginBottom: spacing.md,
  },
  listTitle: {
    color: CoreColors.DarkJungleGreen,
  },
  organizationCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: spacing.md,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedCard: {
    backgroundColor: '#EBF8FF',
    borderColor: CoreColors.TurquoiseBlue,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  orgType: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeIcon: {
    fontSize: 20,
    marginRight: spacing.xs,
  },
  typeLabel: {
    color: CoreColors.SlateGray,
    fontWeight: '600',
  },
  selectedIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: CoreColors.TurquoiseBlue,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkmark: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  cardContent: {
    marginBottom: spacing.md,
  },
  orgName: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.xs,
  },
  orgDescription: {
    color: CoreColors.SlateGray,
    marginBottom: spacing.md,
    lineHeight: 20,
  },
  orgDetails: {
    gap: spacing.xs,
  },
  orgAddress: {
    color: CoreColors.SlateGray,
  },
  orgPhone: {
    color: CoreColors.SlateGray,
  },
  orgEmail: {
    color: CoreColors.SlateGray,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  memberCount: {
    color: CoreColors.SlateGray,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  selectionEmpty: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    color: CoreColors.SlateGray,
    textAlign: 'center',
  },
  selectionDetail: {
    flex: 1,
  },
  detailTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.lg,
  },
  detailCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: spacing.lg,
    marginBottom: spacing.xl,
  },
  detailHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  detailTypeIcon: {
    fontSize: 32,
    marginRight: spacing.md,
  },
  detailTypeInfo: {
    flex: 1,
  },
  detailOrgName: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.xs,
  },
  detailOrgType: {
    color: CoreColors.SlateGray,
  },
  detailDescription: {
    color: CoreColors.SlateGray,
    marginBottom: spacing.lg,
    lineHeight: 24,
  },
  detailSection: {
    marginBottom: spacing.lg,
  },
  detailSectionTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.md,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  detailLabel: {
    color: CoreColors.SlateGray,
    flex: 1,
  },
  detailValue: {
    color: CoreColors.DarkJungleGreen,
    flex: 2,
    textAlign: 'right',
  },
  continueButton: {
    marginTop: 'auto',
  },
  mobileSelection: {
    backgroundColor: '#FFFFFF',
    borderRadius: isTablet() ? 12 : 8,
    margin: spacing.md,
    padding: spacing.lg,
  },
});

export default TabletOrganization;