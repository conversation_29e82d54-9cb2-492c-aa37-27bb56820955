/**
 * Fills a string with placeholder characters to reach a specified length
 * @param input The input string to pad (can be a number or string)
 * @param length The desired final length of the string
 * @param fillChar The character to fill with (default: '#')
 * @param stripNonDigits Whether to remove non-digit characters first (default: false)
 * @returns A string of the specified length, filled with the placeholder character
 */
const fillNumberLength = (
  input: string | number,
  length: number, 
  fillChar: string = '#',
  stripNonDigits: boolean = false
): string => {
  // Convert to string if it's a number
  const inputStr = String(input);
  
  // Process the input string
  const processedInput = stripNonDigits 
    ? inputStr.replace(/\D/g, '')  // Remove non-digit characters if requested
    : inputStr;
  
  // Trim to maximum length if needed
  const trimmedInput = processedInput.length > length 
    ? processedInput.substring(0, length) 
    : processedInput;
  
  // If the trimmed input already meets the length requirement, return it
  if (trimmedInput.length >= length) {
    return trimmedInput;
  }
  
  // Calculate the number of characters to fill
  const fillLength = length - trimmedInput.length;
  
  // Create the fill string using repeat for better performance
  const fillString = fillChar.repeat(fillLength);
  
  // Combine the input and fill string
  return trimmedInput + fillString;
};

export default fillNumberLength;
