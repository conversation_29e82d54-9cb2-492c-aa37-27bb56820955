/**
 * Date formatting utilities for consistent date display across the application
 */

/**
 * Formats a date string to "MMM, DD, YYYY" format (e.g., "Jul, 12, 2025")
 * @param dateString - The date string to format
 * @returns Formatted date string or fallback text
 */
export const formatPatientDOB = (dateString: string | undefined): string => {
  if (!dateString) return "Not provided";
  
  try {
    // Handle various date formats that might come from the API
    const date = new Date(dateString);
    
    // Check if date is valid
    if (isNaN(date.getTime())) {
      // If the date string is already in a readable format, try to parse it differently
      if (dateString.includes("/") || dateString.includes("-")) {
        // Try different parsing approaches for common formats
        const parts = dateString.replace(/[-]/g, "/").split("/");
        if (parts.length === 3) {
          // Try MM/DD/YYYY or DD/MM/YYYY or YYYY/MM/DD
          const possibleDates = [
            new Date(`${parts[0]}/${parts[1]}/${parts[2]}`), // MM/DD/YYYY
            new Date(`${parts[1]}/${parts[0]}/${parts[2]}`), // DD/MM/YYYY  
            new Date(`${parts[2]}/${parts[0]}/${parts[1]}`), // YYYY/MM/DD
          ];
          
          for (const possibleDate of possibleDates) {
            if (!isNaN(possibleDate.getTime())) {
              return possibleDate.toLocaleDateString('en-US', {
                month: 'short',
                day: '2-digit', 
                year: 'numeric'
              }).replace(/(\w{3}) (\d{2}), (\d{4})/, '$1, $2, $3');
            }
          }
        }
      }
      return "Invalid date";
    }
    
    // Format the valid date to "MMM, DD, YYYY" format
    const formatted = date.toLocaleDateString('en-US', {
      month: 'short',
      day: '2-digit',
      year: 'numeric'
    });
    
    // Convert "Dec 12, 2025" to "Dec, 12, 2025" by adding comma after month
    return formatted.replace(/(\w{3}) (\d{2}), (\d{4})/, '$1, $2, $3');
    
  } catch (error) {
    console.warn('Error formatting date:', dateString, error);
    return dateString; // Return original string if there's an error
  }
};

/**
 * Formats a date string to MM/DD/YYYY format for compatibility with existing code
 * @param dateString - The date string to format
 * @returns Formatted date string in MM/DD/YYYY format
 */
export const formatDateMMDDYYYY = (dateString: string | undefined): string => {
  if (!dateString) return "";
  
  try {
    const date = new Date(dateString);
    
    if (isNaN(date.getTime())) {
      if (dateString.includes("/") || dateString.includes("-")) {
        return dateString;
      }
      return "Invalid date";
    }
    
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const year = date.getFullYear();
    
    return `${month}/${day}/${year}`;
  } catch (error) {
    return dateString;
  }
}; 