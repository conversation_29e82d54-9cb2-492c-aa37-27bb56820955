import React, { memo, useCallback } from 'react';
import {
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '@/constants';
import { Program } from '@/models';
import Text from '@/components/Text';

interface ProgramSelectorProps {
  programs: Program[];
  selectedProgram: string;
  onSelectProgram: (programName: string) => void;
  style?: any;
}

const ProgramSelector = memo(({ 
  programs, 
  selectedProgram, 
  onSelectProgram, 
  style 
}: ProgramSelectorProps) => {
  
  const handleProgramPress = useCallback((programName: string) => {
    onSelectProgram(programName);
  }, [onSelectProgram]);

  if (!programs || programs.length === 0) {
    return null;
  }

  return (
    <View style={[styles.container, style]}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        style={styles.scrollView}
      >
        {programs.map((program, index) => {
          const isSelected = selectedProgram === program.programName;
          
          return (
            <TouchableOpacity
              key={`${program.programId}-${index}`}
              onPress={() => handleProgramPress(program.programName)}
              style={[
                styles.programChip,
                isSelected && styles.programChipSelected
              ]}
              activeOpacity={0.7}
            >
              <View style={styles.radioContainer}>
                {isSelected ? (
                  <Ionicons 
                    name="radio-button-on" 
                    size={16} 
                    color={Colors.TealBlue} 
                  />
                ) : (
                  <Ionicons 
                    name="radio-button-off" 
                    size={16} 
                    color={Colors.GrayBlue} 
                  />
                )}
              </View>
              <Text style={[
                styles.programText,
                isSelected && styles.programTextSelected
              ]}>
                {program.programName}
              </Text>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </View>
  );
});

export default ProgramSelector;

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f0f2f5',
    paddingVertical: 8,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E4EF',
  },
  scrollView: {
    flexGrow: 0,
  },
  scrollContent: {
    paddingHorizontal: 8,
    alignItems: 'center',
  },
  programChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.White,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: '#E0E4EF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  programChipSelected: {
    backgroundColor: '#F0F7FF',
    borderColor: Colors.TealBlue,
    shadowColor: Colors.TealBlue,
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  radioContainer: {
    marginRight: 6,
  },
  programText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.GrayBlue,
  },
  programTextSelected: {
    color: Colors.TealBlue,
    fontWeight: '600',
  },
});
