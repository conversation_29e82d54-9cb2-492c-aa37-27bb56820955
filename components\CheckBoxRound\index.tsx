import React, {memo} from 'react';
import {
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {SOURCE_ICON} from '@/assets/images';
import Theme from '@/constants/Theme';

interface CheckBoxProps {
  isCheck?: boolean | number;
  style?: ViewStyle;
  onPress?: () => void;
}

const CheckBox = memo(({isCheck, style, onPress}: CheckBoxProps) => {
  return (
    <TouchableOpacity
      style={{...Theme.icons, ...Theme.center, ...style}}
      onPress={onPress}>
      {isCheck ? (
        <Image source={SOURCE_ICON.radioActive} />
      ) : (
        <View style={styles.unChecked} />
      )}
    </TouchableOpacity>
  );
});

export default CheckBox;

const styles = StyleSheet.create({
  container: {},
  unChecked: {
    width: 20,
    height: 20,
    borderColor: '#979797',
    borderRadius: 20,
    borderWidth: 1,
  },
});
