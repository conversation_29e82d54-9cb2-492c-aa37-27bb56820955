import React from 'react';
import {Dimensions, Text, View} from 'react-native';
import {StackedBarChart} from 'react-native-chart-kit';
import Theme from '@/constants/Theme';

interface GraphProps {
  measuredDates: any[];
  measuredValues: any[];
  title: string;
  min: number;
  max: number;
  cMin: number;
  cMax: number;
}

const SleepMonitorGraph = (props: GraphProps) => {
  console.log('measuredDates!!!', props?.measuredDates);
  console.log('measuredValues!!!', props?.measuredValues);

  const chartConfig = {
    backgroundColor: 'transparent',
    backgroundGradientTo: 'white',
    backgroundGradientFromOpacity: 0,
    backgroundGradientFrom: 'white',
    backgroundGradientToOpacity: 0,
    color: (opacity = 1) => 'white',
    labelColor: (opacity = 1) => `rgba(0,0,0,${opacity})`, // Fixed opacity value and removed space after comma
    barPercentage: 0.9,
    propsForLabels: {
      fontFamily: 'OpenSans-Medium',
      fontSize: 12,
      textAlign: 'center',
    },
    decimalPlaces: 2,
    percentile: false,
  };

  const data = {
    labels: props?.measuredDates,
    data: props?.measuredValues,
    barColors: ['#38bfc6', '#f9a11d'],
    legend: [],
  };

  return (
    <View>
      <View
        style={{
          marginVertical: 20,
          marginHorizontal: 20,
          borderRadius: 10,
          ...Theme.flexRowSpace,
        }}>
        <Text
          style={{
            color: '#38bfc6',
            fontFamily: 'OpenSans-Medium',
            fontSize: 16,
          }}>
          {'\u2B24'} {'Shallow sleep'}
        </Text>
        <Text
          style={{
            color: '#f9a11d',
            fontFamily: 'OpenSans-Medium',
            fontSize: 16,
          }}>
          {'\u2B24'} {'Deep sleep'}
        </Text>
      </View>
      <StackedBarChart
        style={{
          marginVertical: 8,
          borderRadius: 10,
        }}
        data={data}
        width={Dimensions.get('window').width * 0.9}
        height={220}
        chartConfig={chartConfig}
        hideLegend={false}
        decimalPlaces={1}
        fromZero={false}
        percentile={false}
        segments={4}
        formatYLabel={str => str + ' hrs'}
      />
    </View>
  );
};

export default SleepMonitorGraph;
