import Container from "@/components/Layout/Container";
import Loader from "@/components/Loader/Loader";
import PatientSelector from "@/components/PatientSelector";
import Text from "@/components/Text";
import TextWrapper from "@/components/TextWrapper";
import VitalLineGraph from "@/components/VitalsCards";
import Colors from "@/constants/Colors";
import {
  cardDimensions,
  grid,
  iconSizes,
  patientGrowthCard,
  spacing,
  typography,
  fontScale,
} from "@/utils/responsive";
import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import Moment from "moment";
import React, { useCallback, useEffect, useState } from "react";
import {
  Alert,
  BackHandler,
  Dimensions,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Platform,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useDispatch, useSelector } from "react-redux";

import { setCurentPatientId } from "@/services/actions/currentPatientId";
import { setCurrentPatientName } from "@/services/actions/currentPatientName";
import { apiPostWithToken } from "@/services/apis/apiManager";
import URLS from "@/services/config/config";
import { categoryList } from "@/types/category";
import { getOrganizationId } from "@/utils/getOrganizationId";
import moment from "moment";
import { MonthlyStatsData, PatientSummaryData } from "./type";

const { width } = Dimensions.get("window");

const Validation = () => {
  const [loader, setLoader] = useState(false);
  const [activeTab, setActiveTab] = useState(0); // 0 = Monthly Summary, 1 = Patient Summary
  const [monthlyData, setMonthlyData] = useState<MonthlyStatsData | null>(null);
  const [patientSummary, setPatientSummary] =
    useState<PatientSummaryData | null>(null);

  // Date range state
  const [fromDate, setFromDate] = useState<Date>();
  const [toDate, setToDate] = useState<Date>();

  const router = useRouter();
  const dispatch = useDispatch();

  const caregiverId = useSelector(
    (state: any) => state?.loginReducer?.data?.userId
  );
  const orgId = useSelector((state: any) => state?.currentOrgIdReducer?.orgId);

  // Get user role information from Redux store
  const userRole = useSelector((state: any) => state?.loginReducer?.userRole || 'caregiver');
  const roleId = useSelector((state: any) => state?.loginReducer?.roleId || 5);

  useEffect(() => {
    if (orgId) {
      const today = new Date();
      setToDate(today);

      const currentYear = today.getFullYear();
      const currentMonth = today.getMonth();
      const firstOfMonth = new Date(currentYear, currentMonth, 1);

      setFromDate(firstOfMonth);
    }
  }, [orgId]);

  useEffect(() => {
    const backAction = () => {
      if (router.canGoBack()) {
        router.back();
        return true;
      }
      return false;
    };

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      backAction
    );
    return () => backHandler.remove();
  }, [router]);

  const [category, setCategory] = useState({
    id: "",
    name: "Select a patient",
    displayName: "Select a patient",
  });
  const [patientList, setPatientList] = useState<any[]>([]);

  const getPatientsList = async () => {
    setLoader(true);
    try {
      const response = await apiPostWithToken(
        {
          roleId: roleId.toString(),
          userId: String(caregiverId),
          orgId: String(orgId),
        },
        URLS.caregiverUrl + "getPatientsForTasks"
      );

      setLoader(false);
      const responseData = response?.data || response?.response?.data;
      const responseStatus = response?.status || response?.response?.status;

      if (responseStatus == 200 && responseData) {
        let pList: any[] = responseData?.patientMinimalVOList || [];
        let arr: categoryList[] = [];

        if (pList && pList.length > 0) {
          pList.forEach((p) => {
            let obj = {
              id: p.patientId,
              name: p.patientName,
              displayName: p.patientName,
            };
            arr.push(obj);
          });
          setPatientList(arr);
        }
      } else {
        let errorMessage = "Failed to fetch patients list";
        if (response?.error?.message) {
          errorMessage = response.error.message;
        } else if (response?.response?.data?.responseMessage) {
          errorMessage = response.response.data.responseMessage;
        } else if (response?.message) {
          errorMessage =
            response.message === "Network Error"
              ? "Network error. Please check your data connection."
              : response.message;
        }
        Alert.alert("Error", errorMessage, [{ text: "Dismiss" }]);
      }
    } catch (error) {
      setLoader(false);
      Alert.alert(
        "Error",
        "An unexpected error occurred while fetching patients. Please try again.",
        [{ text: "Dismiss" }]
      );
    }
  };

  // Get monthly summary data
  const getMonthlyData = async () => {
    if (!fromDate || !toDate || !caregiverId || !orgId) return;

    setLoader(true);
    try {
      const fDate = Moment(fromDate).format("YYYY-MM-DD");
      const tDate = Moment(toDate).format("YYYY-MM-DD");

      const response = await apiPostWithToken(
        {
          userId: String(caregiverId),
          orgId: String(orgId),
          roleType: roleId.toString(),
          startDate: fDate,
          endDate: tDate,
        },
        URLS.caregiverUrl + "monthlySummary"
      );

      setLoader(false);
      if (response?.status === 200 && response?.data) {
        setMonthlyData(response.data);
      } else {
        setMonthlyData(null);
        Alert.alert("Error", "Failed to load monthly data. Please try again.");
      }
    } catch (error) {
      setLoader(false);
      setMonthlyData(null);
      Alert.alert("Error", "Failed to load monthly data. Please try again.");
    }
  };

  // Handle patient selection
  const handlePatientChange = useCallback(
    (selectedPatient: any) => {
      setCategory(selectedPatient);
      dispatch(setCurentPatientId(selectedPatient.id));
      dispatch(setCurrentPatientName(selectedPatient.name));

      if (selectedPatient.id && selectedPatient.id !== 0) {
        getPatientSummary(selectedPatient.id);
      }
    },
    [dispatch]
  );

  // Get patient summary data
  const getPatientSummary = async (patientId: string | number) => {
    if (!patientId || patientId === 0) return;

    setLoader(true);
    try {
      const response = await apiPostWithToken(
        { patientId: String(patientId) }, // Convert to string
        URLS.caregiverUrl + "getPatientSummary"
      );

      setLoader(false);
      if (response?.status === 200 && response?.data) {
        // Merge patient data with latestVitals at root level to match interface
        const mergedData = {
          ...response.data.patient,
          latestVitals: response.data.latestVitals
        };
        
        setPatientSummary(mergedData);
      } else {
        setPatientSummary(null);
        Alert.alert("Error", "Failed to load patient data. Please try again.");
      }
    } catch (error) {
      setLoader(false);
      setPatientSummary(null);
      Alert.alert("Error", "Failed to load patient data. Please try again.");
    }
  };

  // Handle clearing patient selection
  const handleClearPatient = useCallback(() => {
    setCategory({
      id: "",
      name: "Select a patient",
      displayName: "Select a patient",
    });
    dispatch(setCurentPatientId(0));
    dispatch(setCurrentPatientName(""));
    setPatientSummary(null);
  }, [dispatch]);

  useEffect(() => {
    if (caregiverId && orgId) {
      getPatientsList();
    }
  }, [caregiverId, orgId]);

  // Load monthly data when date range changes
  useEffect(() => {
    if (fromDate && toDate && caregiverId && orgId) {
      getMonthlyData();
    }
  }, [fromDate, toDate, caregiverId, orgId]);

  // Render tab buttons
  const renderTabButtons = () => (
    <TextWrapper style={styles.tabContainer}>
      <TouchableOpacity
        style={[styles.tabButton, activeTab === 0 && styles.activeTabButton]}
        onPress={() => setActiveTab(0)}
      >
        <Text
          size={14}
          bold
          color={activeTab === 0 ? Colors.White : Colors.GrayBlue}
        >
          Monthly Summary
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tabButton, activeTab === 1 && styles.activeTabButton]}
        onPress={() => setActiveTab(1)}
      >
        <Text
          size={14}
          bold
          color={activeTab === 1 ? Colors.White : Colors.GrayBlue}
        >
          Patient Summary
        </Text>
      </TouchableOpacity>
    </TextWrapper>
  );

  const getMinsSecs = (mins: number) => {
    const wholeMinutes = Math.floor(mins);
    const seconds = Math.round((mins - wholeMinutes) * 60);
    return `${wholeMinutes}m:${seconds.toString().padStart(2, "0")}s`;
  };

  const getDeviceIcon = (deviceType: string) => {
    if (deviceType.includes("Watch")) {
      return "watch";
    } else if (deviceType.includes("Mobile")) {
      return "install-mobile";
    } else {
      return "devices-other";
    }
  };

  // Modern vital signs render function
  const renderModernVitalCard = (vital: any) => {
    const cardWidth = 31; // Fixed width percentage for validation screen
    const isNarrow = cardWidth < 35; // Since it's always 31%, it's considered narrow
    
    return (
      <LinearGradient
        key={vital.key}
        colors={[vital.color + '15', vital.color + '05']}
        style={[
          styles.modernVitalCard,
          { width: '31%', marginRight: spacing.sm }
        ]}
      >
        <TextWrapper style={styles.vitalCardHeader}>
          <MaterialIcons
            name={vital.icon}
            size={isNarrow ? 24 : 28}
            color={vital.color}
          />
          <TextWrapper style={styles.vitalLabelContainer}>
            <Text 
              size={isNarrow ? 10 : 11} 
              color={Colors.GrayBlue}
              numberOfLines={2}
            >
              {vital.label.replace('\n', ' ')}
            </Text>
          </TextWrapper>
        </TextWrapper>
        
        <TextWrapper style={styles.vitalCardValueContainer}>
          <Text
            size={isNarrow ? 16 : 20}
            bold
            color={Colors.DarkJungleGreen}
            lineHeight={isNarrow ? 18 : 22}
            numberOfLines={2}
          >
            {vital.value}
          </Text>
          {vital.unit && vital.key !== 'bloodPressure' && (
            <Text 
              size={isNarrow ? 9 : 11} 
              color={Colors.GrayBlue} 
              marginLeft={isNarrow ? 2 : 4}
              numberOfLines={1}
            >
              {vital.unit}
            </Text>
          )}
        </TextWrapper>

        <TextWrapper style={styles.vitalCardFooter}>
          <MaterialIcons name="schedule" size={isNarrow ? 10 : 12} color={Colors.GrayBlue} />
          <Text 
            size={isNarrow ? 9 : 10} 
            color={Colors.GrayBlue} 
            marginLeft={isNarrow ? 2 : 4}
            numberOfLines={1}
          >
            Latest
          </Text>
        </TextWrapper>
      </LinearGradient>
    );
  };

  // Render monthly summary content
  const renderMonthlySummary = () => {
    if (!monthlyData || !monthlyData.patientStats || !monthlyData.minsStats) {
      return (
        <TextWrapper style={styles.emptyCard}>
          <MaterialIcons name="analytics" size={48} color={Colors.GrayBlue} />
          <Text size={16} color={Colors.GrayBlue} marginTop={16} center>
            No monthly data available
          </Text>
          <Text size={14} color={Colors.GrayBlue} marginTop={8} center>
            Monthly statistics will appear here once data is loaded
          </Text>
        </TextWrapper>
      );
    }

    const dateRangeDisplay = () => {
      const startDate = fromDate ? moment(fromDate) : moment().startOf("month");
      const endDate = toDate ? moment(toDate) : moment();

      const formattedRange = `${startDate.format(
        "MMMM Do"
      )} to ${endDate.format("MMMM Do")}`;

      return <Text>{formattedRange}</Text>;
    };

    const { patientStats, minsStats } = monthlyData;

    return (
      <ScrollView
        style={styles.contentScroll}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: spacing.lg }}
      >
        {/* Modern Date Range Selector */}
        <TextWrapper style={styles.modernDateRangeCard}>
          <TextWrapper style={styles.dateRangeHeader}>
            <MaterialIcons name="date-range" size={18} color={Colors.TealBlue} />
            <Text size={13} color={Colors.GrayBlue} marginLeft={6} numberOfLines={1}>
              Reporting Period
            </Text>
          </TextWrapper>
          <TextWrapper style={styles.dateRangeContent}>
            <TextWrapper style={styles.dateColumn}>
              <Text size={10} color={Colors.GrayBlue} numberOfLines={1} style={styles.dateLabel}>FROM</Text>
              <TextWrapper style={styles.dateBox}>
                <Text size={14} bold color={Colors.DarkJungleGreen} numberOfLines={1}>
                  {(fromDate ? moment(fromDate) : moment().startOf("month")).format("MMMM DD")}
                </Text>
              </TextWrapper>
            </TextWrapper>
            <MaterialIcons name="arrow-forward" size={14} color={Colors.GrayBlue} style={{ marginHorizontal: spacing.sm }} />
            <TextWrapper style={styles.dateColumn}>
              <Text size={10} color={Colors.GrayBlue} numberOfLines={1} style={styles.dateLabel}>TO</Text>
              <TextWrapper style={styles.dateBox}>
                <Text size={14} bold color={Colors.DarkJungleGreen} numberOfLines={1}>
                  {(toDate ? moment(toDate) : moment()).format("MMMM DD")}
                </Text>
              </TextWrapper>
            </TextWrapper>
          </TextWrapper>
        </TextWrapper>

        {/* Enhanced Growth Indicator */}
        <TextWrapper style={styles.modernGrowthCard}>
          <LinearGradient
            colors={patientStats.growth >= 0 ? [Colors.ForestGreen + '20', Colors.ForestGreen + '10'] : [Colors.RedNeonFuchsia + '20', Colors.RedNeonFuchsia + '10']}
            style={styles.growthGradientBackground}
          >
            <TextWrapper style={styles.growthHeader}>
              <TextWrapper style={styles.growthIconContainer}>
                <MaterialIcons 
                  name={patientStats.growth >= 0 ? "trending-up" : "trending-down"} 
                  size={20} 
                  color={patientStats.growth >= 0 ? Colors.ForestGreen : Colors.RedNeonFuchsia} 
                />
              </TextWrapper>
              <TextWrapper style={styles.growthLabelContainer}>
                <Text size={13} color={Colors.GrayBlue} numberOfLines={1}>
                  Patient Growth Rate
                </Text>
                <Text size={11} color={Colors.GrayBlue} numberOfLines={1}>
                  vs previous period
                </Text>
              </TextWrapper>
            </TextWrapper>
            <TextWrapper style={styles.growthValueContainer}>
              <Text
                size={28}
                bold
                color={patientStats.growth >= 0 ? Colors.ForestGreen : Colors.RedNeonFuchsia}
                lineHeight={32}
                numberOfLines={1}
              >
                {patientStats.growth >= 0 ? '+' : ''}{patientStats.growth.toFixed(1)}%
              </Text>
              <Text size={11} color={Colors.GrayBlue} marginTop={2} numberOfLines={1}>
                {patientStats.growth >= 0 ? 'Positive Growth' : 'Decline'}
              </Text>
            </TextWrapper>
          </LinearGradient>
        </TextWrapper>

        {/* Patient Statistics */}
        <TextWrapper style={styles.sectionCard}>
          <Text
            size={18}
            bold
            color={Colors.DarkJungleGreen}
            marginBottom={16}
            lineHeight={20}
          >
            Patient Overview
          </Text>
          <TextWrapper style={styles.statsGrid}>
            <TextWrapper style={styles.statItem}>
              <TextWrapper
                style={[
                  styles.statIconWrapper,
                  { backgroundColor: Colors.TurquoiseBlue + "20" },
                ]}
              >
                <MaterialIcons
                  name="people"
                  size={24}
                  color={Colors.TurquoiseBlue}
                />
              </TextWrapper>
              <Text
                size={20}
                bold
                color={Colors.DarkJungleGreen}
                lineHeight={22}
                numberOfLines={1}
              >
                {patientStats.totalPatients}
              </Text>
              <Text size={14} color={Colors.GrayBlue} lineHeight={16} numberOfLines={1}>
                Total Patients
              </Text>
            </TextWrapper>

            <TextWrapper style={styles.statItem}>
              <TextWrapper
                style={[
                  styles.statIconWrapper,
                  { backgroundColor: Colors.ForestGreen + "20" },
                ]}
              >
                <MaterialIcons
                  name="check-circle"
                  size={24}
                  color={Colors.ForestGreen}
                />
              </TextWrapper>
              <Text
                size={20}
                bold
                color={Colors.DarkJungleGreen}
                lineHeight={22}
                numberOfLines={1}
              >
                {patientStats.totalActivePatients}
              </Text>
              <Text size={14} color={Colors.GrayBlue} lineHeight={16} numberOfLines={1}>
                Active Patients
              </Text>
            </TextWrapper>

            <TextWrapper style={styles.statItem}>
              <TextWrapper
                style={[
                  styles.statIconWrapper,
                  { backgroundColor: Colors.Orange + "20" },
                ]}
              >
                <MaterialIcons
                  name="person-add"
                  size={24}
                  color={Colors.Orange}
                />
              </TextWrapper>
              <Text
                size={20}
                bold
                color={Colors.DarkJungleGreen}
                lineHeight={22}
                numberOfLines={1}
              >
                {patientStats.newPatients}
              </Text>
              <Text size={14} color={Colors.GrayBlue} lineHeight={16} numberOfLines={1}>
                New Patients
              </Text>
            </TextWrapper>
          </TextWrapper>
        </TextWrapper>

        {/* Program Distribution */}
        <TextWrapper style={styles.sectionCard}>
          <TextWrapper style={styles.sectionHeader}>
            <MaterialIcons name="people" size={24} color={Colors.TealBlue} />
            <Text
              size={18}
              bold
              color={Colors.DarkJungleGreen}
              marginLeft={8}
              lineHeight={20}
            >
              Program Enrolled
            </Text>
          </TextWrapper>
          <TextWrapper style={styles.programMinutesGrid}>
            <TextWrapper style={styles.cleanProgramCard}>
              <MaterialIcons
                name="monitor-heart"
                size={24}
                color={Colors.TealBlue}
              />
              <Text
                size={20}
                bold
                color={Colors.TealBlue}
                marginTop={8}
                lineHeight={22}
                numberOfLines={1}
              >
                {patientStats.rpmPatients}
              </Text>
              <Text
                size={12}
                color={Colors.GrayBlue}
                marginTop={4}
                lineHeight={14}
                numberOfLines={1}
              >
                RPM
              </Text>
            </TextWrapper>

            <TextWrapper style={styles.cleanProgramCard}>
              <MaterialIcons
                name="health-and-safety"
                size={24}
                color={Colors.BlueCrayola}
              />
              <Text
                size={20}
                bold
                color={Colors.BlueCrayola}
                marginTop={8}
                lineHeight={22}
                numberOfLines={1}
              >
                {patientStats.ccmPatients}
              </Text>
              <Text
                size={12}
                color={Colors.GrayBlue}
                marginTop={4}
                lineHeight={14}
                numberOfLines={1}
              >
                CCM
              </Text>
            </TextWrapper>

            <TextWrapper style={styles.cleanProgramCard}>
              <MaterialIcons
                name="assignment"
                size={24}
                color={Colors.Malachite}
              />
              <Text
                size={20}
                bold
                color={Colors.Malachite}
                marginTop={8}
                lineHeight={22}
                numberOfLines={1}
              >
                {patientStats.pcmPatients}
              </Text>
              <Text
                size={12}
                color={Colors.GrayBlue}
                marginTop={4}
                lineHeight={14}
                numberOfLines={1}
              >
                PCM
              </Text>
            </TextWrapper>
          </TextWrapper>
        </TextWrapper>

        {/* Minutes Distribution */}
        <TextWrapper style={styles.sectionCard}>
          <TextWrapper style={styles.sectionHeader}>
            <MaterialIcons name="analytics" size={24} color={Colors.TealBlue} />
            <Text
              size={18}
              bold
              color={Colors.DarkJungleGreen}
              marginLeft={8}
              lineHeight={20}
            >
              Encounter Distribution
            </Text>
          </TextWrapper>

          {/* RPM Minutes */}
          <TextWrapper style={styles.programSection}>
            <TextWrapper style={styles.programHeader}>
              <MaterialIcons
                name="monitor-heart"
                size={20}
                color={Colors.TealBlue}
              />
              <Text
                size={16}
                bold
                color={Colors.TealBlue}
                marginLeft={8}
                lineHeight={18}
              >
                RPM ({patientStats.rpmPatients} patients)
              </Text>
            </TextWrapper>

            {[
              {
                value: minsStats.rpm20Minless,
                label: "< 20 min",
                color: Colors.RedNeonFuchsia,
                total: minsStats.totalPatients,
              },
              {
                value: minsStats.rpm40Minless,
                label: "20-40 min",
                color: Colors.Orange,
                total: minsStats.totalPatients,
              },
              {
                value: minsStats.rpm60Minless,
                label: "40-60 min",
                color: Colors.BlueCrayola,
                total: minsStats.totalPatients,
              },
              {
                value: minsStats.rpm60Plus,
                label: "60+ min",
                color: Colors.ForestGreen,
                total: minsStats.totalPatients,
              },
            ].map((item, index) => {
              const percentage =
                item.total > 0 ? (item.value / item.total) * 100 : 0;
              return (
                <TextWrapper key={index} style={styles.progressBarItem}>
                  <TextWrapper style={styles.progressBarHeader}>
                    <Text
                      size={14}
                      color={Colors.DarkJungleGreen}
                      lineHeight={16}
                    >
                      {item.label}
                    </Text>
                    <Text size={14} bold color={item.color} lineHeight={16}>
                      {item.value} ({percentage.toFixed(1)}%)
                    </Text>
                  </TextWrapper>
                  <TextWrapper style={styles.progressBarContainer}>
                    <TextWrapper
                      style={[
                        styles.progressBarFill,
                        {
                          width: `${Math.max(percentage, 2)}%`,
                          backgroundColor: item.color,
                        },
                      ]}
                    >
                      <></>
                    </TextWrapper>
                  </TextWrapper>
                </TextWrapper>
              );
            })}
          </TextWrapper>

          {/* CCM Minutes */}
          <TextWrapper style={styles.programSection}>
            <TextWrapper style={styles.programHeader}>
              <MaterialIcons
                name="health-and-safety"
                size={20}
                color={Colors.BlueCrayola}
              />
              <Text
                size={16}
                bold
                color={Colors.BlueCrayola}
                marginLeft={8}
                lineHeight={18}
              >
                CCM ({patientStats.ccmPatients} patients)
              </Text>
            </TextWrapper>

            {[
              {
                value: minsStats.ccm20Minless,
                label: "< 20 min",
                color: Colors.RedNeonFuchsia,
                total: minsStats.totalPatients,
              },
              {
                value: minsStats.ccm40Minless,
                label: "20-40 min",
                color: Colors.Orange,
                total: minsStats.totalPatients,
              },
              {
                value: minsStats.ccm60Minless,
                label: "40-60 min",
                color: Colors.BlueCrayola,
                total: minsStats.totalPatients,
              },
              {
                value: minsStats.ccm60Plus,
                label: "60+ min",
                color: Colors.ForestGreen,
                total: minsStats.totalPatients,
              },
            ].map((item, index) => {
              const percentage =
                item.total > 0 ? (item.value / item.total) * 100 : 0;
              return (
                <TextWrapper key={index} style={styles.progressBarItem}>
                  <TextWrapper style={styles.progressBarHeader}>
                    <Text
                      size={14}
                      color={Colors.DarkJungleGreen}
                      lineHeight={16}
                    >
                      {item.label}
                    </Text>
                    <Text size={14} bold color={item.color} lineHeight={16}>
                      {item.value} ({percentage.toFixed(1)}%)
                    </Text>
                  </TextWrapper>
                  <TextWrapper style={styles.progressBarContainer}>
                    <TextWrapper
                      style={[
                        styles.progressBarFill,
                        {
                          width: `${Math.max(percentage, 2)}%`,
                          backgroundColor: item.color,
                        },
                      ]}
                    >
                      <></>
                    </TextWrapper>
                  </TextWrapper>
                </TextWrapper>
              );
            })}
          </TextWrapper>

          {/* PCM Minutes */}
          <TextWrapper style={styles.programSection}>
            <TextWrapper style={styles.programHeader}>
              <MaterialIcons
                name="assignment"
                size={20}
                color={Colors.Malachite}
              />
              <Text
                size={16}
                bold
                color={Colors.Malachite}
                marginLeft={8}
                lineHeight={18}
              >
                PCM ({patientStats.pcmPatients} patients)
              </Text>
            </TextWrapper>

            {[
              {
                value: minsStats.pcm30Minless,
                label: "< 30 min",
                color: Colors.RedNeonFuchsia,
                total: minsStats.totalPatients,
              },
              {
                value: minsStats.pcm60Minless,
                label: "30-60 min",
                color: Colors.BlueCrayola,
                total: minsStats.totalPatients,
              },
              {
                value: minsStats.pcm60Plus,
                label: "60+ min",
                color: Colors.ForestGreen,
                total: minsStats.totalPatients,
              },
            ].map((item, index) => {
              const percentage =
                item.total > 0 ? (item.value / item.total) * 100 : 0;
              return (
                <TextWrapper key={index} style={styles.progressBarItem}>
                  <TextWrapper style={styles.progressBarHeader}>
                    <Text
                      size={14}
                      color={Colors.DarkJungleGreen}
                      lineHeight={16}
                    >
                      {item.label}
                    </Text>
                    <Text size={14} bold color={item.color} lineHeight={16}>
                      {item.value} ({percentage.toFixed(1)}%)
                    </Text>
                  </TextWrapper>
                  <TextWrapper style={styles.progressBarContainer}>
                    <TextWrapper
                      style={[
                        styles.progressBarFill,
                        {
                          width: `${Math.max(percentage, 2)}%`,
                          backgroundColor: item.color,
                        },
                      ]}
                    >
                      <></>
                    </TextWrapper>
                  </TextWrapper>
                </TextWrapper>
              );
            })}
          </TextWrapper>
        </TextWrapper>

        {/* Additional Metrics */}
        <TextWrapper style={styles.sectionCard}>
          <Text
            size={18}
            bold
            color={Colors.DarkJungleGreen}
            marginBottom={16}
            lineHeight={20}
          >
            Data Collections
          </Text>
          <TextWrapper style={styles.additionalMetrics}>
            <TextWrapper style={styles.metricItem}>
              <MaterialIcons
                name="calendar-today"
                size={20}
                color={Colors.TealBlue}
              />
              <Text
                size={14}
                color={Colors.DarkJungleGreen}
                marginLeft={8}
                lineHeight={16}
              >
                Eligible Patients: {patientStats.rpmPatients}
              </Text>
            </TextWrapper>
            <TextWrapper style={styles.metricItem}>
              <MaterialIcons
                name="trending-up"
                size={20}
                color={Colors.Orange}
              />
              <Text
                size={14}
                color={Colors.DarkJungleGreen}
                marginLeft={8}
                lineHeight={16}
              >
                Readings completed: {minsStats.exceeded16Days}
              </Text>
            </TextWrapper>
            <TextWrapper style={styles.metricItem}>
              <MaterialIcons
                name="assignment"
                size={20}
                color={Colors.BlueCrayola}
              />
              <Text
                size={14}
                color={Colors.DarkJungleGreen}
                marginLeft={8}
                lineHeight={16}
              >
                Under 16 Days: {patientStats.daysReadingCaptured}
              </Text>
            </TextWrapper>
          </TextWrapper>
        </TextWrapper>
      </ScrollView>
    );
  };

  // Render patient summary content
  const renderPatientSummary = () => {
    return (
      <ScrollView
        style={styles.contentScroll}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: spacing.lg }}
      >
        {/* Patient Selector - Always visible */}
        <TextWrapper style={styles.sectionCard}>
          <PatientSelector
            value={category}
            onChange={handlePatientChange}
            items={patientList}
            placeholder="Choose a patient..."
            label="Select Patient"
            onClear={handleClearPatient}
          />
        </TextWrapper>

        {/* Content based on selection */}
        {Number(category?.id) === 0 ? (
          <TextWrapper style={styles.emptyCard}>
            <MaterialIcons
              name="person-search"
              size={48}
              color={Colors.GrayBlue}
            />
            <Text size={16} color={Colors.GrayBlue} marginTop={16} center>
              No Patient Selected
            </Text>
            <Text size={14} color={Colors.GrayBlue} marginTop={8} center>
              Please select a patient to view their detailed summary
            </Text>
          </TextWrapper>
        ) : !patientSummary ? (
          <TextWrapper style={styles.emptyCard}>
            <MaterialIcons
              name="hourglass-empty"
              size={48}
              color={Colors.GrayBlue}
            />
            <Text size={16} color={Colors.GrayBlue} marginTop={16} center>
              Loading patient data...
            </Text>
          </TextWrapper>
        ) : (
          <>
            {/* Patient Header */}
            <TextWrapper style={styles.patientHeaderCard}>
              <TextWrapper style={styles.patientHeaderContent}>
                <TextWrapper style={styles.patientAvatar}>
                  <Text size={20} bold color={Colors.White} lineHeight={22}>
                    {(
                      (patientSummary.firstName?.charAt(0) || '') +
                      (patientSummary.lastName?.charAt(0) || '')
                    ).toUpperCase() || 'P'}
                  </Text>
                </TextWrapper>
                <TextWrapper style={styles.patientInfo}>
                  <TextWrapper style={styles.patientDetailRow}>
                    <MaterialIcons
                      name="person"
                      size={16}
                      color={Colors.TealBlue}
                    />
                    <Text
                      size={18}
                      bold
                      color={Colors.DarkJungleGreen}
                      marginLeft={8}
                      lineHeight={20}
                      numberOfLines={1}
                    >
                      {patientSummary.firstName || ''} {patientSummary.lastName || ''}
                    </Text>
                  </TextWrapper>
                  <TextWrapper style={styles.patientDetailRow}>
                    <MaterialIcons
                      name="assignment-ind"
                      size={16}
                      color={Colors.TealBlue}
                    />
                    <Text
                      size={14}
                      color={Colors.DarkJungleGreen}
                      marginLeft={8}
                      lineHeight={16}
                      numberOfLines={1}
                    >
                      MRN: {patientSummary.mrn}
                    </Text>
                  </TextWrapper>
                  <TextWrapper style={styles.patientDetailRow}>
                    <MaterialIcons
                      name={patientSummary.gender === "M" ? "male" : "female"}
                      size={16}
                      color={Colors.TealBlue}
                    />
                    <Text
                      size={14}
                      color={Colors.DarkJungleGreen}
                      marginLeft={8}
                      lineHeight={16}
                      numberOfLines={1}
                    >
                      {patientSummary.gender === "M" ? "Male" : "Female"}
                    </Text>
                    <MaterialIcons
                      name="calendar-today"
                      size={16}
                      color={Colors.TealBlue}
                      style={{ marginLeft: 16 }}
                    />
                    <Text
                      size={14}
                      color={Colors.DarkJungleGreen}
                      marginLeft={4}
                      lineHeight={16}
                      numberOfLines={1}
                    >
                      {moment(patientSummary.dateOfBirth, "MM-DD-YYYY").format(
                        "MMMM D, YYYY"
                      )}
                    </Text>
                  </TextWrapper>
                </TextWrapper>
              </TextWrapper>
            </TextWrapper>

            {/* Vital Signs with Latest Vitals Data */}
            {patientSummary.latestVitals && (() => {
              // Use latestVitals instead of vitalVO - handle nested structure
              const getLatestVitalValue = (vitalKey: string, subKey?: string) => {
                if (!patientSummary.latestVitals) return null;
                
                if (subKey) {
                  // Handle nested structure like Blood Pressure (systolic/diastolic) or Blood Sugar (fasting/random)
                  const vitalData = patientSummary.latestVitals[vitalKey] as any;
                  if (vitalData && vitalData[subKey] && vitalData[subKey].values && vitalData[subKey].values.length > 0) {
                    return vitalData[subKey].values[0].value.toString();
                  }
                } else {
                  // Handle direct structure like Heart Rate, Temperature, etc.
                  const vitalData = patientSummary.latestVitals[vitalKey] as any;
                  if (vitalData && vitalData.values && vitalData.values.length > 0) {
                    return vitalData.values[0].value.toString();
                  }
                }
                return null;
              };

                             // Define all possible vital signs with their configurations using latestVitals
               const vitalSignsConfig = [
                 {
                   key: 'heartRate',
                   value: getLatestVitalValue('Heart Rate'),
                   label: 'Heart\nRate',
                   icon: 'favorite',
                   color: Colors.RedNeonFuchsia,
                   unit: 'bpm'
                 },
                 {
                   key: 'spo2',
                   value: getLatestVitalValue('Oxygen Saturation'),
                   label: 'SpO2',
                   icon: 'water-drop',
                   color: Colors.BlueCrayola,
                   unit: '%'
                 },
                 {
                   key: 'bloodPressure',
                   value: (() => {
                     const systolic = getLatestVitalValue('Blood Pressure', 'systolic');
                     const diastolic = getLatestVitalValue('Blood Pressure', 'diastolic');
                     if (systolic && diastolic) {
                       return `${systolic}/${diastolic}`;
                     } else if (systolic) {
                       return `${systolic}/--`;
                     } else if (diastolic) {
                       return `--/${diastolic}`;
                     }
                     return null;
                   })(),
                   label: 'Blood\nPressure',
                   icon: 'favorite',
                   color: Colors.TealBlue,
                   unit: 'mmHg'
                 },
                 {
                   key: 'bloodSugar',
                   value: getLatestVitalValue('Blood Sugar', 'fasting'),
                   label: 'Blood\nSugar',
                   icon: 'bloodtype',
                   color: Colors.PinkOrange,
                   unit: 'mg/dL'
                 },
                 {
                   key: 'temperature',
                   value: getLatestVitalValue('Temperature'),
                   label: 'Temperature',
                   icon: 'thermostat',
                   color: Colors.Orange,
                   unit: '°F'
                 },
                 {
                   key: 'weight',
                   value: getLatestVitalValue('Weight'),
                   label: 'Weight',
                   icon: 'scale',
                   color: Colors.Malachite,
                   unit: 'lbs'
                 },
                 {
                   key: 'stepCount',
                   value: getLatestVitalValue('Pedometer'),
                   label: 'Steps',
                   icon: 'directions-walk',
                   color: Colors.ForestGreen,
                   unit: 'steps'
                 }
               ];

              // Filter out vital signs that have no value
              const availableVitalSigns = vitalSignsConfig.filter(vital => 
                vital.value && vital.value.toString().trim() !== '' && vital.value !== '0'
              );

              // No longer need sample data generation since we use real API data

              const getVitalCardWidth = () => {
                const count = availableVitalSigns.length;
                if (count === 1) return '100%';
                if (count === 2) return '48%';
                if (count === 3) return '31%';
                return 140; // Fixed width for scroll
              };

              const renderModernVitalCard = (vital: any) => {
                const cardWidth = availableVitalSigns.length <= 3 ? getVitalCardWidth() : 140;
                const isNarrow = Number(cardWidth) < 120;
                
                return (
                  <LinearGradient
                    key={vital.key}
                    colors={[vital.color + '15', vital.color + '05']}
                    style={[
                      styles.modernVitalCard,
                      { 
                        width: cardWidth, 
                        marginRight: availableVitalSigns.length > 1 ? spacing.sm : 0 
                      }
                    ]}
                  >
                    <TextWrapper style={styles.vitalCardHeader}>
                      <MaterialIcons
                        name={vital.icon}
                        size={isNarrow ? 24 : 28}
                        color={vital.color}
                      />
                      <TextWrapper style={styles.vitalLabelContainer}>
                        <Text 
                          size={isNarrow ? 10 : 11} 
                          color={Colors.GrayBlue}
                          numberOfLines={2}
                        >
                          {vital.label.replace('\n', ' ')}
                        </Text>
                      </TextWrapper>
                    </TextWrapper>
                    
                    <TextWrapper style={styles.vitalCardValueContainer}>
                      <Text
                        size={isNarrow ? 16 : 20}
                        bold
                        color={Colors.DarkJungleGreen}
                        lineHeight={isNarrow ? 18 : 22}
                        numberOfLines={2}
                      >
                        {vital.value}
                      </Text>
                      {vital.unit && (
                        <Text 
                          size={isNarrow ? 9 : 11} 
                          color={Colors.GrayBlue} 
                          marginLeft={isNarrow ? 2 : 4}
                          numberOfLines={1}
                        >
                          {vital.unit}
                        </Text>
                      )}
                    </TextWrapper>

                    <TextWrapper style={styles.vitalCardFooter}>
                      <MaterialIcons name="schedule" size={isNarrow ? 10 : 12} color={Colors.GrayBlue} />
                      <Text 
                        size={isNarrow ? 9 : 10} 
                        color={Colors.GrayBlue} 
                        marginLeft={isNarrow ? 2 : 4}
                        numberOfLines={1}
                      >
                        Latest
                      </Text>
                    </TextWrapper>
                  </LinearGradient>
                );
              };

              return availableVitalSigns.length > 0 ? (
                <TextWrapper style={styles.vitalsSection}>
                  <TextWrapper style={styles.vitalsSectionHeader}>
                    <MaterialIcons
                      name="analytics"
                      size={24}
                      color={Colors.RedNeonFuchsia}
                    />
                    <Text
                      size={18}
                      bold
                      color={Colors.DarkJungleGreen}
                      marginLeft={8}
                      lineHeight={20}
                    >
                      Vital Signs Trends
                    </Text>
                    <TextWrapper style={styles.vitalsCountBadge}>
                      <Text size={12} bold color={Colors.White}>
                        {availableVitalSigns.length}
                      </Text>
                    </TextWrapper>
                  </TextWrapper>
                  
                  {availableVitalSigns.map((vital) => {
                    // Get vitals data for this vital type using latestVitals
                    let vitalTypeKey: string;
                    let subKey: string | undefined;
                    let latestVitalData: any;
                    
                                          switch (vital.key) {
                        case 'heartRate':
                          vitalTypeKey = 'Heart Rate';
                          latestVitalData = patientSummary.latestVitals[vitalTypeKey] as any;
                          break;
                        case 'spo2':
                          vitalTypeKey = 'Oxygen Saturation';
                          latestVitalData = patientSummary.latestVitals[vitalTypeKey] as any;
                          break;
                        case 'bloodPressure':
                          vitalTypeKey = 'Blood Pressure';
                          // For blood pressure, combine systolic and diastolic data
                          const systolicData = (patientSummary.latestVitals[vitalTypeKey] as any)?.systolic;
                          const diastolicData = (patientSummary.latestVitals[vitalTypeKey] as any)?.diastolic;
                          
                          if (systolicData && diastolicData && systolicData.values && diastolicData.values) {
                            // Create combined blood pressure data
                            latestVitalData = {
                              ...systolicData,
                              values: systolicData.values.map((systolicReading: any, index: number) => {
                                const diastolicReading = diastolicData.values[index];
                                return {
                                  ...systolicReading,
                                  value: `${systolicReading.value}/${diastolicReading?.value || '--'}`
                                };
                              })
                            };
                          } else {
                            latestVitalData = systolicData; // Fallback to systolic only
                          }
                          break;
                        case 'bloodSugar':
                          vitalTypeKey = 'Blood Sugar';
                          subKey = 'fasting';
                          latestVitalData = (patientSummary.latestVitals[vitalTypeKey] as any)?.[subKey];
                          break;
                        case 'temperature':
                          vitalTypeKey = 'Temperature';
                          latestVitalData = patientSummary.latestVitals[vitalTypeKey] as any;
                          break;
                        case 'weight':
                          vitalTypeKey = 'Weight';
                          latestVitalData = patientSummary.latestVitals[vitalTypeKey] as any;
                          break;
                        case 'stepCount':
                          vitalTypeKey = 'Pedometer';
                          latestVitalData = patientSummary.latestVitals[vitalTypeKey] as any;
                          break;
                        default:
                          vitalTypeKey = vital.key;
                          latestVitalData = patientSummary.latestVitals[vitalTypeKey] as any;
                      }

                    // latestVitalData is already set in the switch statement above
                    
                    // Only render if we have vital data
                    if (!latestVitalData || !latestVitalData.values || latestVitalData.values.length === 0) {
                      return null;
                    }

                    // Use API-provided min/max values
                    const min = latestVitalData.min;
                    const max = latestVitalData.max;

                    // Convert latestVitals format to VitalLineGraph format
                    const formattedData = latestVitalData.values.map((reading: any) => ({
                      value: reading.value,
                      date: reading.datetime,
                      timestamp: moment(reading.datetime).valueOf(),
                    }));

                    return (
                      <VitalLineGraph
                        key={vital.key}
                        title={vital.label.replace('\n', ' ')}
                        data={formattedData}
                        unit={vital.unit}
                        min={min}
                        max={max}
                        cMin={min} // Use same as min for now, or we can calculate based on medical standards
                        cMax={max} // Use same as max for now, or we can calculate based on medical standards
                        color={vital.color}
                        isPlaceholder={false}
                      />
                    );
                  })}
                </TextWrapper>
              ) : null;
            })()}

            {/* Encounter Minutes - Dynamic based on selected programs */}
            {(() => {
              // Check if patient has selected programs
              if (!patientSummary.programs?.selectedPrograms || patientSummary.programs.selectedPrograms.length === 0) {
                return null;
              }

              // Define program configurations with proper medical icons
              const programConfigs = {
                'RPM': {
                  key: 'rpm',
                  label: 'RPM',
                  color: Colors.TealBlue,
                  icon: 'monitor-heart',
                  description: 'Remote Patient Monitoring',
                  minutes: patientSummary.rpmMins || 0
                },
                'CCM': {
                  key: 'ccm',
                  label: 'CCM',
                  color: Colors.BlueCrayola,
                  icon: 'medical-services',
                  description: 'Chronic Care Management',
                  minutes: patientSummary.ccmMins || 0
                },
                'PCM': {
                  key: 'pcm',
                  label: 'PCM',
                  color: Colors.PinkOrange,
                  icon: 'supervisor-account',
                  description: 'Principal Care Management',
                  minutes: patientSummary.pcmMins || 0
                }
              };

              // Get active programs from patient's selected programs
              const activeProgramsData = patientSummary.programs.selectedPrograms
                .filter(program => program.programActivated)
                .map(program => {
                  const config = programConfigs[program.programName as keyof typeof programConfigs];
                  if (config) {
                    return {
                      ...config,
                      minutes: program.mins || config.minutes || 0
                    };
                  }
                  return null;
                })
                .filter(Boolean) as any[];

              // If no active programs, don't render the section
              if (activeProgramsData.length === 0) {
                return null;
              }

              // Calculate card width based on number of active programs
              const getCardWidth = () => {
                const count = activeProgramsData.length;
                if (count === 1) return '100%';
                if (count === 2) return '48%';
                return '31%'; // 3 programs
              };

              const cardWidth = getCardWidth();

              return (
                <TextWrapper style={styles.sectionCard}>
                  <TextWrapper style={styles.sectionHeader}>
                    <MaterialIcons name="schedule" size={24} color={Colors.TealBlue} />
                    <Text
                      size={18}
                      bold
                      color={Colors.DarkJungleGreen}
                      marginLeft={8}
                      lineHeight={20}
                    >
                      Encounter Minutes
                    </Text>
                    <TextWrapper style={styles.vitalsCountBadge}>
                      <Text size={12} bold color={Colors.White}>
                        {activeProgramsData.length}
                      </Text>
                    </TextWrapper>
                  </TextWrapper>
                  <TextWrapper style={[
                    styles.programMinutesGrid,
                    activeProgramsData.length === 1 && { justifyContent: 'center' }
                  ]}>
                    {activeProgramsData.map((program) => (
                      <TextWrapper 
                        key={program.key} 
                        style={[
                          styles.cleanProgramCard,
                          { 
                            width: cardWidth,
                            marginRight: activeProgramsData.length > 1 ? spacing.sm : 0
                          }
                        ]}
                      >
                        <MaterialIcons
                          name={program.icon as any}
                          size={24}
                          color={program.color}
                        />
                        <Text
                          size={fontScale(14)}
                          bold
                          color={program.color}
                          marginTop={8}
                          lineHeight={fontScale(16)}
                          numberOfLines={1}
                        >
                          {getMinsSecs(program.minutes)}
                        </Text>
                        <Text
                          size={typography.caption.fontSize}
                          color={Colors.GrayBlue}
                          marginTop={4}
                          lineHeight={typography.caption.lineHeight}
                          numberOfLines={1}
                        >
                          {program.label}
                        </Text>
                      </TextWrapper>
                    ))}
                  </TextWrapper>
                </TextWrapper>
              );
            })()}

            <TextWrapper style={styles.sectionCard}>
              <TextWrapper style={styles.sectionHeader}>
                <MaterialIcons
                  name="analytics"
                  size={24}
                  color={Colors.Orange}
                />
                <Text
                  size={18}
                  bold
                  color={Colors.DarkJungleGreen}
                  marginLeft={8}
                  lineHeight={20}
                >
                  Data Collection
                </Text>
              </TextWrapper>
              <TextWrapper style={styles.cleanInfoRow}>
                <MaterialIcons
                  name="calendar-today"
                  size={20}
                  color={Colors.Orange}
                />
                <Text
                  size={14}
                  color={Colors.DarkJungleGreen}
                  marginLeft={12}
                  lineHeight={16}
                  numberOfLines={1}
                >
                  Days Measured: {patientSummary.daysMeasured}
                </Text>
              </TextWrapper>
              <TextWrapper style={styles.cleanInfoRow}>
                <MaterialIcons name="group" size={20} color={Colors.TealBlue} />
                <Text
                  size={14}
                  color={Colors.DarkJungleGreen}
                  marginLeft={12}
                  lineHeight={16}
                  numberOfLines={1}
                >
                  Group: {patientSummary.groupName}
                </Text>
              </TextWrapper>
            </TextWrapper>

            {/* Active Programs */}
            <TextWrapper style={styles.sectionCard}>
              <TextWrapper style={styles.sectionHeader}>
                <MaterialIcons
                  name="checklist"
                  size={24}
                  color={Colors.TealBlue}
                />
                <Text
                  size={18}
                  bold
                  color={Colors.DarkJungleGreen}
                  marginLeft={8}
                  lineHeight={20}
                >
                  Active Programs
                </Text>
              </TextWrapper>
              {patientSummary.programs &&
                patientSummary.programs.selectedPrograms &&
                patientSummary.programs.selectedPrograms.map(
                  (program, index) => (
                    <TextWrapper key={index} style={styles.cleanProgramRow}>
                      <TextWrapper style={styles.programRowLeft}>
                        <TextWrapper
                          style={[
                            styles.programIndicator,
                            {
                              backgroundColor: program.programActivated
                                ? Colors.ForestGreen
                                : Colors.GrayBlue,
                            },
                          ]}
                        >
                          <></>
                        </TextWrapper>
                        <Text
                          size={14}
                          bold
                          color={Colors.DarkJungleGreen}
                          lineHeight={16}
                          numberOfLines={1}
                        >
                          {program.programName}
                        </Text>
                      </TextWrapper>
                      <Text size={14} color={Colors.GrayBlue} lineHeight={16} numberOfLines={1}>
                        {getMinsSecs(program.mins)}
                      </Text>
                    </TextWrapper>
                  )
                )}
            </TextWrapper>

            {/* Chronic Conditions */}
            {patientSummary.chronicConditions &&
              patientSummary.chronicConditions.length > 0 && (
                <TextWrapper style={styles.sectionCard}>
                  <TextWrapper style={styles.sectionHeader}>
                    <MaterialIcons
                      name="medical-services"
                      size={24}
                      color={Colors.Orange}
                    />
                    <Text
                      size={18}
                      bold
                      color={Colors.DarkJungleGreen}
                      marginLeft={8}
                      lineHeight={20}
                    >
                      Chronic Conditions
                    </Text>
                  </TextWrapper>
                  {patientSummary.chronicConditions.map((condition, index) => (
                    <TextWrapper key={index} style={styles.cleanInfoRow}>
                      <MaterialIcons
                        name="circle"
                        size={8}
                        color={Colors.Orange}
                      />
                      <TextWrapper style={styles.conditionInfo}>
                        <Text
                          size={14}
                          bold
                          color={Colors.DarkJungleGreen}
                          lineHeight={16}
                          numberOfLines={2}
                        >
                          {condition.chronicConditionName}
                        </Text>
                        <Text size={12} color={Colors.GrayBlue} lineHeight={16}>
                          ICD Code: {condition.icdCode}
                        </Text>
                      </TextWrapper>
                    </TextWrapper>
                  ))}
                </TextWrapper>
              )}

            {/* Care Team */}
            <TextWrapper style={styles.sectionCard}>
              <TextWrapper style={styles.sectionHeader}>
                <MaterialIcons name="group" size={24} color={Colors.TealBlue} />
                <Text
                  size={18}
                  bold
                  color={Colors.DarkJungleGreen}
                  marginLeft={8}
                  lineHeight={20}
                >
                  Care Team
                </Text>
              </TextWrapper>
              <TextWrapper style={styles.cleanInfoRow}>
                <MaterialIcons
                  name="local-hospital"
                  size={24}
                  color={Colors.TealBlue}
                />
                <TextWrapper style={styles.careTeamInfo}>
                  <Text
                    size={14}
                    bold
                    color={Colors.DarkJungleGreen}
                    lineHeight={16}
                    numberOfLines={1}
                  >
                    Dr. {patientSummary.physicianName}
                  </Text>
                  <Text size={12} color={Colors.GrayBlue} lineHeight={14} numberOfLines={1}>
                    {patientSummary.physicianEmail}
                  </Text>
                  <Text size={12} color={Colors.GrayBlue} lineHeight={14} numberOfLines={1}>
                    {patientSummary.physicianPhone}
                  </Text>
                </TextWrapper>
              </TextWrapper>

              {patientSummary.clinician && (
                <TextWrapper style={styles.cleanInfoRow}>
                  <MaterialIcons
                    name="person"
                    size={24}
                    color={Colors.BlueCrayola}
                  />
                  <TextWrapper style={styles.careTeamInfo}>
                    <Text
                      size={14}
                      bold
                      color={Colors.DarkJungleGreen}
                      lineHeight={16}
                      numberOfLines={1}
                    >
                      {patientSummary.clinician.firstName} {patientSummary.clinician.lastName}
                    </Text>
                    <Text size={12} color={Colors.GrayBlue} lineHeight={14} numberOfLines={1}>
                      {patientSummary.clinician.email}
                    </Text>
                    <Text size={12} color={Colors.GrayBlue} lineHeight={14} numberOfLines={1}>
                      {patientSummary.clinician.phoneNumber}
                    </Text>
                  </TextWrapper>
                </TextWrapper>
              )}
            </TextWrapper>

            {patientSummary.assignedDevices &&
              patientSummary.assignedDevices.length > 0 && (
                <TextWrapper style={styles.sectionCard}>
                  <TextWrapper style={styles.sectionHeader}>
                    <MaterialIcons
                      name="devices-other"
                      size={24}
                      color={Colors.TealBlue}
                    />
                    <Text
                      size={18}
                      bold
                      color={Colors.DarkJungleGreen}
                      marginLeft={8}
                      lineHeight={20}
                    >
                      Assigned Device(s)
                    </Text>
                  </TextWrapper>
                  {patientSummary.assignedDevices.map((condition, index) => (
                    <TextWrapper key={index} style={styles.cleanInfoRow}>
                      <MaterialIcons
                        name={getDeviceIcon(condition.deviceType)}
                        size={16}
                        color={Colors.Orange}
                      />
                      <TextWrapper style={styles.conditionInfo}>
                        <Text
                          size={14}
                          bold
                          color={Colors.DarkJungleGreen}
                          lineHeight={16}
                          numberOfLines={1}
                        >
                          {condition.deviceType}
                        </Text>
                        <Text size={12} color={Colors.GrayBlue} lineHeight={16} numberOfLines={1}>
                          Device ID: {condition.imei}
                        </Text>
                      </TextWrapper>
                    </TextWrapper>
                  ))}
                </TextWrapper>
              )}
          </>
        )}
      </ScrollView>
    );
  };

  return (
    <SafeAreaView style={styles.safeArea} edges={['left', 'right']}>
      <StatusBar 
        barStyle="dark-content" 
        backgroundColor="#f8fafc" 
        translucent={false}
      />
      <Container style={styles.container} disableHeaderPadding={true}>
        <Loader modalVisible={loader} />

        {renderTabButtons()}

        <TextWrapper style={styles.contentContainer}>
          {activeTab === 0 ? renderMonthlySummary() : renderPatientSummary()}
        </TextWrapper>
      </Container>
    </SafeAreaView>
  );
};

export default Validation;

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#f8fafc",
  },
  container: {
    flex: 1,
    backgroundColor: "#f8fafc",
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: Colors.White,
    borderBottomWidth: 1,
    borderBottomColor: "#f1f5f9",
  },
  tabContainer: {
    flexDirection: "row",
    backgroundColor: Colors.White,
    marginHorizontal: spacing.lg,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
    borderRadius: cardDimensions.borderRadius,
    padding: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tabButton: {
    flex: 1,
    paddingVertical: spacing.sm + 2,
    paddingHorizontal: spacing.md,
    borderRadius: cardDimensions.borderRadius - 4,
    alignItems: "center",
    justifyContent: "center",
  },
  activeTabButton: {
    backgroundColor: Colors.TealBlue,
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.md,
    overflow: 'hidden',
  },
  contentScroll: {
    flex: 1,
  },
  emptyCard: {
    backgroundColor: Colors.White,
    borderRadius: cardDimensions.borderRadius,
    padding: spacing.xxl,
    marginHorizontal: spacing.xs,
    marginVertical: spacing.md,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    minHeight: 200,
  },
  sectionCard: {
    backgroundColor: Colors.White,
    borderRadius: cardDimensions.borderRadius,
    padding: cardDimensions.padding,
    marginHorizontal: spacing.xs,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    overflow: 'hidden',
  },
  growthCard: {
    borderRadius: cardDimensions.borderRadius,
    padding: cardDimensions.padding,
    marginHorizontal: spacing.xs,
    marginBottom: spacing.md,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    minHeight: patientGrowthCard.height + 10,
    maxHeight: patientGrowthCard.height + 20,
    borderWidth: 1,
    borderColor: '#d1d5db',
    overflow: 'hidden',
  },
  growthContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "flex-start",
  },
  growthIcon: {
    opacity: 0.8,
    marginLeft: spacing.sm,
  },
  statsGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.md,
    flexWrap: 'wrap',
  },
  statItem: {
    flex: 1,
    alignItems: "center",
    marginHorizontal: spacing.xs,
    minWidth: 80,
    paddingVertical: spacing.sm,
  },
  statIconWrapper: {
    width: iconSizes.xl + 16,
    height: iconSizes.xl + 16,
    borderRadius: (iconSizes.xl + 16) / 2,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: spacing.sm,
  },
  programGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "stretch",
    marginBottom: spacing.md,
    flexWrap: 'wrap',
  },
  programCard: {
    flex: 1,
    backgroundColor: "#f8fafc",
    borderRadius: cardDimensions.borderRadius,
    padding: cardDimensions.padding,
    marginHorizontal: spacing.xs,
    borderWidth: 1,
    borderColor: "#d1d5db",
    alignItems: "center",
    justifyContent: "center",
    minHeight: cardDimensions.minHeight,
    minWidth: 100,
  },

  minutesSection: {
    marginBottom: spacing.lg,
  },
  minutesGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
    flexWrap: 'wrap',
  },
  minuteCard: {
    flex: 1,
    backgroundColor: "#f8fafc",
    borderRadius: cardDimensions.borderRadius,
    padding: spacing.sm,
    marginHorizontal: spacing.xs,
    borderLeftWidth: 3,
    alignItems: "center",
    minWidth: 70,
    minHeight: 60,
  },
  additionalMetrics: {
    gap: spacing.sm,
  },
  metricItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: spacing.sm,
    flexWrap: 'wrap',
  },
  patientHeaderCard: {
    borderRadius: cardDimensions.borderRadius,
    padding: cardDimensions.padding,
    marginHorizontal: spacing.xs,
    marginBottom: spacing.md,
    backgroundColor: Colors.White,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  patientHeaderContent: {
    flexDirection: "row",
    alignItems: "center",
    flexWrap: 'wrap',
  },
  patientAvatar: {
    width: iconSizes.xl + 28,
    height: iconSizes.xl + 28,
    borderRadius: (iconSizes.xl + 28) / 2,
    backgroundColor: Colors.TealBlue,
    alignItems: "center",
    justifyContent: "center",
    marginRight: spacing.md,
    flexShrink: 0,
  },
  patientInfo: {
    flex: 1,
    minWidth: 200,
  },
  patientDetailRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.sm,
    flexWrap: 'wrap',
  },
  vitalsGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
    flexWrap: "wrap",
    gap: spacing.xs,
  },
  vitalCard: {
    flex: 1,
    backgroundColor: "#f8fafc",
    borderRadius: cardDimensions.borderRadius,
    padding: cardDimensions.padding,
    marginHorizontal: spacing.xs,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    borderColor: "#d1d5db",
    minHeight: cardDimensions.minHeight,
    minWidth: 100,
  },
  programMinutesGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
    flexWrap: "wrap",
    gap: spacing.xs,
  },
  programMinuteCard: {
    flex: 1,
    backgroundColor: "#f8fafc",
    borderRadius: cardDimensions.borderRadius,
    padding: spacing.md,
    marginHorizontal: spacing.xs,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#d1d5db",
    borderLeftWidth: 4,
    minWidth: 100,
    minHeight: 80,
  },
  cleanProgramCard: {
    flex: 1,
    backgroundColor: "#f8fafc",
    borderRadius: cardDimensions.borderRadius,
    padding: cardDimensions.padding,
    marginHorizontal: spacing.xs,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#d1d5db",
    minHeight: cardDimensions.minHeight,
    minWidth: 100,
  },
  cleanProgramRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: spacing.sm,
    flexWrap: 'wrap',
  },
  cleanInfoRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: spacing.sm,
    flexWrap: 'wrap',
  },
  programRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: "#f1f5f9",
    flexWrap: 'wrap',
  },
  programRowLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  programIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: spacing.sm,
    flexShrink: 0,
  },
  conditionRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: "#f1f5f9",
    flexWrap: 'wrap',
  },
  conditionInfo: {
    marginLeft: spacing.sm,
    flex: 1,
    minWidth: 150,
  },
  careTeamCard: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: "#f1f5f9",
    flexWrap: 'wrap',
  },
  careTeamInfo: {
    marginLeft: spacing.sm,
    flex: 1,
    gap: spacing.sm,
    minWidth: 200,
  },
  additionalInfo: {
    gap: spacing.sm,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: spacing.sm,
    flexWrap: 'wrap',
  },
  datePickersContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    flexWrap: 'wrap',
  },
  datePickerWrapper: {
    width: "46%",
    minWidth: 150,
  },
  dateSeparator: {
    width: "8%",
    alignItems: "center",
    justifyContent: "center",
    paddingTop: spacing.lg,
    minWidth: 20,
  },
  // Section and program headers
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.lg,
    flexWrap: 'wrap',
  },
  programSection: {
    marginBottom: spacing.lg,
  },
  programHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.md,
    paddingLeft: spacing.xs,
    flexWrap: 'wrap',
  },
  progressBarItem: {
    marginBottom: spacing.sm,
    paddingLeft: spacing.sm,
  },
  progressBarHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.sm,
    flexWrap: 'wrap',
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: "#f1f5f9",
    borderRadius: 4,
    overflow: "hidden",
    minWidth: 100,
  },
  progressBarFill: {
    height: "100%",
    borderRadius: 4,
    minWidth: 8,
  },
  circularProgressContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingVertical: spacing.md,
    flexWrap: 'wrap',
  },
  circularProgressItem: {
    alignItems: "center",
    marginVertical: spacing.sm,
  },
  circularProgress: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 3,
    backgroundColor: Colors.White,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  pcmCardsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: spacing.md,
    flexWrap: 'wrap',
  },
  pcmCard: {
    flex: 1,
    alignItems: "center",
    padding: spacing.md,
    borderRadius: cardDimensions.borderRadius,
    marginHorizontal: spacing.xs,
    minWidth: 100,
    minHeight: 80,
  },
  
  // Modern Vitals Styles
  vitalsSection: {
    marginBottom: spacing.md,
  },
  vitalsSectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: Colors.White,
    borderRadius: cardDimensions.borderRadius,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  vitalsCountBadge: {
    backgroundColor: Colors.RedNeonFuchsia,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginLeft: 'auto',
  },
  vitalsScrollView: {
    marginLeft: -spacing.md,
    marginRight: -spacing.md,
  },
  vitalsScrollContainer: {
    paddingHorizontal: spacing.md,
    paddingRight: spacing.lg,
  },
  vitalsStaticContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
  },
  vitalsStaticContainerCentered: {
    justifyContent: 'center',
    alignItems: 'center',
    gap: spacing.md,
  },
  modernVitalCard: {
    minHeight: 110,
    maxHeight: 140,
    borderRadius: 16,
    padding: spacing.md,
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  vitalCardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.xs,
  },
  vitalCardValueContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    justifyContent: 'flex-start',
    marginVertical: spacing.xs,
    flexWrap: 'wrap',
  },
  vitalCardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    opacity: 0.7,
    marginTop: spacing.xs,
  },
  vitalLabelContainer: {
    flex: 1,
    marginLeft: spacing.sm,
  },

  // Modern Date Range Styles
  modernDateRangeCard: {
    backgroundColor: Colors.White,
    borderRadius: cardDimensions.borderRadius,
    padding: spacing.sm,
    marginHorizontal: spacing.xs,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  dateRangeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  dateRangeContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dateColumn: {
    alignItems: 'center',
  },
  dateLabel: {
    marginBottom: spacing.xs,
  },
  dateBox: {
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    borderRadius: 6,
    padding: spacing.xs,
    minWidth: 90,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },

  // Modern Growth Styles
  modernGrowthCard: {
    marginHorizontal: spacing.xs,
    marginBottom: spacing.sm,
    borderRadius: cardDimensions.borderRadius,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  growthGradientBackground: {
    padding: spacing.md,
  },
  growthHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  growthIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.White,
    alignItems: 'center',
    justifyContent: 'center',
  },
  growthLabelContainer: {
    flex: 1,
    marginLeft: spacing.sm,
  },
  growthValueContainer: {
    alignItems: 'center',
  },
});
