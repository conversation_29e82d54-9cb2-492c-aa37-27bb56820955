import Container from "@/components/Layout/Container";
import Loader from "@/components/Loader/Loader";
import NavigationHeader from "@/components/NavigationHeader";
import Text from "@/components/Text";
import TextWrapper from "@/components/TextWrapper";
import VitalLineGraph from "@/components/VitalsCards";
import { Colors } from "@/constants";
import { apiPostWithToken } from "@/services/apis/apiManager";
import URLS from "@/services/config/config";
import { cardDimensions, grid, iconSizes, spacing, typography, fontScale } from "@/utils/responsive";
import { MaterialIcons } from "@expo/vector-icons";
import { useRoute } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";
import moment from "moment";
import React, { useEffect, useState } from "react";
import { Al<PERSON>, ScrollView, StyleSheet, View } from "react-native";
import { useSelector } from 'react-redux';

interface PatientSummaryData {
  patientId: string;
  patientCreatedDate: string;
  clinician: {
    phoneNumber: string;
    available: boolean;
    firstName: string;
    lastName: string;
    email: string;
    clinicianId: string;
  };
  mrn: string;
  vitalVO: {
    weight: string;
    weightDate: string;
    temperature: string;
    temperatureDate: string;
    stepCount: string;
    stepCountDate: string;
    heartRate: string;
    heartRateDate: string;
    deepSleep: string;
    shallowSleep: string;
    sleepTime: string;
  };
  latestVitals: {
    [key: string]: {
      min: number;
      max: number;
      values: Array<{
        datetime: string;
        value: number;
      }>;
    };
  };
  firstName: string;
  lastName: string;
  status: string;
  physicianName: string;
  physicianEmail: string;
  physicianPhone: string;
  chronicConditions: Array<{
    chonicId: string;
    chronicConditionName: string;
    icdCode: string;
  }>;
  email: string;
  rpmMins: number;
  ccmMins: number;
  pcmMins: number;
  dateOfBirth: string;
  daysMeasured: number;
  groupName: string;
  programs: {
    selectedPrograms: Array<{
      mins: number;
      programName: string;
      programActivated: boolean;
      patientProgramId: string;
      programId: string;
    }>;
  };
  gender: string;
  patientAddress: string;
  assignedDevices: Array<{
    assignedWatch: string;
    imei: string;
    deviceType: string;
  }>;
}

interface RouteParams {
  patientId?: string;
  patientName?: string;
}

/**
 * Patient Summary Screen
 * Displays a comprehensive summary of patient information and recent activities
 */
const SummaryScreen = () => {
  const [loader, setLoader] = useState(false);
  const [patientSummary, setPatientSummary] =
    useState<PatientSummaryData | null>(null);
  
  const caregiverId = useSelector((state: any) => state?.loginReducer?.data?.userId);
  const userRole = useSelector((state: any) => state?.loginReducer?.userRole || 'caregiver');
  const roleId = useSelector((state: any) => state?.loginReducer?.roleId || 5);

  const route = useRoute();
  const params = route.params as RouteParams;
  const patientId = params?.patientId || "";
  const patientName = params?.patientName || "Patient Summary";

  const getPatientSummary = async (patientId: string | number) => {
    if (!patientId || patientId === 0) return;

    setLoader(true);
    try {
      const response = await apiPostWithToken(
        { 
          patientId: String(patientId),
          userId: String(caregiverId),
          roleType: roleId.toString()
        },
        URLS.caregiverUrl + "getPatientSummary"
      );

      setLoader(false);
      if (response?.status === 200 && response?.data) {
        // Merge patient data with latestVitals at root level to match interface
        const mergedData = {
          ...response.data.patient,
          latestVitals: response.data.latestVitals
        };
        
        setPatientSummary(mergedData);
      } else {
        setPatientSummary(null);
        Alert.alert("Error", "Failed to load patient data. Please try again.");
      }
    } catch (error) {
      setLoader(false);
      setPatientSummary(null);
      Alert.alert("Error", "Failed to load patient data. Please try again.");
    }
  };

  useEffect(() => {
    if (patientId && caregiverId) {
      getPatientSummary(patientId);
    }
  }, [patientId, caregiverId]);

  const renderPatientSummary = () => {
    if (!patientSummary) {
      return (
        <TextWrapper style={styles.emptyCard}>
          <MaterialIcons
            name="person-search"
            size={48}
            color={Colors.GrayBlue}
          />
          <Text size={16} color={Colors.GrayBlue} marginTop={16} center>
            No Patient Data Available
          </Text>
          <Text size={14} color={Colors.GrayBlue} marginTop={8} center>
            Unable to load patient summary information
          </Text>
        </TextWrapper>
      );
    }

    const getDeviceIcon = (deviceType: string) => {
      if (deviceType.includes("Watch")) {
        return "watch";
      } else if (deviceType.includes("Mobile")) {
        return "install-mobile";
      } else {
        return "devices-other";
      }
    };

    const getMinsSecs = (mins: number) => {
      const wholeMinutes = Math.floor(mins);
      const seconds = Math.round((mins - wholeMinutes) * 60);
      return `${wholeMinutes}m:${seconds.toString().padStart(2, "0")}s`;
    };

    // Use latestVitals instead of vitalVO - handle nested structure
    const getLatestVitalValue = (vitalKey: string, subKey?: string) => {
      if (!patientSummary.latestVitals) return null;
      
      if (subKey) {
        // Handle nested structure like Blood Pressure (systolic/diastolic) or Blood Sugar (fasting/random)
        const vitalData = patientSummary.latestVitals[vitalKey] as any;
        if (vitalData && vitalData[subKey] && vitalData[subKey].values && vitalData[subKey].values.length > 0) {
          return vitalData[subKey].values[0].value.toString();
        }
      } else {
        // Handle direct structure like Heart Rate, Temperature, etc.
        const vitalData = patientSummary.latestVitals[vitalKey] as any;
        if (vitalData && vitalData.values && vitalData.values.length > 0) {
          return vitalData.values[0].value.toString();
        }
      }
      return null;
    };

    // Define all possible vital signs with their configurations using latestVitals
    const vitalSignsConfig = [
      {
        key: 'heartRate',
        value: getLatestVitalValue('Heart Rate'),
        label: 'Heart\nRate',
        icon: 'favorite',
        color: Colors.RedNeonFuchsia,
        unit: 'bpm'
      },
      {
        key: 'spo2',
        value: getLatestVitalValue('Oxygen Saturation'),
        label: 'SpO2',
        icon: 'water-drop',
        color: Colors.BlueCrayola,
        unit: '%'
      },
      {
        key: 'bloodPressure',
        value: (() => {
          const systolic = getLatestVitalValue('Blood Pressure', 'systolic');
          const diastolic = getLatestVitalValue('Blood Pressure', 'diastolic');
          if (systolic && diastolic) {
            return `${systolic}/${diastolic}`;
          } else if (systolic) {
            return `${systolic}/--`;
          } else if (diastolic) {
            return `--/${diastolic}`;
          }
          return null;
        })(),
        label: 'Blood\nPressure',
        icon: 'favorite',
        color: Colors.TealBlue,
        unit: 'mmHg'
      },
      {
        key: 'bloodSugar',
        value: getLatestVitalValue('Blood Sugar', 'fasting'),
        label: 'Blood\nSugar',
        icon: 'bloodtype',
        color: Colors.PinkOrange,
        unit: 'mg/dL'
      },
      {
        key: 'temperature',
        value: getLatestVitalValue('Temperature'),
        label: 'Temperature',
        icon: 'thermostat',
        color: Colors.Orange,
        unit: '°F'
      },
      {
        key: 'weight',
        value: getLatestVitalValue('Weight'),
        label: 'Weight',
        icon: 'scale',
        color: Colors.Malachite,
        unit: 'lbs'
      },
      {
        key: 'stepCount',
        value: getLatestVitalValue('Pedometer'),
        label: 'Steps',
        icon: 'directions-walk',
        color: Colors.ForestGreen,
        unit: 'steps'
      }
    ];

    // Filter out vital signs that have no value
    const availableVitalSigns = vitalSignsConfig.filter(vital => 
      vital.value && vital.value.toString().trim() !== '' && vital.value !== '0'
    );

    const getCardWidth = () => {
      const count = availableVitalSigns.length;
      // Calculate optimal width based on count for responsive layout
      if (count === 1) return '100%';
      if (count === 2) return '48.5%'; // 2 per row with space
      if (count === 3) return '31.5%'; // 3 per row
      if (count === 4) return '48.5%'; // 2x2 grid
      if (count === 5) return '31.5%'; // 3 per row (2 rows: 3+2)
      if (count === 6) return '31.5%'; // 3 per row (2 rows: 3+3)
      return '23%'; // 4 per row for 7+ items
    };

    const getVitalCardWidth = () => {
      const count = availableVitalSigns.length;
      if (count === 1) return '100%';
      if (count === 2) return '48%';
      if (count === 3) return '31%';
      return 140; // Fixed width for scroll
    };

    const renderModernVitalCard = (vital: any) => {
      const cardWidth = availableVitalSigns.length <= 3 ? getVitalCardWidth() : 140;
      const isNarrow = Number(cardWidth) < 120;
      
      return (
        <LinearGradient
          key={vital.key}
          colors={[vital.color + '15', vital.color + '05']}
          style={[
            styles.modernVitalCard,
            { 
              width: cardWidth, 
              marginRight: availableVitalSigns.length > 1 ? spacing.sm : 0 
            }
          ]}
        >
          <TextWrapper style={styles.vitalCardHeader}>
            <MaterialIcons
              name={vital.icon}
              size={isNarrow ? 24 : 28}
              color={vital.color}
            />
            <TextWrapper style={styles.vitalLabelContainer}>
              <Text 
                size={isNarrow ? 10 : 11} 
                color={Colors.GrayBlue}
                numberOfLines={2}
              >
                {vital.label.replace('\n', ' ')}
              </Text>
            </TextWrapper>
          </TextWrapper>
          
          <TextWrapper style={styles.vitalCardValueContainer}>
            <Text
              size={isNarrow ? 16 : 20}
              bold
              color={Colors.DarkJungleGreen}
              lineHeight={isNarrow ? 18 : 22}
              numberOfLines={2}
            >
              {vital.value}
            </Text>
            {vital.unit && (
              <Text 
                size={isNarrow ? 9 : 11} 
                color={Colors.GrayBlue} 
                marginLeft={isNarrow ? 2 : 4}
                numberOfLines={1}
              >
                {vital.unit}
              </Text>
            )}
          </TextWrapper>

          <TextWrapper style={styles.vitalCardFooter}>
            <MaterialIcons name="schedule" size={isNarrow ? 10 : 12} color={Colors.GrayBlue} />
            <Text 
              size={isNarrow ? 9 : 10} 
              color={Colors.GrayBlue} 
              marginLeft={isNarrow ? 2 : 4}
              numberOfLines={1}
            >
              Latest
            </Text>
          </TextWrapper>
        </LinearGradient>
      );
    };

    return (
      <ScrollView
        style={styles.contentScroll}
        showsVerticalScrollIndicator={false}
      >
        {/* Patient Header */}
        <TextWrapper style={styles.patientHeaderCard}>
          <TextWrapper style={styles.patientHeaderContent}>
            <TextWrapper style={styles.patientAvatar}>
              <Text size={20} bold color={Colors.White} lineHeight={22}>
                {(
                  (patientSummary.firstName?.charAt(0) || '') +
                  (patientSummary.lastName?.charAt(0) || '')
                ).toUpperCase() || 'P'}
              </Text>
            </TextWrapper>
            <TextWrapper style={styles.patientInfo}>
              <TextWrapper style={styles.patientDetailRow}>
                <MaterialIcons
                  name="person"
                  size={16}
                  color={Colors.TealBlue}
                />
                <Text
                  size={18}
                  bold
                  color={Colors.DarkJungleGreen}
                  marginLeft={8}
                  lineHeight={20}
                  numberOfLines={1}
                >
                  {patientSummary.firstName || ''} {patientSummary.lastName || ''}
                </Text>
              </TextWrapper>
              <TextWrapper style={styles.patientDetailRow}>
                <MaterialIcons
                  name="assignment-ind"
                  size={16}
                  color={Colors.TealBlue}
                />
                <Text
                  size={14}
                  color={Colors.DarkJungleGreen}
                  marginLeft={8}
                  lineHeight={16}
                  numberOfLines={1}
                >
                  MRN: {patientSummary.mrn}
                </Text>
              </TextWrapper>
              <TextWrapper style={styles.patientDetailRow}>
                <MaterialIcons
                  name={patientSummary.gender === "M" ? "male" : "female"}
                  size={16}
                  color={Colors.TealBlue}
                />
                <Text
                  size={14}
                  color={Colors.DarkJungleGreen}
                  marginLeft={8}
                  lineHeight={16}
                  numberOfLines={1}
                >
                  {patientSummary.gender === "M" ? "Male" : "Female"}
                </Text>
                <MaterialIcons
                  name="calendar-today"
                  size={16}
                  color={Colors.TealBlue}
                  style={{ marginLeft: 16 }}
                />
                <Text
                  size={14}
                  color={Colors.DarkJungleGreen}
                  marginLeft={4}
                  lineHeight={16}
                  numberOfLines={1}
                >
                  {moment(patientSummary.dateOfBirth, "MM-DD-YYYY").format(
                    "MMMM D, YYYY"
                  )}
                </Text>
              </TextWrapper>
            </TextWrapper>
          </TextWrapper>
        </TextWrapper>

        {/* Vital Signs with Latest Vitals Data */}
        {patientSummary.latestVitals && availableVitalSigns.length > 0 && (
          <TextWrapper style={styles.vitalsSection}>
            <TextWrapper style={styles.vitalsSectionHeader}>
              <MaterialIcons
                name="analytics"
                size={24}
                color={Colors.RedNeonFuchsia}
              />
              <Text
                size={18}
                bold
                color={Colors.DarkJungleGreen}
                marginLeft={8}
                lineHeight={20}
              >
                Vital Signs
              </Text>
              <TextWrapper style={styles.vitalsCountBadge}>
                <Text size={12} bold color={Colors.White}>
                  {availableVitalSigns.length}
                </Text>
              </TextWrapper>
            </TextWrapper>
            
            {availableVitalSigns.map((vital) => {
              // Get vitals data for this vital type
              let vitalTypeKey: string;
              let subKey: string | undefined;
              let latestVitalData: any;
              
              switch (vital.key) {
                case 'heartRate':
                  vitalTypeKey = 'Heart Rate';
                  latestVitalData = patientSummary.latestVitals[vitalTypeKey] as any;
                  break;
                case 'spo2':
                  vitalTypeKey = 'Oxygen Saturation';
                  latestVitalData = patientSummary.latestVitals[vitalTypeKey] as any;
                  break;
                case 'bloodPressure':
                  vitalTypeKey = 'Blood Pressure';
                  // For blood pressure, combine systolic and diastolic data
                  const systolicData = (patientSummary.latestVitals[vitalTypeKey] as any)?.systolic;
                  const diastolicData = (patientSummary.latestVitals[vitalTypeKey] as any)?.diastolic;
                  
                  if (systolicData && diastolicData && systolicData.values && diastolicData.values) {
                    // Create combined blood pressure data
                    latestVitalData = {
                      ...systolicData,
                      values: systolicData.values.map((systolicReading: any, index: number) => {
                        const diastolicReading = diastolicData.values[index];
                        return {
                          ...systolicReading,
                          value: `${systolicReading.value}/${diastolicReading?.value || '--'}`
                        };
                      })
                    };
                  } else {
                    latestVitalData = systolicData; // Fallback to systolic only
                  }
                  break;
                case 'bloodSugar':
                  vitalTypeKey = 'Blood Sugar';
                  subKey = 'fasting';
                  latestVitalData = (patientSummary.latestVitals[vitalTypeKey] as any)?.[subKey];
                  break;
                case 'temperature':
                  vitalTypeKey = 'Temperature';
                  latestVitalData = patientSummary.latestVitals[vitalTypeKey] as any;
                  break;
                case 'weight':
                  vitalTypeKey = 'Weight';
                  latestVitalData = patientSummary.latestVitals[vitalTypeKey] as any;
                  break;
                case 'stepCount':
                  vitalTypeKey = 'Pedometer';
                  latestVitalData = patientSummary.latestVitals[vitalTypeKey] as any;
                  break;
                default:
                  vitalTypeKey = vital.key;
                  latestVitalData = patientSummary.latestVitals[vitalTypeKey] as any;
              }
              
              // Only render if we have vital data
              if (!latestVitalData || !latestVitalData.values || latestVitalData.values.length === 0) {
                return null;
              }

              // Use API-provided min/max values
              const min = latestVitalData.min;
              const max = latestVitalData.max;
              
              // Convert latestVitals format to VitalLineGraph format
              const formattedData = latestVitalData.values.map((reading: any) => {
                // Handle blood pressure specially - combine systolic and diastolic
                if (vital.key === 'bloodPressure') {
                  // For blood pressure, we need to get both systolic and diastolic
                  const systolicData = patientSummary.latestVitals['Systolic Blood Pressure'];
                  const diastolicData = patientSummary.latestVitals['Diastolic Blood Pressure'];
                  
                  // Find matching diastolic reading for this datetime
                  const matchingDiastolic = diastolicData?.values?.find((d: any) => d.datetime === reading.datetime);
                  
                  if (matchingDiastolic) {
                    return {
                      value: `${reading.value}/${matchingDiastolic.value}`,
                      date: reading.datetime,
                      timestamp: moment(reading.datetime).valueOf(),
                    };
                  }
                }
                
                return {
                  value: reading.value,
                  date: reading.datetime,
                  timestamp: moment(reading.datetime).valueOf(),
                };
              });

              return (
                <VitalLineGraph
                  key={vital.key}
                  title={vital.label.replace('\n', ' ')}
                  data={formattedData}
                  unit={vital.unit}
                  min={min}
                  max={max}
                  cMin={min} // Use same as min for now, or we can calculate based on medical standards
                  cMax={max} // Use same as max for now, or we can calculate based on medical standards
                />
              );
            })}
          </TextWrapper>
        )}

        {/* Encounter Minutes - Dynamic based on selected programs */}
        {(() => {
          // Check if patient has selected programs
          if (!patientSummary.programs?.selectedPrograms || patientSummary.programs.selectedPrograms.length === 0) {
            return null;
          }

          // Define program configurations with proper medical icons
          const programConfigs = {
            'RPM': {
              key: 'rpm',
              label: 'RPM',
              color: Colors.TealBlue,
              icon: 'monitor-heart',
              description: 'Remote Patient Monitoring',
              minutes: patientSummary.rpmMins || 0
            },
            'CCM': {
              key: 'ccm',
              label: 'CCM',
              color: Colors.BlueCrayola,
              icon: 'medical-services',
              description: 'Chronic Care Management',
              minutes: patientSummary.ccmMins || 0
            },
            'PCM': {
              key: 'pcm',
              label: 'PCM',
              color: Colors.PinkOrange,
              icon: 'supervisor-account',
              description: 'Principal Care Management',
              minutes: patientSummary.pcmMins || 0
            }
          };

          // Get active programs from patient's selected programs
          const activeProgramsData = patientSummary.programs.selectedPrograms
            .filter(program => program.programActivated)
            .map(program => {
              const config = programConfigs[program.programName as keyof typeof programConfigs];
              if (config) {
                return {
                  ...config,
                  minutes: program.mins || config.minutes || 0
                };
              }
              return null;
            })
            .filter(Boolean) as any[];

          // If no active programs, don't render the section
          if (activeProgramsData.length === 0) {
            return null;
          }

          // Calculate card width based on number of active programs
          const getCardWidth = () => {
            const count = activeProgramsData.length;
            if (count === 1) return '100%';
            if (count === 2) return '48%';
            return '31%'; // 3 programs
          };

          const cardWidth = getCardWidth();

          return (
            <TextWrapper style={styles.sectionCard}>
              <TextWrapper style={styles.sectionHeader}>
                <MaterialIcons name="schedule" size={24} color={Colors.TealBlue} />
                <Text
                  size={18}
                  bold
                  color={Colors.DarkJungleGreen}
                  marginLeft={8}
                  lineHeight={20}
                >
                  Encounter Minutes
                </Text>
                <TextWrapper style={styles.vitalsCountBadge}>
                  <Text size={12} bold color={Colors.White}>
                    {activeProgramsData.length}
                  </Text>
                </TextWrapper>
              </TextWrapper>
              <TextWrapper style={[
                styles.programMinutesGrid,
                activeProgramsData.length === 1 && { justifyContent: 'center' }
              ]}>
                {activeProgramsData.map((program) => (
                  <TextWrapper 
                    key={program.key} 
                    style={[
                      styles.cleanProgramCard,
                      { 
                        width: cardWidth,
                        marginRight: activeProgramsData.length > 1 ? spacing.sm : 0
                      }
                    ]}
                  >
                    <MaterialIcons
                      name={program.icon as any}
                      size={24}
                      color={program.color}
                    />
                    <Text
                      size={fontScale(14)}
                      bold
                      color={program.color}
                      marginTop={8}
                      lineHeight={fontScale(16)}
                      numberOfLines={1}
                    >
                      {getMinsSecs(program.minutes)}
                    </Text>
                    <Text
                      size={typography.caption.fontSize}
                      color={Colors.GrayBlue}
                      marginTop={4}
                      lineHeight={typography.caption.lineHeight}
                      numberOfLines={1}
                    >
                      {program.label}
                    </Text>
                  </TextWrapper>
                ))}
              </TextWrapper>
            </TextWrapper>
          );
        })()}

        {/* Data Collection */}
        <TextWrapper style={styles.sectionCard}>
          <TextWrapper style={styles.sectionHeader}>
            <MaterialIcons name="analytics" size={24} color={Colors.Orange} />
            <Text
              size={18}
              bold
              color={Colors.DarkJungleGreen}
              marginLeft={8}
              lineHeight={20}
            >
              Data Collection
            </Text>
          </TextWrapper>
          <TextWrapper style={styles.cleanInfoRow}>
            <MaterialIcons
              name="calendar-today"
              size={20}
              color={Colors.Orange}
            />
            <Text
              size={14}
              color={Colors.DarkJungleGreen}
              marginLeft={12}
              lineHeight={16}
            >
              Days Measured: {patientSummary.daysMeasured}
            </Text>
          </TextWrapper>
          <TextWrapper style={styles.cleanInfoRow}>
            <MaterialIcons name="group" size={20} color={Colors.TealBlue} />
            <Text
              size={14}
              color={Colors.DarkJungleGreen}
              marginLeft={12}
              lineHeight={16}
            >
              Group: {patientSummary.groupName}
            </Text>
          </TextWrapper>
        </TextWrapper>

        {/* Active Programs */}
        <TextWrapper style={styles.sectionCard}>
          <TextWrapper style={styles.sectionHeader}>
            <MaterialIcons name="checklist" size={24} color={Colors.TealBlue} />
            <Text
              size={18}
              bold
              color={Colors.DarkJungleGreen}
              marginLeft={8}
              lineHeight={20} 
            >
              Active Programs
            </Text>
          </TextWrapper>
          {patientSummary.programs &&
            patientSummary.programs.selectedPrograms &&
            patientSummary.programs.selectedPrograms.map((program, index) => (
              <TextWrapper key={index} style={styles.cleanProgramRow}>
                <TextWrapper style={styles.programRowLeft}>
                  <TextWrapper
                    style={[
                      styles.programIndicator,
                      {
                        backgroundColor: program.programActivated
                          ? Colors.ForestGreen
                          : Colors.GrayBlue,
                      },
                    ]}
                  >
                    <></>
                  </TextWrapper>
                  <Text
                    size={14}
                    bold
                    color={Colors.DarkJungleGreen}
                    lineHeight={16}
                    numberOfLines={2}
                  >
                    {program.programName}
                  </Text>
                </TextWrapper>
                <Text size={14} color={Colors.GrayBlue} lineHeight={16}>
                  {getMinsSecs(program.mins)}
                </Text>
              </TextWrapper>
            ))}
        </TextWrapper>

        {/* Chronic Conditions */}
        {patientSummary.chronicConditions &&
          patientSummary.chronicConditions.length > 0 && (
            <TextWrapper style={styles.sectionCard}>
              <TextWrapper style={styles.sectionHeader}>
                <MaterialIcons
                  name="medical-services"
                  size={24}
                  color={Colors.Orange}
                />
                <Text
                  size={18}
                  bold
                  color={Colors.DarkJungleGreen}
                  marginLeft={8}
                  lineHeight={20}
                >
                  Chronic Conditions
                </Text>
              </TextWrapper>
              {patientSummary.chronicConditions.map((condition, index) => (
                <TextWrapper key={index} style={styles.cleanInfoRow}>
                  <MaterialIcons name="circle" size={8} color={Colors.Orange} />
                  <TextWrapper style={styles.conditionInfo}>
                    <Text
                      size={14}
                      bold
                      color={Colors.DarkJungleGreen}
                      lineHeight={16}
                      numberOfLines={2}
                    >
                      {condition.chronicConditionName}
                    </Text>
                    <Text size={12} color={Colors.GrayBlue} lineHeight={16}>
                      ICD Code: {condition.icdCode}
                    </Text>
                  </TextWrapper>
                </TextWrapper>
              ))}
            </TextWrapper>
          )}

        {/* Care Team */}
        <TextWrapper style={styles.sectionCard}>
          <TextWrapper style={styles.sectionHeader}>
            <MaterialIcons name="group" size={24} color={Colors.TealBlue} />
            <Text
              size={18}
              bold
              color={Colors.DarkJungleGreen}
              marginLeft={8}
              lineHeight={20}
            >
              Care Team
            </Text>
          </TextWrapper>
          <TextWrapper style={styles.cleanInfoRow}>
            <MaterialIcons
              name="local-hospital"
              size={24}
              color={Colors.TealBlue}
            />
            <TextWrapper style={styles.careTeamInfo}>
              <Text
                size={14}
                bold
                color={Colors.DarkJungleGreen}
                lineHeight={16}
                numberOfLines={1}
              >
                Dr. {patientSummary.physicianName}
              </Text>
              <Text size={12} color={Colors.GrayBlue} lineHeight={14} numberOfLines={1}>
                {patientSummary.physicianEmail}
              </Text>
              <Text size={12} color={Colors.GrayBlue} lineHeight={14} numberOfLines={1}>
                {patientSummary.physicianPhone}
              </Text>
            </TextWrapper>
          </TextWrapper>

          <TextWrapper style={styles.cleanInfoRow}>
            <MaterialIcons name="person" size={24} color={Colors.BlueCrayola} />
            <TextWrapper style={styles.careTeamInfo}>
              <Text
                size={14}
                bold
                color={Colors.DarkJungleGreen}
                lineHeight={16}
                numberOfLines={1}
              >
                {patientSummary.clinician.firstName}{" "}
                {patientSummary.clinician.lastName}
              </Text>
              <Text size={12} color={Colors.GrayBlue} lineHeight={14} numberOfLines={1}>
                {patientSummary.clinician.email}
              </Text>
              <Text size={12} color={Colors.GrayBlue} lineHeight={14} numberOfLines={1}>
                {patientSummary.clinician.phoneNumber}
              </Text>
            </TextWrapper>
          </TextWrapper>
        </TextWrapper>

        {/* Assigned Devicec*/}
        {patientSummary.assignedDevices &&
          patientSummary.assignedDevices.length > 0 && (
            <TextWrapper style={styles.sectionCard}>
              <TextWrapper style={styles.sectionHeader}>
                <MaterialIcons
                  name="devices-other"
                  size={24}
                  color={Colors.TealBlue}
                />
                <Text
                  size={18}
                  bold
                  color={Colors.DarkJungleGreen}
                  marginLeft={8}
                  lineHeight={20}
                >
                  Assigned Device(s)
                </Text>
              </TextWrapper>
              {patientSummary.assignedDevices.map((condition, index) => (
                <TextWrapper key={index} style={styles.cleanInfoRow}>
                  <MaterialIcons
                    name={getDeviceIcon(condition.deviceType)}
                    size={16}
                    color={Colors.Orange}
                  />
                  <TextWrapper style={styles.conditionInfo}>
                    <Text
                      size={14}
                      bold
                      color={Colors.DarkJungleGreen}
                      lineHeight={16}
                      numberOfLines={1}
                    >
                      {condition.deviceType}
                    </Text>
                    <Text size={12} color={Colors.GrayBlue} lineHeight={16} numberOfLines={1}>
                      Device ID: {condition.imei}
                    </Text>
                  </TextWrapper>
                </TextWrapper>
              ))}
            </TextWrapper>
          )}
      </ScrollView>
    );
  };

  return (
    <Container style={styles.container}>
      <NavigationHeader
        title={
          patientSummary != null
            ? patientSummary.firstName + " " + patientSummary.lastName
            : "Patient Summary"
        }
        showBackButton={true}
        showLogo={false}
        showRightLogo={true}
      />
      <Loader modalVisible={loader} />

      <TextWrapper style={styles.contentContainer}>
        {renderPatientSummary()}
      </TextWrapper>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8fafc",
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: spacing.md,
    paddingTop: spacing.sm,
  },
  contentScroll: {
    flex: 1,
    paddingBottom: spacing.lg,
  },
  emptyCard: {
    flex: 1,
    backgroundColor: Colors.White,
    borderRadius: cardDimensions.borderRadius,
    padding: spacing.xxl,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionCard: {
    backgroundColor: Colors.White,
    borderRadius: cardDimensions.borderRadius,
    padding: cardDimensions.padding,
    marginBottom: spacing.md,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#e5e7eb',

    // iOS Shadow
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,

    // Android Shadow
    elevation: 3,
  },

  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.lg,
  },
  patientHeaderCard: {
    backgroundColor: Colors.White,
    borderRadius: cardDimensions.borderRadius,
    padding: cardDimensions.padding,
    marginBottom: spacing.md,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  patientHeaderContent: {
    flexDirection: "row",
    alignItems: "center",
    flexWrap: 'wrap',
  },
  patientAvatar: {
    width: iconSizes.xl + 28,
    height: iconSizes.xl + 28,
    borderRadius: (iconSizes.xl + 28) / 2,
    backgroundColor: Colors.TealBlue,
    alignItems: "center",
    justifyContent: "center",
    marginRight: spacing.md,
    flexShrink: 0,
  },
  patientInfo: {
    flex: 1,
    paddingRight: spacing.sm,
  },
  patientDetailRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: spacing.xs,
    flexWrap: 'wrap',
  },
  vitalsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    rowGap: spacing.md,
    columnGap: spacing.xs,
  },
  vitalCard: {
    backgroundColor: Colors.White,
    borderRadius: cardDimensions.borderRadius,
    padding: spacing.md,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    borderColor: "#e2e8f0",
    minHeight: 110,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  programMinutesGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.md,
    flexWrap: "wrap",
    gap: spacing.xs,
  },
  cleanProgramCard: {
    flex: 1,
    backgroundColor: "#f8fafc",
    borderRadius: cardDimensions.borderRadius,
    padding: cardDimensions.padding,
    marginHorizontal: spacing.xs,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#d1d5db",
    minHeight: cardDimensions.minHeight,
    minWidth: 100,
  },
  cleanProgramRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: spacing.sm,
  },
  cleanInfoRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: spacing.sm,
  },
  programRowLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  programIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: spacing.sm,
  },
  conditionInfo: {
    marginLeft: spacing.sm,
    flex: 1,
  },
  careTeamInfo: {
    marginLeft: spacing.sm,
    flex: 1,
    gap: spacing.sm,
  },
  
  // Modern Vitals Styles
  vitalsSection: {
    marginBottom: spacing.md,
  },
  vitalsSectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: Colors.White,
    borderRadius: cardDimensions.borderRadius,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  vitalsCountBadge: {
    backgroundColor: Colors.RedNeonFuchsia,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginLeft: 'auto',
  },
  vitalsScrollView: {
    marginLeft: -spacing.md,
    marginRight: -spacing.md,
  },
  vitalsScrollContainer: {
    paddingHorizontal: spacing.md,
    paddingRight: spacing.lg,
  },
  vitalsStaticContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
  },
  vitalsStaticContainerCentered: {
    justifyContent: 'center',
    alignItems: 'center',
    gap: spacing.md,
  },
  modernVitalCard: {
    minHeight: 110,
    maxHeight: 140,
    borderRadius: 16,
    padding: spacing.md,
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  vitalCardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.xs,
  },
  vitalLabelContainer: {
    flex: 1,
    marginLeft: 8,
  },
  vitalCardValueContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    justifyContent: 'flex-start',
    marginVertical: spacing.xs,
    flexWrap: 'wrap',
  },
  vitalCardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    opacity: 0.7,
    marginTop: spacing.xs,
  },
});

export default SummaryScreen;
