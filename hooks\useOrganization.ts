import { useSelector } from 'react-redux';

interface OrganizationDetails {
  id: string;
  name: string;
}

interface OrganizationState {
  orgId: string | null;
  orgDetails: OrganizationDetails | null;
}

interface RootState {
  currentOrgIdReducer?: OrganizationState;
}

/**
 * Custom hook to get organization details from Redux store
 * @returns Object containing organization ID, name, and details
 */
export const useOrganization = () => {
  const organizationState = useSelector((state: RootState) => state?.currentOrgIdReducer);
  
  const orgId = organizationState?.orgId || null;
  const orgDetails = organizationState?.orgDetails || null;
  const orgName = orgDetails?.name || 'WatchRx';
  
  return {
    orgId,
    orgName,
    orgDetails,
    hasOrganization: !!orgDetails,
  };
};

export default useOrganization; 