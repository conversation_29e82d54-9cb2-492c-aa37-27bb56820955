import {
  FETCH_DTASKS_LIST,
  FETCH_DTASKS_LIST_FAILURE,
  FETCH_DTASKS_LIST_SUCCESS,
} from '../actions/type';

const initialState = {
  data: [],
  isFetching: false,
  msg: '',
};

export default function futureTasksListReducer(
  state = initialState,
  action: {type: any; data: any; msg: any},
) {
  switch (action.type) {
    case FETCH_DTASKS_LIST:
      return {...state, isFetching: true, data: ''};
    case FETCH_DTASKS_LIST_SUCCESS:
      return {
        ...state,
        isFetching: false,
        data: action.data,
      };
    case FETCH_DTASKS_LIST_FAILURE:
      return {
        ...state,
        isFetching: false,
        msg: action.msg,
      };

    default:
      return state;
  }
}
