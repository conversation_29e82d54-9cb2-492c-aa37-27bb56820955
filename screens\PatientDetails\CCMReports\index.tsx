import React, {useRef, memo, useCallback, useEffect} from 'react';
import {StyleSheet, View, TouchableOpacity, Animated, Easing, Dimensions} from 'react-native';
import Container from '@/components/Layout/Container';
import Text from '@/components/Text';
import { FontAwesome5, MaterialCommunityIcons, Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { useSelector } from 'react-redux';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface CCMFeature {
  id: number;
  icon: {
    family: string;
    name: string;
    size: number;
  };
  title: string;
  subtitle: string;
  route: string;
  color: string;
  gradientColors: string[];
  priority: string;
  animation: string;
}

interface CcmReportsProps {
  patientId?: string;
  directPatientId?: string;
  navigation?: any;
}

// Enhanced CCM dashboard grid item component matching PatientDetails style
const CCMDashboardGridItem = memo(({ item, patientId, style }: { item: CCMFeature; patientId: string; style?: any }) => {
  const router = useRouter();
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.timing(scaleAnim, {
      toValue: 0.95,
      duration: 150,
      useNativeDriver: true,
      easing: Easing.bezier(0.4, 0.0, 0.2, 1),
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 4,
      tension: 100,
      useNativeDriver: true,
    }).start();
  };

  const handlePress = () => {
    // Navigate to CCM placeholder screen for all features
    try {
      // Map titles to proper program type IDs
      const titleToTypeMap: Record<string, string> = {
        'RPM': 'rpm',
        'CCM': 'ccm',
        'PCM': 'pcm',
        'RTM': 'rtm',
        'Home Health': 'homehealth',
        'Hospice': 'hospice'
      };
      
      router.push({
        pathname: '/patient/ccm/placeholder' as any,
        params: {
          patientId: patientId,
          title: `${item.title} - ${item.subtitle}`,
          type: titleToTypeMap[item.title] || item.title.toLowerCase(),
          tileIndex: item.id
        }
      });
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  const renderIcon = () => {
    const iconProps = {
      size: 32,
      color: "#FFFFFF",
    };

    switch (item.icon.family) {
      case 'MaterialCommunityIcons':
        return <MaterialCommunityIcons name={item.icon.name as any} {...iconProps} />;
      case 'FontAwesome5':
        return <FontAwesome5 name={item.icon.name as any} {...iconProps} />;
      case 'Ionicons':
        return <Ionicons name={item.icon.name as any} {...iconProps} />;
      default:
        return <MaterialCommunityIcons name="hospital-building" {...iconProps} />;
    }
  };

  return (
    <TouchableOpacity
      activeOpacity={1}
      onPress={handlePress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      style={style}
    >
      <Animated.View
        style={[
          styles.gridItem,
          {
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <LinearGradient
          colors={[item.gradientColors[0], item.gradientColors[0]]}
          style={styles.gridItemGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {/* Icon container with enhanced styling */}
          <View style={styles.gridIconContainer}>
            <View style={styles.iconBackground}>
              {renderIcon()}
            </View>
          </View>

          {/* Content with improved typography */}
          <View style={styles.gridItemContent}>
            <Text style={styles.gridItemTitle} numberOfLines={1}>
              {item.title}
            </Text>
            <Text style={styles.gridItemSubtitle} numberOfLines={1}>
              {item.subtitle}
            </Text>
          </View>
        </LinearGradient>
      </Animated.View>
    </TouchableOpacity>
  );
});

/**
 * CCM Reports Screen
 * Shows care plan details for a specific patient
 */
const CcmReports = (props: CcmReportsProps) => {
  // Enhanced CCM features with gradient colors matching main dashboard behavior  
  const FEATURES: CCMFeature[] = [
    {
      id: 1,
      icon: {
        family: 'MaterialCommunityIcons',
        name: 'heart-pulse',
        size: 28
      },
      title: 'RPM',
      subtitle: 'Remote Patient Monitoring',
      route: '/patient/ccm/placeholder',
      color: '#4ECDC4',
      gradientColors: ["#4ECDC4", "#44A08D"], // Primary monitoring - teal like main Vitals Report
      priority: "high",
      animation: "pulse"
    },
    {
      id: 2,
      icon: {
        family: 'FontAwesome5',
        name: 'notes-medical',
        size: 28
      },
      title: 'CCM',
      subtitle: 'Chronic Care Management',
      route: '/patient/ccm/placeholder',
      color: '#9381FF',
      gradientColors: ["#9381FF", "#8B5CF6"], // Clinical documentation - purple like Care Team Notes
      priority: "high",
      animation: "scale"
    },
    {
      id: 3,
      icon: {
        family: 'MaterialCommunityIcons',
        name: 'account-heart',
        size: 28
      },
      title: 'PCM',
      subtitle: 'Principal Care Management',
      route: '/patient/ccm/placeholder',
      color: '#7A77FF',
      gradientColors: ["#7A77FF", "#6366F1"], // Care coordination - blue-purple like Medications
      priority: "medium",
      animation: "bounce"
    },
    {
      id: 4,
      icon: {
        family: 'MaterialCommunityIcons',
        name: 'chart-line',
        size: 28
      },
      title: 'RTM',
      subtitle: 'Remote Therapeutic Monitoring',
      route: '/patient/ccm/placeholder',
      color: '#36B5E0',
      gradientColors: ["#36B5E0", "#3490DC"], // Monitoring alerts - blue like main Alerts
      priority: "medium",
      animation: "rotate"
    },
    {
      id: 5,
      icon: {
        family: 'FontAwesome5',
        name: 'home',
        size: 28
      },
      title: 'Home Health',
      subtitle: 'In-home care services',
      route: '/patient/ccm/placeholder',
      color: '#5FB49C',
      gradientColors: ["#5FB49C", "#10B981"], // Health services - green like Check In
      priority: "low",
      animation: "pulse"
    },
    {
      id: 6,
      icon: {
        family: 'MaterialCommunityIcons',
        name: 'hand-heart',
        size: 28
      },
      title: 'Hospice',
      subtitle: 'End-of-life care',
      route: '/patient/ccm/placeholder',
      color: '#FF9F1C',
      gradientColors: ["#FF9F1C", "#F59E0B"], // Specialized care - orange like Custom Reminders
      priority: "low",
      animation: "scale"
    },
  ];

  const patientId = useSelector(
    (state: any) => state?.currentPatientIdReducer?.patientId,
  );

  // Animation references for the grid items
  const gridAnimationRefs = FEATURES.map(() => useRef(new Animated.Value(0)).current);
  
  // Animation timing function for staggered grid animation
  useEffect(() => {
    const animations = gridAnimationRefs.map((anim, index) => {
      return Animated.timing(anim, {
        toValue: 1,
        duration: 300,
        delay: 50 + index * 30,
        useNativeDriver: true,
      });
    });
    
    Animated.stagger(30, animations).start();
  }, []);

  // Get the effective patient ID
  const effectivePatientId = props.patientId || props.directPatientId || String(patientId);



  // Fixed grid layout that ensures all 6 tiles are visible
  const renderDashboardGrid = () => {
    const pairs = [];
    for (let i = 0; i < FEATURES.length; i += 2) {
      pairs.push(FEATURES.slice(i, i + 2));
    }

    return pairs.map((pair, rowIndex) => (
      <View key={rowIndex} style={styles.gridRow}>
        {pair.map((item, itemIndex) => (
          <CCMDashboardGridItem
            key={item.id}
            item={item}
            patientId={effectivePatientId}
            style={styles.gridItemContainer}
          />
        ))}
        {/* If odd number of items, add spacer for last row */}
        {pair.length === 1 && <View style={styles.gridItemContainer} />}
      </View>
    ));
  };

  return (
    <Container style={styles.container}>
      <View style={styles.featuresContainer}>
        {renderDashboardGrid()}
      </View>
    </Container>
  );
};

export default CcmReports;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FAFBFC",
  },
  featuresContainer: {
    paddingHorizontal: 16,
    marginTop: 12,
    paddingBottom: 16,
  },
  gridRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  gridItemContainer: {
    flex: 1,
    marginHorizontal: 4,
    height: 130,
  },
  gridItem: {
    width: "100%",
    height: "100%",
    borderRadius: 12,
    backgroundColor: "#FFFFFF",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    overflow: "hidden",
  },
  gridItemGradient: {
    flex: 1,
    padding: 12,
    justifyContent: "space-between",
  },
  gridIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "flex-start",
  },
  gridItemContent: {
    marginTop: 8,
  },
  gridItemTitle: {
    fontSize: 15,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 2,
    letterSpacing: -0.1,
    lineHeight: 17,
  },
  gridItemSubtitle: {
    fontSize: 11,
    color: "rgba(255, 255, 255, 0.85)",
    fontWeight: "500",
    lineHeight: 13,
  },
  iconBackground: {
    backgroundColor: "rgba(255, 255, 255, 0.25)",
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: "center",
    alignItems: "center",
  },
});
