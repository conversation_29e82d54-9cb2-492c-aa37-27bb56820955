import {
  FETCH_PATIENTS_LIST,
  FETCH_PATIENTS_LIST_FAILURE,
  FETCH_PATIENTS_LIST_SUCCESS,
  FETCH_MORE_PATIENTS_LIST,
  FETCH_MORE_PATIENTS_LIST_SUCCESS,
  FETCH_MORE_PATIENTS_LIST_FAILURE,
} from '../actions/type';

const initialState = {
  data: [],
  isFetching: false,
  isLoadingMore: false,
  msg: '',
  orgId: null, // Track which organization this data belongs to
  totalCount: 0,
  currentPage: -1, // Start with -1 so first page (0) is properly detected
  pageSize: 10,
  hasMoreData: true,
};

export default function patientsListReducer(
  state = initialState,
  action: {
    type: any; 
    data: any; 
    msg: any; 
    orgId?: any;
    totalCount?: number;
    pageNumber?: number;
    pageSize?: number;
    isLoadingMore?: boolean;
  },
) {
  switch (action.type) {
    case FETCH_PATIENTS_LIST:
      return {
        ...state, 
        isFetching: true,
        isLoadingMore: false,
        msg: '',
      };
      
    case FETCH_MORE_PATIENTS_LIST:
      return {
        ...state, 
        isFetching: false,
        isLoadingMore: true,
        msg: '',
      };
      
    case FETCH_PATIENTS_LIST_SUCCESS:
      const newData = action.data || [];
      const totalCount = action.totalCount || newData.length;
      const currentPage = action.pageNumber !== undefined ? action.pageNumber : 0;
      const pageSize = action.pageSize || 10;
      
      // Calculate if there's more data to load (0-based pagination)
      const totalPagesCount = Math.ceil(totalCount / pageSize);
      const hasMoreData = (currentPage + 1) < totalPagesCount;
      
      console.log('Reducer - Loading patients with pagination (0-based):', {
        patientsReceived: newData.length,
        totalCount,
        currentPage,
        pageSize,
        totalPagesCount,
        hasMoreData,
        isReset: action.isLoadingMore !== true,
      });
      
      return {
        ...state,
        isFetching: false,
        isLoadingMore: false,
        data: action.isLoadingMore ? [...state.data, ...newData] : newData, // Append if loading more, replace if initial load
        orgId: action.orgId || state.orgId,
        totalCount: totalCount,
        currentPage: currentPage,
        pageSize: pageSize,
        hasMoreData: hasMoreData,
        msg: '',
      };
      
    case FETCH_MORE_PATIENTS_LIST_SUCCESS:
      const moreData = action.data || [];
      const newTotalCount = action.totalCount || state.totalCount;
      const newCurrentPage = action.pageNumber || state.currentPage + 1;
      const currentPageSize = action.pageSize || state.pageSize;
      
      // Calculate if there's more data to load (0-based pagination)
      const totalPagesForMore = Math.ceil(newTotalCount / currentPageSize);
      const stillHasMoreData = (newCurrentPage + 1) < totalPagesForMore;
      
      console.log('Reducer - Loading more patients:', {
        morePatientsReceived: moreData.length,
        newTotalCount,
        newCurrentPage,
        totalPagesForMore,
        stillHasMoreData,
        existingDataLength: state.data.length,
      });
      
      return {
        ...state,
        isFetching: false,
        isLoadingMore: false,
        data: [...state.data, ...moreData], // Always append for load more
        orgId: action.orgId || state.orgId,
        totalCount: newTotalCount,
        currentPage: newCurrentPage,
        pageSize: currentPageSize,
        hasMoreData: stillHasMoreData,
        msg: '',
      };
      
    case FETCH_PATIENTS_LIST_FAILURE:
    case FETCH_MORE_PATIENTS_LIST_FAILURE:
      return {
        ...state,
        isFetching: false,
        isLoadingMore: false,
        msg: action.msg,
      };

    default:
      return state;
  }
}
