import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { useOrientation } from '@/hooks/useOrientation';
import { isTablet, isLargeTablet } from '@/utils/responsive';

export interface NavigationItem {
  id: string;
  label: string;
  icon: string;
  route?: string;
  badge?: number;
  children?: NavigationItem[];
}

interface TabletNavigationState {
  sidebarVisible: boolean;
  sidebarCollapsed: boolean;
  masterDetailMode: boolean;
  activeNavigationId?: string;
  selectedMasterId?: string;
  navigationItems: NavigationItem[];
  breadcrumbs: string[];
}

interface TabletNavigationContextType extends TabletNavigationState {
  toggleSidebar: () => void;
  setSidebarVisible: (visible: boolean) => void;
  toggleSidebarCollapse: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  setMasterDetailMode: (enabled: boolean) => void;
  setActiveNavigation: (id: string) => void;
  setSelectedMaster: (id: string) => void;
  setNavigationItems: (items: NavigationItem[]) => void;
  addBreadcrumb: (label: string) => void;
  removeBreadcrumb: (index: number) => void;
  clearBreadcrumbs: () => void;
  navigateToItem: (item: NavigationItem) => void;
}

type TabletNavigationAction =
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'SET_SIDEBAR_VISIBLE'; payload: boolean }
  | { type: 'TOGGLE_SIDEBAR_COLLAPSE' }
  | { type: 'SET_SIDEBAR_COLLAPSED'; payload: boolean }
  | { type: 'SET_MASTER_DETAIL_MODE'; payload: boolean }
  | { type: 'SET_ACTIVE_NAVIGATION'; payload: string }
  | { type: 'SET_SELECTED_MASTER'; payload: string }
  | { type: 'SET_NAVIGATION_ITEMS'; payload: NavigationItem[] }
  | { type: 'ADD_BREADCRUMB'; payload: string }
  | { type: 'REMOVE_BREADCRUMB'; payload: number }
  | { type: 'CLEAR_BREADCRUMBS' }
  | { type: 'NAVIGATE_TO_ITEM'; payload: NavigationItem };

const initialState: TabletNavigationState = {
  sidebarVisible: false,
  sidebarCollapsed: false,
  masterDetailMode: false,
  navigationItems: [],
  breadcrumbs: [],
};

const tabletNavigationReducer = (
  state: TabletNavigationState,
  action: TabletNavigationAction
): TabletNavigationState => {
  switch (action.type) {
    case 'TOGGLE_SIDEBAR':
      return {
        ...state,
        sidebarVisible: !state.sidebarVisible,
      };

    case 'SET_SIDEBAR_VISIBLE':
      return {
        ...state,
        sidebarVisible: action.payload,
      };

    case 'TOGGLE_SIDEBAR_COLLAPSE':
      return {
        ...state,
        sidebarCollapsed: !state.sidebarCollapsed,
      };

    case 'SET_SIDEBAR_COLLAPSED':
      return {
        ...state,
        sidebarCollapsed: action.payload,
      };

    case 'SET_MASTER_DETAIL_MODE':
      return {
        ...state,
        masterDetailMode: action.payload,
      };

    case 'SET_ACTIVE_NAVIGATION':
      return {
        ...state,
        activeNavigationId: action.payload,
      };

    case 'SET_SELECTED_MASTER':
      return {
        ...state,
        selectedMasterId: action.payload,
      };

    case 'SET_NAVIGATION_ITEMS':
      return {
        ...state,
        navigationItems: action.payload,
      };

    case 'ADD_BREADCRUMB':
      return {
        ...state,
        breadcrumbs: [...state.breadcrumbs, action.payload],
      };

    case 'REMOVE_BREADCRUMB':
      return {
        ...state,
        breadcrumbs: state.breadcrumbs.slice(0, action.payload),
      };

    case 'CLEAR_BREADCRUMBS':
      return {
        ...state,
        breadcrumbs: [],
      };

    case 'NAVIGATE_TO_ITEM':
      return {
        ...state,
        activeNavigationId: action.payload.id,
        breadcrumbs: [...state.breadcrumbs, action.payload.label],
      };

    default:
      return state;
  }
};

const TabletNavigationContext = createContext<TabletNavigationContextType | undefined>(undefined);

interface TabletNavigationProviderProps {
  children: ReactNode;
  defaultNavigationItems?: NavigationItem[];
}

export const TabletNavigationProvider: React.FC<TabletNavigationProviderProps> = ({
  children,
  defaultNavigationItems = [],
}) => {
  const [state, dispatch] = useReducer(tabletNavigationReducer, {
    ...initialState,
    navigationItems: defaultNavigationItems,
  });

  const { orientation } = useOrientation();

  // Auto-adjust navigation based on device capabilities
  React.useEffect(() => {
    const shouldShowSidebar = isTablet() && orientation === 'landscape';
    const shouldUseMasterDetail = isLargeTablet();

    dispatch({ type: 'SET_SIDEBAR_VISIBLE', payload: shouldShowSidebar });
    dispatch({ type: 'SET_MASTER_DETAIL_MODE', payload: shouldUseMasterDetail });
  }, [orientation]);

  const contextValue: TabletNavigationContextType = {
    ...state,
    toggleSidebar: () => dispatch({ type: 'TOGGLE_SIDEBAR' }),
    setSidebarVisible: (visible: boolean) => 
      dispatch({ type: 'SET_SIDEBAR_VISIBLE', payload: visible }),
    toggleSidebarCollapse: () => dispatch({ type: 'TOGGLE_SIDEBAR_COLLAPSE' }),
    setSidebarCollapsed: (collapsed: boolean) => 
      dispatch({ type: 'SET_SIDEBAR_COLLAPSED', payload: collapsed }),
    setMasterDetailMode: (enabled: boolean) => 
      dispatch({ type: 'SET_MASTER_DETAIL_MODE', payload: enabled }),
    setActiveNavigation: (id: string) => 
      dispatch({ type: 'SET_ACTIVE_NAVIGATION', payload: id }),
    setSelectedMaster: (id: string) => 
      dispatch({ type: 'SET_SELECTED_MASTER', payload: id }),
    setNavigationItems: (items: NavigationItem[]) => 
      dispatch({ type: 'SET_NAVIGATION_ITEMS', payload: items }),
    addBreadcrumb: (label: string) => 
      dispatch({ type: 'ADD_BREADCRUMB', payload: label }),
    removeBreadcrumb: (index: number) => 
      dispatch({ type: 'REMOVE_BREADCRUMB', payload: index }),
    clearBreadcrumbs: () => dispatch({ type: 'CLEAR_BREADCRUMBS' }),
    navigateToItem: (item: NavigationItem) => 
      dispatch({ type: 'NAVIGATE_TO_ITEM', payload: item }),
  };

  return (
    <TabletNavigationContext.Provider value={contextValue}>
      {children}
    </TabletNavigationContext.Provider>
  );
};

export const useTabletNavigation = (): TabletNavigationContextType => {
  const context = useContext(TabletNavigationContext);
  if (!context) {
    throw new Error('useTabletNavigation must be used within a TabletNavigationProvider');
  }
  return context;
};

/**
 * Hook for managing tablet-specific navigation patterns
 */
export const useTabletNavigationPatterns = () => {
  const navigation = useTabletNavigation();
  const { orientation, isLandscape } = useOrientation();

  const getNavigationPattern = () => {
    if (isLargeTablet()) {
      return 'master-detail';
    } else if (isTablet() && isLandscape) {
      return 'sidebar';
    } else if (isTablet()) {
      return 'modal-sidebar';
    } else {
      return 'standard';
    }
  };

  const shouldShowSidebar = () => {
    const pattern = getNavigationPattern();
    return pattern === 'sidebar' || pattern === 'master-detail';
  };

  const shouldUseMasterDetail = () => {
    return getNavigationPattern() === 'master-detail';
  };

  const shouldUseModalSidebar = () => {
    return getNavigationPattern() === 'modal-sidebar';
  };

  return {
    ...navigation,
    navigationPattern: getNavigationPattern(),
    shouldShowSidebar: shouldShowSidebar(),
    shouldUseMasterDetail: shouldUseMasterDetail(),
    shouldUseModalSidebar: shouldUseModalSidebar(),
    orientation,
    isLandscape,
  };
};

export default TabletNavigationContext;