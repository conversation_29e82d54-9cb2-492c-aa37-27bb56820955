const fs = require('fs');
const path = require('path');

// Path to the gradle.properties file
const gradlePropertiesPath = path.join(__dirname, '..', 'android', 'gradle.properties');

// Android VoIP files content
const ANDROID_FILES = {
  MainActivity: `package com.watchrxhealth.caregiver

import expo.modules.splashscreen.SplashScreenManager
import android.Manifest
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.widget.Toast
import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.fabricEnabled
import com.facebook.react.defaults.DefaultReactActivityDelegate
import com.twiliovoicereactnative.VoiceActivityProxy
import expo.modules.ReactActivityDelegateWrapper

class MainActivity : ReactActivity() {

  private val activityProxy = VoiceActivityProxy(
        this
    ) { permission: String ->
        if (Manifest.permission.RECORD_AUDIO == permission) {
            Toast.makeText(
                this@MainActivity,
                "Microphone permissions needed. Please allow in your application settings.",
                Toast.LENGTH_LONG
            ).show()
        } else if ((Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) && (Manifest.permission.BLUETOOTH_CONNECT == permission)) {
            Toast.makeText(
                this@MainActivity,
                "Bluetooth permissions needed. Please allow in your application settings.",
                Toast.LENGTH_LONG
            ).show()
        } else if ((Build.VERSION.SDK_INT > Build.VERSION_CODES.S_V2) && (Manifest.permission.POST_NOTIFICATIONS == permission)) {
            Toast.makeText(
                this@MainActivity,
                "Notification permissions needed. Please allow in your application settings.",
                Toast.LENGTH_LONG
            ).show()
        }
    }

  override fun onCreate(savedInstanceState: Bundle?) {
    // Set the theme to AppTheme BEFORE onCreate to support
    // coloring the background, status bar, and navigation bar.
    // This is required for expo-splash-screen.
    // setTheme(R.style.AppTheme);
    // @generated begin expo-splashscreen - expo prebuild (DO NOT MODIFY) sync-f3ff59a738c56c9a6119210cb55f0b613eb8b6af
    SplashScreenManager.registerOnActivity(this)
    // @generated end expo-splashscreen
    super.onCreate(savedInstanceState)
    activityProxy.onCreate(savedInstanceState)
  }

  override fun onDestroy() {
    activityProxy.onDestroy()
    super.onDestroy()
  }

  override fun onNewIntent(intent: Intent?) {
    super.onNewIntent(intent)
    activityProxy.onNewIntent(intent)
  }

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */
  override fun getMainComponentName(): String = "main"

  /**
   * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
   * which allows you to enable New Architecture with a single boolean flags [fabricEnabled]
   */
  override fun createReactActivityDelegate(): ReactActivityDelegate {
    return ReactActivityDelegateWrapper(
          this,
          BuildConfig.IS_NEW_ARCHITECTURE_ENABLED,
          object : DefaultReactActivityDelegate(
              this,
              mainComponentName,
              fabricEnabled
          ){})
  }

  /**
    * Align the back button behavior with Android S
    * where moving root activities to background instead of finishing activities.
    * @see <a href="https://developer.android.com/reference/android/app/Activity#onBackPressed()">onBackPressed</a>
    */
  override fun invokeDefaultOnBackPressed() {
      if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.R) {
          if (!moveTaskToBack(false)) {
              // For non-root activities, use the default implementation to finish them.
              super.invokeDefaultOnBackPressed()
          }
          return
      }

      // Use the default back button implementation on Android S
      // because it's doing more than [Activity.moveTaskToBack] in fact.
      super.invokeDefaultOnBackPressed()
  }
}`,

  MainApplication: `package com.watchrxhealth.caregiver

import android.app.Application
import android.content.res.Configuration
import com.facebook.react.PackageList
import com.facebook.react.ReactApplication
import com.facebook.react.ReactNativeHost
import com.facebook.react.ReactPackage
import com.facebook.react.ReactHost
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.load
import com.facebook.react.defaults.DefaultReactNativeHost
import com.facebook.react.soloader.OpenSourceMergedSoMapping
import com.facebook.soloader.SoLoader
import com.twiliovoicereactnative.VoiceApplicationProxy
import expo.modules.ApplicationLifecycleDispatcher
import expo.modules.ReactNativeHostWrapper

class MainApplication : Application(), ReactApplication {

  private val mReactNativeHost = MainReactNativeHost(this)
  private val voiceApplicationProxy = VoiceApplicationProxy(mReactNativeHost)

  override val reactNativeHost: ReactNativeHost = ReactNativeHostWrapper(
        this,
        mReactNativeHost
  )

  override val reactHost: ReactHost
    get() = ReactNativeHostWrapper.createReactHost(applicationContext, reactNativeHost)

  override fun onCreate() {
    super.onCreate()
    voiceApplicationProxy.onCreate()
    SoLoader.init(this, OpenSourceMergedSoMapping)
    if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
      // If you opted-in for the New Architecture, we load the native entry point for this app.
      load()
    }
    ApplicationLifecycleDispatcher.onApplicationCreate(this)
  }

  override fun onConfigurationChanged(newConfig: Configuration) {
    super.onConfigurationChanged(newConfig)
    ApplicationLifecycleDispatcher.onConfigurationChanged(this, newConfig)
  }

  override fun onTerminate() {
    voiceApplicationProxy.onTerminate()
    super.onTerminate()
  }
}`,

  MainReactNativeHost: `package com.watchrxhealth.caregiver

import android.app.Application
import com.facebook.react.PackageList
import com.facebook.react.ReactPackage
import com.twiliovoicereactnative.VoiceApplicationProxy

class MainReactNativeHost(application: Application) : VoiceApplicationProxy.VoiceReactNativeHost(application) {
    
    override fun getUseDeveloperSupport(): Boolean = BuildConfig.DEBUG

    override fun getPackages(): List<ReactPackage> {
        val packages = PackageList(this).packages
        // Packages that cannot be autolinked yet can be added manually here
        // packages.add(MyReactNativePackage())
        return packages
    }

    override fun getJSMainModuleName(): String = ".expo/.virtual-metro-entry"
}`
};

// Function to ensure directory exists
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`Created directory: ${dirPath}`);
  }
}

// Function to verify and update Android VoIP files
function verifyAndroidVoIPFiles() {
  console.log('Verifying Android VoIP files...');
  
  const androidPackagePath = path.join(__dirname, '..', 'android', 'app', 'src', 'main', 'java', 'com', 'watchrxhealth', 'caregiver');
  
  // Ensure the package directory exists
  ensureDirectoryExists(androidPackagePath);
  
  const files = [
    { name: 'MainActivity.kt', content: ANDROID_FILES.MainActivity },
    { name: 'MainApplication.kt', content: ANDROID_FILES.MainApplication },
    { name: 'MainReactNativeHost.kt', content: ANDROID_FILES.MainReactNativeHost }
  ];
  
  files.forEach(file => {
    const filePath = path.join(androidPackagePath, file.name);
    
    if (fs.existsSync(filePath)) {
      const existingContent = fs.readFileSync(filePath, 'utf8');
      
      // Normalize whitespace for comparison (remove extra spaces, normalize line endings)
      const normalizeContent = (content) => content.replace(/\s+/g, ' ').trim();
      
      if (normalizeContent(existingContent) !== normalizeContent(file.content)) {
        console.log(`Updating ${file.name} with VoIP configuration...`);
        fs.writeFileSync(filePath, file.content, 'utf8');
        console.log(`✅ Successfully updated ${file.name}`);
      } else {
        console.log(`✅ ${file.name} already has correct VoIP configuration`);
      }
    } else {
      console.log(`Creating ${file.name} with VoIP configuration...`);
      fs.writeFileSync(filePath, file.content, 'utf8');
      console.log(`✅ Successfully created ${file.name}`);
    }
  });
}

// // Function to check if Jetifier is enabled in the project
// function verifyJetifier() {
//   console.log('Verifying Jetifier configuration...');
  
//   // Check main gradle.properties
//   if (fs.existsSync(gradlePropertiesPath)) {
//     const gradleContent = fs.readFileSync(gradlePropertiesPath, 'utf8');
//     if (!gradleContent.includes('android.enableJetifier=true')) {
//       console.log('Adding android.enableJetifier=true to main gradle.properties');
//       fs.appendFileSync(gradlePropertiesPath, '\nandroid.enableJetifier=true\n', 'utf8');
//       console.log('IMPORTANT: Jetifier was not enabled before. It has been enabled now.');
//     } else {
//       console.log('✅ android.enableJetifier=true already exists in main gradle.properties');
//     }
    
//     // Make sure useAndroidX is also enabled
//     if (!gradleContent.includes('android.useAndroidX=true')) {
//       console.log('Adding android.useAndroidX=true to main gradle.properties');
//       fs.appendFileSync(gradlePropertiesPath, '\nandroid.useAndroidX=true\n', 'utf8');
//     } else {
//       console.log('✅ android.useAndroidX=true already exists in main gradle.properties');
//     }
//   } else {
//     console.log('❌ Main gradle.properties not found. Creating it...');
//     const newContent = `
// # AndroidX package structure to make it clearer which packages are bundled with the
// # Android operating system, and which are packaged with your app's APK
// # https://developer.android.com/topic/libraries/support-library/androidx-rn
// android.useAndroidX=true
// # Automatically convert third-party libraries to use AndroidX
// android.enableJetifier=true
// `;
//     fs.writeFileSync(gradlePropertiesPath, newContent, 'utf8');
//     console.log('✅ Created new gradle.properties with AndroidX and Jetifier enabled');
//   }
// }

// Run verification and fixes
console.log('🔧 Starting postinstall script...\n');

// Verify Android VoIP files first
verifyAndroidVoIPFiles();
console.log('');

// Then verify Jetifier
// verifyJetifier();
// console.log('');

// // Finally fix Twilio package
// fixTwilioPackage();
// console.log('');

console.log('🎉 Postinstall script completed successfully!');