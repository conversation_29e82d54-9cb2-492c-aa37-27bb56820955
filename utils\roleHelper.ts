export interface UserRole {
  userType: 'caregiver' | 'physician';
  roleId: number;
  roleType: string | number;
}

export const determineUserRole = (email: string, userType?: string): UserRole => {
  if (userType === 'physician') {
    return {
      userType: 'physician',
      roleId: 3,
      roleType: '3'
    };
  }
  
  if (userType === 'caregiver') {
    return {
      userType: 'caregiver',
      roleId: 5,
      roleType: '5'
    };
  }

  return {
    userType: 'caregiver',
    roleId: 5,
    roleType: '5'
  };
};