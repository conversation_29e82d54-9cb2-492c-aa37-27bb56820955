/**
 * Chat Notification Helper Utilities
 * 
 * This module provides utilities for handling patient chat notifications
 * including patient ID extraction and notification formatting.
 */

/**
 * Chat notification data structure from backend
 */
export interface ChatNotificationData {
  type: string;
  chatId: string;
  patientId: string;  // Direct patient ID
  patientName: string;
  requiresResponse: string;
  title: string;
  body: string;
  sentTime: string;
}

export function extractPatientIdFromChatId(chatId: string): string {
  if (!chatId) {
    console.warn('ChatNotificationHelper: No chatId provided');
    return '';
  }

  // Remove common prefixes
  const cleanId = chatId
    .replace(/^chat_/, '')
    .replace(/^patient_/, '')
    .replace(/^caregiver_/, '');

  // Validate that the result is a number
  if (!/^\d+$/.test(cleanId)) {
    console.warn('ChatNotificationHelper: Invalid patient ID format:', chatId, '->', cleanId);
    return '';
  }

  return cleanId;
}

/**
 * Format chat notification title based on patient name and requirements
 */
export function formatChatNotificationTitle(
  patientName?: string,
  requiresResponse?: string
): string {
  const baseTitle = patientName ? 
    `${patientName} sent you a message` : 
    'New Message from Patient';

  if (requiresResponse === 'true') {
    return `${baseTitle} (Response Required)`;
  }

  return baseTitle;
}

/**
 * Format chat notification body with time information
 */
export function formatChatNotificationBody(
  message: string,
  sentTime?: string
): string {
  if (!message) {
    return 'You have received a new message';
  }

  // Truncate message if too long
  const maxLength = 100;
  const truncatedMessage = message.length > maxLength ? 
    `${message.substring(0, maxLength)}...` : 
    message;

  // Add time information if available
  if (sentTime) {
    try {
      const timeString = new Date(sentTime).toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit'
      });
      return `${truncatedMessage}\n\nSent at ${timeString}`;
    } catch (error) {
      console.warn('ChatNotificationHelper: Error formatting time:', sentTime);
    }
  }

  return truncatedMessage;
}

/**
 * Validate chat notification data
 */
export function validateChatNotificationData(data: any): data is ChatNotificationData {
  return (
    data &&
    typeof data === 'object' &&
    data.type === 'chat_message' &&
    typeof data.patientId === 'string' &&
    data.patientId.length > 0 &&
    /^\d+$/.test(data.patientId) // Ensure patientId is numeric
  );
}

/**
 * Log chat notification for debugging
 */
export function logChatNotification(data: ChatNotificationData): void {
  console.log('ChatNotificationHelper: Received chat notification:', {
    patientId: data.patientId,
    chatId: data.chatId,
    patientName: data.patientName || 'Unknown',
    requiresResponse: data.requiresResponse,
    messageLength: data.body?.length || 0,
    sentTime: data.sentTime
  });
} 