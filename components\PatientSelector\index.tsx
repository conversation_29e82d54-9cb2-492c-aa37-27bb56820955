import {
  CheckmarkIcon,
  ChevronDownIcon,
  CloseIcon,
  SearchIcon,
} from "@/components/Icons";
import { Colors } from "@/constants";
import { useTheme } from "@/constants/Theme";
import useModalAnimation from "@/hooks/useModalAnimation";
import { BlurView } from "expo-blur";
import React, { useCallback, useState } from "react";
import {
  Dimensions,
  FlatList,
  Modal,
  Platform,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import Text from "@/components/Text";
import TextWrapper from "@/components/TextWrapper";
import Animated, { useAnimatedStyle } from "react-native-reanimated";

const { height: SCREEN_HEIGHT } = Dimensions.get("window");

interface PatientItem {
  id: number | string;
  name: string;
  displayName?: string;
  patientId?: string;
  image?: string;
  address?: string;
  phone?: string;
}

interface PatientSelectorProps {
  value: PatientItem;
  onChange: (item: PatientItem) => void;
  items: PatientItem[];
  placeholder?: string;
  label?: string;
  disabled?: boolean;
  error?: string;
  onClear?: () => void;
}

/**
 * A modern patient selector component with a bottom sheet dropdown
 */
const PatientSelector: React.FC<PatientSelectorProps> = ({
  value,
  onChange,
  items,
  placeholder = "Select a patient",
  label = "Patient",
  disabled = false,
  error,
  onClear,
}) => {
  // Modal animation hook
  const {
    open: openDropdown,
    close: closeDropdown,
    visible: dropdownVisible,
    transY: transYDropdown,
  } = useModalAnimation();

  // Search functionality
  const [searchQuery, setSearchQuery] = useState("");
  const filteredItems = items.filter((item) =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Theme
  const { theme } = useTheme();

  // Handle item selection
  const handleSelectItem = useCallback(
    (item: PatientItem) => {
      onChange(item);
      closeDropdown();
    },
    [onChange, closeDropdown]
  );

  // Generate initials from patient name
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  // Render patient item
  const renderItem = useCallback(
    ({ item }: { item: PatientItem }) => {
      const isSelected = value?.id === item.id;
      const initials = getInitials(item.name);

      return (
        <TouchableOpacity
          style={[
            styles.itemContainer,
            isSelected && { backgroundColor: Colors.TealBlue + "20" },
          ]}
          onPress={() => handleSelectItem(item)}
          activeOpacity={0.7}
        >
          <View
            style={[
              styles.iconContainer,
              { backgroundColor: Colors.TealBlue + "30" },
            ]}
          >
            <Text size={16} bold color={Colors.TealBlue} center>
              {initials}
            </Text>
          </View>
          <View style={styles.itemTextContainer}>
            <Text size={15} bold={isSelected} color={theme.text}>
              {item.name}
            </Text>
            {item.patientId && (
              <Text size={12} color={Colors.GrayBlue}>
                ID: {item.patientId}
              </Text>
            )}
          </View>
          {isSelected && (
            <View style={styles.checkmarkContainer}>
              <CheckmarkIcon color={Colors.TealBlue} size={18} />
            </View>
          )}
        </TouchableOpacity>
      );
    },
    [value, handleSelectItem, theme]
  );

  // Animated style for the modal
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: SCREEN_HEIGHT - transYDropdown.value }],
    };
  });

  return (
    <TextWrapper style={styles.container}>
      {/* Label */}
      {label && (
        <Text size={15} bold color={theme.text} marginBottom={8}>
          {label}
        </Text>
      )}

      {/* Selector Button */}
      <TouchableOpacity
        style={[
          styles.selectorButton,
          {
            backgroundColor: theme.searchBox,
            borderColor: error ? Colors.Tomato : theme.innearColor,
          },
        ]}
        onPress={openDropdown}
        disabled={disabled}
        activeOpacity={0.7}
      >
        <Text
          size={15}
          color={value?.id ? theme.text : Colors.GrayBlue}
          numberOfLines={1}
          style={{ flex: 1 }}
        >
          {value?.name || placeholder}
        </Text>
        <ChevronDownIcon color={Colors.GrayBlue} size={12} />
      </TouchableOpacity>

      {/* Clear button */}
      {value?.id && onClear && (
        <TouchableOpacity style={styles.clearButton} onPress={onClear}>
          <Text size={12} color={Colors.TealBlue}>
            Clear
          </Text>
        </TouchableOpacity>
      )}

      {/* Error message */}
      {error && (
        <Text size={12} color={Colors.Tomato} marginTop={4}>
          {error}
        </Text>
      )}

      {/* Dropdown Modal */}
      <Modal
        visible={dropdownVisible}
        transparent
        animationType="none"
        onRequestClose={closeDropdown}
      >
        <View style={styles.modalContainer}>
          {/* Backdrop with blur effect */}
          {Platform.OS === "ios" && (
            <BlurView
              intensity={20}
              style={StyleSheet.absoluteFill}
              tint="light"
            />
          )}

          {/* Close backdrop */}
          <TouchableOpacity
            style={StyleSheet.absoluteFill}
            onPress={closeDropdown}
            activeOpacity={1}
          />

          {/* Dropdown content */}
          <Animated.View
            style={[
              styles.dropdownContainer,
              { backgroundColor: Colors.White },
              animatedStyle,
            ]}
          >
            {/* Handle bar */}
            <View style={styles.handleBar} />

            {/* Header */}
            <View style={styles.dropdownHeader}>
              <Text size={18} bold color={theme.text}>
                Select Patient
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={closeDropdown}
              >
                <CloseIcon color={Colors.DarkJungleGreen} size={16} />
              </TouchableOpacity>
            </View>

            {/* Search input */}
            <View
              style={[
                styles.searchContainer,
                { backgroundColor: theme.searchBox },
              ]}
            >
              <View style={styles.searchIcon}>
                <SearchIcon color={Colors.GrayBlue} size={16} />
              </View>
              <TextInput
                style={[styles.searchInput, { color: theme.text }]}
                placeholder="Search patients..."
                placeholderTextColor={Colors.GrayBlue}
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoCapitalize="none"
                autoCorrect={false}
                accessibilityLabel="Search patients"
                maxLength={100}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity
                  style={styles.clearSearchButton}
                  onPress={() => setSearchQuery("")}
                >
                  <CloseIcon color={Colors.DarkJungleGreen} size={12} />
                </TouchableOpacity>
              )}
            </View>

            {/* Patient list */}
            <FlatList
              data={filteredItems}
              renderItem={renderItem}
              keyExtractor={(item) => item.id.toString()}
              contentContainerStyle={styles.listContainer}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text size={15} center color={theme.text}>
                    {searchQuery.length > 0
                      ? "No patients found"
                      : "No patients available"}
                  </Text>
                </View>
              }
            />
          </Animated.View>
        </View>
      </Modal>
    </TextWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
    marginBottom: 16,
  },
  selectorButton: {
    height: 48,
    borderRadius: 8,
    borderWidth: 1,
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    justifyContent: "space-between",
  },
  clearButton: {
    alignSelf: "flex-end",
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  modalContainer: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor:
      Platform.OS === "ios" ? "transparent" : "rgba(0, 0, 0, 0.5)",
  },
  dropdownContainer: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingBottom: Platform.OS === "ios" ? 34 : 24, // Account for bottom safe area
    maxHeight: SCREEN_HEIGHT * 0.7,
  },
  handleBar: {
    width: 48,
    height: 6,
    backgroundColor: Colors.Platinum,
    borderRadius: 3,
    alignSelf: "center",
    marginTop: 12,
    marginBottom: 8,
  },
  dropdownHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: Colors.Platinum,
    alignItems: "center",
    justifyContent: "center",
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    height: 44,
    borderRadius: 8,
    marginHorizontal: 24,
    marginBottom: 16,
    paddingHorizontal: 12,
  },
  searchIcon: {
    width: 24,
    height: 24,
    marginRight: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  searchInput: {
    flex: 1,
    height: "100%",
    fontSize: 15,
  },
  clearSearchButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: Colors.Platinum,
    alignItems: "center",
    justifyContent: "center",
  },
  listContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  itemContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 8,
    paddingHorizontal: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  itemTextContainer: {
    flex: 1,
  },
  checkmarkContainer: {
    width: 24,
    height: 24,
    alignItems: "center",
    justifyContent: "center",
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 24,
  },
});

export default PatientSelector;
