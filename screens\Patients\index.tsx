import { useIsFocused } from '@react-navigation/native';
import React, { useEffect, useState, useCallback, useRef } from 'react';
import { FlatList, StyleSheet, View, RefreshControl, AppState, ActivityIndicator } from 'react-native';
import { getBottomSpace } from 'react-native-iphone-x-helper';
import { useDispatch, useSelector } from 'react-redux';
import { PatientItemProps } from '@/models';
import Container from '@/components/Layout/Container';
import Loader from '@/components/Loader/Loader';
import PatientItem from '@/components/Patients';
import SearchBox from '@/components/SearchBox';
import { Colors } from '@/constants';
import { Ionicons } from '@expo/vector-icons';
import Text from '@/components/Text';
import { setCurentPatientId } from '@/services/actions/currentPatientId';
import { setCurrentPatientName } from '@/services/actions/currentPatientName';
import {
  fetchPatientsList,
  fetchMorePatientsList,
  fetchPatientsListSuccess,
} from '@/services/actions/fetchPatientListActions';
import { FETCH_PATIENTS_LIST_SUCCESS } from '@/services/actions/type';
import { getHeaderHeight, getBottomTabSafePadding } from '@/utils/layoutHelper';

export default () => {
  const dispatch = useDispatch();
  const isFocused = useIsFocused();
  const [patientList, setPatientList] = useState<PatientItemProps[]>();
  const [filteredDataSource, setFilteredDataSource] =
    useState<PatientItemProps[]>();
  const [refreshing, setRefreshing] = useState(false);
  const [searchKey, setSearchKey] = useState('');
  
  // Pagination state
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  
  // Track app state for reload detection
  const appState = useRef(AppState.currentState);
  const hasLoadedOnce = useRef(false);
  const shouldReloadOnFocus = useRef(false);

  const caregiverId = useSelector(
    (state: any) => state?.loginReducer?.data?.userId,
  );

  const orgId = useSelector((state: any) => state?.currentOrgIdReducer?.orgId);

  const patientListData = useSelector(
    (state: any) => state?.patientsListReducer?.data,
  );

  const isFetching = useSelector(
    (state: any) => state?.patientsListReducer?.isFetching,
  );

  const isLoadingMoreFromStore = useSelector(
    (state: any) => state?.patientsListReducer?.isLoadingMore,
  );

  const totalCount = useSelector(
    (state: any) => state?.patientsListReducer?.totalCount,
  );

  const currentPage = useSelector(
    (state: any) => state?.patientsListReducer?.currentPage,
  );

  const pageSize = useSelector(
    (state: any) => state?.patientsListReducer?.pageSize,
  );

  const hasMoreData = useSelector(
    (state: any) => state?.patientsListReducer?.hasMoreData,
  );

  // Monitor app state changes to detect when app comes back from background
  useEffect(() => {
    const handleAppStateChange = (nextAppState: any) => {
      console.log('App state changed from', appState.current, 'to', nextAppState);
      
      // If app is coming from background to foreground, mark for reload
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        console.log('App returned from background - will reload data on next focus');
        shouldReloadOnFocus.current = true;
      }
      
      appState.current = nextAppState;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, []);

  // Function to clear existing patient data
  const clearPatientData = useCallback(() => {
    console.log('Clearing existing patient data');
    dispatch(fetchPatientsListSuccess({
      type: FETCH_PATIENTS_LIST_SUCCESS, 
      data: [], 
      orgId: undefined,
      totalCount: 0,
    }));
    setPatientList([]);
    setFilteredDataSource([]);
    setSearchKey('');
    setHasSearched(false);
  }, [dispatch]);

  // Function to fetch initial pages (0-9) as requested
  const fetchInitialPatientsData = useCallback(() => {
    if (caregiverId && orgId) {
      console.log('Fetching initial patients with pagination (pages 0-9):', {
        caregiverId, 
        orgId,
        pageNumber: 0,
        pageSize: 10, // Request 10 items per page
        totalPagesToLoad: 10, // Load first 10 pages (0-9)
      });
      
      dispatch(fetchPatientsList({
        careGiverId: caregiverId, 
        orgId: orgId,
        pageNumber: 0, // Start from page 0
        pageSize: 10, // 10 items per page
        resetPagination: true,
      }));
    }
  }, [caregiverId, orgId, dispatch]);

  // Function to load more patients (next page)
  const loadMorePatients = useCallback(() => {
    if (caregiverId && orgId && hasMoreData && !isLoadingMoreFromStore && !hasSearched) {
      const nextPage = currentPage + 1;
      
      console.log('Loading more patients (next page):', {
        caregiverId,
        orgId,
        currentPage,
        nextPage,
        currentDataLength: patientListData?.length || 0,
        totalCount,
        hasMoreData,
      });

      setIsLoadingMore(true);
      dispatch(fetchMorePatientsList({
        careGiverId: caregiverId,
        orgId: orgId,
        pageNumber: nextPage, // Next page
        pageSize: 10, // 10 items per page
      }));
    }
  }, [caregiverId, orgId, hasMoreData, isLoadingMoreFromStore, currentPage, patientListData?.length, totalCount, hasSearched, dispatch]);

  // Load data only when needed - first time or after app backgrounding
  useEffect(() => {
    if (isFocused && caregiverId && orgId) {
      const hasData = patientListData?.length > 0;
      
      console.log('Screen focused - checking if data reload needed:', {
        hasLoadedOnce: hasLoadedOnce.current,
        shouldReloadOnFocus: shouldReloadOnFocus.current,
        hasData,
        dataLength: patientListData?.length || 0
      });
      
      // Only load data if:
      // 1. Haven't loaded once this session, OR
      // 2. App came back from background, OR
      // 3. No data exists
      if (!hasLoadedOnce.current || shouldReloadOnFocus.current || !hasData) {
        console.log('Loading initial patients - reason:', {
          firstTime: !hasLoadedOnce.current,
          appReturned: shouldReloadOnFocus.current,
          noData: !hasData
        });
        
        clearPatientData();
        fetchInitialPatientsData();
        hasLoadedOnce.current = true;
        shouldReloadOnFocus.current = false;
      } else {
        console.log('Using existing data - no reload needed');
      }

      // Always reset current patient selection when focusing the list
      dispatch(setCurentPatientId(0));
      dispatch(setCurrentPatientName(''));
    }
  }, [isFocused, caregiverId, orgId, patientListData?.length, fetchInitialPatientsData, clearPatientData, dispatch]);

  // Update local state when Redux data changes
  useEffect(() => {
    if (patientListData?.length) {
      setPatientList(patientListData);
      if (!hasSearched) {
        setFilteredDataSource(patientListData); 
      }
    } else if (patientListData?.length === 0) {
      // Handle case when data is explicitly cleared (empty array vs undefined)
      setPatientList([]);
      setFilteredDataSource([]);
    }
  }, [patientListData, hasSearched]);

  // Stop loading more when request completes
  useEffect(() => {
    if (!isLoadingMoreFromStore && isLoadingMore) {
      setIsLoadingMore(false);
    }
  }, [isLoadingMoreFromStore, isLoadingMore]);

  // Pull to refresh handler - reload initial data
  const onRefresh = useCallback(() => {
    console.log('Manual refresh triggered by user for org', orgId);
    setRefreshing(true);
    clearPatientData(); // Clear existing data
    fetchInitialPatientsData(); // Load initial pages again
  }, [fetchInitialPatientsData, clearPatientData, orgId]);

  // Stop refreshing when loading completes
  useEffect(() => {
    if (!isFetching && refreshing) {
      setRefreshing(false);
    }
  }, [isFetching, refreshing]);

  const searchFilterFunction = (text: any) => {
    if (text?.trim() !== '') {
      setHasSearched(true);
      const newData = patientList?.filter(item => {
        const itemData = item.patientName
          ? item.patientName.toLowerCase()
          : ''.toLowerCase();
        const textData = text.toLowerCase();
        return itemData.indexOf(textData) > -1;
      });
      setFilteredDataSource(newData);
    } else {
      setHasSearched(false);
      console.log('patientList size:', patientList?.length);
      setFilteredDataSource(patientList);
    }
  };

  // Handle end reached for infinite scroll
  const handleEndReached = useCallback(() => {
    if (!isFetching && !isLoadingMoreFromStore && hasMoreData && !hasSearched) {
      console.log('End reached, loading more patients...');
      loadMorePatients();
    }
  }, [isFetching, isLoadingMoreFromStore, hasMoreData, hasSearched, loadMorePatients]);

  // Render loading footer
  const renderFooter = () => {
    if (!isLoadingMoreFromStore || hasSearched) return null;
    
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color={Colors.TealBlue} />
        <Text size={12} color={Colors.GrayBlue} marginTop={8}>
          Loading more patients...
        </Text>
      </View>
    );
  };

  const renderHeader = () => {
    return (
      <View>
        <View style={styles.headerContainer}/>
        <View style={styles.searchContainer}>
          <View style={styles.searchBoxWrapper}>
            <Ionicons name="search-outline" size={20} color={Colors.GrayBlue} style={styles.searchIcon} />
            <SearchBox
              placeholder={'Search patient'}
              value={searchKey}
              onChangeText={text => {
                setSearchKey(text);
                searchFilterFunction(text);
              }}
              style={styles.searchInput}
            />
          </View>
        </View>
        {/* Show pagination info */}
        {totalCount > 0 && (
          <View style={styles.paginationInfo}>
            <Text size={12} color={Colors.GrayBlue}>
              {searchKey.trim() ? 
                `Showing ${filteredDataSource?.length || 0} of ${totalCount} patients (filtered)` :
                `Loaded: ${patientListData?.length || 0} of ${totalCount} patients${hasMoreData ? ' (scroll for more)' : ''}`
              }
            </Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <Container style={styles.container}>
      <Loader modalVisible={isFetching && !refreshing && !isLoadingMoreFromStore} />

      {renderHeader()}

      <FlatList
        data={filteredDataSource}
        renderItem={({item, index}) => <PatientItem index={index} {...item} />}
        keyExtractor={(item, index) => item.patientId.toString() + '-' + index}
        scrollEventThrottle={16}
        contentContainerStyle={styles.contentContainerStyle}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[Colors.TealBlue]} // Android
            tintColor={Colors.TealBlue} // iOS
            title="Pull to refresh patients..." // iOS
            titleColor={Colors.DarkJungleGreen} // iOS
          />
        }
        onEndReached={handleEndReached}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <View style={styles.emptyIconContainer}>
              <Ionicons name="people-outline" size={60} color={Colors.GrayBlue} />
            </View>
            <Text size={18} bold color={Colors.DarkJungleGreen} marginBottom={8}>
              No Patients Found
            </Text>
            <Text size={14} color={Colors.GrayBlue} center style={styles.emptyText}>
              {searchKey.trim() ? 'There are no patients matching your search criteria.' : 'Pull down to refresh and load patients.'}
            </Text>
          </View>
        )}
      />
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFBFC',
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 12,
    backgroundColor: '#FAFBFC',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    fontFamily: 'DMSans-Bold',
    color: Colors.DarkJungleGreen,
    marginLeft: 8,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingBottom: 16,
    backgroundColor: '#FAFBFC',
  },
  searchBoxWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 0,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    height: 40,
    padding: 0,
    fontSize: 16,
    color: '#1A1D29',
  },
  contentContainerStyle: {
    paddingTop: 8,
    paddingBottom: getBottomTabSafePadding() + 20, // Extra padding to ensure content stays above FAB and tab bar
    backgroundColor: '#FAFBFC',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 60,
    backgroundColor: '#FAFBFC',
  },
  emptyIconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  emptyText: {
    maxWidth: 240,
    textAlign: 'center',
    lineHeight: 20,
  },
  paginationInfo: {
    paddingHorizontal: 20,
    paddingBottom: 16,
    backgroundColor: '#FAFBFC',
  },
  footerLoader: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
