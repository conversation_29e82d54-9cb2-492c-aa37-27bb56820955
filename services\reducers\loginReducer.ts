import {LOGGING_IN, LOG_IN_FAILURE, LOG_IN_SUCCESSFUL} from '../actions/type';

const initialState = {
  data: '',
  isLoggedIn: false,
  isLoggingIn: false,
  msg: '',
  userRole: null,
  roleId: null,
};

export default function loginReducer(
  state = initialState,
  action: {type: any; data: any; msg: any},
) {
  switch (action.type) {
    case LOGGING_IN:
      return {...state, isLoggingIn: true, isLoggedIn: false, data: ''};
    case LOG_IN_SUCCESSFUL:
      const userRole = action.data?.userType || 'caregiver';
      const roleId = userRole === 'physician' ? 3 : 5;
      
      return {
        ...state,
        isLoggedIn: true,
        isLoggingIn: false,
        data: action.data,
        userRole: userRole,
        roleId: roleId,
      };
    case LOG_IN_FAILURE:
      return {
        ...state,
        isLoggedIn: false,
        isLoggingIn: false,
        msg: action.msg,
        userRole: null,
        roleId: null,
      };

    default:
      return state;
  }
}
