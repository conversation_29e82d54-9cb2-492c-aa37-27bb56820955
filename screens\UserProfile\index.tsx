import {useNavigation} from '@react-navigation/native';
import {useRouter} from 'expo-router';
import React, {memo, useCallback, useEffect, useState} from 'react';
import {Image, StyleSheet, View, ScrollView, RefreshControl} from 'react-native';
import * as Application from 'expo-application';
import {useDispatch, useSelector} from 'react-redux';
import ButtonLinear from '@/components/Buttons/ButtonLinear';
import Container from '@/components/Layout/Container';
import Loader from '@/components/Loader/Loader';
import Text from '@/components/Text';
import UserProfileManagement from '@/components/UserProfile/UserProfileManagement';
import {Routes} from '@/constants';
import {Colors} from '@/constants';
import {width} from '@/constants/Const';
import {SOURCE_ICON} from '@/assets/images';
import {fetchUserProfile} from '@/services/actions/userProfileActions';
import scale from '@/utils/scale';

export default memo(() => {
  const [profileManagement, setProfileManagement] = useState<any>();
  const [refreshing, setRefreshing] = useState(false);
  const dispatch = useDispatch();
  const {navigate} = useNavigation() as any;
  const router = useRouter();
  const [version, setVersion] = useState('0.0');
  const [build, setBuild] = useState('0');

  const caregiverId = useSelector(
    (state: any) => state?.loginReducer?.data?.userId,
  );

  const userProfileDetails = useSelector(
    (state: any) => state?.userProfileReducer?.data,
  );

  const isFetching = useSelector(
    (state: any) => state?.userProfileReducer?.isFetching,
  );

  // Function to refresh profile data
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    dispatch(fetchUserProfile({careGiverId: caregiverId}));
    setRefreshing(false);
  }, [dispatch, caregiverId]);

  useEffect(() => {
    dispatch(fetchUserProfile({careGiverId: caregiverId}));

    // Get app version and build number using Expo packages
    const getAppInfo = () => {
      try {
        const appVersion = Application.nativeApplicationVersion;
        const buildNumber = Application.nativeBuildVersion;

        setVersion(appVersion || '0.0');
        setBuild(buildNumber || '0');

        console.log('Version:', appVersion);
        console.log('Build Number:', buildNumber);
      } catch (error) {
        console.error('Error getting app info:', error);
        setVersion('0.0');
        setBuild('0');
      }
    };

    getAppInfo();
  }, []);

  const onGoToUpdateProfile = useCallback(() => {
    try {
      // Use Expo Router for navigation
      console.log('Navigating to update profile using Expo Router');
      router.push('/profile/update');
    } catch (error) {
      console.error('Error navigating to update profile with Expo Router:', error);
      // Fall back to React Navigation if needed
      navigate(Routes.UpdateProfile);
    }
  }, [router, navigate]);

  const onGoToChangePassword = useCallback(() => {
    try {
      // Use Expo Router for navigation
      console.log('Navigating to change password using Expo Router');
      router.push('/profile/change-password');
    } catch (error) {
      console.error('Error navigating to change password with Expo Router:', error);
      // Fall back to React Navigation if needed
      navigate(Routes.ChangePassword);
    }
  }, [router, navigate]);

  useEffect(() => {
    const data = {
      phoneNumber: userProfileDetails?.phoneNumber,
      firstName: userProfileDetails?.firstName,
      lastName: userProfileDetails?.lastName,
      picPath: userProfileDetails?.picPath,
      name: userProfileDetails?.name,
      address1: userProfileDetails?.address?.address1,
      address2: userProfileDetails?.address?.address2,
      state: userProfileDetails?.address?.state,
      city: userProfileDetails?.address?.city,
      zip: userProfileDetails?.address?.zip,
      country: userProfileDetails?.address?.country,
      addresId: userProfileDetails?.address?.addresId,
      userName: userProfileDetails?.userName,
    };
    setProfileManagement(data);
  }, [userProfileDetails]);

  return (
    <Container style={styles.container}>
      <Loader modalVisible={isFetching && !refreshing} />

      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={[Colors.DodgerBlue]} />
        }
        scrollEventThrottle={16}
      >
        <View style={styles.profileContainer}>
          {/* Background Image */}
          <Image style={styles.background} source={require('@/assets/images/Images/img_background.png')} />

          {/* White Card with Profile Information */}
          <View style={styles.cardContainer}>
            <UserProfileManagement {...profileManagement} />
          </View>

          {/* Buttons - Below the card */}
          <View style={styles.buttonContainer}>
            <ButtonLinear
              leftChildren={
                <Image style={styles.iconEdit} source={SOURCE_ICON.edit} />
              }
              style={styles.buttonUpdate}
              title={'Update Profile'}
              onPress={onGoToUpdateProfile}
            />
            <ButtonLinear
              white
              leftChildren={
                <Image style={styles.iconEdit} source={SOURCE_ICON.edit} />
              }
              style={{...styles.buttonUpdate, ...styles.buttonChangePassword}}
              title={'Change Password'}
              onPress={onGoToChangePassword}
            />
          </View>
        </View>

        <View style={styles.versionContainer}>
          <Text center size={16} lineHeight={24} color="#000000">
            App version: {version}
          </Text>
        </View>
      </ScrollView>
    </Container>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  scrollContainer: {
    flexGrow: 1,
  },
  profileContainer: {
    position: 'relative',
  },
  background: {
    width: width,
    height: scale(250),
    resizeMode: 'cover',
    position: 'absolute',
    top: 0,
    left: 0,
    backgroundColor: '#1ECBE1',
  },
  cardContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    marginHorizontal: 16,
    marginTop: scale(110),
    padding: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 2,
  },
  buttonContainer: {
    marginTop: 20,
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  iconEdit: {
    marginRight: 8,
    width: 20,
    height: 20,
    tintColor: 'white',
  },
  buttonUpdate: {
    marginTop: 10,
    marginBottom: 10,
    width: '100%',
    borderRadius: 8,
    height: 50,
    backgroundColor: '#1ECBE1',
  },
  buttonChangePassword: {
    backgroundColor: Colors.White,
    borderWidth: 1,
    borderColor: '#1ECBE1',
    marginTop: 10,
  },
  versionContainer: {
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 30,
    justifyContent: 'center',
    paddingHorizontal: 16,
  },
});
