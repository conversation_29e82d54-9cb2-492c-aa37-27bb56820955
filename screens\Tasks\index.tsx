import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, RefreshControl, ActivityIndicator, Alert } from 'react-native';
import { useSelector } from 'react-redux';
import { useIsFocused } from '@react-navigation/native';
import moment from 'moment';
import { Ionicons } from '@expo/vector-icons';

import Container from '@/components/Layout/Container';
import Text from '@/components/Text';
import { Colors } from '@/constants';
import { apiPostWithToken } from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import { TodayTaskItem, TodayTasksResponse } from '@/models';
import { TodayTaskItemComponent } from './components';
import { getBottomTabSafePadding } from '@/utils/layoutHelper';
import TaskCreateEditScreen from '@/screens/Schedule/components/TaskCreateEditScreen';
import { Task } from '@/screens/Schedule/types';
import TaskService from '@/screens/Schedule/services/TaskService';

const TaskList = () => {
  const [tasks, setTasks] = useState<TodayTaskItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showTaskScreen, setShowTaskScreen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<TodayTaskItem | null>(null);
  const [isTaskActionLoading, setIsTaskActionLoading] = useState(false);
  
  const isFocused = useIsFocused();
  
  const userId = useSelector((state: any) => state?.loginReducer?.data?.userId);
  const orgId = useSelector((state: any) => state?.currentOrgIdReducer?.orgId);

  // Get today's date in device timezone
  const getTodayDate = () => {
    return moment().format('YYYY-MM-DD');
  };

  // Get device timezone
  const getDeviceTimezone = () => {
    // Use a simple approach to get timezone
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    return timezone || 'UTC';
  };

  // Fetch today's tasks
  const fetchTodayTasks = async (showLoader = true) => {
    // Validate required parameters
    if (!userId || !orgId) {
      console.error('Missing required parameters:', { userId, orgId });
      Alert.alert('Error', 'User session invalid. Please login again.', [{ text: 'OK' }]);
      return;
    }

    if (showLoader) {
      setIsLoading(true);
    } else {
      setIsRefreshing(true);
    }

    try {
      console.log('Fetching today tasks with params:', {
        userId: String(userId),
        orgId: String(orgId),
        date: getTodayDate(),
        timezone: getDeviceTimezone(),
      });
      console.log('Original values and types:', {
        userId: { value: userId, type: typeof userId },
        orgId: { value: orgId, type: typeof orgId }
      });

      const response = await apiPostWithToken<TodayTasksResponse>(
        {
          userId: String(userId),
          orgId: String(orgId),
          date: getTodayDate(),
          timezone: getDeviceTimezone(),
        },
        URLS.caregiverUrl + 'getTodayTasks'
      );

      console.log('API Response:', response);

      // Check if we have a successful response
      if (response?.status === 200) {
        // Handle different response structures
        if (response?.data?.success && response?.data?.data) {
          console.log('Tasks found:', response.data.data.length);
          setTasks(response.data.data || []);
        } else if (response?.data && Array.isArray(response?.data)) {
          // In case the response structure is different
          console.log('Tasks found (direct array):', response.data.length);
          setTasks(response.data || []);
        } else {
          console.log('No tasks found or unexpected response structure');
          setTasks([]);
        }
      } else {
        console.error('API Error - Status:', response?.status);
        console.error('API Error - Response:', response);
        const errorMessage = response?.error?.message || 
          response?.message ||
          'Failed to fetch today\'s tasks. Please try again.';
        Alert.alert('Error', errorMessage, [{ text: 'OK' }]);
        setTasks([]);
      }
    } catch (error) {
      console.error('Error fetching today tasks:', error);
      
      // More detailed error information
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.error('Error details:', errorMessage);
      
      Alert.alert(
        'Error', 
        'Network error. Please check your connection and try again.', 
        [{ text: 'OK' }]
      );
      setTasks([]);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Initial load when screen is focused
  useEffect(() => {
    if (isFocused && userId && orgId) {
      fetchTodayTasks();
    }
  }, [isFocused, userId, orgId]);

  // Handle pull to refresh
  const handleRefresh = () => {
    fetchTodayTasks(false);
  };

  // Handle task item press
  const handleTaskPress = (task: TodayTaskItem) => {
    // TODO: Navigate to task details or edit screen
    console.log('Task pressed:', task);
  };

  // Handle task deletion
  const handleTaskDelete = (taskId: string) => {
    setTasks(prevTasks => prevTasks.filter(task => task.id !== taskId));
  };

  // Convert TodayTaskItem to Task format for editing
  const convertTodayTaskToTask = (todayTask: TodayTaskItem): Task => {
    return {
      id: todayTask.id,
      title: todayTask.title,
      description: todayTask.description,
      priority: todayTask.priority as 'Low' | 'Medium' | 'High' | 'Critical',
      start: todayTask.start,
      end: todayTask.end,
      patientId: todayTask.patientId,
      patientName: todayTask.patinetName, // Note: API has typo
      userId: parseInt(todayTask.userId),
      className: todayTask.className,
      timezone: 'America/New_York', // Default timezone
    };
  };

  // Handle task edit
  const handleTaskEdit = (task: TodayTaskItem) => {
    setSelectedTask(task);
    setShowTaskScreen(true);
  };

  // Handle task update
  const handleUpdateTask = async (task: Task) => {
    setIsTaskActionLoading(true);
    try {
      await TaskService.updateTask(task);
      
      // Refresh the tasks list
      fetchTodayTasks(false);
      setShowTaskScreen(false);
      setSelectedTask(null);
      
      Alert.alert('✅ Success', 'Task updated successfully!');
    } catch (error: any) {
      console.error('Error updating task:', error);
      Alert.alert('❌ Error', error?.message || 'Failed to update task. Please try again.');
    } finally {
      setIsTaskActionLoading(false);
    }
  };

  // Handle task deletion from edit screen
  const handleDeleteTaskFromEdit = async (taskId: string) => {
    setIsTaskActionLoading(true);
    try {
      await TaskService.deleteTask(taskId);
      setTasks(prevTasks => prevTasks.filter(task => task.id !== taskId));
      setShowTaskScreen(false);
      setSelectedTask(null);
      
      Alert.alert('✅ Success', 'Task deleted successfully!');
    } catch (error: any) {
      console.error('Error deleting task:', error);
      Alert.alert('❌ Error', 'Failed to delete task. Please try again.');
    } finally {
      setIsTaskActionLoading(false);
    }
  };

  // Close task screen
  const handleCloseTaskScreen = () => {
    setShowTaskScreen(false);
    setSelectedTask(null);
  };

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <View style={styles.emptyIconContainer}>
        <Ionicons name="checkmark-done-circle" size={64} color={Colors.GrayBlue} />
      </View>
      <Text size={20} bold color={Colors.DarkJungleGreen} marginBottom={8}>
        No Tasks Today
      </Text>
      <Text size={16} color={Colors.GrayBlue} center style={styles.emptyText}>
        You're all caught up! No tasks scheduled for today.
      </Text>
      <Text size={14} color={Colors.GrayBlue} center style={styles.emptySubtext}>
        Pull down to refresh or check back later.
      </Text>
    </View>
  );

  // Render loading state
  if (isLoading) {
    return (
      <Container style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.TealBlue} />
          <Text size={16} color={Colors.GrayBlue} marginTop={16}>
            Loading today's tasks...
          </Text>
        </View>
      </Container>
    );
  }

  // If task screen is shown, render it instead of the main content
  if (showTaskScreen && selectedTask) {
    return (
      <TaskCreateEditScreen
        selectedDate={new Date(selectedTask.start)}
        selectedTask={convertTodayTaskToTask(selectedTask)}
        isEditMode={true}
        onSubmit={handleUpdateTask}
        onDelete={handleDeleteTaskFromEdit}
        onClose={handleCloseTaskScreen}
        isLoading={isTaskActionLoading}
      />
    );
  }

  return (
    <Container style={styles.container}>
      {/* Tasks List */}
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[Colors.TealBlue]}
            tintColor={Colors.TealBlue}
          />
        }
        contentContainerStyle={[
          styles.scrollContent,
          tasks.length === 0 && styles.scrollContentEmpty
        ]}
      >
        {tasks.length === 0 ? (
          renderEmptyState()
        ) : (
          tasks.map((task, index) => (
            <TodayTaskItemComponent
              key={`today-task-${task.id}-${index}`}
              task={task}
              onPress={() => handleTaskPress(task)}
              onRefresh={handleRefresh}
              onDelete={handleTaskDelete}
              onEdit={handleTaskEdit}
            />
          ))
        )}
      </ScrollView>
    </Container>
  );
};

export default TaskList;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.Snow,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: getBottomTabSafePadding() + 20, // Extra padding to ensure content stays above FAB and tab bar
  },
  scrollContentEmpty: {
    flexGrow: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyIconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.White,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  emptyText: {
    maxWidth: 280,
    lineHeight: 22,
    marginBottom: 8,
  },
  emptySubtext: {
    maxWidth: 240,
    lineHeight: 18,
  },
});
