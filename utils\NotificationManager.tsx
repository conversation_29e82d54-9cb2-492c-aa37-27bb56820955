import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import * as Notifications from 'expo-notifications';
import { Alert, Platform, AppState } from 'react-native';
import { 
  registerForPushNotificationsAsync, 
  registerDeviceWithBackend,
  getAndClearBackgroundNotifications, 
} from './notificationHelper';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useRouteContext } from '../context/RouteContext';
import { useSelector } from 'react-redux';
import { apiPostWithToken } from '../services/apis/apiManager';
import URLS from '../services/config/config';
import { initializeFirebaseApp } from './firebaseConfig';
import { onMessage, onNotificationOpenedApp, getInitialNotification } from './firebaseMessaging';
import { useRouter, usePathname } from 'expo-router';
import { fcmNotificationEvents } from './NotificationEvents';
import { 
  formatChatNotificationTitle, 
  formatChatNotificationBody,
  validateChatNotificationData,
  logChatNotification 
} from './chatNotificationHelper';

import { showToast, hideToast } from '@/components/ToastNotification';

const LAST_NOTIFICATION_KEY = '@LastNotification';
const FCM_NOTIFICATION_HISTORY_KEY = '@FCMNotificationHistory';

// Context for notification data
interface NotificationContextType {
  lastNotification: Notifications.Notification | null;
  pendingNotifications: Notifications.Notification[];
  showLastNotification: () => void;
  clearLastNotification: () => void;
  showPendingNotifications: () => void;
  currentChatPatientId: string | null;
  appState: string;
  addToNotificationHistory: (notification: Notifications.Notification) => void;
}

const NotificationContext = createContext<NotificationContextType>({
  lastNotification: null,
  pendingNotifications: [],
  showLastNotification: () => {},
  clearLastNotification: () => {},
  showPendingNotifications: () => {},
  currentChatPatientId: null,
  appState: 'active',
  addToNotificationHistory: () => {},
});

export const useNotification = () => useContext(NotificationContext);

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [lastNotification, setLastNotification] = useState<Notifications.Notification | null>(null);
  const [pendingNotifications, setPendingNotifications] = useState<Notifications.Notification[]>([]);
  const setupComplete = useRef(false);
  const router = useRouter();
  const pathname = usePathname();

  const [appState, setAppState] = useState(AppState.currentState);
  const [currentChatPatientId, setCurrentChatPatientId] = useState<string | null>(null);
  
  const caregiverId = useSelector(
    (state: {loginReducer?: {data?: {userId?: string}}}) =>
      state?.loginReducer?.data?.userId || '',
  );

  const { isRestrictedRoute, hasSelectedOrganization } = useRouteContext();

  const [notificationListeners, setNotificationListeners] = useState<{
    notificationListener?: Notifications.Subscription;
    responseListener?: Notifications.Subscription;
  }>({});

  const wasOnRestrictedRoute = useRef(isRestrictedRoute);

  useEffect(() => {
    const handleAppStateChange = (nextAppState: any) => {
      console.log('NotificationManager: App state changed:', appState, '->', nextAppState);
      setAppState(nextAppState);
      
      if (appState.match(/inactive|background/) && nextAppState === 'active') {
        console.log('NotificationManager: App became active, checking current screen and loading background notifications');
        checkCurrentChatScreen();
        loadBackgroundNotifications();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [appState]);

  useEffect(() => {
    checkCurrentChatScreen();
  }, [pathname]);

  const checkCurrentChatScreen = () => {
    if (pathname && pathname.includes('/patient/chat/')) {
      const matches = pathname.match(/\/patient\/chat\/(\d+)/);
      if (matches && matches[1]) {
        setCurrentChatPatientId(matches[1]);
        console.log('NotificationManager: User is on chat screen for patient:', matches[1]);
      }
    } else {
      setCurrentChatPatientId(null);
      console.log('NotificationManager: User is not on a chat screen');
    }
  };

  // Load last notification from storage on mount
  useEffect(() => {
    loadLastNotification();
    loadPendingNotifications();
    setupNotifications();
    
    // Load background notifications when app starts
    loadBackgroundNotifications();

    return () => {
      // Clean up notification listeners
      if (notificationListeners.notificationListener) {
        Notifications.removeNotificationSubscription(notificationListeners.notificationListener);
      }
      if (notificationListeners.responseListener) {
        Notifications.removeNotificationSubscription(notificationListeners.responseListener);
      }
    };
  }, []);

  // Handle backend registration when caregiver ID becomes available
  useEffect(() => {
    if (caregiverId && setupComplete.current) {
      console.log('NotificationManager: Caregiver ID available, attempting backend registration');
      handleBackendRegistration();
    }
  }, [caregiverId]);

  // Keep track of restricted route state for reference
  useEffect(() => {
    // Update the ref
    wasOnRestrictedRoute.current = isRestrictedRoute;
  }, [isRestrictedRoute]);

  // Load last notification from AsyncStorage
  const loadLastNotification = async () => {
    try {
      const storedNotification = await AsyncStorage.getItem(LAST_NOTIFICATION_KEY);
      if (storedNotification) {
        const parsedNotification = JSON.parse(storedNotification);
        setLastNotification(parsedNotification);
        console.log('NotificationManager: Loaded stored notification:', parsedNotification);
      }
    } catch (error) {
      console.error('NotificationManager: Error loading stored notification:', error);
    }
  };

  // Load pending notifications from AsyncStorage
  const loadPendingNotifications = async () => {
    try {
      const storedPendingNotifications = await AsyncStorage.getItem('PENDING_NOTIFICATIONS_KEY');
      if (storedPendingNotifications) {
        const parsedNotifications = JSON.parse(storedPendingNotifications);
        setPendingNotifications(parsedNotifications);
        console.log('NotificationManager: Loaded pending notifications:', parsedNotifications.length);
      }
    } catch (error) {
      console.error('NotificationManager: Error loading pending notifications:', error);
    }
  };

  // Save pending notifications to AsyncStorage
  const savePendingNotifications = async (notifications: Notifications.Notification[]) => {
    try {
      await AsyncStorage.setItem('PENDING_NOTIFICATIONS_KEY', JSON.stringify(notifications));
    } catch (error) {
      console.error('NotificationManager: Error saving pending notifications:', error);
    }
  };

  // Save notification to AsyncStorage
  const saveLastNotification = async (notification: Notifications.Notification) => {
    try {
      await AsyncStorage.setItem(LAST_NOTIFICATION_KEY, JSON.stringify(notification));
    } catch (error) {
      console.error('NotificationManager: Error saving notification:', error);
    }
  };

  // Load and process background notifications
  const loadBackgroundNotifications = async () => {
    try {
      console.log('📥 NotificationManager: Loading background notifications...');
      
      // Get background notifications and clear them from storage
      const backgroundNotifications = await getAndClearBackgroundNotifications();
      
      if (backgroundNotifications.length > 0) {
        console.log(`🔍 NotificationManager: Found ${backgroundNotifications.length} background notifications:`);
        backgroundNotifications.forEach((notif, index) => {
          console.log(`   ${index + 1}. "${notif.title}" - ${notif.body.substring(0, 50)}...`);
        });
        
        // Process each background notification and add to notification history
        for (const bgNotification of backgroundNotifications) {
          // Convert background notification to Expo notification format
          const expoNotification: Notifications.Notification = {
            date: bgNotification.timestamp,
            request: {
              content: {
                title: bgNotification.title,
                subtitle: null,
                body: bgNotification.body,
                data: {
                  ...bgNotification.data,
                  receivedInBackground: true,
                },
                sound: null,
                badge: undefined,
              },
              identifier: bgNotification.id,
              trigger: null as any,
            },
          };
          
          // Add to notification history
          await addToNotificationHistory(expoNotification);
          
          // Emit event for real-time updates
          fcmNotificationEvents.newNotificationReceived(expoNotification);
        }
        
        // Trigger notification count update
        fcmNotificationEvents.notificationsChanged();
        
        console.log(`✅ NotificationManager: Successfully processed ${backgroundNotifications.length} background notifications`);
      } else {
        console.log('📭 NotificationManager: No background notifications found');
      }
    } catch (error) {
      console.error('❌ NotificationManager: Error loading background notifications:', error);
    }
  };

  // Handle backend registration with FCM token
  const handleBackendRegistration = async () => {
    if (!caregiverId) {
      console.log('NotificationManager: No caregiver ID available for backend registration');
      return;
    }

    try {
      // Get the stored FCM token
      const storedToken = await AsyncStorage.getItem('@FCMToken');
      if (storedToken) {
        console.log('NotificationManager: Registering FCM token with backend');
        await registerDeviceWithBackend(apiPostWithToken, caregiverId, storedToken, URLS.FCMURL);
      } else {
        console.log('NotificationManager: No FCM token available for backend registration');
      }
    } catch (error) {
      console.error('NotificationManager: Error in backend registration:', error);
    }
  };

  // Set up notification handlers
  const setupNotifications = async () => {
    try {
      console.log('NotificationManager: Setting up notifications');

      // Initialize Firebase app for v22 compatibility
      try {
        initializeFirebaseApp();
        console.log('NotificationManager: Firebase app initialized');
      } catch (error) {
        console.error('NotificationManager: Error initializing Firebase:', error);
      }

      // Register for Firebase push notifications
      await registerForPushNotificationsAsync();

      // NOTE: Notification handler is already set in notificationHelper.ts
      // No need to set it again here as it would override the correct configuration

      // Setup Firebase messaging listeners for FCM notifications
      const unsubscribeOnMessage = onMessage(async remoteMessage => {
        console.log('NotificationManager: FCM message received in foreground:', JSON.stringify(remoteMessage));
        
        // Create a notification-like object from the FCM message that conforms to Expo Notification type
        const fcmNotification: Notifications.Notification = {
          date: Date.now(),
          request: {
            content: {
              title: remoteMessage.notification?.title || 'WatchRx Caregiver',
              subtitle: null,
              body: remoteMessage.notification?.body || '',
              data: remoteMessage.data || {},
              sound: null,
              badge: undefined,
            },
            identifier: 'fcm-' + Date.now(),
            trigger: null as any,
          },
        };

        // Handle the FCM message like a regular notification
        const extractedContent = extractNotificationContent(fcmNotification.request.content);
        
        const normalizedNotification = {
          ...fcmNotification,
          request: {
            ...fcmNotification.request,
            content: {
              ...fcmNotification.request.content,
              title: extractedContent.title,
              body: extractedContent.body,
            }
          }
        };

        // Store the notification
        setLastNotification(normalizedNotification);
        saveLastNotification(normalizedNotification);
        addToNotificationHistory(normalizedNotification);
        
        // Emit event for real-time updates
        fcmNotificationEvents.newNotificationReceived(normalizedNotification);
        
        // Force trigger notification count update
        setTimeout(() => {
          fcmNotificationEvents.notificationsChanged();
        }, 100);

        // Handle chat notifications based on current screen context
        if (extractedContent.notificationType === 'chat_message' && extractedContent.patientId) {
          const notificationPatientId = extractedContent.patientId;
          
          console.log('✅ NotificationManager: Chat notification received with patientId:', notificationPatientId);
          console.log('📱 NotificationManager: Currently on chat for patient:', currentChatPatientId);
          console.log('📋 NotificationManager: Notification content:', {
            type: extractedContent.notificationType,
            patientId: extractedContent.patientId,
            patientName: extractedContent.patientName,
            chatId: extractedContent.chatId
          });
          
          // Don't show in-app notification if user is already on the same chat
          if (currentChatPatientId === notificationPatientId) {
            console.log('NotificationManager: User already on chat screen, skipping notification');
            return;
          }
        }

        // Show WhatsApp-style in-app notification
        console.log('NotificationManager: Showing in-app notification');
        showToast(
          extractedContent.title, 
          extractedContent.body,
          () => {
            // When notification is tapped, handle navigation
            if (extractedContent.notificationType === 'chat_message' && extractedContent.patientId) {
              navigateToChat(extractedContent.patientId, extractedContent.patientName || '');
            }
          }
        );
      });

      // Handle FCM notification opened (when app is in background and user taps notification)
      onNotificationOpenedApp(remoteMessage => {
        console.log('NotificationManager: FCM notification opened app:', JSON.stringify(remoteMessage));
        
        // Handle the opened FCM notification
        const fcmNotification: Notifications.Notification = {
          date: Date.now(),
          request: {
            content: {
              title: remoteMessage.notification?.title || 'WatchRx Caregiver',
              subtitle: null,
              body: remoteMessage.notification?.body || '',
              data: remoteMessage.data || {},
              sound: null,
              badge: undefined,
            },
            identifier: 'fcm-opened-' + Date.now(),
            trigger: null as any,
          },
        };

        const extractedContent = extractNotificationContent(fcmNotification.request.content);
        
        // Store the notification
        const normalizedNotification = {
          ...fcmNotification,
          request: {
            ...fcmNotification.request,
            content: {
              ...fcmNotification.request.content,
              title: extractedContent.title,
              body: extractedContent.body,
            }
          }
        };

        setLastNotification(normalizedNotification);
        saveLastNotification(normalizedNotification);
        addToNotificationHistory(normalizedNotification);
        
        // Emit event for real-time updates
        fcmNotificationEvents.newNotificationReceived(normalizedNotification);
        
        // Handle direct navigation based on notification type and content
        if (extractedContent.notificationType === 'chat_message' && extractedContent.patientId) {
          console.log('🚀 NotificationManager: Navigating to chat from background notification');
          setTimeout(() => {
            navigateToChat(extractedContent.patientId, extractedContent.patientName || '');
          }, 1000);
        } else if (extractedContent.patientId && extractedContent.notificationType !== 'general') {
          // Handle other notification types with patient ID
          console.log('🚀 NotificationManager: Navigating based on notification type:', extractedContent.notificationType);
          setTimeout(() => {
            navigateBasedOnType(extractedContent);
          }, 1000);
        } else if (extractedContent.route) {
          // Handle custom route navigation
          console.log('🚀 NotificationManager: Navigating to custom route:', extractedContent.route);
          setTimeout(() => {
            router.push(extractedContent.route);
          }, 1000);
        } else {
          // For notifications without specific navigation, show toast
          showToast(extractedContent.title, extractedContent.body);
        }
      });

      // Check if app was opened from a FCM notification (cold start)
      getInitialNotification()
        .then(remoteMessage => {
          if (remoteMessage) {
            console.log('NotificationManager: App opened from FCM notification (cold start):', JSON.stringify(remoteMessage));
            
            const fcmNotification: Notifications.Notification = {
              date: Date.now(),
              request: {
                content: {
                  title: remoteMessage.notification?.title || 'WatchRx Caregiver',
                  subtitle: null,
                  body: remoteMessage.notification?.body || '',
                  data: remoteMessage.data || {},
                  sound: null,
                  badge: undefined,
                },
                identifier: 'fcm-initial-' + Date.now(),
                trigger: null as any,
              },
            };

            const extractedContent = extractNotificationContent(fcmNotification.request.content);
            
            // Store the notification
            const normalizedNotification = {
              ...fcmNotification,
              request: {
                ...fcmNotification.request,
                content: {
                  ...fcmNotification.request.content,
                  title: extractedContent.title,
                  body: extractedContent.body,
                }
              }
            };

            setLastNotification(normalizedNotification);
            saveLastNotification(normalizedNotification);
            addToNotificationHistory(normalizedNotification);
            
            // Emit event for real-time updates
            fcmNotificationEvents.newNotificationReceived(normalizedNotification);

            // Handle navigation for cold start notifications
            if (extractedContent.notificationType === 'chat_message' && extractedContent.patientId) {
              console.log('🚀 NotificationManager: Navigating to chat from cold start FCM notification');
              setTimeout(() => {
                navigateToChat(extractedContent.patientId, extractedContent.patientName || '');
              }, 2000); // Longer delay for cold start
            } else if (extractedContent.patientId && extractedContent.notificationType !== 'general') {
              // Handle other notification types with patient ID
              console.log('🚀 NotificationManager: Navigating based on notification type (cold start):', extractedContent.notificationType);
              setTimeout(() => {
                navigateBasedOnType(extractedContent);
              }, 2000);
            } else if (extractedContent.route) {
              // Handle custom route navigation
              console.log('🚀 NotificationManager: Navigating to custom route (cold start):', extractedContent.route);
              setTimeout(() => {
                router.push(extractedContent.route);
              }, 2000);
            } else {
              // For notifications without specific navigation, show toast with callback
              setTimeout(() => {
                console.log('📱 NotificationManager: Showing toast notification for FCM cold start');
                showToast(
                  extractedContent.title, 
                  extractedContent.body,
                  () => {
                    // When notification is tapped, try to navigate based on available data
                    if (extractedContent.notificationType === 'chat_message' && extractedContent.patientId) {
                      navigateToChat(extractedContent.patientId, extractedContent.patientName || '');
                    } else if (extractedContent.patientId && extractedContent.notificationType !== 'general') {
                      navigateBasedOnType(extractedContent);
                    } else if (extractedContent.route) {
                      router.push(extractedContent.route);
                    }
                  }
                );
              }, 1000);
            }
          }
        });

      // Handle foreground notifications (Expo)
      const notificationListener = Notifications.addNotificationReceivedListener(notification => {
        console.log('NotificationManager: Expo notification received in foreground:',
          JSON.stringify(notification.request.content));
        
        // Extract notification content
        const extractedContent = extractNotificationContent(notification.request.content);
        
        // Create a normalized notification with the correct title and body
        const normalizedNotification = {
          ...notification,
          request: {
            ...notification.request,
            content: {
              ...notification.request.content,
              title: extractedContent.title,
              body: extractedContent.body,
            }
          }
        };
        
        // Store the notification
        setLastNotification(normalizedNotification);
        saveLastNotification(normalizedNotification);
        addToNotificationHistory(normalizedNotification);
        
        // Emit event for real-time updates
        fcmNotificationEvents.newNotificationReceived(normalizedNotification);
        
        // For chat notifications, check if user is already on that chat
        if (extractedContent.notificationType === 'chat_message' && extractedContent.patientId) {
          // Don't show in-app notification if user is already on the same chat
          if (currentChatPatientId === extractedContent.patientId) {
            console.log('NotificationManager: User already on chat screen, skipping notification');
            return;
          }
        }
        
        // Show WhatsApp-style in-app notification
        console.log('NotificationManager: Showing in-app notification for Expo notification');
        showToast(
          extractedContent.title, 
          extractedContent.body,
          () => {
            // When notification is tapped, handle navigation
            if (extractedContent.notificationType === 'chat_message' && extractedContent.patientId) {
              navigateToChat(extractedContent.patientId, extractedContent.patientName || '');
            }
          }
        );
      });

      // Handle notification responses (when user taps on notification) (Expo)
      const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
        const notification = response.notification;

        console.log('NotificationManager: Expo notification response received:',
          JSON.stringify(notification.request.content));

        // Extract the proper notification content using our helper function
        const extractedContent = extractNotificationContent(notification.request.content);

        // Create a normalized notification with the correct title and body
        const normalizedNotification = {
          ...notification,
          request: {
            ...notification.request,
            content: {
              ...notification.request.content,
              title: extractedContent.title,
              body: extractedContent.body,
            }
          }
        };

        // Store the normalized notification for later display
        setLastNotification(normalizedNotification);
        saveLastNotification(normalizedNotification);
        addToNotificationHistory(normalizedNotification);
        
        // Emit event for real-time updates
        fcmNotificationEvents.newNotificationReceived(normalizedNotification);

        // For chat notifications, directly navigate to chat screen without showing alert
        if (extractedContent.notificationType === 'chat_message' && extractedContent.patientId) {
          console.log('NotificationManager: Directly navigating to chat from notification tap');
          navigateToChat(extractedContent.patientId, extractedContent.patientName || '');
        } else {
          // For non-chat notifications, show alert
          showToast(extractedContent.title, extractedContent.body);
        }
      });

      setNotificationListeners({
        notificationListener,
        responseListener,
      });

      // Check for initial notification that launched the app (Expo)
      const expoInitialNotification = await Notifications.getLastNotificationResponseAsync();
      if (expoInitialNotification) {
        const notification = expoInitialNotification.notification;

        console.log('NotificationManager: App launched from Expo notification:',
          JSON.stringify(notification.request.content));

        // Extract the proper notification content using our helper function
        const extractedContent = extractNotificationContent(notification.request.content);

        // Create a normalized notification with the correct title and body
        const normalizedNotification = {
          ...notification,
          request: {
            ...notification.request,
            content: {
              ...notification.request.content,
              title: extractedContent.title,
              body: extractedContent.body,
            }
          }
        };

        // Store the normalized notification for later display
        setLastNotification(normalizedNotification);
        saveLastNotification(normalizedNotification);
        addToNotificationHistory(normalizedNotification);
        
        // Emit event for real-time updates
        fcmNotificationEvents.newNotificationReceived(normalizedNotification);

        // For chat notifications, directly navigate to chat screen
        if (extractedContent.notificationType === 'chat_message' && extractedContent.patientId) {
          console.log('NotificationManager: Directly navigating to chat from cold start Expo notification');
          setTimeout(() => {
            navigateToChat(extractedContent.patientId, extractedContent.patientName || '');
          }, 1000);
        } else {
          // For all notifications, show toast with appropriate callback
          setTimeout(() => {
            showToast(
              extractedContent.title, 
              extractedContent.body,
              () => {
                // When notification is tapped, handle navigation
                if (extractedContent.notificationType === 'chat_message' && extractedContent.patientId) {
                  navigateToChat(extractedContent.patientId, extractedContent.patientName || '');
                }
              }
            );
          }, 1000);
        }
      }

      // Store Firebase unsubscribe function for cleanup
      return () => {
        unsubscribeOnMessage();
      };
    } catch (error) {
      console.error('NotificationManager: Error setting up notifications:', error);
    } finally {
      // Mark setup as complete
      setupComplete.current = true;
      
      // If caregiver ID is available, register with backend
      if (caregiverId) {
        console.log('NotificationManager: Setup complete, attempting backend registration');
        handleBackendRegistration();
      }
    }
  };

  // Extract the actual notification content from FCM data format
  const extractNotificationContent = (content: any) => {
    const { title, body, data } = content;
    
    console.log('🔍 NotificationManager: Extracting content from:', { title, body, data });

    // Check if this is an FCM notification with data format
    if (data) {
      // Handle patient chat message notifications
      if (data.type === 'chat_message' && validateChatNotificationData(data)) {
        // Log the notification for debugging
        logChatNotification(data);

        // Format the notification content
        const formattedTitle = formatChatNotificationTitle(data.patientName, data.requiresResponse);
        const formattedBody = formatChatNotificationBody(data.body, data.sentTime);

        return {
          title: formattedTitle,
          body: formattedBody,
          notificationType: 'chat_message',
          chatId: data.chatId,
          patientId: data.patientId,  // Use direct patient ID
          patientName: data.patientName,
          requiresResponse: data.requiresResponse,
          sentTime: data.sentTime
        };
      }

      // Enhanced Android FCM data extraction
      // Handle different Android notification data structures
      let extractedData = { ...data };
      
      // Check for nested data structures common in Android FCM
      if (data.data && typeof data.data === 'object') {
        extractedData = { ...extractedData, ...data.data };
      }
      
      // Check for Google's FCM format
      if (data['gcm.notification.body'] || data['gcm.notification.title']) {
        extractedData = {
          ...extractedData,
          title: data['gcm.notification.title'] || extractedData.title,
          body: data['gcm.notification.body'] || extractedData.body
        };
      }

      // Extract navigation-related data from various possible locations
      const notificationType = extractedData.type || extractedData.notificationType || extractedData.category || 'general';
      const patientId = extractedData.patientId || extractedData.patient_id || extractedData.id || extractedData.userId;
      const patientName = extractedData.patientName || extractedData.patient_name || extractedData.name;
      const route = extractedData.route || extractedData.screen || extractedData.target;

      // Determine final title and body
      const finalTitle = extractedData.title || title || 'WatchRx Caregiver';
      const finalBody = extractedData.body || body || 'New notification received';

      console.log('📱 NotificationManager: Extracted notification data:', {
        notificationType,
        patientId,
        patientName,
        route,
        finalTitle,
        finalBody
      });

      // Return comprehensive notification content
      return {
        title: finalTitle,
        body: finalBody,
        notificationType,
        patientId,
        patientName,
        route,
        // Include original data for any additional processing
        originalData: data
      };
    }

    // Default fallback for notifications without data
    return {
      title: title || 'Notification',
      body: body || '',
      notificationType: 'general'
    };
  };

  // Show chat notification with redirect option (Requirement 3)
  const showChatNotificationWithRedirect = (notification: Notifications.Notification, patientId: string, patientName?: string) => {
    const content = extractNotificationContent(notification.request.content);
    
    showToast(
      content.title,
      content.body,
      () => {
        console.log('NotificationManager: Redirecting to chat for patient:', patientId);
        navigateToChat(patientId, patientName || '');
      }
    );
  };

  // Navigate based on notification type
  const navigateBasedOnType = (extractedContent: any) => {
    const { notificationType, patientId, route } = extractedContent;
    
    try {
      console.log('🎯 NotificationManager: Navigating based on type:', notificationType, 'patientId:', patientId);
      
      switch (notificationType) {
        case 'location':
        case 'gps':
          if (patientId) {
            router.push(`/patient/map/${patientId}`);
          } else {
            router.push('/(tabs)/patients');
          }
          break;
          
        case 'alert':
        case 'critical':
          if (patientId) {
            router.push(`/patient/alerts?patientId=${patientId}`);
          } else {
            router.push('/alerts');
          }
          break;
          
        case 'medication':
        case 'medicine':
          if (patientId) {
            router.push(`/patient/medications/${patientId}`);
          } else {
            router.push('/(tabs)/patients');
          }
          break;
          
        case 'vitals':
        case 'vital':
          if (patientId) {
            router.push(`/patient/vitals/${patientId}`);
          } else {
            router.push('/(tabs)/patients');
          }
          break;
          
        case 'schedule':
        case 'appointment':
          router.push('/schedule');
          break;
          
        case 'task':
        case 'tasks':
          router.push('/tasks');
          break;
          
        case 'ccm':
          if (patientId) {
            router.push(`/patient/ccm/${patientId}`);
          } else {
            router.push('/(tabs)/patients');
          }
          break;
          
        default:
          // Default behavior - go to patient details or home
          if (patientId) {
            router.push(`/patient/${patientId}`);
          } else if (route) {
            router.push(route);
          } else {
            router.push('/(tabs)/' as any);
          }
          break;
      }
      
    } catch (error) {
      // Fallback to showing toast
      showToast('Navigation Error', 'Unable to navigate from this notification. Please try again.');
    }
  };

  // Navigate to patient chat with notification context
  const navigateToChat = (patientId: string, patientName?: string) => {
    try {
      // Validate patient ID
      if (!patientId || patientId === '') {
        showToast('Error', 'Invalid patient information. Unable to navigate to chat.');
        return;
      }
      
      const navigationPath = `/patient/chat/${patientId}`;
      console.log('NotificationManager: Navigation path:', navigationPath);
      
      // Navigate directly to chat with notification flag and refresh parameter
      router.push({
        pathname: navigationPath as any,
        params: {
          fromNotification: 'true',
          refreshChat: 'true',
          notificationTimestamp: Date.now().toString(),
          patientName: patientName || ''
        }
      });
      
      console.log('NotificationManager: Navigation initiated successfully');
    } catch (error) {
      console.error('NotificationManager: Error navigating to chat:', error);
      // Fallback to simple navigation
      try {
        router.push(`/patient/chat/${patientId}?fromNotification=true&refreshChat=true`);
      } catch (fallbackError) {
        console.error('NotificationManager: Fallback navigation also failed:', fallbackError);
        // Show error toast if all navigation attempts fail
        showToast(
          'Navigation Error',
          'Unable to open chat. Please try again.'
        );
      }
    }
  };

  // Show notification content in an alert or navigate directly
  const showNotificationContent = (notification: Notifications.Notification) => {
    // Extract notification content
    const content = extractNotificationContent(notification.request.content);

    console.log('NotificationManager: Showing notification with title:', content.title);
    console.log('NotificationManager: Showing notification with body:', content.body);
    console.log('NotificationManager: Notification type:', content.notificationType);

    if (content.title || content.body) {
      // Handle patient chat message notifications with direct navigation
      if (content.notificationType === 'chat_message' && content.patientId) {
        const patientId = content.patientId;
        
        if (!patientId) {
          console.error('NotificationManager: Invalid patient ID:', content.patientId);
          // Fall back to showing a generic toast
          showToast(content.title, content.body);
          return;
        }

        // Directly navigate to chat without showing alert
        console.log('NotificationManager: Directly navigating to chat for patient:', patientId);
        navigateToChat(patientId, content.patientName || '');
      } else {
        // Show toast notification for other notifications
        showToast(content.title, content.body);
      }
    }
  };

  // Function to show the last notification
  const showLastNotification = () => {
    if (lastNotification) {
      showNotificationContent(lastNotification);
    } else {
      showToast('No Notification', 'There are no recent notifications to display.');
    }
  };

  // Function to clear the last notification
  const clearLastNotification = async () => {
    setLastNotification(null);
    try {
      await AsyncStorage.removeItem(LAST_NOTIFICATION_KEY);
    } catch (error) {
      console.error('NotificationManager: Error clearing notification:', error);
    }
  };

  // Function to show pending notifications
  const showPendingNotifications = () => {
    if (pendingNotifications.length > 0) {
      console.log('NotificationManager: Processing pending notifications:', pendingNotifications.length);

      // Check if any pending notifications are chat messages for direct navigation
      const chatNotifications = pendingNotifications.filter(notification => {
        const content = extractNotificationContent(notification.request.content);
        return content.notificationType === 'chat_message' && content.patientId;
      });

      if (chatNotifications.length > 0) {
        // Handle the most recent chat notification with direct navigation
        const mostRecentChatNotification = chatNotifications[chatNotifications.length - 1];
        const content = extractNotificationContent(mostRecentChatNotification.request.content);
        const patientId = content.patientId;
        
        if (patientId) {
          console.log('NotificationManager: Directly navigating to chat from pending notification for patient:', patientId);
          // Clear pending notifications first
          setPendingNotifications([]);
          savePendingNotifications([]);
          
          // Navigate directly to chat
          setTimeout(() => {
            navigateToChat(patientId, content.patientName || '');
          }, 500);
          return;
        }
      }

      // Show the most recent notification for non-chat notifications
      const mostRecentNotification = pendingNotifications[pendingNotifications.length - 1];
      showNotificationContent(mostRecentNotification);

      // Clear pending notifications
      setPendingNotifications([]);
      savePendingNotifications([]);
    }
  };

  // Function to add notification to history for FCM notifications screen
  const addToNotificationHistory = async (notification: Notifications.Notification) => {
    try {
      // Validate notification before processing
      if (!isValidNotification(notification)) {
        return;
      }

      // Load existing history
      const storedNotifications = await AsyncStorage.getItem(FCM_NOTIFICATION_HISTORY_KEY);
      let notificationHistory: any[] = [];
      
      if (storedNotifications) {
        notificationHistory = JSON.parse(storedNotifications);
      }
      
      // Create notification item with better validation
      const content = notification.request.content;
      const notificationId = notification.request.identifier || `fcm-${Date.now()}-${Math.random()}`;
      
      // Check if notification already exists (prevent duplicates)
      const existingNotification = notificationHistory.find(n => n.id === notificationId);
      if (existingNotification) {
        return;
      }

      // Ensure valid timestamp (never allow 0 or null timestamps)
      let validTimestamp = notification.date || Date.now();
      if (validTimestamp === 0 || validTimestamp < 1000000000000) { // If timestamp is too small or 0
        validTimestamp = Date.now();
      }

      const notificationItem = {
        id: notificationId,
        title: (content.title && content.title.trim()) || 'WatchRx Caregiver',
        body: (content.body && content.body.trim()) || 'New notification received',
        data: content.data || {},
        timestamp: validTimestamp,
        read: false,
        type: (content.data?.type && content.data.type.trim()) || 'general',
        receivedInBackground: content.data?.receivedInBackground || false
      };

      // Additional validation for the notification item
      if (!isValidNotificationItem(notificationItem)) {
        return;
      }
      
      // Add to history (newest first)
      notificationHistory.unshift(notificationItem);
      
      // Limit history to 100 notifications
      if (notificationHistory.length > 100) {
        notificationHistory = notificationHistory.slice(0, 100);
      }
      
      // Save updated history
      await AsyncStorage.setItem(FCM_NOTIFICATION_HISTORY_KEY, JSON.stringify(notificationHistory));
      console.log('NotificationManager: Added valid notification to history:', notificationItem.title);
    } catch (error) {
      console.error('NotificationManager: Error adding notification to history:', error);
    }
  };

  // Helper function to validate notification structure
  const isValidNotification = (notification: any): notification is Notifications.Notification => {
    return !!(
      notification &&
      typeof notification === 'object' &&
      notification.request &&
      notification.request.content &&
      notification.request.identifier
    );
  };

  // Helper function to validate notification item
  const isValidNotificationItem = (item: any): boolean => {
    return !!(
      item &&
      typeof item === 'object' &&
      item.id &&
      typeof item.id === 'string' &&
      item.id.trim().length > 0 &&
      item.timestamp &&
      typeof item.timestamp === 'number' &&
      item.timestamp > 0 &&
      item.title &&
      typeof item.title === 'string' &&
      item.title.trim().length > 0 &&
      item.body &&
      typeof item.body === 'string' &&
      item.body.trim().length > 0
    );
  };

  return (
    <NotificationContext.Provider
      value={{
        lastNotification,
        pendingNotifications,
        showLastNotification,
        clearLastNotification,
        showPendingNotifications,
        currentChatPatientId,
        appState,
        addToNotificationHistory
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

// Component to handle notifications without rendering anything
export const NotificationHandler: React.FC = () => {
  // Use the notification context to ensure it's initialized
  useNotification();
  return null;
};

export default NotificationProvider;
