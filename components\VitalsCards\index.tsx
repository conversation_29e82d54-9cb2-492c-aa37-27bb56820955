import Text from "@/components/Text";
import VitalRangeBar from "./VitalRangeBar";
import { MaterialIcons, MaterialCommunityIcons, FontAwesome5 } from "@expo/vector-icons";
import moment from "moment";
import React from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";

interface VitalDataPoint {
  value: number;
  date: string;
  timestamp?: number;
  diastolic?: number;
  isCombined?: boolean;
  combinedValue?: string;
}

interface VitalCardsProps {
  title: string;
  data?: VitalDataPoint[]; // Keep for backward compatibility
  value?: string; // Single value from server
  date?: string; // Single date from server in "YYYY-MM-DD HH:mm:ss" format
  unit: string;
  min?: number;
  max?: number;
  cMin?: number;
  cMax?: number;
  onPress?: () => void;
  isPlaceholder?: boolean;
  color?: string;
}

const VitalCards = (props: VitalCardsProps) => {
  const {
    title,
    data = [],
    value,
    date,
    unit,
    min,
    max,
    cMin,
    cMax,
    onPress,
    isPlaceholder = false,
  } = props;

  // Medical-grade vital sign configuration
  const getVitalConfig = () => {
    const configs = {
      // 1. Heart Rate
      "Heart Rate": {
        icon: "favorite",
        IconComponent: MaterialIcons,
        primaryColor: "#E53E3E",
        lightColor: "#FFF5F5",
        darkColor: "#C53030",
      },
      // 2. SpO2
      "SpO2": {
        icon: "lungs",
        IconComponent: FontAwesome5,
        primaryColor: "#00B5D8",
        lightColor: "#E6FFFA",
        darkColor: "#0987A0",
      },
      "Oxygen Saturation": {
        icon: "lungs",
        IconComponent: FontAwesome5,
        primaryColor: "#00B5D8",
        lightColor: "#E6FFFA",
        darkColor: "#0987A0",
      },
      // 3. Fasting Glucose
      "Fasting Glucose": {
        icon: "bloodtype",
        IconComponent: MaterialIcons,
        primaryColor: "#3182CE",
        lightColor: "#EBF8FF",
        darkColor: "#2C5282",
      },
      "Blood Sugar": {
        icon: "bloodtype",
        IconComponent: MaterialIcons,
        primaryColor: "#3182CE",
        lightColor: "#EBF8FF",
        darkColor: "#2C5282",
      },
      // 4. Blood Pressure (Combined)
      "Blood Pressure": {
        icon: "heart-pulse",
        IconComponent: MaterialCommunityIcons,
        primaryColor: "#D53F8C",
        lightColor: "#FFF5F8",
        darkColor: "#B83280",
      },
      // 6. Steps
      "Steps": {
        icon: "directions-walk",
        IconComponent: MaterialIcons,
        primaryColor: "#38A169",
        lightColor: "#F0FFF4",
        darkColor: "#2F855A",
      },
      // Other vitals
      "Temperature": {
        icon: "thermometer-half",
        IconComponent: FontAwesome5,
        primaryColor: "#DD6B20",
        lightColor: "#FFFAF0",
        darkColor: "#C05621",
      },
      "Weight": {
        icon: "weight",
        IconComponent: FontAwesome5,
        primaryColor: "#805AD5",
        lightColor: "#FAF5FF",
        darkColor: "#6B46C1",
      },
    };

    return configs[title as keyof typeof configs] || {
      icon: "favorite",
      IconComponent: MaterialIcons,
      primaryColor: "#718096",
      lightColor: "#F7FAFC",
      darkColor: "#4A5568",
    };
  };

  const vitalConfig = getVitalConfig();

  // Process and validate data - handle both server format and array format
  const processedData = React.useMemo(() => {
    // If we have single value and date from server (new format)
    if (value && date && !isPlaceholder) {
      // Handle combined blood pressure values (e.g., "120/80")
      if (title === "Blood Pressure" && typeof value === 'string' && value.includes("/")) {
        const [systolic, diastolic] = value.split("/").map(v => parseFloat(v.trim()));
        if (!isNaN(systolic)) {
          return [{
            value: systolic, // Use systolic for calculations
            diastolic: diastolic, // Store diastolic for reference
            date: date, // Server format: "2025-05-24 13:15:00"
            timestamp: moment(date).valueOf(),
            isCombined: true,
            combinedValue: value, // Store the full "120/80" string for display
          }];
        }
      } else {
        const numericValue = parseFloat(value);
        if (!isNaN(numericValue)) {
          return [{
            value: numericValue,
            date: date, // Server format: "2025-05-24 13:15:00"
            timestamp: moment(date).valueOf(),
          }];
        }
      }
    }

    // If we have array data
    if (data && data.length > 0 && !isPlaceholder) {
      // Sort data by timestamp or date (most recent first)
      const sortedData = [...data].sort((a, b) => {
        const aTime = a.timestamp || moment(a.date).valueOf();
        const bTime = b.timestamp || moment(b.date).valueOf();
        return bTime - aTime;
      });

      // Process data and handle combined blood pressure values
      const processedSortedData = sortedData.map((item: any) => {
        // Handle combined blood pressure values (e.g., "120/80")
        if (title === "Blood Pressure" && typeof item.value === 'string' && item.value.includes("/")) {
          const [systolic, diastolic] = item.value.split("/").map((v: string) => parseFloat(v.trim()));
          if (!isNaN(systolic)) {
            return {
              ...item,
              value: systolic, // Use systolic for calculations
              diastolic: diastolic, // Store diastolic for reference
              isCombined: true,
              combinedValue: item.value, // Store the full "120/80" string for display
            };
          }
        }
        return item;
      });

      // Take the last 5 data points
      return processedSortedData.slice(0, 5);
    }

    // Fallback to placeholder data
    if (isPlaceholder) {
      return Array.from({ length: 5 }, (_, index) => ({
        value: Math.random() * 50 + 50,
        date: moment().subtract(4 - index, 'days').format('YYYY-MM-DD HH:mm:ss'),
        timestamp: moment().subtract(4 - index, 'days').valueOf(),
      }));
    }

    // No data available
    return [];
  }, [data, value, date, isPlaceholder]);

  const latestValue = processedData[0]?.value || 0;
  const previousValue = processedData[1]?.value || latestValue;
  const lastUpdated = processedData[0]?.date || null;

  // Medical-grade status assessment
  const getVitalStatus = (value: number) => {
    if (isNaN(value) || value === 0) {
      return {
        status: "NO_DATA",
        color: "#A0AEC0",
        backgroundColor: "#F7FAFC",
        borderColor: "#E2E8F0",
        text: "No Data",
        priority: 0,
      };
    }

    if (cMin !== undefined && cMax !== undefined && (value < cMin || value > cMax)) {
      return {
        status: "CRITICAL",
        color: "#E53E3E",
        backgroundColor: "#FED7D7",
        borderColor: "#FC8181",
        text: "Critical",
        priority: 3,
      };
    }

    if (min !== undefined && max !== undefined && (value < min || value > max)) {
      return {
        status: "ABNORMAL",
        color: "#D69E2E",
        backgroundColor: "#FEF5E7",
        borderColor: "#F6E05E",
        text: "Abnormal",
        priority: 2,
      };
    }

    return {
      status: "NORMAL",
      color: "#38A169",
      backgroundColor: "#C6F6D5",
      borderColor: "#9AE6B4",
      text: "Normal",
      priority: 1,
    };
  };

  const vitalStatus = getVitalStatus(latestValue);

  // Calculate trend
  const getTrend = () => {
    if (latestValue === previousValue || previousValue === 0) {
      return { direction: "stable", change: 0, icon: "trending-flat" };
    }

    const change = latestValue - previousValue;
    const percentChange = Math.abs((change / previousValue) * 100);

    if (percentChange < 2) {
      return { direction: "stable", change: 0, icon: "trending-flat" };
    }

    return {
      direction: change > 0 ? "up" : "down",
      change: percentChange,
      icon: change > 0 ? "trending-up" : "trending-down",
    };
  };

  const trend = getTrend();

  // Format time for medical context
  const formatMedicalTime = (dateString: string | null) => {
    if (!dateString) return "No data";
    
    const date = moment(dateString);
    if (!date.isValid()) return "Invalid date";
    
    const now = moment();
    const diffMinutes = now.diff(date, 'minutes');
    
    if (diffMinutes < 1) {
      return "Just now";
    } else if (diffMinutes < 60) {
      return `${diffMinutes}min ago`;
    } else if (diffMinutes < 1440) { // 24 hours
      return `${Math.floor(diffMinutes / 60)}h ago`;
    } else if (diffMinutes < 10080) { // 7 days
      return `${Math.floor(diffMinutes / 1440)}d ago`;
    } else {
      return date.format("MMM D, HH:mm");
    }
  };

  return (
    <TouchableOpacity 
      style={styles.container} 
      onPress={onPress} 
      activeOpacity={0.8}
    >
      <View style={styles.cardContent}>
        {/* Header Section */}
        <View style={styles.header}>
          <View style={styles.titleSection}>
            <View style={[styles.iconContainer, { backgroundColor: vitalConfig.lightColor }]}>
              <vitalConfig.IconComponent 
                name={vitalConfig.icon as any}
                size={20}
                color={vitalConfig.primaryColor}
              />
            </View>
            <View style={styles.titleText}>
              <Text size={15} bold color="#2D3748">
                {title}
              </Text>
              <Text size={11} color="#718096">
                {unit}
              </Text>
            </View>
          </View>

          {/* Status Badge */}
          <View style={[styles.statusBadge, { backgroundColor: vitalStatus.backgroundColor }]}>
            <Text size={10} bold color={vitalStatus.color}>
              {vitalStatus.text.toUpperCase()}
            </Text>
          </View>
        </View>

        {/* Main Value Section */}
        <View style={styles.valueSection}>
          <View style={styles.mainValue}>
            <Text size={32} bold color={vitalStatus.color} style={styles.valueText}>
              {isPlaceholder ? "---" : 
                processedData[0]?.isCombined ? 
                  processedData[0].combinedValue : 
                  latestValue.toFixed(title === "Temperature" ? 1 : 0)
              }
            </Text>
            
            {/* Trend Indicator */}
            {!isPlaceholder && trend.direction !== "stable" && (
              <View style={styles.trendContainer}>
                <MaterialIcons
                  name={trend.icon as any}
                  size={16}
                  color={
                    trend.direction === "up" ? "#E53E3E" : 
                    trend.direction === "down" ? "#38A169" : 
                    "#A0AEC0"
                  }
                />
                <Text 
                  size={10} 
                  color="#718096"
                  style={styles.trendText}
                >
                  {trend.change.toFixed(1)}%
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Past 5 Readings */}
        {processedData.length > 0 && !value && (
          <View style={styles.readingsSection}>
            <Text size={11} bold color="#4A5568" style={[styles.readingsTitle, styles.boldText]}>
              Recent Readings
            </Text>
            <View style={styles.readingsContainer}>
              {processedData.map((reading, index) => {
                const readingStatus = getVitalStatus(reading.value);
                const isLatest = index === 0;
                
                return (
                  <View key={index} style={[
                    styles.readingItem, 
                    isLatest && {
                      ...styles.latestReading,
                      borderColor: readingStatus.color,
                    }
                  ]}>
                    <View style={styles.readingValue}>
                      <Text 
                        size={reading.isCombined && reading.combinedValue && reading.combinedValue.length > 6 ? 10 : (isLatest ? 14 : 12)} 
                        bold={true}
                        color={readingStatus.color}
                        style={[
                          isLatest ? styles.latestValueText : styles.regularValueText,
                          styles.autoSizeText
                        ]}
                        numberOfLines={1}
                      >
                        {reading.isCombined ? 
                          reading.combinedValue : 
                          reading.value.toFixed(title === "Temperature" ? 1 : 0)
                        }
                      </Text>
                    </View>
                    <Text size={10} bold={true} color="#718096" style={[styles.readingTime, styles.boldText]}>
                      {moment(reading.date).format("MMM D")}
                    </Text>
                    <Text size={9} bold={true} color="#A0AEC0" style={styles.boldText}>
                      {moment(reading.date).isValid() ? moment(reading.date).format("HH:mm") : "00:00"}
                    </Text>
                  </View>
                );
              })}
            </View>
          </View>
        )}

        {/* Single Reading Display for Server Data */}
        {value && date && (
          <View style={styles.singleReadingSection}>
            <Text size={11} bold color="#4A5568" style={[styles.readingsTitle, styles.boldText]}>
              Last Reading
            </Text>
            <View style={styles.singleReading}>
              <Text size={12} color="#718096" style={styles.boldText}>
                {formatMedicalTime(date)}
              </Text>
              <Text size={10} color="#A0AEC0" style={styles.boldText}>
                {moment(date).format("MMM D, YYYY at HH:mm")}
              </Text>
            </View>
          </View>
        )}

        {/* Visual Range Bar */}
        <VitalRangeBar
          currentValue={latestValue}
          min={min}
          max={max}
          cMin={cMin}
          cMax={cMax}
          unit={unit}
          title={title}
        />

      </View>
    </TouchableOpacity>
  );
};

export default VitalCards;

const styles = StyleSheet.create({
  // Main container with medical-grade design
  container: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: "#E2E8F0",
    overflow: "hidden",
  },
  
  // Main content area
  cardContent: {
    padding: 16,
  },
  
  // Header section
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  
  titleSection: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  
  titleText: {
    flex: 1,
  },
  
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    minWidth: 60,
    alignItems: "center",
  },
  
  // Value display section
  valueSection: {
    marginBottom: 16,
  },
  
  mainValue: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  
  valueText: {
    letterSpacing: -0.5,
    lineHeight: 36,
  },
  
  trendContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F8FAFC",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  
  trendText: {
    marginLeft: 4,
  },
  

  
  // Footer section
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: "#F1F5F9",
  },
  
  timeSection: {
    flexDirection: "row",
    alignItems: "center",
  },
  
  timeText: {
    marginLeft: 4,
  },
  
  dataPoints: {
    flexDirection: "row",
    alignItems: "center",
  },
  
  dataPointsText: {
    marginLeft: 4,
  },
  
  // Recent readings section
  readingsSection: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: "#FAFBFC",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#E2E8F0",
  },
  
  readingsTitle: {
    marginBottom: 12,
  },
  
  readingsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 8,
  },
  
  readingItem: {
    flex: 1,
    alignItems: "center",
    paddingVertical: 10,
    paddingHorizontal: 6,
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#E2E8F0",
  },
  
  latestReading: {
    backgroundColor: "#FFFFFF",
    borderWidth: 3,
  },
  
  readingValue: {
    alignItems: "center",
    marginBottom: 6,
    position: "relative",
  },
  
  readingIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginTop: 3,
  },
  
  readingTime: {
    textAlign: "center",
    marginBottom: 2,
  },
  
  // Single reading section for server data
  singleReadingSection: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: "#FAFBFC",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#E2E8F0",
  },
  
  singleReading: {
    alignItems: "center",
    paddingVertical: 8,
  },
  
  // Enhanced text styles for better readability
  latestValueText: {
    fontWeight: '900', // Ultra bold for latest value
    letterSpacing: 0.5,
  },
  
  regularValueText: {
    fontWeight: '800', // Extra bold for other readings
    letterSpacing: 0.3,
  },
  
  boldText: {
    fontWeight: '800', // Extra bold for all text elements
  },
  
  autoSizeText: {
    flexShrink: 1,
    textAlign: 'center',
  },
});
