import React, {memo} from 'react';
import {
  ColorValue,
  Image,
  ImageSourcePropType,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import {Colors} from '@/constants';
import Theme from '@/constants/Theme';

interface ButtonIconProps {
  size: number;
  borderRadius?: number;
  style?: ViewStyle;
  icon: ImageSourcePropType;
  color?: string;
  onPress?: () => void;
  tintColor?: ColorValue | string;
  disabled?: boolean;
}

const ButtonIcon = memo(
  ({
    size,
    borderRadius,
    style,
    icon,
    color,
    onPress,
    tintColor,
    disabled,
  }: ButtonIconProps) => {
    return (
      <TouchableOpacity
        activeOpacity={0.54}
        style={[
          styles.container,
          style,
          {
            width: size,
            height: size,
            borderRadius: borderRadius,
            backgroundColor: color || Colors.White20,
          },
        ]}
        {...{onPress}}
        disabled={disabled}>
        <Image
          source={icon}
          style={{tintColor: tintColor, height: 40, width: 40}}
        />
      </TouchableOpacity>
    );
  },
);

export default ButtonIcon;

const styles = StyleSheet.create({
  container: {
    ...Theme.center,
  },
});
