import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { useIsFocused } from '@react-navigation/native';
import { getAlertsCountByDate, AlertsCountResponse } from '@/services/apis/apiManager';
import { fcmNotificationEvents } from '@/utils/NotificationEvents';

// Helper function to get dynamic date range (last 30 days)
const getAlertsCountDateRange = () => {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - 30);
  
  return {
    startDate: startDate.toISOString().split('T')[0], // YYYY-MM-DD format
    endDate: endDate.toISOString().split('T')[0] // YYYY-MM-DD format
  };
};

// Interface for the context - Device alerts data for dynamic 30-day period
interface AlertsCountContextType {
  messageCount: number;
  criticalCount: number;
  alertsCount: number;
  warningCount: number;
  alarmCount: number;
  infoCount: number;
  loading: boolean;
  lastUpdated: Date | null;
  refreshAlertsCount: () => Promise<void>;
}

// Create context with default values
const AlertsCountContext = createContext<AlertsCountContextType>({
  messageCount: 0,
  criticalCount: 0,
  alertsCount: 0,
  warningCount: 0,
  alarmCount: 0,
  infoCount: 0,
  loading: false,
  lastUpdated: null,
  refreshAlertsCount: async () => {},
});

// Hook to use the alerts count context
export const useAlertsCount = () => useContext(AlertsCountContext);

// Provider component
export const AlertsCountProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [messageCount, setMessageCount] = useState<number>(0);
  const [criticalCount, setCriticalCount] = useState<number>(0);
  const [warningCount, setWarningCount] = useState<number>(0);
  const [alarmCount, setAlarmCount] = useState<number>(0);
  const [infoCount, setInfoCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Get user data from Redux
  const userId = useSelector((state: any) => state?.loginReducer?.data?.userId);
  const orgId = useSelector((state: any) => state?.currentOrgIdReducer?.orgId);
  const isFocused = useIsFocused();

  // Calculate combined alerts count (Warning + Alarm + Info)
  const alertsCount = warningCount + alarmCount + infoCount;

  // Function to refresh device alerts count for dynamic 30-day period
  const refreshAlertsCount = useCallback(async () => {
    setLoading(true);
    
    try {
      const dateRange = getAlertsCountDateRange();
      
      const params = {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        userId: userId.toString(),
        orgId: orgId.toString()
      };

      // Call the centralized API directly
      const response = await getAlertsCountByDate(params);
      
      if (response.error) {
        console.error('❌ AlertsCountContext: API error:', response.error);
        throw new Error(response.error.message);
      }
      
      const alertsData = response.data;
      
      if (alertsData) {
        // Parse string values to numbers with safe parsing
        const newMessageCount = Math.max(0, parseInt(String(alertsData.messageCount || '0'), 10) || 0);
        const newCriticalCount = Math.max(0, parseInt(String(alertsData.Critical || '0'), 10) || 0);
        const newWarningCount = Math.max(0, parseInt(String(alertsData.Warning || '0'), 10) || 0);
        const newAlarmCount = Math.max(0, parseInt(String(alertsData.Alarm || '0'), 10) || 0);
        const newInfoCount = Math.max(0, parseInt(String(alertsData.Info || '0'), 10) || 0);

        
        setMessageCount(newMessageCount);
        setCriticalCount(newCriticalCount);
        setWarningCount(newWarningCount);
        setAlarmCount(newAlarmCount);
        setInfoCount(newInfoCount);
        setLastUpdated(new Date());
      } else {
        setMessageCount(0);
        setCriticalCount(0);
        setWarningCount(0);
        setAlarmCount(0);
        setInfoCount(0);
      }
    } catch (error) {
      console.error('❌ AlertsCountContext: Error refreshing alerts count:', error);
      setMessageCount(0);
      setCriticalCount(0);
      setWarningCount(0);
      setAlarmCount(0);
      setInfoCount(0);
    } finally {
      setLoading(false);
    }
  }, [userId, orgId]);

  // Refresh on mount and when screen is focused
  useEffect(() => {
    if (isFocused && userId && orgId) {
      refreshAlertsCount();
    }
  }, [isFocused, userId, orgId, refreshAlertsCount]);

  // Listen for FCM notification events to auto-refresh
  useEffect(() => {
    const handleNewNotification = () => {
      refreshAlertsCount();
    };

    const handleNotificationsChanged = () => {
      refreshAlertsCount();
    };

    // Subscribe to FCM notification events
    fcmNotificationEvents.on('new_notification', handleNewNotification);
    fcmNotificationEvents.on('notifications_changed', handleNotificationsChanged);

    return () => {
      console.log('🎧 AlertsCountContext: Cleaning up FCM notification listeners');
      fcmNotificationEvents.off('new_notification', handleNewNotification);
      fcmNotificationEvents.off('notifications_changed', handleNotificationsChanged);
    };
  }, [refreshAlertsCount]);

  // Auto-refresh every 5 minutes when app is focused (ensures dynamic 30-day window)
  useEffect(() => {
    if (!isFocused) return;

    const interval = setInterval(() => {
      refreshAlertsCount();
    }, 5 * 60 * 1000); // 5 minutes

    return () => {
      clearInterval(interval);
    };
  }, [isFocused, refreshAlertsCount]);

  // Additional daily refresh to ensure the 30-day window moves with each new day
  useEffect(() => {
    if (!isFocused) return;

    // Calculate milliseconds until next midnight
    const now = new Date();
    const tomorrow = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
    const msUntilMidnight = tomorrow.getTime() - now.getTime();

    // Set timeout for midnight, then interval for every 24 hours
    const midnightTimeout = setTimeout(() => {
      refreshAlertsCount();

      // Set up daily interval after first midnight refresh
      const dailyInterval = setInterval(() => {
        refreshAlertsCount();
      }, 24 * 60 * 60 * 1000); // 24 hours

      // Clear daily interval when component unmounts or loses focus
      return () => {
        clearInterval(dailyInterval);
      };
    }, msUntilMidnight);

    return () => {
      clearTimeout(midnightTimeout);
    };
  }, [isFocused, refreshAlertsCount]);

  const contextValue: AlertsCountContextType = {
    messageCount,
    criticalCount,
    alertsCount,
    warningCount,
    alarmCount,
    infoCount,
    loading,
    lastUpdated,
    refreshAlertsCount,
  };

  return (
    <AlertsCountContext.Provider value={contextValue}>
      {children}
    </AlertsCountContext.Provider>
  );
}; 