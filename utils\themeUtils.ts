import { ViewStyle, TextStyle, ImageStyle } from 'react-native';
import { defaultTheme } from '@/constants/Theme';
import { 
  isTablet, 
  isLargeTablet, 
  getOrientation, 
  getDeviceType 
} from './responsive';

export type StyleType = ViewStyle | TextStyle | ImageStyle;

/**
 * Get tablet-aware spacing values from theme
 */
export const getTabletSpacing = (size: keyof typeof defaultTheme.spacing) => {
  if (isTablet()) {
    return defaultTheme.tablet.enhancedSpacing[size] || defaultTheme.spacing[size];
  }
  return defaultTheme.spacing[size];
};

/**
 * Get tablet-aware typography values from theme
 */
export const getTabletTypography = (variant: keyof typeof defaultTheme.typography) => {
  return defaultTheme.typography[variant];
};

/**
 * Get tablet-aware component sizing from theme
 */
export const getTabletComponentSize = (component: keyof typeof defaultTheme.tablet.components) => {
  return defaultTheme.tablet.components[component];
};

/**
 * Get orientation-specific theme values
 */
export const getOrientationTheme = (orientation?: 'portrait' | 'landscape') => {
  const currentOrientation = orientation || getOrientation();
  return defaultTheme.tablet.orientation[currentOrientation];
};

/**
 * Create tablet-aware styles with automatic responsive adjustments
 */
export const createTabletStyles = <T extends Record<string, StyleType>>(
  phoneStyles: T,
  tabletStyles?: Partial<T>,
  largeTabletStyles?: Partial<T>
): T => {
  if (isLargeTablet() && largeTabletStyles) {
    return { ...phoneStyles, ...tabletStyles, ...largeTabletStyles };
  } else if (isTablet() && tabletStyles) {
    return { ...phoneStyles, ...tabletStyles };
  }
  return phoneStyles;
};

/**
 * Create orientation-aware styles
 */
export const createOrientationStyles = <T extends Record<string, StyleType>>(
  portraitStyles: T,
  landscapeStyles?: Partial<T>,
  orientation?: 'portrait' | 'landscape'
): T => {
  const currentOrientation = orientation || getOrientation();
  if (currentOrientation === 'landscape' && landscapeStyles) {
    return { ...portraitStyles, ...landscapeStyles };
  }
  return portraitStyles;
};

/**
 * Create comprehensive responsive styles combining device type and orientation
 */
export const createResponsiveStyles = <T extends Record<string, StyleType>>(styles: {
  phone: T;
  phonePortrait?: Partial<T>;
  phoneLandscape?: Partial<T>;
  tablet?: Partial<T>;
  tabletPortrait?: Partial<T>;
  tabletLandscape?: Partial<T>;
  largeTablet?: Partial<T>;
}): T => {
  const deviceType = getDeviceType();
  const orientation = getOrientation();
  
  let result = styles.phone;
  
  // Apply device-specific styles
  if (deviceType === 'large-tablet' && styles.largeTablet) {
    result = { ...result, ...styles.largeTablet };
  } else if ((deviceType === 'tablet' || deviceType === 'large-tablet') && styles.tablet) {
    result = { ...result, ...styles.tablet };
  }
  
  // Apply orientation-specific styles
  if (deviceType === 'phone') {
    if (orientation === 'landscape' && styles.phoneLandscape) {
      result = { ...result, ...styles.phoneLandscape };
    } else if (orientation === 'portrait' && styles.phonePortrait) {
      result = { ...result, ...styles.phonePortrait };
    }
  } else if (deviceType === 'tablet' || deviceType === 'large-tablet') {
    if (orientation === 'landscape' && styles.tabletLandscape) {
      result = { ...result, ...styles.tabletLandscape };
    } else if (orientation === 'portrait' && styles.tabletPortrait) {
      result = { ...result, ...styles.tabletPortrait };
    }
  }
  
  return result;
};

/**
 * Get tablet-aware touch target size
 */
export const getTabletTouchTarget = (size: 'minimum' | 'comfortable' | 'spacious' = 'comfortable') => {
  if (isTablet()) {
    return defaultTheme.tablet.touchTargets[size];
  }
  return defaultTheme.tablet.touchTargets.minimum; // 44px minimum for phones
};

/**
 * Get tablet-aware shadow styles
 */
export const getTabletShadow = (intensity: 'light' | 'medium' | 'heavy' = 'medium') => {
  const baseElevation = isTablet() ? 8 : 4;
  const baseShadowRadius = isTablet() ? 12 : 8;
  const baseShadowOpacity = isTablet() ? 0.15 : 0.1;
  
  const multipliers = {
    light: 0.5,
    medium: 1,
    heavy: 1.5,
  };
  
  const multiplier = multipliers[intensity];
  
  return {
    elevation: Math.round(baseElevation * multiplier),
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: Math.round(2 * multiplier),
    },
    shadowOpacity: baseShadowOpacity * multiplier,
    shadowRadius: Math.round(baseShadowRadius * multiplier),
  };
};

/**
 * Get tablet-aware border radius
 */
export const getTabletBorderRadius = (size: 'small' | 'medium' | 'large' = 'medium') => {
  const baseRadius = isTablet() ? 12 : 8;
  
  const multipliers = {
    small: 0.5,
    medium: 1,
    large: 1.5,
  };
  
  return Math.round(baseRadius * multipliers[size]);
};

/**
 * Get tablet-aware grid configuration
 */
export const getTabletGridConfig = (orientation?: 'portrait' | 'landscape') => {
  const currentOrientation = orientation || getOrientation();
  const deviceType = getDeviceType();
  
  if (deviceType === 'large-tablet') {
    return {
      columns: currentOrientation === 'landscape' ? 4 : 3,
      spacing: getTabletSpacing('lg'),
      itemMinWidth: 200,
    };
  } else if (deviceType === 'tablet') {
    return {
      columns: currentOrientation === 'landscape' ? 3 : 2,
      spacing: getTabletSpacing('md'),
      itemMinWidth: 180,
    };
  } else {
    return {
      columns: 1,
      spacing: getTabletSpacing('sm'),
      itemMinWidth: 150,
    };
  }
};

/**
 * Get tablet-aware modal configuration
 */
export const getTabletModalConfig = () => {
  if (isLargeTablet()) {
    return {
      maxWidth: 800,
      padding: 40,
      borderRadius: 24,
      marginHorizontal: 60,
    };
  } else if (isTablet()) {
    return {
      maxWidth: 600,
      padding: 32,
      borderRadius: 20,
      marginHorizontal: 40,
    };
  } else {
    return {
      maxWidth: '90%',
      padding: 24,
      borderRadius: 16,
      marginHorizontal: 20,
    };
  }
};

/**
 * Utility to create tablet-aware padding
 */
export const createTabletPadding = (
  base: number,
  tabletMultiplier: number = 1.5,
  largeTabletMultiplier: number = 2
) => {
  if (isLargeTablet()) {
    return Math.round(base * largeTabletMultiplier);
  } else if (isTablet()) {
    return Math.round(base * tabletMultiplier);
  }
  return base;
};

/**
 * Utility to create tablet-aware font sizes
 */
export const createTabletFontSize = (
  base: number,
  tabletMultiplier: number = 1.2,
  largeTabletMultiplier: number = 1.4
) => {
  if (isLargeTablet()) {
    return Math.round(base * largeTabletMultiplier);
  } else if (isTablet()) {
    return Math.round(base * tabletMultiplier);
  }
  return base;
};

export default {
  getTabletSpacing,
  getTabletTypography,
  getTabletComponentSize,
  getOrientationTheme,
  createTabletStyles,
  createOrientationStyles,
  createResponsiveStyles,
  getTabletTouchTarget,
  getTabletShadow,
  getTabletBorderRadius,
  getTabletGridConfig,
  getTabletModalConfig,
  createTabletPadding,
  createTabletFontSize,
};