import AlertsDatePicker from "@/components/DatePickerButtons/alerts-date-picker";
import Container from "@/components/Layout/Container";
import Loader from "@/components/Loader/Loader";
import NavigationHeader from "@/components/NavigationHeader";
import Text from "@/components/Text";
import { Colors } from "@/constants";
import { AlertsProps } from "@/models";
import { apiPostWithToken } from "@/services/apis/apiManager";
import URLS from "@/services/config/config";
import { useIsFocused } from "@react-navigation/native";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import Moment from "moment";
import React, { useEffect, useState } from "react";
import { Alert, StyleSheet, View, RefreshControl, StatusBar } from "react-native";
import { useSelector } from "react-redux";
import AlertsPage from "../PatientsAlerts/AlertsPage";

const CriticalAlerts = () => {
  const isFocused = useIsFocused();
  const router = useRouter();
  const [alertsList, setAlertsList] = useState<AlertsProps[]>();
  const [loader, setLoader] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const orgId = useSelector((state: any) => state?.currentOrgIdReducer?.orgId);

  const caregiverId = useSelector(
    (state: any) => state?.loginReducer?.data?.userId
  );

  const patientId = useSelector(
    (state: any) => state?.currentPatientIdReducer?.patientId
  );

  const [fromDate, setFromDate] = useState<Date>();
  const [toDate, setToDate] = useState<Date>();

  // Initialize date range on component mount
  useEffect(() => {
    const today = new Date();
    setToDate(today);
    setFromDate(Moment(today).add(-30, "days").toDate());
  }, []);

  useEffect(() => {
    if (isFocused) {
      const tDate = Moment(toDate).format("YYYY-MM-DD");
      const fDate = Moment(fromDate).format("YYYY-MM-DD");
      getAlerts(fDate, tDate);
    }
  }, [isFocused, fromDate, toDate]);

  const getAlerts = async (startDate: string, endDate: string) => {
    setLoader(true);
    const response = await apiPostWithToken(
      {
        careGiverId: caregiverId,
        startDate: startDate,
        endDate: endDate,
        orgId: orgId,
      },
      URLS.caregiverUrl + "getAlertbynurseid"
    );
    if (response?.status == 200) {
      setLoader(false);
      const alerts = response?.data?.data;
      if (alerts?.length) {
        // Filter only Critical alerts
        const criticalAlerts = alerts.filter((alert: any) => alert.alertType === 'Critical');
        
        if (patientId > 0) {
          setAlertsList(
            criticalAlerts.filter((alert: any) => {
              return alert.patientId.toString() === patientId.toString();
            })
          );
        } else {
          setAlertsList(criticalAlerts);
        }
      } else {
        setAlertsList([]);
      }
    } else {
      setLoader(false);
      const errorMessage = response?.response?.data?.responseMessage
        ? response?.response?.data?.responseMessage
        : response.message === "Network Error"
        ? "Network error. Please check your data connection."
        : response.message;
      Alert.alert("Error", errorMessage, [{ text: "Dismiss" }]);
    }
    setRefreshing(false);
  };

  const hasName = () => {
    return patientId ? false : true;
  };
  
  const handleRefresh = () => {
    setRefreshing(true);
    const tDate = Moment(toDate).format("YYYY-MM-DD");
    const fDate = Moment(fromDate).format("YYYY-MM-DD");
    getAlerts(fDate, tDate);
  };

  return (
    <Container style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.TurquoiseBlue} />
      <NavigationHeader 
        title="Critical Alerts" 
        showBackButton={true}
        showLogo={false}
        showRightLogo={true}
      />
      <Loader modalVisible={loader} />
      <View style={styles.contentContainer}>
        
        <View style={styles.datePickersContainer}>
          <View style={styles.datePickerWrapper}>
            <AlertsDatePicker
              date={fromDate}
              onDateChange={(date) => setFromDate(date)}
              label="Start Date"
            />
          </View>
          <View style={styles.dateSeparator}>
            <Ionicons name="arrow-forward" size={18} color={Colors.GrayBlue} />
          </View>
          <View style={styles.datePickerWrapper}>
            <AlertsDatePicker
              date={toDate}
              onDateChange={(date) => setToDate(date)}
              label="End Date"
            />
          </View>
        </View>

        <View style={styles.alertsContainer}>
          <AlertsPage
            data={alertsList}
            title="Critical"
            nameDisplay={hasName()}
            onRefresh={handleRefresh}
            refreshing={refreshing}
          />
        </View>
      </View>
    </Container>
  );
};

export default CriticalAlerts;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: "100%",
    backgroundColor: "#f8f9fa",
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
    marginTop: 10, // Reduced space for the header
  },
  datePickersContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  datePickerWrapper: {
    width: "46%",
  },
  dateSeparator: {
    width: "8%",
    alignItems: "center",
    justifyContent: "center",
    paddingTop: 20,
  },
  alertsContainer: {
    flex: 1,
    marginTop: 16,
  },
});
