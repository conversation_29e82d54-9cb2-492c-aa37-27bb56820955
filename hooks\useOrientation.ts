import { useState, useEffect } from 'react';
import { Dimensions } from 'react-native';
import { getOrientation } from '@/utils/responsive';

export type Orientation = 'portrait' | 'landscape';

interface OrientationData {
  orientation: Orientation;
  isPortrait: boolean;
  isLandscape: boolean;
  screenWidth: number;
  screenHeight: number;
}

/**
 * Custom hook for detecting and managing device orientation changes
 * Provides orientation state and utilities for responsive design
 */
export const useOrientation = (): OrientationData => {
  const [orientationData, setOrientationData] = useState<OrientationData>(() => {
    const { width, height } = Dimensions.get('window');
    const orientation = getOrientation();
    
    return {
      orientation,
      isPortrait: orientation === 'portrait',
      isLandscape: orientation === 'landscape',
      screenWidth: width,
      screenHeight: height,
    };
  });

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      const orientation = window.width > window.height ? 'landscape' : 'portrait';
      
      setOrientationData({
        orientation,
        isPortrait: orientation === 'portrait',
        isLandscape: orientation === 'landscape',
        screenWidth: window.width,
        screenHeight: window.height,
      });
    });

    return () => subscription?.remove?.();
  }, []);

  return orientationData;
};

/**
 * Hook for orientation-specific values
 * Returns different values based on current orientation
 */
export const useOrientationValue = <T>(portraitValue: T, landscapeValue: T): T => {
  const { isLandscape } = useOrientation();
  return isLandscape ? landscapeValue : portraitValue;
};

/**
 * Hook for device-specific and orientation-specific values
 * Provides fine-grained control over responsive values
 */
export const useResponsiveValue = <T>(values: {
  phone?: T;
  phonePortrait?: T;
  phoneLandscape?: T;
  tablet?: T;
  tabletPortrait?: T;
  tabletLandscape?: T;
  largeTablet?: T;
  default: T;
}): T => {
  const { orientation } = useOrientation();
  
  // Import inside the hook to avoid circular dependencies
  const { isTablet, isLargeTablet } = require('@/utils/responsive');
  
  if (isLargeTablet()) {
    return values.largeTablet ?? values.tablet ?? values.default;
  }
  
  if (isTablet()) {
    if (orientation === 'landscape') {
      return values.tabletLandscape ?? values.tablet ?? values.default;
    } else {
      return values.tabletPortrait ?? values.tablet ?? values.default;
    }
  }
  
  // Phone
  if (orientation === 'landscape') {
    return values.phoneLandscape ?? values.phone ?? values.default;
  } else {
    return values.phonePortrait ?? values.phone ?? values.default;
  }
};

/**
 * Hook that combines orientation detection with layout utilities
 * Provides comprehensive orientation management for components
 */
export const useOrientationLayout = () => {
  const orientationData = useOrientation();
  
  // Import utilities inside the hook to avoid circular dependencies
  const orientationUtils = require('@/utils/orientationUtils');
  
  return {
    ...orientationData,
    spacing: orientationUtils.getOrientationSpacing(orientationData.orientation),
    typography: orientationUtils.getOrientationTypography(orientationData.orientation),
    layout: orientationUtils.getOrientationLayout(orientationData.orientation),
    masterDetail: orientationUtils.getMasterDetailConfig(orientationData.orientation),
    animations: orientationUtils.getOrientationAnimations(orientationData.orientation),
    safeArea: orientationUtils.getOrientationSafeArea(orientationData.orientation),
    createStyles: <T extends any>(portraitStyles: T, landscapeStyles: T) => 
      orientationUtils.createOrientationStyles(portraitStyles, landscapeStyles, orientationData.orientation),
  };
};

export default useOrientation;