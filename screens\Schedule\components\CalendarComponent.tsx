import Layout from "@/components/Layout/Layout";
import Text from "@/components/Text";
import { Colors } from "@/constants";
import { apiPostWithToken } from "@/services/apis/apiManager";
import URLS from "@/services/config/config";
import { getBottomTabSafePadding } from "@/utils/layoutHelper";
import { Ionicons } from "@expo/vector-icons";
import moment from "moment";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { Calendar, CalendarProps } from "react-native-calendars";
import { Task } from "../types";

// Clean, minimal design system
const COLORS = {
  white: "#FFFFFF",
  gray50: "#F9FAFB",
  gray100: "#F3F4F6",
  gray200: "#E5E7EB",
  gray300: "#D1D5DB",
  gray400: "#9CA3AF",
  gray500: "#6B7280",
  gray600: "#4B5563",
  gray700: "#374151",
  gray800: "#1F2937",
  gray900: "#111827",
  blue500: "#3B82F6",
  blue600: "#2563EB",
  blue50: "#EFF6FF",
  blue100: "#DBEAFE",
  green500: "#10B981",
  green50: "#ECFDF5",
  amber500: "#F59E0B",
  amber50: "#FFFBEB",
  red500: "#EF4444",
  red50: "#FEF2F2",
  purple500: "#8B5CF6",
  purple50: "#F5F3FF",
};

const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 20,
  xl: 24,
  xxl: 32,
};

const TYPOGRAPHY = {
  xs: 12,
  sm: 14,
  base: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
};

interface CalendarComponentProps {
  tasks: Task[];
  onDateSelect: (date: Date) => void;
  onTaskSelect: (task: Task) => void;
  onTaskDelete?: (taskId: string) => void;
  isLoading: boolean;
}

const CalendarComponent: React.FC<CalendarComponentProps> = ({
  tasks,
  onDateSelect,
  onTaskSelect,
  onTaskDelete,
  isLoading,
}) => {
  const [selectedDate, setSelectedDate] = useState(
    moment().format("YYYY-MM-DD")
  );
  const [markedDates, setMarkedDates] = useState<any>({});
  const [tasksForSelectedDate, setTasksForSelectedDate] = useState<Task[]>([]);

  // Update marked dates and tasks when tasks change
  useEffect(() => {
    const marked: any = {};
    const today = moment().format("YYYY-MM-DD");

    // Mark the selected date
    marked[selectedDate] = {
      selected: true,
      selectedColor: COLORS.blue500,
      selectedTextColor: COLORS.white,
    };

    // Mark dates with tasks using clean dots
    tasks.forEach((task) => {
      // Parse the task start date safely with multiple format support
      const taskDate = moment(task.start, [
        "MM-DD-YYYY hh:mm A",
        "YYYY-MM-DDTHH:mm:ss",
        moment.ISO_8601,
      ]).format("YYYY-MM-DD");

      if (marked[taskDate]) {
        marked[taskDate].dots = [
          ...(marked[taskDate].dots || []),
          { color: getPriorityColor(task.priority) },
        ];
      } else {
        marked[taskDate] = {
          dots: [{ color: getPriorityColor(task.priority) }],
          selected: taskDate === selectedDate,
          selectedColor: taskDate === selectedDate ? COLORS.blue500 : undefined,
          selectedTextColor:
            taskDate === selectedDate ? COLORS.white : undefined,
        };
      }
    });

    // Disable previous dates
    const startOfMonth = moment(selectedDate).startOf("month");
    const endOfMonth = moment(selectedDate).endOf("month");

    for (
      let date = startOfMonth.clone();
      date.isBefore(endOfMonth);
      date.add(1, "day")
    ) {
      const dateString = date.format("YYYY-MM-DD");
      if (date.isBefore(today, "day")) {
        if (marked[dateString]) {
          marked[dateString].disabled = true;
          marked[dateString].disableTouchEvent = true;
          marked[dateString].textColor = COLORS.gray400;
        } else {
          marked[dateString] = {
            disabled: true,
            disableTouchEvent: true,
            textColor: COLORS.gray400,
          };
        }
      }
    }

    setMarkedDates(marked);

    // Filter tasks for selected date
    const filteredTasks = tasks.filter((task) => {
      // Parse the task start date safely with multiple format support
      const taskDate = moment(task.start, [
        "MM-DD-YYYY hh:mm A",
        "YYYY-MM-DDTHH:mm:ss",
        moment.ISO_8601,
      ]).format("YYYY-MM-DD");
      return taskDate === selectedDate;
    });
    setTasksForSelectedDate(filteredTasks);
  }, [tasks, selectedDate]);

  // Get priority color - Updated to match Tasks screen
  const getPriorityColor = (priority: string): string => {
    switch (priority.toLowerCase()) {
      case "low":
        return "#059669"; // Modern green
      case "medium":
        return "#D97706"; // Modern amber
      case "high":
        return "#DC2626"; // Modern red
      case "critical":
        return "#7C2D12"; // Dark red
      default:
        return Colors.TealBlue;
    }
  };

  // Get priority background color - Updated to match Tasks screen
  const getPriorityBgColor = (priority: string): string => {
    switch (priority.toLowerCase()) {
      case "low":
        return "#ECFDF5"; // Very light green
      case "medium":
        return "#FFFBEB"; // Very light amber
      case "high":
        return "#FEF2F2"; // Very light red
      case "critical":
        return "#FEF2F2"; // Very light red
      default:
        return "#F0F9FF"; // Very light blue
    }
  };

  // Handle date selection
  const handleDayPress: CalendarProps["onDayPress"] = (day: any) => {
    const selectedMoment = moment(day.dateString);
    const today = moment().startOf("day");

    if (selectedMoment.isBefore(today)) {
      return;
    }

    setSelectedDate(day.dateString);
  };

  // Handle add task
  const handleAddTask = () => {
    const selectedMoment = moment(selectedDate);
    const today = moment().startOf("day");

    if (selectedMoment.isBefore(today)) {
      return;
    }

    onDateSelect(new Date(selectedDate));
  };

  // Check if can add task
  const canAddTask = () => {
    const selectedMoment = moment(selectedDate);
    const today = moment().startOf("day");
    return !selectedMoment.isBefore(today);
  };

  // Format time - Matching Tasks screen
  const formatTime = (dateString: string): string => {
    return moment(dateString, [
      "MM-DD-YYYY hh:mm A",
      "YYYY-MM-DDTHH:mm:ss",
      moment.ISO_8601,
    ]).format("h:mm A");
  };

  // Get duration - Matching Tasks screen
  const getDuration = (task: Task): string => {
    const start = moment(task.start, [
      "MM-DD-YYYY hh:mm A",
      "YYYY-MM-DDTHH:mm:ss",
      moment.ISO_8601,
    ]);
    const end = moment(task.end, [
      "MM-DD-YYYY hh:mm A",
      "YYYY-MM-DDTHH:mm:ss",
      moment.ISO_8601,
    ]);
    const duration = moment.duration(end.diff(start));

    const hours = Math.floor(duration.asHours());
    const minutes = duration.minutes();

    if (hours > 0) {
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }
    return `${minutes}m`;
  };

  // Check if task is ongoing - Matching Tasks screen
  const isOngoing = (task: Task): boolean => {
    const now = moment();
    const start = moment(task.start, [
      "MM-DD-YYYY hh:mm A",
      "YYYY-MM-DDTHH:mm:ss",
      moment.ISO_8601,
    ]);
    const end = moment(task.end, [
      "MM-DD-YYYY hh:mm A",
      "YYYY-MM-DDTHH:mm:ss",
      moment.ISO_8601,
    ]);
    return now.isBetween(start, end);
  };

  // Check if task is upcoming - Matching Tasks screen
  const isUpcoming = (task: Task): boolean => {
    const now = moment();
    const start = moment(task.start, [
      "MM-DD-YYYY hh:mm A",
      "YYYY-MM-DDTHH:mm:ss",
      moment.ISO_8601,
    ]);
    return start.isAfter(now) && start.diff(now, "minutes") <= 30;
  };

  // Get status indicator - Matching Tasks screen
  const getStatusIndicator = (task: Task) => {
    if (isOngoing(task)) {
      return {
        color: Colors.ForestGreen,
        text: "ONGOING",
        icon: "play-circle",
      };
    }
    if (isUpcoming(task)) {
      return { color: Colors.Orange, text: "UPCOMING", icon: "time" };
    }
    return { color: Colors.GrayBlue, text: "SCHEDULED", icon: "calendar" };
  };

  // Handle delete task
  const handleDeleteTask = (task: Task) => {
    Alert.alert(
      "Delete Task",
      "Are you sure you want to delete this task? This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => deleteCalendarTask(task),
        },
      ]
    );
  };

  // Delete calendar task API call
  const deleteCalendarTask = async (task: Task) => {
    try {
      console.log("Deleting calendar task with ID:", task.id);

      // Use the calendar-specific delete API endpoint
      const response = await apiPostWithToken(
        { id: task.id },
        URLS.baseUrl + "/_ah/api/caregiverapi/v1/deleteCalendarTask"
      );

      console.log("Delete calendar task API response:", response);

      if (response?.status === 200 && response?.data?.success) {
        console.log("Calendar task deleted successfully");
        if (onTaskDelete) {
          onTaskDelete(task.id);
        }
        Alert.alert("Success", "Task deleted successfully!");
      } else {
        const errorMessage =
          response?.data?.messages?.[0] ||
          response?.error?.message ||
          response?.message === "Network Error"
            ? "Network error. Please check your data connection."
            : response?.message || "Failed to delete task";
        Alert.alert("Error", errorMessage);
      }
    } catch (error) {
      console.error("Error deleting calendar task:", error);
      Alert.alert("Error", "Failed to delete task. Please try again.");
    }
  };

  // Render task item - Updated to match Tasks screen design
  const renderTaskItem = ({ item: task }: { item: Task }) => {
    const status = getStatusIndicator(task);
    const priorityColor = getPriorityColor(task.priority);
    const priorityBgColor = getPriorityBgColor(task.priority);

    return (
      <View style={styles.container}>
        <Layout style={styles.content}>
          {/* Main Content - Tappable */}
          <TouchableOpacity
            onPress={() => onTaskSelect(task)}
            activeOpacity={0.7}
            style={styles.mainContent}
          >
            {/* Header with status and priority */}
            <View style={styles.header}>
              <View style={styles.statusContainer}>
                <View
                  style={[styles.statusDot, { backgroundColor: status.color }]}
                />
                <Text size={10} color={status.color} bold lineHeight={12}>
                  {status.text}
                </Text>
              </View>
              <View
                style={[
                  styles.priorityBadge,
                  { backgroundColor: priorityBgColor },
                ]}
              >
                <Text
                  size={9}
                  color={priorityColor}
                  bold
                  style={styles.priorityText}
                  lineHeight={11}
                >
                  {task.priority.toUpperCase()}
                </Text>
              </View>
            </View>

            {/* Task Title */}
            <View style={styles.titleSection}>
              <Text
                size={16}
                bold
                color={Colors.DarkJungleGreen}
                numberOfLines={2}
                lineHeight={18}
              >
                {task.title}
              </Text>
            </View>

            {/* Patient Info */}
            {task.patientName && (
              <View style={styles.patientSection}>
                <Ionicons name="person" size={14} color={Colors.TealBlue} />
                <Text
                  size={12}
                  color={Colors.TealBlue}
                  bold
                  style={styles.patientText}
                  lineHeight={14}
                >
                  {task.patientName}
                </Text>
              </View>
            )}

            {/* Time Information */}
            <View style={styles.timeSection}>
              <View style={styles.timeRow}>
                <Ionicons name="time" size={14} color={Colors.GrayBlue} />
                <Text
                  size={12}
                  color={Colors.GrayBlue}
                  style={styles.timeText}
                  lineHeight={14}
                >
                  {formatTime(task.start)} - {formatTime(task.end)}
                </Text>
                <View style={styles.durationBadge}>
                  <Text size={10} color={Colors.GrayBlue} lineHeight={12}>
                    {getDuration(task)}
                  </Text>
                </View>
              </View>
            </View>

            {/* Description */}
            {task.description && (
              <View style={styles.descriptionSection}>
                <Text
                  size={12}
                  color={Colors.Black68}
                  numberOfLines={2}
                  style={styles.description}
                >
                  {task.description}
                </Text>
              </View>
            )}
          </TouchableOpacity>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => onTaskSelect(task)}
              activeOpacity={0.7}
            >
              <Ionicons
                name="create-outline"
                size={16}
                color={Colors.TealBlue}
              />
              <Text
                size={12}
                color={Colors.TealBlue}
                bold
                style={styles.actionButtonText}
              >
                Edit
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.deleteButton}
              onPress={() => handleDeleteTask(task)}
              activeOpacity={0.7}
            >
              <Ionicons
                name="trash-outline"
                size={16}
                color={Colors.pastelRed}
              />
              <Text
                size={12}
                color={Colors.pastelRed}
                bold
                style={styles.actionButtonText}
              >
                Delete
              </Text>
            </TouchableOpacity>
          </View>
        </Layout>
      </View>
    );
  };

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <View style={styles.emptyIcon}>
        <Ionicons name="calendar-outline" size={48} color={COLORS.gray400} />
      </View>
      <Text style={styles.emptyTitle}>No tasks scheduled</Text>
      <Text style={styles.emptySubtitle}>
        {canAddTask()
          ? "Tap the + button to add your first task"
          : "Select today or a future date to view tasks"}
      </Text>
    </View>
  );

  return (
    <View style={styles.mainContainer}>
      {/* Calendar Section */}
      <View style={styles.calendarContainer}>
        <Calendar
          style={styles.calendar}
          theme={{
            calendarBackground: COLORS.white,
            textSectionTitleColor: COLORS.gray600,
            selectedDayBackgroundColor: COLORS.blue500,
            selectedDayTextColor: COLORS.white,
            todayTextColor: COLORS.blue500,
            dayTextColor: COLORS.gray800,
            textDisabledColor: COLORS.gray400,
            dotColor: COLORS.blue500,
            selectedDotColor: COLORS.white,
            arrowColor: COLORS.blue500,
            monthTextColor: COLORS.gray800,
            indicatorColor: COLORS.blue500,
            textDayFontWeight: "500",
            textMonthFontWeight: "600",
            textDayHeaderFontWeight: "600",
            textDayFontSize: TYPOGRAPHY.base,
            textMonthFontSize: TYPOGRAPHY.lg,
            textDayHeaderFontSize: TYPOGRAPHY.sm,
          }}
          markingType="multi-dot"
          markedDates={markedDates}
          onDayPress={handleDayPress}
          enableSwipeMonths
          hideExtraDays={false}
          minDate={moment().format("YYYY-MM-DD")}
        />
      </View>

      {/* Tasks Section */}
      <View style={styles.tasksContainer}>
        {/* Header */}
        <View style={styles.tasksHeader}>
          <View style={styles.dateInfo}>
            <Text style={styles.selectedDate}>
              {moment(selectedDate).format("MMM D, YYYY")}
            </Text>
            <Text style={styles.taskCount}>
              {tasksForSelectedDate.length}{" "}
              {tasksForSelectedDate.length === 1 ? "task" : "tasks"}
            </Text>
          </View>

          {canAddTask() && (
            <TouchableOpacity
              style={styles.addButton}
              onPress={handleAddTask}
              activeOpacity={0.7}
            >
              <Ionicons name="add" size={16} color={COLORS.white} />
              <Text style={styles.addButtonText}>Add Tasks</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Tasks List */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.blue500} />
            <Text style={styles.loadingText}>Loading tasks...</Text>
          </View>
        ) : (
          <FlatList
            data={tasksForSelectedDate}
            renderItem={renderTaskItem}
            keyExtractor={(item) => item.id}
            ListEmptyComponent={renderEmptyState}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.tasksList}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Updated styles to match Tasks screen design
  container: {
    marginBottom: 8,
  },
  content: {
    padding: 12,
    backgroundColor: Colors.White,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 4,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.06)",
    minWidth: 50,
    alignItems: "center",
  },
  titleSection: {
    marginBottom: 6,
  },
  patientSection: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 6,
  },
  patientText: {
    marginLeft: 4,
    flex: 1,
  },
  timeSection: {
    marginBottom: 6,
  },
  timeRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  timeText: {
    marginLeft: 4,
    flex: 1,
  },
  durationBadge: {
    backgroundColor: Colors.Snow,
    paddingHorizontal: 6,
    paddingVertical: 1,
    borderRadius: 4,
  },
  descriptionSection: {
    marginBottom: 4,
  },
  description: {
    lineHeight: 16,
  },
  priorityText: {
    letterSpacing: 0.5,
    fontWeight: "600",
  },
  mainContent: {
    flex: 1,
  },
  actionButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: Colors.Snow,
  },
  editButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: Colors.Snow,
    flex: 1,
    marginRight: 6,
    justifyContent: "center",
  },
  deleteButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: "#FEF2F2",
    flex: 1,
    marginLeft: 6,
    justifyContent: "center",
  },
  actionButtonText: {
    marginLeft: 4,
    fontSize: 12,
    fontWeight: "600",
  },

  // Existing styles for layout
  mainContainer: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  calendarContainer: {
    backgroundColor: COLORS.white,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray200,
  },
  calendar: {
    paddingBottom: SPACING.md,
  },
  tasksContainer: {
    flex: 1,
    backgroundColor: Colors.Snow,
  },
  tasksHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: SPACING.lg,
    backgroundColor: COLORS.white,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray200,
  },
  dateInfo: {
    flex: 1,
  },
  selectedDate: {
    fontSize: TYPOGRAPHY.lg,
    fontWeight: "600",
    color: COLORS.gray900,
    marginBottom: SPACING.xs,
  },
  taskCount: {
    fontSize: TYPOGRAPHY.sm,
    color: COLORS.gray600,
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: SPACING.sm,
    borderRadius: 12,
    backgroundColor: COLORS.blue500,
  },
  addButtonText: {
    fontSize: TYPOGRAPHY.xs,
    fontWeight: "600",
    color: COLORS.white,
    marginLeft: SPACING.xs,
  },
  tasksList: {
    paddingHorizontal: SPACING.md,
    paddingTop: SPACING.md,
    paddingBottom: getBottomTabSafePadding() + 20, // Extra padding to ensure content stays above FAB and tab bar
    flexGrow: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: SPACING.xxl,
  },
  loadingText: {
    fontSize: TYPOGRAPHY.base,
    color: COLORS.gray600,
    marginTop: SPACING.md,
  },
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: SPACING.xxl,
  },
  emptyIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.gray100,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: SPACING.xl,
  },
  emptyTitle: {
    fontSize: TYPOGRAPHY.lg,
    fontWeight: "600",
    color: COLORS.gray900,
    marginBottom: SPACING.sm,
  },
  emptySubtitle: {
    fontSize: TYPOGRAPHY.sm,
    color: COLORS.gray600,
    textAlign: "center",
    lineHeight: 20,
    paddingHorizontal: SPACING.xl,
  },
});

export default CalendarComponent;
