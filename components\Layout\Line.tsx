import React from 'react';
import {
  PressableStateCallbackType,
  View as DefaultView,
  ViewProps,
  ViewStyle,
} from 'react-native';
import {useTheme} from '@/constants/Theme';
interface Props extends ViewProps {
  children?:
    | React.ReactNode
    | ((state: PressableStateCallbackType) => React.ReactNode);
  style?: ViewStyle;
  marginBottom?: number;
  marginTop?: number;
  width?: number | string;
}

const Line = (props: Props) => {
  const {theme} = useTheme();
  return (
    <DefaultView
      style={[
        {
          height: 1,
          backgroundColor: theme.lineColor,
          marginBottom: props.marginBottom,
          marginTop: props.marginTop,
          justifyContent: 'center',
          width: props.width ? props.width : '100%',
          alignSelf: 'center',
        },
        props.style,
      ]}>
      {props.children}
    </DefaultView>
  );
};
export default Line;
