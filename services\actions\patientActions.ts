import {
  CURRENT_PATIENT_ID,
  CURRENT_PATIENT_NAME,
  FETCH_PATIENTS_LIST,
  FETCH_PATIENTS_LIST_FAILURE,
  FETCH_PATIENTS_LIST_SUCCESS,
} from './type';

export const fetchPatientsList = (payload: any) => {
  return {
    type: FETCH_PATIENTS_LIST,
    payload,
  };
};

export const fetchPatientsListSuccess = (data: any) => {
  return {
    type: FETCH_PATIENTS_LIST_SUCCESS,
    data,
  };
};

export const fetchPatientsListFailure = (error: any) => {
  return {
    type: FETCH_PATIENTS_LIST_FAILURE,
    error,
  };
};

export const setCurrentPatientId = (patientId: string) => {
  return {
    type: CURRENT_PATIENT_ID,
    patientId,
  };
};

export const setCurrentPatientName = (patientName: string) => {
  return {
    type: CURRENT_PATIENT_NAME,
    patientName,
  };
};