import React, { forwardRef } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { getMinimumTouchTargetSize } from '@/utils/accessibilityUtils';
import { isTablet } from '@/utils/responsive';

interface WithAccessibilityOptions {
  ensureMinimumTouchTarget?: boolean;
  enhanceContrast?: boolean;
  addFocusRing?: boolean;
  increaseSpacing?: boolean;
  increaseTextSize?: boolean;
}

/**
 * Higher-order component to enhance components with accessibility features
 */
export function withAccessibility<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: WithAccessibilityOptions = {
    ensureMinimumTouchTarget: true,
    enhanceContrast: false,
    addFocusRing: false,
    increaseSpacing: false,
    increaseTextSize: false,
  }
) {
  const EnhancedComponent = forwardRef<any, P & { style?: ViewStyle; onFocus?: () => void; onBlur?: () => void }>(
    (props, ref) => {
      const {
        ensureMinimumTouchTarget = true,
        enhanceContrast = false,
        addFocusRing = false,
        increaseSpacing = false,
        increaseTextSize = false,
      } = options;
      
      const [isFocused, setIsFocused] = React.useState(false);
      
      // Handle focus events
      const handleFocus = () => {
        setIsFocused(true);
        if (props.onFocus) {
          props.onFocus();
        }
      };
      
      const handleBlur = () => {
        setIsFocused(false);
        if (props.onBlur) {
          props.onBlur();
        }
      };
      
      // Calculate container style
      const containerStyle: ViewStyle = {};
      
      // Ensure minimum touch target size
      if (ensureMinimumTouchTarget) {
        const minSize = getMinimumTouchTargetSize();
        containerStyle.minWidth = minSize;
        containerStyle.minHeight = minSize;
      }
      
      // Add focus ring when focused
      if (addFocusRing && isFocused) {
        containerStyle.borderWidth = isTablet() ? 3 : 2;
        containerStyle.borderColor = '#2196F3';
        containerStyle.borderRadius = isTablet() ? 8 : 4;
      }
      
      // Increase spacing
      if (increaseSpacing) {
        containerStyle.padding = isTablet() ? 16 : 12;
        containerStyle.margin = isTablet() ? 8 : 4;
      }
      
      // Enhanced props
      const enhancedProps = {
        ...props,
        onFocus: handleFocus,
        onBlur: handleBlur,
        accessible: true,
        accessibilityRole: props.accessibilityRole || 'button',
        style: [props.style, containerStyle],
      };
      
      return (
        <WrappedComponent
          {...(enhancedProps as any)}
          ref={ref}
        />
      );
    }
  );
  
  EnhancedComponent.displayName = `withAccessibility(${WrappedComponent.displayName || WrappedComponent.name || 'Component'})`;
  return EnhancedComponent;
}

export default withAccessibility;