import React from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import ScheduleScreen from '@/screens/Schedule';

export default function ScheduleRoute() {
  return (
    // Explicitly exclude the top and bottom edges to prevent double padding with the header and remove bottom safe area
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      <ScheduleScreen />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    marginTop: 0,
    paddingTop: 0, // The ScheduleScreen will use Container which now handles padding
    paddingBottom: 0,
    position: 'relative',
    zIndex: 1,
  },
});
