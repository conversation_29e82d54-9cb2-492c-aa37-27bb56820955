{"cli": {"version": ">= 16.3.2"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}, "ios": {"simulator": true}, "env": {"EXPO_PUBLIC_API_URL": "https://watchrxapp.com"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk"}, "ios": {"simulator": false}}, "production": {"autoIncrement": false, "android": {"buildType": "app-bundle"}}, "apk": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease", "credentialsSource": "local"}}, "ipa": {"distribution": "internal", "ios": {"simulator": false}}}, "submit": {"production": {}}}