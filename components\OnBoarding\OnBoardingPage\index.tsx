import React, {memo} from 'react';
import {Image, ImageSourcePropType, StyleSheet, View} from 'react-native';
import Text from '@/components/Text';
import {Colors, Constants} from '@/constants';

interface OnboardingPageProps {
  image: ImageSourcePropType;
  title: string;
  desc: string;
  isFirstItem?: boolean;
  isLastItem?: boolean;
}

const OnboardingPage = memo(
  ({image, title, desc, isFirstItem, isLastItem}: OnboardingPageProps) => {
    return (
      <View style={styles.page}>
        <View
          style={[
            styles.container,
            isFirstItem && styles.isFirstItem,
            isLastItem && styles.isLastItem,
          ]}>
          <View style={styles.imageContainer}>
            <Image source={image} style={styles.image} resizeMode="cover" />
          </View>
          
          <View style={styles.contentContainer}>
            <Text darkJungGreen type="H2" bold style={styles.title}>
              {title}
            </Text>
            <Text darkJungGreen type="H4" style={styles.desc}>
              {desc}
            </Text>
          </View>
        </View>
      </View>
    );
  },
);

export default OnboardingPage;

const styles = StyleSheet.create({
  page: {
    width: Constants.width,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.White,
    marginHorizontal: 16,
    marginTop: 40,
    borderRadius: 24,
    shadowColor: Colors.Black,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
    overflow: 'hidden',
  },
  imageContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: (Constants.height / 812) * 420,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  contentContainer: {
    padding: 24,
    paddingTop: 20,
    paddingBottom: 32,
    minHeight: 140,
  },
  title: {
    marginBottom: 12,
    lineHeight: 32,
  },
  desc: {
    lineHeight: 24,
    opacity: 0.8,
  },
  isFirstItem: {
    borderBottomLeftRadius: 24,
  },
  isLastItem: {
    borderBottomRightRadius: 24,
  },
});
