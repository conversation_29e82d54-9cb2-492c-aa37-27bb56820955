import React, {useEffect, useState, useCallback, useMemo} from 'react';
import {Alert, Image, ScrollView, StyleSheet, View} from 'react-native';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Container from '@/components/Layout/Container';
import Loader from '@/components/Loader/Loader';
import SearchBox from '@/components/SearchBox';
import Text from '@/components/Text';
import {Colors} from '@/constants';
import {apiPostWithToken} from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import Theme from '@/constants/Theme';
import scale from '@/utils/scale';
import BillingItem from './BillingItem';

interface BillingInfoItem {
  patientId: string;
  patientName: string;
  billingStatus: string;
  [key: string]: any; // For additional billing properties
}

interface BillingInfoProps {
  navigation?: any;
}

/**
 * BillingInfo Screen
 * Displays patient billing information with search functionality
 */
const BillingInfo = (props: BillingInfoProps) => {
  const [loader, setLoader] = useState(false);
  const [billingInfo, setBillingInfo] = useState<BillingInfoItem[]>([]);
  const [filteredDataSource, setFilteredDataSource] = useState<BillingInfoItem[]>([]);
  const [searchKey, setSearchKey] = useState('');

  const caregiverId = useSelector(
    (state: {loginReducer?: {data?: {userId?: string}}}) =>
      state?.loginReducer?.data?.userId || '',
  );

  const orgId = useSelector(
    (state: {currentOrgIdReducer?: {orgId?: string}}) =>
      state?.currentOrgIdReducer?.orgId || '',
  );

  // Store billing info in AsyncStorage for offline access
  const storeBillingInfoLocally = async (data: BillingInfoItem[]) => {
    try {
      const jsonValue = JSON.stringify(data);
      await AsyncStorage.setItem('billing_info_cache', jsonValue);
      await AsyncStorage.setItem('billing_info_timestamp', new Date().toISOString());
    } catch (e) {
      // Silent error - just log it
      console.error('Error storing billing info locally:', e);
    }
  };

  // Get cached billing info
  const getCachedBillingInfo = async (): Promise<{data: BillingInfoItem[], timestamp: string} | null> => {
    try {
      const jsonValue = await AsyncStorage.getItem('billing_info_cache');
      const timestamp = await AsyncStorage.getItem('billing_info_timestamp');

      if (jsonValue && timestamp) {
        return {
          data: JSON.parse(jsonValue),
          timestamp
        };
      }
      return null;
    } catch (e) {
      return null;
    }
  };

  const getBillingInfo = useCallback(async () => {
    setLoader(true);
    try {
      // Match the original implementation exactly
      const response = await apiPostWithToken(
        {
          roleType: '5',
          userId: '' + caregiverId, // Convert to string like in original
          index: '0',
          pageSize: '1000',
          status: 'CaregiverApp',
          orgId: '' + orgId, // Convert to string like in original
        },
        URLS.caregiverUrl + 'patientAddressBillingInfo'
      );

      if (response?.status === 200) {
        setLoader(false);
        const patientsInfo = response?.data?.patientsInfo || [];
        setBillingInfo(patientsInfo);
        setFilteredDataSource(patientsInfo);

        // Store the data locally for offline access
        storeBillingInfoLocally(patientsInfo);
      } else {
        setLoader(false);
        // Handle error response
        const errorMessage = response?.response?.data?.responseMessage
          ? response?.response?.data?.responseMessage
          : response.message === 'Network Error'
          ? 'Network error. Please check your data connection.'
          : response.message || 'Unknown error occurred';

        // Try to load cached data as a fallback
        const cachedData = await getCachedBillingInfo();

        if (cachedData) {
          setBillingInfo(cachedData.data);
          setFilteredDataSource(cachedData.data);

          Alert.alert(
            'Using Cached Data',
            'Unable to connect to the server. Showing previously cached data.',
            [{text: 'OK'}]
          );
        } else {
          Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
        }
      }
    } catch (error) {
      setLoader(false);

      // Try to load cached data as a fallback
      const cachedData = await getCachedBillingInfo();

      if (cachedData) {
        setBillingInfo(cachedData.data);
        setFilteredDataSource(cachedData.data);

        Alert.alert(
          'Using Cached Data',
          'Unable to connect to the server. Showing previously cached data.',
          [{text: 'OK'}]
        );
      } else {
        Alert.alert(
          'Error',
          'An unexpected error occurred while fetching billing information.',
          [{text: 'Dismiss'}]
        );
      }
    }
  }, [caregiverId, orgId]);

  useEffect(() => {
    getBillingInfo();
  }, [getBillingInfo]);

  // Search filter implemented with useMemo for better performance
  const searchFilterFunction = useCallback((text: string) => {
    const trimmedText = text?.trim() || '';

    if (trimmedText === '') {
      setFilteredDataSource(billingInfo);
      return;
    }

    const textLower = trimmedText.toLowerCase();
    const newData = billingInfo.filter(item => {
      const patientNameLower = (item.patientName || '').toLowerCase();
      const billingStatusLower = (item.billingStatus || '').toLowerCase();

      return (
        patientNameLower.includes(textLower) ||
        billingStatusLower.includes(textLower)
      );
    });

    setFilteredDataSource(newData);
  }, [billingInfo]);

  // We'll use renderHeader as a function rather than memoized JSX
  // This allows us to directly use the SearchBox with its expected props
  const renderHeader = () => {
    return (
      <View style={styles.searchContainer}>
        <SearchBox
          placeholder={'Search patient, status'}
          value={searchKey}
          onChangeText={text => {
            if (typeof text === 'function') {
              // Handle the case where React passes a function updater
              setSearchKey(text);
            } else {
              // Handle direct string value
              setSearchKey(text);
              searchFilterFunction(text);
            }
          }}
        />
      </View>
    );
  };

  const emptyStateView = useMemo(() => (
    <View style={styles.emptyContainer}>
      <Image
        source={require('@/assets/images/Consults/img_no_upcoming.png')}
        style={styles.emptyImage}
      />
      <Text size={15} lineHeight={24} marginTop={scale(56, true)}>
        Billing Information not found!
      </Text>
    </View>
  ), []);

  return (
    <Container style={styles.container}>
      <Loader modalVisible={loader} />
      {filteredDataSource.length === 0 ? (
        emptyStateView
      ) : (
        <>
          {renderHeader()}
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}>
            {filteredDataSource.map(item => (
              <BillingItem
                index={parseInt(item.patientId) || 0}
                key={item.patientId}
                patientId={item.patientId}
                patientName={item.patientName || ''}
                billingStatus={item.billingStatus || ''}
                dob={item.dob || 'Not available'}
                noOfDays={item.noOfDays || 0}
                totalMins={item.totalMins || 0}
              />
            ))}
          </ScrollView>
        </>
      )}
    </Container>
  );
};

export default BillingInfo;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.Platinum,
  },
  searchContainer: {
    padding: 5,
    margin: 10,
  },
  emptyContainer: {
    height: '70%',
    ...Theme.center,
  },
  emptyImage: {
    width: scale(160, true),
    height: scale(160, true),
  },
  scrollContent: {
    paddingTop: 0,
    paddingHorizontal: 0,
  },
});
