import React, {memo, useCallback, useEffect, useState} from 'react';
import {
  <PERSON>ert,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {getBottomSpace} from 'react-native-iphone-x-helper';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useSelector} from 'react-redux';
import {Colors} from '@/constants';
import {Program, WatchRxPrograms} from '@/models';
import {apiPostWithToken} from '@/services/apis/apiManager';
import URLS from '@/services/config/config';

import CheckBox from '../CheckBox';
import InputApp from '../InputApp';
import Layout from '../Layout/Layout';
import Text from '../Text';

interface ModalSendMessageProps {
  close: () => void;
  patientId: number;
  loader: (status: Boolean) => void;
  programs?: WatchRxPrograms;
  connectingDevice?: string;
}

const SendMessageModal = memo((props: ModalSendMessageProps) => {
  const [note, setNote] = React.useState('');
  const [addResponseCheck, setAddResponseCheck] = useState(false);
  const [option1, setOption1] = useState('');
  const [option2, setOption2] = useState('');
  const [option3, setOption3] = useState('');
  const [selectedProgram, setSelectedProgram] = React.useState('');
  const [programs, setPrograms] = useState<Program[]>();
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  const caregiverId = useSelector(
    (state: any) => state?.loginReducer?.data?.userId,
  );

  // Initialize programs when component mounts or props.programs changes
  useEffect(() => {
    if (props?.programs?.selectedPrograms) {
      setPrograms(props.programs.selectedPrograms);
    } else {
      // Create default programs if none are provided
      const defaultPrograms = [
        {
          mins: 0,
          programName: 'RPM',
          programActivated: true,
          patientProgramId: '1',
          programId: '1'
        },
        {
          mins: 0,
          programName: 'CCM',
          programActivated: true,
          patientProgramId: '2',
          programId: '2'
        },
        {
          mins: 0,
          programName: 'PERSONAL',
          programActivated: true,
          patientProgramId: '0',
          programId: '0'
        }
      ];
      setPrograms(defaultPrograms);
    }
  }, [props.programs]);

  // Auto-select the first program
  useEffect(() => {
    if (programs && programs.length > 0 && selectedProgram === '') {
      setSelectedProgram(programs[0].programName);
    }
  }, [programs, selectedProgram]);

  const isWatchDevice = props.connectingDevice === 'watch' || props.connectingDevice === undefined;
  
  const validationCheck = () => {
    // For mobile devices, we don't require program selection validation
    if (!isWatchDevice) {
      return addResponseCheck ? (option1 && option2) : true;
    }
    return addResponseCheck ? (option1 && option2) : true;
  };

  const resetForm = useCallback(() => {
    setNote('');
    setOption1('');
    setOption2('');
    setOption3('');
    setAddResponseCheck(false);
    setSelectedProgram('');
  }, []);

  const sendMessage = useCallback(async () => {
    props?.loader(true);

    // Prepare response options if enabled
    let ans: string[] = [];
    if (addResponseCheck) {
      if (option1) ans.push(option1.trim());
      if (option2) ans.push(option2.trim());
      if (option3) ans.push(option3.trim());
    }

    const requestParams = {
      patientId: props?.patientId?.toString(),
      chatText: note?.trim(),
      isSenderServer: true,
      isSenderApp: false,
      program: selectedProgram.toLowerCase(),
    };

    const apiUrl = URLS.caregiverUrl + 'sendChatMessage';

    try {
      const response = await apiPostWithToken(requestParams, apiUrl);

      if (response?.status === 200 && response?.data?.success) {
        resetForm();
        props?.loader(false);
        props.close();
      } else {
        props?.loader(false);
        const errorMessage = response?.data?.message || response?.data?.responseMessage || 'Message may not have been sent successfully. Please check and try again.';
        Alert.alert('Warning', errorMessage, [{text: 'Dismiss'}]);
      }
    } catch (error: any) {
      props?.loader(false);
      const errorMessage = error.response?.data?.responseMessage || 'Network error. Please check your connection.';
      Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
    }
  }, [
    props,
    note,
    addResponseCheck,
    option1,
    option2,
    option3,
    selectedProgram,
    resetForm,
    caregiverId,
  ]);

  const onCancel = useCallback(() => {
    props.close();
  }, [props]);

  const onChangeText = React.useCallback((text: string) => {
    if (text.length <= 150) {
      return setNote(text);
    }
    let _text = text.substring(0, 150);
    return setNote(_text);
  }, []);

  const handleSelectProgram = (programName: string) => {
    setSelectedProgram(programName);
  };

  // Add keyboard listeners
  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (event) => {
        setIsKeyboardVisible(true);
      }
    );

    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setIsKeyboardVisible(false);
      }
    );

    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
    };
  }, []);

  // Dismiss keyboard when tapping outside input
  const dismissKeyboard = useCallback(() => {
    Keyboard.dismiss();
  }, []);

  return (
    <SafeAreaView style={styles.modalContainer}>
      <TouchableWithoutFeedback onPress={dismissKeyboard}>
        <View style={styles.modalOverlay}>
          <TouchableOpacity
            style={StyleSheet.absoluteFillObject}
            onPress={props.close}
            activeOpacity={1}
          />
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={styles.keyboardAvoidingView}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 0}
          >
            <View 
              style={styles.modalContentWrapper}
            >
              <View style={styles.modalHandle} />
              <ScrollView
                bounces={false}
                keyboardShouldPersistTaps="handled"
                contentContainerStyle={[
                  styles.scrollViewContent,
                  { paddingBottom: isKeyboardVisible ? 20 : getBottomSpace() + 8 }
                ]}
                showsVerticalScrollIndicator={false}
              >
                <Layout style={styles.modalContent}>
                  <View style={styles.modalHeader}>
                    <Text style={styles.modalTitle}>New Message</Text>
                    <TouchableOpacity
                      style={styles.closeButton}
                      onPress={props.close}
                    >
                      <Ionicons name="close" size={22} color={Colors.GrayBlue} />
                    </TouchableOpacity>
                  </View>

                  {/* Message Section */}
                  <View style={styles.section}>
                    <View style={styles.sectionHeaderRow}>
                      <Text style={styles.sectionTitle}>Message</Text>
                      <Text style={styles.charCount}>
                        {note.length}/150
                      </Text>
                    </View>

                    <View style={styles.textInputContainer}>
                      <TextInput
                        multiline
                        style={styles.textInput}
                        value={note}
                        onChangeText={onChangeText}
                        placeholder={`Send a message to ${props.connectingDevice === 'mobile' ? 'patient' : 'patient'}...`}
                        placeholderTextColor="#9DA3B4"
                        textAlignVertical="top"
                        returnKeyType="default"
                        blurOnSubmit={false}
                        maxLength={150}
                        accessible={true}
                        accessibilityLabel="Message input field"
                        accessibilityHint="Enter your message here"
                      />
                    </View>
                  </View>

                  {/* Response Options Section */}
                  <View style={styles.section}>
                    <TouchableOpacity 
                      style={styles.checkboxContainer}
                      onPress={() => setAddResponseCheck(!addResponseCheck)}
                    >
                      <CheckBox
                        isCheck={addResponseCheck}
                        onPress={() => setAddResponseCheck(!addResponseCheck)}
                      />
                      <Text style={styles.checkboxLabel}>
                        Include response options
                      </Text>
                    </TouchableOpacity>

                    {addResponseCheck && (
                      <View style={styles.responseOptions}>
                        <InputApp
                          title="Option 1"
                          value={option1}
                          onChangeText={setOption1}
                          placeholder="Enter first option"
                          marginTop={12}
                        />
                        <InputApp
                          title="Option 2"
                          value={option2}
                          onChangeText={setOption2}
                          placeholder="Enter second option"
                          marginTop={12}
                        />
                        <InputApp
                          title="Option 3"
                          value={option3}
                          onChangeText={setOption3}
                          placeholder="Enter third option (optional)"
                          marginTop={12}
                        />
                      </View>
                    )}
                  </View>

                  {/* Program Selection - Only shown for watch devices */}
                  {isWatchDevice && (
                    <View style={styles.section}>
                      <Text style={styles.sectionTitle}>Program Category</Text>
                      <View style={styles.programsGrid}>
                        {programs?.map((program, index) => (
                          <TouchableOpacity
                            key={index}
                            onPress={() => handleSelectProgram(program.programName)}
                            style={[
                              styles.programItem, 
                              selectedProgram === program.programName && styles.programItemActive
                            ]}
                          >
                            {selectedProgram === program.programName ? (
                              <Ionicons name="checkmark-circle" size={18} color={Colors.TealBlue} style={styles.checkIcon} />
                            ) : (
                              <View style={styles.emptyCircle} />
                            )}
                            <Text 
                              style={[
                                styles.programName, 
                                selectedProgram === program.programName && styles.programNameActive
                              ]}
                            >
                              {program.programName}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    </View>
                  )}

                  {/* Validation Messages */}
                  {note?.length <= 0 && (
                    <Text style={styles.validationText}>
                      Please enter a message
                    </Text>
                  )}
                  {isWatchDevice && selectedProgram === '' && note?.length > 0 && (
                    <Text style={styles.validationText}>
                      Please select a program category
                    </Text>
                  )}
                  {addResponseCheck && !validationCheck() && note?.length > 0 && (isWatchDevice ? selectedProgram !== '' : true) && (
                    <Text style={styles.validationText}>
                      Please fill in at least 2 response options
                    </Text>
                  )}

                  <View style={styles.buttonContainer}>
                    <TouchableOpacity
                      style={styles.cancelButton}
                      onPress={onCancel}
                      activeOpacity={0.7}
                    >
                      <Text style={styles.cancelButtonText}>Cancel</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[
                        styles.sendButton, 
                        (note?.length <= 0 || !validationCheck() || (isWatchDevice && selectedProgram === '')) && styles.sendButtonDisabled
                      ]}
                      onPress={sendMessage}
                      activeOpacity={0.7}
                      disabled={note?.length <= 0 || !validationCheck() || (isWatchDevice && selectedProgram === '')}
                    >
                      <Text 
                        style={[
                          styles.sendButtonText,
                          (note?.length <= 0 || !validationCheck() || (isWatchDevice && selectedProgram === '')) && styles.sendButtonTextDisabled
                        ]}
                      >
                        Send Message
                      </Text>
                    </TouchableOpacity>
                  </View>
                </Layout>
              </ScrollView>
            </View>
          </KeyboardAvoidingView>
        </View>
      </TouchableWithoutFeedback>
    </SafeAreaView>
  );
});

export default SendMessageModal;

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(24, 35, 64, 0.5)',
    justifyContent: 'flex-end',
    margin: 0,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  keyboardAvoidingView: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  modalContentWrapper: {
    backgroundColor: Colors.White,
    borderTopLeftRadius: 28,
    borderTopRightRadius: 28,
    paddingTop: 12,
    maxHeight: '90%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  scrollViewContent: {
    paddingBottom: getBottomSpace() + 8,
  },
  modalHandle: {
    width: 40,
    height: 5,
    borderRadius: 3,
    backgroundColor: '#E0E4EF',
    alignSelf: 'center',
    marginBottom: 12,
  },
  modalContent: {
    paddingHorizontal: 24,
    paddingTop: 8,
    paddingBottom: getBottomSpace() + 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingTop: 8,
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: Colors.DarkJungleGreen,
  },
  closeButton: {
    width: 34,
    height: 34,
    borderRadius: 17,
    backgroundColor: '#F5F7FB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  section: {
    marginBottom: 24,
  },
  sectionHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: Colors.DarkJungleGreen,
  },
  charCount: {
    fontSize: 13,
    color: Colors.GrayBlue,
  },
  textInputContainer: {
    borderColor: '#E0E4EF',
    borderWidth: 1,
    borderRadius: 16,
    height: 140,
    padding: 16,
    backgroundColor: '#FAFBFF',
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.DarkJungleGreen,
    textAlignVertical: 'top',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  checkboxLabel: {
    marginLeft: 12,
    fontSize: 16,
    color: Colors.DarkJungleGreen,
  },
  responseOptions: {
    marginTop: 8,
  },
  optionInput: {
    marginBottom: 12,
  },
  programsGrid: {
    marginTop: 12,
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -6,
  },
  programItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E0E4EF',
    marginHorizontal: 6,
    marginBottom: 12,
    minWidth: '45%',
  },
  programItemActive: {
    backgroundColor: '#F0F7FF',
    borderColor: Colors.TealBlue,
  },
  emptyCircle: {
    width: 18,
    height: 18,
    borderRadius: 9,
    borderWidth: 1,
    borderColor: '#9DA3B4',
    marginRight: 8,
  },
  checkIcon: {
    marginRight: 8,
  },
  programName: {
    fontSize: 15,
    color: Colors.GrayBlue,
  },
  programNameActive: {
    color: Colors.TealBlue,
    fontWeight: '600',
  },
  validationText: {
    fontSize: 14,
    color: '#FF6B6B',
    marginBottom: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  cancelButton: {
    flex: 1,
    height: 56,
    borderRadius: 16,
    backgroundColor: '#F5F7FB',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.GrayBlue,
  },
  sendButton: {
    flex: 1,
    height: 56,
    borderRadius: 16,
    backgroundColor: Colors.TealBlue,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: '#E0E4EF',
  },
  sendButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.White,
  },
  sendButtonTextDisabled: {
    color: '#9DA3B4',
  },
});
