import { Colors, Routes } from "@/constants";
import { PatientItemProps } from "@/models";
import { formatPatientDOB } from "@/utils/dateFormatter";
import {
  Feather,
  FontAwesome5,
  Ionicons,
  MaterialCommunityIcons,
  MaterialIcons,
} from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import moment from "moment";
import React, { memo, useCallback, useEffect, useRef, useState } from "react";
import {
  Alert,
  Animated,
  Dimensions,
  Linking,
  Modal,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import Layout from "../Layout/Layout";
import Loader from "../Loader/Loader";
import SendMessageModal from "../Modals/SendMessage";
import Text from "../Text";

const { width, height } = Dimensions.get("window");
const isIOS = Platform.OS === "ios";

const PatientProfile = memo((props: PatientItemProps) => {
  const [sendMessageModal, setSendMessageModal] = useState(false);
  const navigation = useNavigation() as any;
  const router = useRouter();

  // Animation values for touch feedback
  const videoButtonScale = useRef(new Animated.Value(1)).current;
  const callButtonScale = useRef(new Animated.Value(1)).current;
  const chatButtonScale = useRef(new Animated.Value(1)).current;
  const summaryButtonScale = useRef(new Animated.Value(1)).current;

  const openModal = useCallback(() => {
    setSendMessageModal(true);
  }, []);

  const closeModal = useCallback(() => {
    setSendMessageModal(false);
  }, []);

  const [loader, setLoader] = useState(false);

  const handleLoader = (status: any) => {
    setLoader(status);
  };

  // Animation functions for button press
  const animateButtonPress = (animatedValue: Animated.Value) => {
    Animated.sequence([
      Animated.timing(animatedValue, {
        toValue: 0.92,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handleVideoCall = useCallback(() => {
    animateButtonPress(videoButtonScale);
    try {
      // Use the new Expo Router navigation
      if (!props.patientId) {
        Alert.alert("Error", "Patient ID is required for video call");
        return;
      }

      // Ensure patientId is a string
      const patientIdStr = String(props.patientId);
      router.push(`/patient/video-call/${patientIdStr}`);
    } catch (error) {
      console.error("Error navigating to video call:", error);
      // Fallback to old navigation
      navigation.navigate(Routes.PatientVideoCall, {
        patientId: props.patientId,
        patientName: props.patientName,
      });
    }
  }, [props.patientId, props.patientName]);

  const handlePhoneCall = useCallback(() => {
    animateButtonPress(callButtonScale);
    
    if (!props.patientId) {
      Alert.alert(
        "Error",
        "Patient ID is required to start a voice call.",
        [{ text: "OK" }]
      );
      return;
    }

    // Direct navigation to pre-call screen without confirmation popup
    try {
      const patientIdStr = String(props.patientId);
      router.push(`/patient/voip-call/${patientIdStr}`);
    } catch (error) {
      console.error("Error navigating to VOIP call:", error);
      Alert.alert(
        "Navigation Error",
        "Unable to start voice call. Please try again.",
        [{ text: "OK" }]
      );
    }
  }, [props.patientId, props.patientName]);

  const handleChat = useCallback(() => {
    animateButtonPress(chatButtonScale);
    try {
      // Use the new Expo Router navigation
      router.push(`/patient/chat/${props.patientId}`);
    } catch (error) {
      // Fallback to old navigation if needed
      navigation.navigate(Routes.ChatDetail, {
        patientId: props.patientId,
        patientName: props.patientName,
        programs: props.programs,
      });
    }
  }, [props.patientId, props.patientName, props.programs]);

  const handleSummary = useCallback(() => {
    animateButtonPress(summaryButtonScale);
    // Navigate to patient summary screen using navigation prop
    navigation.navigate("patient-summary", {
      patientId: props.patientId,
      patientName: props.patientName,
    });
  }, [props.patientId, props.patientName, navigation]);

  // Animation values for profile elements - simplified for performance
  const profileOpacity = useRef(new Animated.Value(0)).current;
  const actionsOpacity = useRef(new Animated.Value(0)).current;

  // Animate profile elements on mount - faster and simpler animations
  useEffect(() => {
    Animated.parallel([
      Animated.timing(profileOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(actionsOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Format phone number for better readability
  const formatPhoneNumber = (phoneNumber: string) => {
    if (!phoneNumber) return "No phone available";

    // Remove all non-numeric characters
    const cleaned = phoneNumber.replace(/\D/g, "");

    // Format as (XXX) XXX-XXXX if 10 digits
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(
        6
      )}`;
    }

    // Otherwise return as is
    return phoneNumber;
  };

  // Format MRN for better display
  const formatMRN = (mrn: string) => {
    if (!mrn) return "Not available";

    // If MRN is numeric and less than 6 digits, pad with leading zeros
    if (/^\d+$/.test(mrn) && mrn.length < 6) {
      return mrn.padStart(6, "0");
    }

    return mrn;
  };

  // Format date for better readability using the new utility
  const formatDate = (dateString: string) => {
    return formatPatientDOB(dateString);
  };

  // Get status text based on reachable status
  const getStatusText = () => {
    return props.reachableStatus ? "Online" : "Offline";
  };

  return (
    <Layout style={styles.container}>
      <Loader modalVisible={loader} />

      {/* Compact profile section with minimal styling */}
      <LinearGradient
        colors={[Colors.White, Colors.White]}
        style={styles.gradientContainer}
      >
        <Animated.View
          style={[styles.contentContainer, { opacity: profileOpacity }]}
        >
          {/* Patient name centered with status indicator */}
          <View style={styles.nameContainer}>
            <View style={styles.nameInnerContainer}>
              <Text
                size={18}
                lineHeight={22}
                color={Colors.DarkJungleGreen}
                bold
                center
                numberOfLines={1}
                style={{ fontFamily: "DMSans-Bold" }}
              >
                {props.patientName}
              </Text>
            </View>
          </View>

          {/* Patient details with DOB and Phone side by side */}
          <View style={styles.detailsContainer}>
            {/* DOB and Phone in one row with proper alignment */}
            <View style={styles.detailsRow}>
              {/* DOB */}
              <View style={styles.detailItemHalf}>
                <View style={styles.detailItemContent}>
                  <MaterialCommunityIcons
                    name="calendar-account"
                    size={16}
                    color={Colors.TealBlue}
                  />
                  <Text
                    size={12}
                    lineHeight={16}
                    color={Colors.DarkJungleGreen}
                    marginLeft={4}
                    numberOfLines={1}
                  >
                    {moment(props.dob, "MM-DD-YYYY").format("MMMM D, YYYY")}
                  </Text>
                </View>
              </View>

              {/* Phone */}
              <View style={styles.detailItemHalf}>
                <View style={styles.detailItemContent}>
                  <Feather name="phone" size={16} color={Colors.TealBlue} />
                  <Text
                    size={12}
                    lineHeight={16}
                    color={Colors.DarkJungleGreen}
                    marginLeft={4}
                    numberOfLines={1}
                  >
                    {formatPhoneNumber(props.phone || "")}
                  </Text>
                </View>
              </View>
            </View>

            {/* MRN in its own row with highlight box */}
            <View style={styles.mrnContainer}>
              <View style={styles.mrnHighlightBox}>
                <FontAwesome5
                  name="file-medical"
                  size={14}
                  color={Colors.TealBlue}
                />
                <Text
                  size={12}
                  lineHeight={16}
                  color={Colors.DarkJungleGreen}
                  marginLeft={4}
                  bold
                  numberOfLines={1}
                >
                  MRN: {props.mrn ? formatMRN(props.mrn) : "Not available"}
                </Text>
              </View>
            </View>
          </View>
        </Animated.View>

        {/* Floating action buttons with titles */}
        <Animated.View
          style={[styles.floatingActionsContainer, { opacity: actionsOpacity }]}
        >
          <TouchableOpacity
            style={styles.floatingButton}
            onPress={handleVideoCall}
            activeOpacity={0.7}
          >
            <View style={styles.floatingButtonInner}>
              <Ionicons name="videocam" size={20} color={Colors.White} />
            </View>
            <Text size={10} color={Colors.GrayBlue} center marginTop={4}>
              Video
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.floatingButton}
            onPress={handlePhoneCall}
            activeOpacity={0.7}
          >
            <View style={styles.floatingButtonInner}>
              <Ionicons name="call" size={20} color={Colors.White} />
            </View>
            <Text size={10} color={Colors.GrayBlue} center marginTop={4}>
              Voice
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.floatingButton}
            onPress={handleChat}
            activeOpacity={0.7}
          >
            <View style={styles.floatingButtonInner}>
              <Ionicons name="chatbubble" size={20} color={Colors.White} />
            </View>
            <Text size={10} color={Colors.GrayBlue} center marginTop={4}>
              Chat
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.floatingButton}
            onPress={handleSummary}
            activeOpacity={0.7}
          >
            <View style={styles.floatingButtonInner}>
              <MaterialIcons name="summarize" size={20} color={Colors.White} />
            </View>
            <Text size={10} color={Colors.GrayBlue} center marginTop={4}>
              Summary
            </Text>
          </TouchableOpacity>
        </Animated.View>
      </LinearGradient>

      <Modal
        visible={sendMessageModal}
        onRequestClose={closeModal}
        transparent
        animationType={"fade"}
      >
        <SendMessageModal
          close={closeModal}
          patientId={props?.patientId}
          loader={handleLoader}
          programs={props?.programs}
        />
      </Modal>
    </Layout>
  );
});

export default PatientProfile;

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
    backgroundColor: Colors.White,
    width: "100%",
    overflow: "hidden",
    marginTop: 10,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  gradientContainer: {
    width: "100%",
    paddingVertical: 12,
    paddingHorizontal: 12,
  },
  contentContainer: {
    width: "100%",
  },
  nameContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 10,
  },
  nameInnerContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginLeft: 8,
  },
  detailsContainer: {
    marginBottom: 10,
  },
  detailsRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  detailItemHalf: {
    flex: 1,
    paddingHorizontal: 4,
    alignItems: "center",
  },
  detailItemContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  mrnContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 4,
    marginTop: 4,
  },
  mrnHighlightBox: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#E6F7FF", // Light cyan color
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: Colors.TealBlue,
  },
  floatingActionsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingTop: 0,
    marginTop: 2,
    paddingHorizontal: 4,
  },
  floatingButton: {
    marginHorizontal: 4,
    alignItems: "center",
  },
  floatingButtonInner: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.TealBlue,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "rgba(0,0,0,0.3)",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
});
