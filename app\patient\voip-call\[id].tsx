import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams } from 'expo-router';
import { useDispatch, useSelector } from 'react-redux';

import { TwilioVOIPCall } from '@/screens/PatientDetails/VideoCall/TwilioVOIPCall';
import { setCurrentPatientId } from '@/services/actions/patientActions';
import { Colors } from '@/constants';

export default function VOIPCallRoute() {
  const { id } = useLocalSearchParams();
  const dispatch = useDispatch();
  
  // Convert id to a proper format (string or number)
  const patientId = Array.isArray(id) ? id[0] : id;
  
  // Get patient data from Redux store
  const patientsList = useSelector((state: any) => state?.patientsListReducer?.data);
  const [patientData, setPatientData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [programOptions, setProgramOptions] = useState<Array<{programName: string, programId: string, mins: number, programActivated: boolean}>>([]);

  // Effect to find patient data from the patients list
  useEffect(() => {
    console.log('VOIPCallRoute: Searching for patient in list:', {
      patientsList: patientsList?.length,
      searchingForId: patientId,
      searchingForIdType: typeof patientId
    });
    
    if (patientsList && patientsList.length > 0 && patientId) {
      // Convert both IDs to strings for comparison
      const searchId = String(patientId);
      const patient = patientsList.find((p: any) => String(p.patientId) === searchId);
      
      if (patient) {
        console.log('VOIPCallRoute: Found patient data:', patient);
        setPatientData(patient);
      } else {
        console.log('VOIPCallRoute: No patient found with ID:', searchId);
        console.log('VOIPCallRoute: Available patient IDs:', patientsList.map((p: any) => ({
          id: p.patientId,
          type: typeof p.patientId
        })));
      }
    } else {
      console.log('VOIPCallRoute: Cannot search for patient:', {
        hasPatientsList: Boolean(patientsList),
        patientsListLength: patientsList?.length,
        hasPatientId: Boolean(patientId)
      });
    }
    setLoading(false);
  }, [patientsList, patientId]);

  // Extract program data from patient details
  useEffect(() => {
    console.log('VOIPCallRoute: patientData updated:', patientData);
    if (patientData && patientData.programs) {
      console.log('VOIPCallRoute: Found patient programs:', patientData.programs);
      
      // If patient has selected programs, show only those
      if (patientData.programs.selectedPrograms?.length > 0) {
        console.log('VOIPCallRoute: Using selected programs:', patientData.programs.selectedPrograms);
        setProgramOptions(patientData.programs.selectedPrograms);
      }
      // Otherwise show all available programs
      else if (patientData.programs.availablePrograms?.length > 0) {
        console.log('VOIPCallRoute: No selected programs, using available programs:', patientData.programs.availablePrograms);
        setProgramOptions(patientData.programs.availablePrograms);
      }
      // If no programs at all, show empty array
      else {
        console.log('VOIPCallRoute: No programs found');
        setProgramOptions([]);
      }
    } else {
      console.log('VOIPCallRoute: No patient data or programs found:', { patientData });
      setProgramOptions([]);
    }
  }, [patientData]);

  useEffect(() => {
    try {
      console.log('VOIPCallRoute: Received ID parameter:', id);
      if (id) {
        console.log('VOIPCallRoute: Setting current patient ID:', id);
        dispatch(setCurrentPatientId(id as string));
      } else {
        console.warn('VOIPCallRoute: No ID parameter received');
        setError('No patient ID provided');
      }
    } catch (err) {
      console.error('VOIPCallRoute: Error in useEffect:', err);
      setError('Error initializing VOIP call');
    }
  }, [id, dispatch]);

  // Only show loading state while fetching initial patient data
  if (loading) {
    return (
      <SafeAreaView style={styles.container} edges={['left', 'right']}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.statusText}>Loading patient data...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container} edges={['left', 'right']}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      <TwilioVOIPCall 
        patientId={patientId as string}
        patientName={patientData?.patientName || `Patient ${patientId}`}
        programOptions={programOptions}
        onCallEnd={() => {
          // Handle call end - could navigate back or show a summary
          console.log('VOIPCallRoute: Call ended');
        }}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  statusText: {
    fontSize: 16,
    color: '#007AFF',
    textAlign: 'center',
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: Colors.red,
    textAlign: 'center',
  },
}); 