import WatchRxDatePicker from "@/components/DatePickerButtons/date-picker";
import DatePickerLeftIcon from "@/components/DatePickerButtons/date-picker-left-arrow";
import DatePickerRightIcon from "@/components/DatePickerButtons/date-picker-right-arrow";
import Container from "@/components/Layout/Container";
import Loader from "@/components/Loader/Loader";
import Text from "@/components/Text";
import VitalCard from "@/components/VitalCard";
import { Colors } from "@/constants";
import Constants, { width } from "@/constants/Const";
import Theme from "@/constants/Theme";
import { apiPostWithToken } from "@/services/apis/apiManager";
import URLS from "@/services/config/config";
import scale from "@/utils/scale";
import { useFocusEffect, useRoute, useNavigation } from "@react-navigation/native";
import { useLocalSearchParams } from "expo-router";
import Moment from "moment";
import React, { memo, useEffect, useState, useLayoutEffect } from "react";
import { useCCMContentContext } from "@/context/CCMContentContext";
import {
  Alert,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useSelector } from "react-redux";

const VitalReportItem: React.FC<any> = memo((props: any) => {
  const [loader, setLoader] = useState(true);
  const route = useRoute();
  const navigation = useNavigation();
  const { setVitalsTitle, setDynamicTitle } = useCCMContentContext();
  const [focusDate, setFocusDate] = useState<Date>(new Date());
  const [title, setTitle] = useState("") as any;
  // Keep these states for backward compatibility with existing API
  const [measureDates, setMeasureDates] = useState<any[] | []>([]);
  const [customDatesRange, setCustomDatesRange] = useState<any[] | []>([]);
  const [measuredValues, setMeasuredValues] = useState<any[] | []>([]);
  const [measuredValues2, setMeasuredValues2] = useState<any[] | []>([]);
  const [sleepMonitorData, setSleepMonitorData] = useState<any[] | []>([]);

  // State for the new API data - will be used when API is ready
  const [dailyVitalData, setDailyVitalData] = useState<any[] | []>([]);

  const [min, setMin] = useState(0);
  const [max, setMax] = useState(0);
  const [cMin, setCMin] = useState(0);
  const [cMax, setCMax] = useState(0);
  const [unit, setUnit] = useState("");

  const patientId = useSelector(
    (state: any) => state?.currentPatientIdReducer?.patientId
  );

  const patientName = useSelector(
    (state: any) => state?.currentPatientNameReducer?.patientName
  );
  // Get the vitalId from URL params
  const params = useLocalSearchParams();
  const vitalId = params.id as string;

  // Set the navigation header title to match the selected vital type using context
  useLayoutEffect(() => {
    if (title && patientName) {
      const vitalsHeaderTitle = `${title}`;
      console.log('Vitals Report: Setting title to:', vitalsHeaderTitle);
      setVitalsTitle(vitalsHeaderTitle);
      setDynamicTitle(vitalsHeaderTitle);
      navigation.setOptions({
        title: vitalsHeaderTitle
      });
    } else if (title) {
      console.log('Vitals Report: Setting title to:', title);
      setVitalsTitle(title);
      setDynamicTitle(title);
      navigation.setOptions({
        title: title
      });
    }
  }, [navigation, title, patientName, setVitalsTitle, setDynamicTitle]);

  // Clean up the title when the component unmounts
  useEffect(() => {
    return () => {
      setVitalsTitle('');
      setDynamicTitle('');
    };
  }, [setVitalsTitle, setDynamicTitle]);

  // Legacy navigation setup for React Navigation (kept for compatibility)
  useEffect(() => {
    // Only use this with React Navigation, not with Expo Router
    if (props.navigation && props.navigation.setOptions) {
      props.navigation.setOptions({
        headerTitle: () => (
          <View
            style={{
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Text medium size={16} color={"#000"}>
              {patientName}
              {" " + title}
            </Text>
          </View>
        ),
      });
    }
  }, [title, props.navigation, patientName]);

  // Get URL params outside the useFocusEffect to avoid hook rule violation
  const urlParams = useLocalSearchParams();

  useFocusEffect(
    React.useCallback(() => {
      // Get vital type from various possible sources
      let vitalType = "";

      // Order of precedence for getting the vital type:
      // 1. title parameter from URL params (Expo Router)
      // 2. title from route.params (React Navigation)
      // 3. Map from ID if title not provided directly
      // 4. Default to 'Heart Rate' if all else fails

      // First check props.title (direct prop)
      if (props.title) {
        vitalType = props.title;
        console.log("Got vital type from props:", vitalType);
      }
      // Then check URL params (Expo Router)
      else if (urlParams.title) {
        vitalType = urlParams.title as string;
        console.log("Got vital type from URL params:", vitalType);
      }
      // Then try route params (React Navigation)
      else {
        const obj = route?.params as any;
        if (obj?.title) {
          vitalType = obj.title;
          console.log("Got vital type from route params:", vitalType);
        }
      }

      // If we still don't have a vital type, try to map from ID
      if (!vitalType) {
        // First try props.vitalId
        if (props.vitalId) {
          // Map the vitalId to a title
          switch (props.vitalId) {
            case "1":
              vitalType = "Blood Pressure";
              break;
            case "2":
              vitalType = "Blood Sugar";
              break;
            case "3":
              vitalType = "Heart Rate";
              break;
            case "4":
              vitalType = "Pedometer";
              break;
            case "5":
              vitalType = "Oxygen Saturation";
              break;
            case "6":
              vitalType = "Temperature";
              break;
            case "7":
              vitalType = "Sleep Monitor";
              break;
            case "8":
              vitalType = "Weight";
              break;
            default:
              vitalType = "Heart Rate"; // Default
          }
          console.log("Mapped vital type from props.vitalId:", vitalType);
        }
        // Then try URL vitalId
        else if (vitalId) {
          // Map the vitalId to a title
          switch (vitalId) {
            case "1":
              vitalType = "Blood Pressure";
              break;
            case "2":
              vitalType = "Blood Sugar";
              break;
            case "3":
              vitalType = "Heart Rate";
              break;
            case "4":
              vitalType = "Pedometer";
              break;
            case "5":
              vitalType = "Oxygen Saturation";
              break;
            case "6":
              vitalType = "Temperature";
              break;
            case "7":
              vitalType = "Sleep Monitor";
              break;
            case "8":
              vitalType = "Weight";
              break;
            default:
              vitalType = "Heart Rate"; // Default
          }
          console.log("Mapped vital type from URL vitalId:", vitalType);
        }
      }

      // If we still don't have a vital type after all checks, default to Heart Rate
      if (!vitalType) {
        vitalType = "Heart Rate";
        console.log("Using default vital type:", vitalType);
      } else {
        console.log("Using vital type:", vitalType);
      }

      setTitle(vitalType);

      if (vitalType === "Heart Rate") {
        setUnit("Bpm");
      } else if (vitalType === "Oxygen Saturation") {
        setUnit("%");
      } else if (vitalType === "Temperature") {
        setUnit("°F");
      } else if (vitalType === "Blood Pressure") {
        setUnit("mmHg");
      } else if (vitalType === "Weight") {
        setUnit("lbs");
      } else if (vitalType === "Blood Sugar") {
        setUnit("mg/dl");
      } else if (vitalType === "Pedometer") {
        setUnit("steps");
      } else if (vitalType === "Sleep Monitor") {
        setUnit("Hrs");
      }

      // Call the placeholder function for the new API
      // This will be replaced with the actual API call when it's ready
      getVitalDataByDate(vitalType);

      // For now, we'll still call the old API functions to get min/max values
      // and to maintain backward compatibility
      if (vitalType === "Pedometer") {
        getPedoMeterVitalData();
      } else if (vitalType === "Sleep Monitor") {
        getSleepMonitorVitalData();
      } else {
        getVitalReportByPatientIdAndType(vitalType);
      }
    }, [focusDate, vitalId, props.vitalId])
  );

  useEffect(() => {
    const obj = route?.params as any;
    setTitle(obj?.title);
    console.log("Title", title);
  }, [title]);

  const setNewDate = (date: Date) => {
    if (date.getTime() <= new Date().getTime()) {
      setFocusDate(date);
    }
  };

  const invalidNextDate =
    Moment(focusDate).add(1, "days").toDate().getTime() > new Date().getTime();

  const getCustomDate = (date: any) => {
    return Moment(date).format("dd");
  };

  const getCustomDateForSLeep = (date: any) => {
    return Moment(date, "MM-DD-YYYY").format("dd");
  };

  const getVitalReportByPatientIdAndType = async (vitalType: string) => {
    setLoader(true);
    const vitalTypeRequest: any[] = [];
    if (vitalType === "Blood Pressure") {
      vitalTypeRequest.push("Systolic Blood Pressure");
      vitalTypeRequest.push("Diastolic Blood Pressure");
    } else if (vitalType === "Blood Sugar") {
      vitalTypeRequest.push("Fasting Blood Sugar");
      vitalTypeRequest.push("Random Blood Sugar");
    } else {
      vitalTypeRequest.push(vitalType);
    }
    const requestDate = Moment(focusDate).format("YYYY-MM-DD");
    const response = await apiPostWithToken(
      {
        patientId: patientId,
        vitalTypeNameList: vitalTypeRequest,
        periodType: "WEEKLY",
        requestedDate: requestDate,
      },
      URLS.caregiverUrl + "vitalByPatient"
    );
    if (response?.status == 200) {
      setLoader(false);
      console.log("Data:", JSON.stringify(response?.data));
      if (response?.data?.success) {
        try {
          // Safely handle measured dates
          if (
            response?.data?.measuredDates &&
            Array.isArray(response.data.measuredDates) &&
            response.data.measuredDates.length > 0
          ) {
            const dates = response.data.measuredDates;
            setCustomDatesRange(dates);
            const customDates: any[] = [];
            dates.forEach((item: any) => {
              if (item) {
                customDates.push(getCustomDate(item));
              }
            });
            setMeasureDates(customDates);
          } else {
            // Reset if no data
            setCustomDatesRange([]);
            setMeasureDates([]);
          }

          // Safely handle vital count data
          const vitalsGraphData = response?.data?.vitalsCountGraphVOs;
          if (
            vitalsGraphData &&
            Array.isArray(vitalsGraphData) &&
            vitalsGraphData.length > 0
          ) {
            // Set primary values
            setMeasuredValues(vitalsGraphData[0]?.counts || []);

            // Set secondary values if applicable
            if (
              (vitalType === "Blood Pressure" || vitalType === "Blood Sugar") &&
              vitalsGraphData.length > 1
            ) {
              setMeasuredValues2(vitalsGraphData[1]?.counts || []);
            } else {
              setMeasuredValues2([]);
            }
          } else {
            setMeasuredValues([]);
            setMeasuredValues2([]);
          }

          // Safely handle threshold config
          setMinMaxValues(response?.data?.thresholdConfig || {});
        } catch (error) {
          console.error("Error processing vital data:", error);
          // Set defaults in case of error
          setMeasuredValues([]);
          setMeasuredValues2([]);
          setMinMaxValues({});
        }
      }
    } else {
      setLoader(false);
      const errorMessage = response?.response?.data?.responseMessage
        ? response?.response?.data?.responseMessage
        : response.message === "Network Error"
        ? "Network error. Please check your data connection."
        : response.message;
      Alert.alert("Error", errorMessage, [{ text: "Dismiss" }]);
    }
  };

  const setMinMaxValues = (item: any) => {
    // Enhanced null check to prevent "Cannot convert undefined value to object" error
    if (!item) {
      console.log("Warning: thresholdConfig is undefined");
      // Set default values when no data is available
      setMin(0);
      setMax(0);
      setCMin(0);
      setCMax(0);
      return;
    }

    // Handle case where item might not have eList property or it's not an array
    if (!item.eList || !Array.isArray(item.eList) || item.eList.length === 0) {
      console.log(
        "Warning: thresholdConfig.eList is undefined, not an array, or empty"
      );
      // Try to get values directly from item if available
      setMin(item.vitalMin || 0);
      setMax(item.vitalMax || 0);
      setCMin(item.vitalCriticalMin || 0);
      setCMax(item.vitalCriticalMax || 0);
      return;
    }

    const data: any[] = item.eList;
    // Use the first item in the array (most common case)
    const d = data[0];
    if (d) {
      setMin(d.vitalMin || 0);
      setMax(d.vitalMax || 0);
      setCMin(d.vitalCriticalMin || 0);
      setCMax(d.vitalCriticalMax || 0);
    } else {
      // Set defaults if no valid data found
      setMin(0);
      setMax(0);
      setCMin(0);
      setCMax(0);
    }
  };

  const getPedoMeterVitalData = async () => {
    setLoader(true);

    try {
      const requestDate = Moment(focusDate).format("YYYY-MM-DD");
      const response = await apiPostWithToken(
        {
          patientId: patientId,
          periodType: "WEEKLY",
          requestedDate: requestDate,
        },
        URLS.caregiverUrl + "vitalPedometerByPatient"
      );

      setLoader(false);

      if (response?.status == 200) {
        console.log("Pedometer Data:", JSON.stringify(response?.data));

        if (response?.data?.success) {
          try {
            // Safely handle period dates
            if (
              response?.data?.period &&
              Array.isArray(response.data.period) &&
              response.data.period.length > 0
            ) {
              const dates = response.data.period;
              setCustomDatesRange(dates);
              const customDates: any[] = [];
              dates.forEach((item: any) => {
                if (item) {
                  customDates.push(getCustomDate(item));
                }
              });
              setMeasureDates(customDates);
            } else {
              // Reset if no data
              setCustomDatesRange([]);
              setMeasureDates([]);
            }

            // Safely handle vital count data
            const vitalsGraphData = response?.data?.vitalsCountGraphVOs;
            if (
              vitalsGraphData &&
              Array.isArray(vitalsGraphData) &&
              vitalsGraphData.length > 0
            ) {
              setMeasuredValues(vitalsGraphData[0]?.counts || []);
            } else {
              setMeasuredValues([]);
            }

            // Always reset secondary values for pedometer
            setMeasuredValues2([]);

            // Handle threshold values with default fallbacks
            const thresholdConfig = response?.data?.thresholdConfigVO || {};
            setMin(thresholdConfig.pedometerStepCountMin || 0);
            setMax(thresholdConfig.pedometerStepCountMax || 10000);
            setCMin(thresholdConfig.pedometerStepCountCriticalMin || 0);
            setCMax(thresholdConfig.pedometerStepCountCriticalMax || 15000);
          } catch (error) {
            console.error("Error processing pedometer data:", error);
            // Set defaults in case of error
            setMeasuredValues([]);
            setMeasuredValues2([]);
            setCustomDatesRange([]);
            setMeasureDates([]);
            setMin(0);
            setMax(10000);
            setCMin(0);
            setCMax(15000);
          }
        } else {
          // Set defaults if success is false
          setMeasuredValues([]);
          setMeasuredValues2([]);
          setCustomDatesRange([]);
          setMeasureDates([]);
          setMin(0);
          setMax(10000);
          setCMin(0);
          setCMax(15000);
        }
      } else {
        const errorMessage = response?.response?.data?.responseMessage
          ? response?.response?.data?.responseMessage
          : response?.message === "Network Error"
          ? "Network error. Please check your data connection."
          : response?.message || "Unknown error";
        Alert.alert("Error", errorMessage, [{ text: "Dismiss" }]);

        // Set defaults on error
        setMeasuredValues([]);
        setMeasuredValues2([]);
        setCustomDatesRange([]);
        setMeasureDates([]);
        setMin(0);
        setMax(10000);
        setCMin(0);
        setCMax(15000);
      }
    } catch (error) {
      setLoader(false);
      console.error("Error in getPedoMeterVitalData:", error);
      Alert.alert("Error", "An unexpected error occurred. Please try again.", [
        { text: "Dismiss" },
      ]);

      // Set defaults on exception
      setMeasuredValues([]);
      setMeasuredValues2([]);
      setCustomDatesRange([]);
      setMeasureDates([]);
      setMin(0);
      setMax(10000);
      setCMin(0);
      setCMax(15000);
    }
  };

  const getSleepMonitorVitalData = async () => {
    setLoader(true);

    try {
      const requestDate = Moment(focusDate).format("YYYY-MM-DD");
      const response = await apiPostWithToken(
        {
          patientId: patientId,
          periodType: "WEEKLY",
          requestedDate: requestDate,
        },
        URLS.caregiverUrl + "vitalSleepMonitorByPatient"
      );

      setLoader(false);

      if (response?.status == 200) {
        console.log("Sleep Monitor Data:", JSON.stringify(response?.data));

        if (response?.data?.success) {
          try {
            // Safely handle period dates
            if (
              response?.data?.period &&
              Array.isArray(response.data.period) &&
              response.data.period.length > 0
            ) {
              const dates = response.data.period;
              setCustomDatesRange(dates);
              const customDates: any[] = [];
              dates.forEach((item: any) => {
                if (item) {
                  customDates.push(getCustomDateForSLeep(item));
                }
              });
              setMeasureDates(customDates);
            } else {
              // Reset if no data
              setCustomDatesRange([]);
              setMeasureDates([]);
            }

            // Safely handle vital count data
            setMin(0);
            setMax(24);
            setCMin(0);
            setCMax(24);

            const vitalsGraphData = response?.data?.vitalsCountGraphVOs;
            const fullData: Array<{ shallowSleep: number; deepSleep: number }> =
              [];

            if (
              vitalsGraphData &&
              Array.isArray(vitalsGraphData) &&
              vitalsGraphData.length >= 2
            ) {
              let shallowSleepArr = vitalsGraphData[0]?.counts1 || [];
              let deepSleepArr = vitalsGraphData[1]?.counts1 || [];

              // Create combined data for sleep monitor
              for (let i = 0; i < shallowSleepArr.length; i++) {
                fullData.push({
                  shallowSleep: shallowSleepArr[i] || 0,
                  deepSleep: deepSleepArr[i] || 0,
                });
              }

              setSleepMonitorData(fullData);
            } else {
              // If no valid data, set empty arrays
              setSleepMonitorData([]);
            }

            // Set measured values for backward compatibility
            setMeasuredValues([]);
            setMeasuredValues2([]);
          } catch (error) {
            console.error("Error processing sleep monitor data:", error);
            // Set defaults in case of error
            setMeasuredValues([]);
            setMeasuredValues2([]);
            setCustomDatesRange([]);
            setMeasureDates([]);
            setSleepMonitorData([]);
          }
        } else {
          // Set defaults if success is false
          setMeasuredValues([]);
          setMeasuredValues2([]);
          setCustomDatesRange([]);
          setMeasureDates([]);
          setSleepMonitorData([]);
        }
      } else {
        const errorMessage = response?.response?.data?.responseMessage
          ? response?.response?.data?.responseMessage
          : response?.message === "Network Error"
          ? "Network error. Please check your data connection."
          : response?.message || "Unknown error";
        Alert.alert("Error", errorMessage, [{ text: "Dismiss" }]);

        // Set defaults on error
        setMeasuredValues([]);
        setMeasuredValues2([]);
        setCustomDatesRange([]);
        setMeasureDates([]);
        setSleepMonitorData([]);
      }
    } catch (error) {
      setLoader(false);
      console.error("Error in getSleepMonitorVitalData:", error);
      Alert.alert("Error", "An unexpected error occurred. Please try again.", [
        { text: "Dismiss" },
      ]);

      // Set defaults on exception
      setMeasuredValues([]);
      setMeasuredValues2([]);
      setCustomDatesRange([]);
      setMeasureDates([]);
      setSleepMonitorData([]);
    }
  };

  const getVitalDataByDate = async (_vitalType: string) => {
    setLoader(true);
    // Map display names to API vital types if needed
    let apiVitalType = _vitalType;
    if (_vitalType === "Oxygen") {
      apiVitalType = "Oxygen Saturation";
    } else if (_vitalType === "Sleep" || _vitalType === "Sleep Monitor") {
      apiVitalType = "Sleep Monitor";
    }
    const now = Moment();
    const focus = Moment(focusDate);
    const startDate = focus
      .clone()
      .startOf("month")
      .format("YYYY-MM-DD 00:00:00");

    const isCurrentMonth = focus.isSame(Moment(), "month");

    const endDate =
      isCurrentMonth && focus.isBefore(now, "day")
        ? now.clone().format("YYYY-MM-DD 23:59:59")
        : isCurrentMonth
        ? focus.clone().format("YYYY-MM-DD 23:59:59")
        : focus.clone().endOf("month").format("YYYY-MM-DD 23:59:59");

    const response = await apiPostWithToken(
      {
        patientId: patientId,
        vitalType: apiVitalType,
        startDate: startDate,
        endDate: endDate,
      },
      URLS.caregiverUrl + "getVitalByDate"
    );
    setLoader(false);
    if (response?.status == 200 && response?.data?.status) {
      // For sleep, try to normalize the vitalType in the returned data as well
      let data = response?.data?.data;
      if (apiVitalType === "Sleep Monitor" && Array.isArray(data)) {
        data = data.map((item) => ({
          ...item,
          vitalType: "Sleep Monitor",
        }));
      }
      setDailyVitalData(data);
    } else {
      setDailyVitalData([]);
    }
  };

  return (
    <Container style={styles.container}>
      <Loader modalVisible={loader} />
      {/* Minimal Date Selector */}
      <View style={styles.datePickerContainer}>
        <View style={styles.dateNavigationContainer}>
          <TouchableOpacity
            style={styles.navButton}
            onPress={() =>
              setNewDate(Moment(focusDate).add(-1, "month").toDate())
            }
            activeOpacity={0.7}
          >
            <DatePickerLeftIcon stroke={"#4F46E5"} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.datePickerWrapper} activeOpacity={0.8}>
            <View style={styles.datePickerContent}>
              <Ionicons name="calendar-outline" size={18} color="#4F46E5" style={styles.calendarIcon} />
              <Text size={16} bold color="#1E293B">
                {Moment(focusDate).format("MMMM YYYY")}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#64748B" style={styles.dropdownIcon} />
            </View>
            <View style={styles.fullClickableArea}>
              <WatchRxDatePicker
                date={focusDate}
                onDateChange={(date) => setNewDate(date)}
              />
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity
            disabled={invalidNextDate}
            style={[
              styles.navButton,
              invalidNextDate && styles.navButtonDisabled
            ]}
            onPress={() => {
              setNewDate(Moment(focusDate).add(1, "month").toDate());
            }}
            activeOpacity={invalidNextDate ? 1 : 0.7}
          >
            <DatePickerRightIcon
              stroke={invalidNextDate ? "#CBD5E1" : "#4F46E5"}
            />
          </TouchableOpacity>
        </View>
      </View>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContentContainer}
        style={styles.scrollViewStyle}
        bounces={true}
      >
        <View style={styles.cardsContainer}>
          {dailyVitalData.length > 0 ? (
            // Sort data by date/time in descending order (latest first)
            dailyVitalData
              .sort((a, b) => {
                const dateA = new Date(a.date || a.createdAt || '');
                const dateB = new Date(b.date || b.createdAt || '');
                return dateB.getTime() - dateA.getTime();
              })
              .map((item, index) => (
                <VitalCard
                  key={`${item.vitalType}-${item.date}-${index}`}
                  title={item.vitalType}
                  value={
                    item.vitalData.includes("/")
                      ? Math.round(parseFloat(item.vitalData.split("/")[0]))
                      : Math.round(parseFloat(item.vitalData))
                  }
                  value2={
                    item.vitalData.includes("/")
                      ? Math.round(parseFloat(item.vitalData.split("/")[1]))
                      : undefined
                  }
                  date={item.date}
                  unit={unit}
                  min={min}
                  max={max}
                  cMin={cMin}
                  cMax={cMax}
                  isPlaceholder={false}
                />
              ))
          ) : (
            <View style={styles.emptyStateContainer}>
              <Text size={16} color={Colors.GrayBlue} center>
                No vital data available for this date
              </Text>
              <Text size={14} color={Colors.GrayBlue} center style={{ marginTop: 8 }}>
                Select a different date or check back later
              </Text>
            </View>
          )}
        </View>

        {/* <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            backgroundColor: Colors.White,
          }}
        >
          <View
            style={{
              ...styles.minMax,
              backgroundColor: Colors.pastelOrange,
              opacity: 0.7,
            }}
          >
            <Text center bold size={16} type={"H3"}>
              {"Min"}
            </Text>
            <Text center semibold size={16} marginTop={15}>
              {min}&nbsp;
              {unit}
            </Text>
          </View>
          <View
            style={{
              ...styles.minMax,
              backgroundColor: Colors.pastelOrange,
            }}
          >
            <Text center bold size={16} type={"H3"}>
              {"Max"}
            </Text>
            <Text center semibold size={16} marginTop={15}>
              {max}&nbsp;
              {unit}
            </Text>
          </View>
        </View> */}
        {/* <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            backgroundColor: Colors.White,
          }}
        >
          <View
            style={{
              ...styles.minMax,
              backgroundColor: Colors.pastelRed,
              opacity: 0.7,
            }}
          >
            <Text center bold size={16} type={"H3"}>
              {"Critical Min"}&nbsp;
            </Text>
            <Text center semibold size={16} marginTop={15}>
              {cMin}&nbsp;
              {unit}
            </Text>
          </View>
          <View style={{ ...styles.minMax, backgroundColor: Colors.pastelRed }}>
            <Text center bold size={16} type={"H3"}>
              {"Critical Max"}
            </Text>
            <Text center semibold size={16} marginTop={15}>
              {cMax}&nbsp;
              {unit}
            </Text>
          </View>
        </View> */}
      </ScrollView>
    </Container>
  );
});

export default VitalReportItem;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: width,
    paddingTop: 0,
    backgroundColor: "#FFFFFF",
  },
  scrollViewStyle: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  scrollContentContainer: {
    flexGrow: 1,
    paddingBottom: 40,
  },
  item: {
    padding: 8,
    borderRadius: 12,
    width: "100%",
    ...Theme.shadow,
    justifyContent: "center",
    marginBottom: 16,
    flex: 1,
    flexDirection: "column",
  },
  // Minimal Date Picker Styles
  datePickerContainer: {
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginBottom: 8,
  },
  dateNavigationContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 0,
  },
  navButton: {
    width: 52,
    height: 52,
    borderRadius: 12,
    backgroundColor: "#FFFFFF",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#1E293B",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: "#E2E8F0",
  },
  navButtonDisabled: {
    backgroundColor: "#F8FAFC",
    borderColor: "#E2E8F0",
    shadowOpacity: 0.02,
  },
  datePickerWrapper: {
    flex: 1,
    marginHorizontal: 16,
    height: 52,
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E2E8F0",
    position: "relative",
    overflow: "hidden",
    shadowColor: "#1E293B",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  datePickerContent: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
    height: "100%",
    position: "relative",
    zIndex: 1,
  },
  calendarIcon: {
    marginRight: 8,
  },
  dropdownIcon: {
    marginLeft: 8,
  },
  fullClickableArea: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 10,
    opacity: 0,
  },
  cardsContainer: {
    width: "100%",
    paddingHorizontal: 16,
    marginBottom: 16,
    paddingTop: 16,
    backgroundColor: "#FFFFFF",
  },
  listContent: {
    paddingTop: 5,
    paddingBottom: 10,
  },
  emptyStateContainer: {
    padding: 32,
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    marginVertical: 20,
    marginHorizontal: 16,
    shadowColor: "#1E293B",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: "#F1F5F9",
  },
  verticalDivider: {
    width: 1,
    height: "85%",
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
    marginVertical: scale(5),
  },
  minMax: {
    backgroundColor: "white",
    padding: 10,
    borderRadius: 10,
    width: (Constants.width - 40) / 2,
    height: (Constants.width - 220) / 2,
    ...Theme.shadow,
    justifyContent: "center",
    marginBottom: 10,
    marginLeft: 10,
    marginRight: 10,
    marginTop: 0,
  },
});
