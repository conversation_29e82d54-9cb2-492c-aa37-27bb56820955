import React from 'react';
import { Stack, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StyleSheet } from 'react-native';
import { Colors } from '@/constants';
import SummaryScreen from '@/screens/PatientDetails/SummaryScreen';

/**
 * Patient Summary Screen Route
 * This screen displays a comprehensive summary of patient information
 */
export default function PatientSummaryRoute() {
  return (
    <>
      <Stack.Screen 
        options={{
          headerShown: false // Hide the Stack.Screen header
        }} 
      />
      <SafeAreaView style={styles.container} edges={['left', 'right']}>
        <SummaryScreen />
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
