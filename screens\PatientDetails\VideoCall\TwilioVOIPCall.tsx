import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Platform,
  PermissionsAndroid,
  Animated,
  StatusBar,
  Dimensions,
  NativeModules
} from 'react-native';
import { Voice, Call } from '@twilio/voice-react-native-sdk';
import { Audio } from 'expo-av';
import { getTwilioVOIPToken } from '../../../services/apis/apiManager';
import Colors, { CoreColors } from '../../../constants/Colors';
import { MaterialIcons, Ionicons, FontAwesome } from '@expo/vector-icons';
import Container from '../../../components/Layout/Container';
import NavigationHeader from '../../../components/NavigationHeader';

// Responsive dimensions - following app pattern
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const isSmallScreen = screenHeight < 700;
const isVerySmallScreen = screenHeight < 600;

interface TwilioVOIPCallProps {
  patientId?: string;
  patientName?: string;
  onCallEnd?: () => void;
  programOptions?: Array<{programName: string, programId: string, mins: number, programActivated: boolean}>;
}

interface VOIPTokenResponse {
  token: string;
  identity: string;
  phoneNumber: string;
  programs: any[];
  status: boolean;
}

type CallScreen = 'preCall' | 'initializing' | 'calling' | 'connected' | 'ended';

export const TwilioVOIPCall: React.FC<TwilioVOIPCallProps> = ({
  patientId,
  patientName = `Patient ${patientId}`,
  onCallEnd,
  programOptions = [],
}) => {
  const [callScreen, setCallScreen] = useState<CallScreen>('preCall');
  const [callState, setCallState] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const [error, setError] = useState<string | null>(null);
  const [isMuted, setIsMuted] = useState(false);
  const [isSpeakerOn, setIsSpeakerOn] = useState(false);
  const [programs, setPrograms] = useState<any[]>([]);
  const [selectedProgram, setSelectedProgram] = useState<string>('');
  
  // Map program options to the format needed for the dropdown - exactly like ZoomVideoCall
  const programCategories = programOptions.map(program => ({
    label: program.programName,
    value: program.programId,
    activated: program.programActivated
  }));
  const [callDuration, setCallDuration] = useState(0);
  
  const voiceRef = useRef<Voice | null>(null);
  const currentCallRef = useRef<Call | null>(null);
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const callDurationInterval = useRef<NodeJS.Timeout | null>(null);

  // Start call duration timer
  const startCallTimer = () => {
    setCallDuration(0);
    callDurationInterval.current = setInterval(() => {
      setCallDuration(prev => prev + 1);
    }, 1000);
  };

  // Stop call duration timer
  const stopCallTimer = () => {
    if (callDurationInterval.current) {
      clearInterval(callDurationInterval.current);
      callDurationInterval.current = null;
    }
  };

  // Format call duration
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Pulse animation for calling state
  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    ).start();
  };

  const stopPulseAnimation = () => {
    pulseAnim.stopAnimation();
    pulseAnim.setValue(1);
  };

  // Audio permissions
  const requestAudioPermissions = async (): Promise<boolean> => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
          {
            title: 'Audio Permission',
            message: 'This app needs access to your microphone to make calls.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn('Audio permission error:', err);
        return false;
      }
    }
    return true;
  };

  // Initialize Voice SDK with retry logic and better error handling
  const initializeVoice = useCallback(async (forceReinit = false) => {
    try {
      console.log('TwilioVOIP: Initializing Voice SDK (forceReinit:', forceReinit, ')');
      
      // Clean up existing instance if force reinit or if there's an error state
      if (forceReinit && voiceRef.current) {
        try {
          console.log('TwilioVOIP: Cleaning up existing Voice instance');
          voiceRef.current = null;
        } catch (cleanupError) {
          console.warn('TwilioVOIP: Error cleaning up Voice instance:', cleanupError);
        }
      }
      
      if (voiceRef.current && !forceReinit) {
        console.log('TwilioVOIP: Voice already initialized');
        return true;
      }

      const voice = new Voice();
      voiceRef.current = voice;

      // Add comprehensive event listeners for better debugging
      voice.on(Voice.Event.Error, (error: any) => {
        console.error('TwilioVOIP: Voice error:', error);
        setError(`SDK Error: ${error.message || 'Unknown voice SDK error'}`);
      });

      voice.on(Voice.Event.Registered, () => {
        console.log('TwilioVOIP: Voice SDK registered successfully');
      });

      voice.on(Voice.Event.Unregistered, () => {
        console.log('TwilioVOIP: Voice SDK unregistered');
      });

      // Add a small delay to ensure SDK is fully initialized
      await new Promise(resolve => setTimeout(resolve, 100));

      console.log('TwilioVOIP: Voice SDK initialized successfully');
      return true;
    } catch (error: any) {
      console.error('TwilioVOIP: Failed to initialize Voice SDK:', error);
      setError(`Voice SDK initialization failed: ${error.message || 'Unknown error'}`);
      return false;
    }
  }, []);

  // Make direct call
  const makeDirectCall = async (token: string) => {
    try {
      const voice = voiceRef.current;
      if (!voice) {
        throw new Error('Voice SDK not initialized');
      }

      console.log('TwilioVOIP: Making direct outgoing call');
      setCallState('connecting');
      setCallScreen('calling');
      startPulseAnimation();
      
      const callParams = {
        To: patientId || '',
        patientId: patientId || '',
        review: selectedProgram,
      };

      console.log('TwilioVOIP: Call params', callParams);
      
      const call = await voice.connect(token, { params: callParams });
      currentCallRef.current = call;

      call.on(Call.Event.Connected, async () => {
        console.log('TwilioVOIP: Call connected!');
        setCallState('connected');
        setCallScreen('connected');
        stopPulseAnimation();
        startCallTimer();
        
        // Initialize audio to earpiece by default for VoIP calls
        try {
          await setAudioRoute('earpiece');
        } catch (error) {
          console.error('Failed to set initial audio route:', error);
        }
      });

      call.on(Call.Event.Disconnected, async () => {
        console.log('TwilioVOIP: Call ended');
        setCallState('disconnected');
        setCallScreen('ended');
        stopPulseAnimation();
        stopCallTimer();
        currentCallRef.current = null;
        
        // Reset audio session to default after call ends
        try {
          await Audio.setAudioModeAsync({
            allowsRecordingIOS: false,
            staysActiveInBackground: false,
            playsInSilentModeIOS: false,
            shouldDuckAndroid: false,
            playThroughEarpieceAndroid: false,
          });
          setIsSpeakerOn(false);
          console.log('Audio session reset to default');
        } catch (error: any) {
          console.error('Failed to reset audio session:', error.message || error);
          setIsSpeakerOn(false);
        }
        
        setTimeout(() => {
          onCallEnd?.();
        }, 2000);
      });

      call.on(Call.Event.ConnectFailure, (error: any) => {
        console.error('TwilioVOIP: Call failed:', error);
        setError(`Call failed: ${error.message || 'Unknown error'}`);
        setCallState('disconnected');
        setCallScreen('ended');
        stopPulseAnimation();
        stopCallTimer();
        currentCallRef.current = null;
      });

      call.on(Call.Event.Ringing, () => {
        console.log('TwilioVOIP: Call ringing...');
      });

    } catch (error: any) {
      console.error('TwilioVOIP: Direct call error:', error);
      setError(`Call error: ${error.message || 'Failed to make call'}`);
      setCallState('disconnected');
      setCallScreen('ended');
      stopPulseAnimation();
    }
  };

  // Start call from pre-call screen with retry logic
  const startCallInternal = async (retryCount = 0) => {
    const maxRetries = 2;
    
    if (!patientId) {
      setError('No patient ID provided');
      return;
    }

    if (!selectedProgram) {
      setError('Please select a program before starting the call');
      return;
    }

    setCallScreen('initializing');
    setError(null);

    try {
      console.log(`TwilioVOIP: Starting call attempt ${retryCount + 1}/${maxRetries + 1}`);
      
      // 1. Check permissions
      const hasPermission = await requestAudioPermissions();
      if (!hasPermission) {
        throw new Error('Microphone permission required');
      }

      // 2. Initialize Voice SDK with force reinit on retries
      const initSuccess = await initializeVoice(retryCount > 0);
      if (!initSuccess) {
        throw new Error('Failed to initialize Voice SDK');
      }

      // 3. Get token from backend with retry logic
      console.log('TwilioVOIP: Getting token...');
      const response = await getTwilioVOIPToken(patientId, 2); // API has built-in retry
      
      if (response.error || !response.data) {
        throw new Error(response.error?.message || 'Failed to get token from server');
      }

      const tokenData = response.data as VOIPTokenResponse;
      if (!tokenData.token) {
        throw new Error('No valid token received from backend');
      }

      console.log('TwilioVOIP: Received token, identity:', tokenData.identity);
      
      // 4. Small delay before making call to ensure everything is initialized
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 5. Make the call
      await makeDirectCall(tokenData.token);

    } catch (error: any) {
      console.error(`TwilioVOIP: Start call attempt ${retryCount + 1} failed:`, error);
      
      // Retry logic for the first call issue
      if (retryCount < maxRetries && 
          (error.message.includes('SDK') || 
           error.message.includes('token') || 
           error.message.includes('initialize'))) {
        
        console.log(`TwilioVOIP: Retrying call in 2 seconds (attempt ${retryCount + 2}/${maxRetries + 1})`);
        setError(`Connection issue. Retrying... (${retryCount + 2}/${maxRetries + 1})`);
        
                 setTimeout(() => {
           startCallInternal(retryCount + 1);
         }, 2000);
         return;
       }
       
       // Final error after all retries
       setError(error.message || 'Failed to start call');
       setCallState('disconnected');
       setCallScreen('ended');
     }
   };

   // Wrapper for onPress handler
   const startCall = () => {
     startCallInternal(0);
   };

  // Load programs when component mounts
  const loadPrograms = async () => {
    if (!patientId) return;

    try {
      console.log('TwilioVOIP: Loading programs...');
      const response = await getTwilioVOIPToken(patientId);
      
      if (response.data && response.data.programs && response.data.programs.length > 0) {
        setPrograms(response.data.programs);
        setSelectedProgram(response.data.programs[0].programName);
        console.log('TwilioVOIP: Available programs:', response.data.programs);
      }
    } catch (error: any) {
      console.error('TwilioVOIP: Failed to load programs:', error);
    }
  };

  // Load programs on component mount
  useEffect(() => {
    loadPrograms();
  }, [patientId]);

  // Toggle mute
  const toggleMute = () => {
    const call = currentCallRef.current;
    if (call) {
      if (isMuted) {
        call.mute(false);
      } else {
        call.mute(true);
      }
      setIsMuted(!isMuted);
    }
  };

  // Audio routing functions using expo-av and platform-specific routing
  const setAudioRoute = async (route: 'speaker' | 'earpiece') => {
    try {
      console.log('Setting audio route to:', route);
      
      // Use expo-av for basic audio session setup
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        staysActiveInBackground: true,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: route === 'earpiece', // Control Android routing
      });
      
      // Platform-specific audio routing
      if (Platform.OS === 'ios') {
        try {
          // Try using native modules for iOS audio session override
          const { AudioUtils } = NativeModules;
          if (AudioUtils) {
            if (route === 'speaker') {
              await AudioUtils.setSpeakerOn(true);
            } else {
              await AudioUtils.setSpeakerOn(false);
            }
          } else {
            console.log('iOS AudioUtils not available, using expo-av only');
          }
        } catch (iosError: any) {
          console.log('iOS audio routing fallback:', iosError.message || iosError);
        }
      } else if (Platform.OS === 'android') {
        try {
          // Try using native modules for Android audio manager
          const { AudioManager } = NativeModules;
          if (AudioManager) {
            await AudioManager.setSpeakerphoneOn(route === 'speaker');
          } else {
            console.log('Android AudioManager not available, using expo-av only');
          }
        } catch (androidError: any) {
          console.log('Android audio routing fallback:', androidError.message || androidError);
        }
      }
      
      setIsSpeakerOn(route === 'speaker');
      console.log('Audio route successfully set to:', route);
    } catch (error) {
      console.error('Error setting audio route:', error);
      // Still update UI state for user feedback even if audio routing fails
      setIsSpeakerOn(route === 'speaker');
    }
  };

  // Toggle speaker
  const toggleSpeaker = async () => {
    const newRoute = isSpeakerOn ? 'earpiece' : 'speaker';
    await setAudioRoute(newRoute);
  };

  // End call
  const endCall = () => {
    const call = currentCallRef.current;
    if (call) {
      console.log('TwilioVOIP: Ending call');
      call.disconnect();
    }
  };

  // Initialize programs when component mounts
  useEffect(() => {
    // Set default program if programs are available
    if (programs.length > 0 && !selectedProgram) {
      setSelectedProgram(programs[0].value);
    }
  }, [programs]);

  // Cleanup
  useEffect(() => {
    return () => {
      stopCallTimer();
      if (currentCallRef.current) {
        currentCallRef.current.disconnect();
      }
    };
  }, []);

  // Render pre-call screen - exactly matching ZoomVideoCall
  const renderPreCallScreen = () => (
    <Container style={styles.preCallContainer}>
      <NavigationHeader 
        title="Voice Call" 
        showBackButton={true}
        showLogo={false}
      />
      
      {/* Main Content */}
      <View style={styles.preCallContent}>
        {/* Patient Info Card */}
        <View style={styles.patientCard}>
          <View style={styles.avatarContainer}>
            <MaterialIcons name="person" size={32} color={CoreColors.TurquoiseBlue} />
          </View>
          <Text style={styles.patientName}>{patientName}</Text>
          <Text style={styles.callSubtitle}>Ready to start your voice call</Text>
        </View>

        {/* Program Selection */}
        <View style={styles.preCallProgramSelector}>
          <Text style={styles.preCallProgramLabel}>Select Program Category</Text>
          <View style={styles.preCallRadioContainer}>
            {programCategories.map((item) => (
              <TouchableOpacity
                key={item.value}
                style={[
                  styles.preCallRadioButton,
                  selectedProgram === item.value && styles.preCallRadioButtonSelected,
                  !item.activated && styles.preCallRadioButtonDisabled
                ]}
                onPress={() => {
                  if (item.activated) {
                    setSelectedProgram(item.value);
                    setError(null);
                  }
                }}
                disabled={!item.activated}
              >
                <View style={[
                  styles.preCallRadioCircle,
                  selectedProgram === item.value && styles.preCallRadioCircleSelected
                ]}>
                  {selectedProgram === item.value && (
                    <MaterialIcons name="check" size={14} color="white" />
                  )}
                </View>
                <View style={styles.preCallRadioTextContainer}>
                  <Text style={[
                    styles.preCallRadioText,
                    !item.activated && styles.preCallRadioTextDisabled
                  ]}>
                    {item.label}
                  </Text>
                  {!item.activated && (
                    <Text style={styles.preCallRadioSubtext}>Not Available</Text>
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </View>
          {error && <Text style={styles.preCallErrorText}>{error}</Text>}
        </View>
      </View>

      {/* Footer with Start Call Button */}
      <View style={styles.preCallFooter}>
        <TouchableOpacity
          style={[
            styles.preCallJoinButton,
            !selectedProgram && styles.preCallJoinButtonDisabled
          ]}
          onPress={startCall}
          disabled={!selectedProgram}
        >
          <MaterialIcons 
            name="phone" 
            size={24} 
            color="white" 
            style={styles.preCallJoinButtonIcon}
          />
          <Text style={styles.preCallJoinButtonText}>Start Call</Text>
        </TouchableOpacity>
      </View>
    </Container>
  );

    // Render initializing screen - responsive design
  const renderInitializingScreen = () => (
    <Container style={styles.initializingContainer}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      <View style={styles.initializingContent}>
        <View style={styles.avatarContainer}>
          <MaterialIcons 
            name="person" 
            size={isSmallScreen ? 28 : 32} 
            color={CoreColors.TurquoiseBlue} 
          />
        </View>
        <Text style={styles.patientName}>{patientName}</Text>
        <Text style={styles.initializingText}>Initializing call...</Text>
        
        <Animated.View style={[styles.loadingIndicator, { opacity: pulseAnim }]}>
          <MaterialIcons 
            name="phone" 
            size={isSmallScreen ? 28 : 32} 
            color={CoreColors.TurquoiseBlue} 
          />
        </Animated.View>
      </View>
    </Container>
  );

    // Render calling screen - Apple Watch style with floating FAB
  const renderCallingScreen = () => (
    <Container style={styles.callingContainer}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      <View style={styles.callingContent}>
        {/* Patient info at top */}
        <View style={styles.callingHeader}>
          <Text style={styles.callingPatientName}>{patientName}</Text>
          <Text style={styles.callingText}>Ringing...</Text>
        </View>
        
        {/* Large circular avatar in center */}
        <View style={styles.callingAvatarContainer}>
          <Animated.View style={[styles.callingAvatar, { transform: [{ scale: pulseAnim }] }]}>
            <MaterialIcons 
              name="person" 
              size={isSmallScreen ? 70 : 80} 
              color={CoreColors.TurquoiseBlue} 
            />
          </Animated.View>
        </View>
        
        {/* Floating FAB controls at bottom */}
        <View style={styles.floatingFABContainer}>
          <TouchableOpacity style={styles.endCallFAB} onPress={endCall}>
            <MaterialIcons name="call-end" size={28} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </View>
    </Container>
  );

    // Render connected screen - Apple Watch style with floating FAB
  const renderConnectedScreen = () => (
    <Container style={styles.connectedContainer}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      <View style={styles.connectedContent}>
        {/* Patient info at top */}
        <View style={styles.connectedHeader}>
          <Text style={styles.callingPatientName}>{patientName}</Text>
          <Text style={styles.callDuration}>{formatDuration(callDuration)}</Text>
        </View>
        
        {/* Large circular avatar in center */}
        <View style={styles.connectedAvatarContainer}>
          <View style={styles.callingAvatar}>
            <MaterialIcons 
              name="person" 
              size={isSmallScreen ? 70 : 80} 
              color={CoreColors.TurquoiseBlue} 
            />
          </View>
        </View>
        
        {/* Floating dock with all controls */}
        <View style={styles.floatingDock}>
          <View style={styles.dockContainer}>
            <TouchableOpacity 
              style={[styles.dockButton, isMuted && styles.dockButtonActive]} 
              onPress={toggleMute}
            >
              <MaterialIcons 
                name={isMuted ? "mic-off" : "mic"} 
                size={24} 
                color={isMuted ? "#FFFFFF" : "#1A1A1A"}
              />
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.dockButton, isSpeakerOn && styles.dockButtonActive]} 
              onPress={toggleSpeaker}
            >
              <MaterialIcons 
                name={isSpeakerOn ? "volume-up" : "volume-down"} 
                size={24} 
                color={isSpeakerOn ? "#FFFFFF" : "#1A1A1A"}
              />
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.dockEndCallButton} onPress={endCall}>
              <MaterialIcons name="call-end" size={28} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Container>
  );

  // Render ended screen
  const renderEndedScreen = () => (
    <Container style={styles.endedContainer}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      <View style={styles.endedContent}>
        <View style={styles.endedAvatar}>
          <MaterialIcons name="person" size={64} color={CoreColors.TurquoiseBlue} />
        </View>
        <Text style={styles.endedPatientName}>{patientName}</Text>
        <Text style={styles.endedText}>
          {error ? 'Call Failed' : 'Call Ended'}
        </Text>
        {error && <Text style={styles.errorText}>{error}</Text>}
      </View>
    </Container>
  );

  // Main render
  switch (callScreen) {
    case 'preCall':
      return renderPreCallScreen();
    case 'initializing':
      return renderInitializingScreen();
    case 'calling':
      return renderCallingScreen();
    case 'connected':
      return renderConnectedScreen();
    case 'ended':
      return renderEndedScreen();
    default:
      return renderPreCallScreen();
  }
};

const styles = StyleSheet.create({
  // Pre-call screen styles - matching Zoom exactly
  preCallContainer: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  preCallContent: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  patientCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    padding: 24,
    alignItems: "center",
    marginBottom: 32,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 1,
    borderColor: "#F0F0F0",
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#F0F8FF",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
    borderWidth: 2,
    borderColor: CoreColors.TurquoiseBlue + "20",
  },
  patientName: {
    fontSize: 22,
    fontWeight: "700",
    color: "#1A1A1A",
    marginBottom: 8,
    textAlign: "center",
  },
  callSubtitle: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
    lineHeight: 22,
  },
  // Program selection styles - matching ZoomVideoCall exactly
  preCallProgramSelector: {
    marginBottom: 32,
  },
  preCallProgramLabel: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1A1A1A",
    marginBottom: 16,
  },
  preCallRadioContainer: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#E5E7EB",
    overflow: "hidden",
  },
  preCallRadioButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#F3F4F6",
    backgroundColor: "#FFFFFF",
  },
  preCallRadioButtonSelected: {
    backgroundColor: "#F0F8FF",
    borderBottomColor: CoreColors.TurquoiseBlue + "20",
  },
  preCallRadioButtonDisabled: {
    backgroundColor: "#F9FAFB",
    opacity: 0.6,
  },
  preCallRadioCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#D1D5DB",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  preCallRadioCircleSelected: {
    backgroundColor: CoreColors.TurquoiseBlue,
    borderColor: CoreColors.TurquoiseBlue,
  },
  preCallRadioTextContainer: {
    flex: 1,
  },
  preCallRadioText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#1F2937",
    marginBottom: 2,
  },
  preCallRadioTextDisabled: {
    color: "#9CA3AF",
  },
  preCallRadioSubtext: {
    fontSize: 14,
    color: "#6B7280",
    fontStyle: "italic",
  },
  preCallErrorText: {
    fontSize: 14,
    color: "#EF4444",
    textAlign: "center",
    marginTop: 12,
    fontWeight: "500",
  },
  preCallFooter: {
    padding: 24,
    paddingBottom: Platform.OS === "ios" ? 34 : 24,
    backgroundColor: "#FFFFFF",
    borderTopWidth: 1,
    borderTopColor: "#E5E7EB",
  },
  preCallJoinButton: {
    backgroundColor: CoreColors.TurquoiseBlue,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    paddingHorizontal: 24,
    shadowColor: CoreColors.TurquoiseBlue,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  preCallJoinButtonDisabled: {
    backgroundColor: "#D1D5DB",
    shadowOpacity: 0,
    elevation: 0,
  },
  preCallJoinButtonIcon: {
    marginRight: 8,
  },
  preCallJoinButtonText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#FFFFFF",
  },
  errorContainer: {
    backgroundColor: "#FEF2F2",
    borderWidth: 1,
    borderColor: "#FECACA",
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  errorText: {
    fontSize: 14,
    color: "#EF4444",
    textAlign: "center",
    fontWeight: "500",
  },

  // Call screen styles - Apple Watch style with white background
  initializingContainer: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  callingContainer: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  connectedContainer: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  endedContainer: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  
  // Content containers - Apple Watch style with responsive design
  initializingContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 40,
  },
  callingContent: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 20,
  },
  connectedContent: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 20,
  },
  endedContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 40,
  },
  
  // Calling screen layout - responsive with proper centering
  callingHeader: {
    alignItems: "center",
    paddingTop: isSmallScreen ? 40 : 60,
    paddingBottom: 20,
  },
  callingAvatarContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    marginTop: -60, // Pull avatar up to center it better
  },
  
  // Connected screen layout - responsive with proper centering
  connectedHeader: {
    alignItems: "center",
    paddingTop: isSmallScreen ? 40 : 60,
    paddingBottom: 20,
  },
  connectedAvatarContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    marginTop: -60, // Pull avatar up to center it better
  },
  
  // Text styles for call screens - Apple Watch style with responsive sizing
  initializingText: {
    fontSize: isSmallScreen ? 18 : 20,
    fontWeight: "600",
    color: "#1F2937",
    textAlign: "center",
    marginTop: 20,
    marginBottom: 8,
  },
  callingText: {
    fontSize: isSmallScreen ? 16 : 18,
    fontWeight: "400",
    color: "#6B7280",
    textAlign: "center",
    marginTop: 16,
    marginBottom: 10,
  },

  callDuration: {
    fontSize: isSmallScreen ? 14 : 16,
    color: "#6B7280",
    textAlign: "center",
    marginBottom: 15,
  },
  endedText: {
    fontSize: isSmallScreen ? 18 : 20,
    fontWeight: "600",
    color: "#1F2937",
    textAlign: "center",
    marginTop: 20,
    marginBottom: 8,
  },
  
  // Loading indicator
  loadingIndicator: {
    alignItems: "center",
  },
  
  // Call controls - Apple Watch style
  callingActions: {
    position: "absolute",
    bottom: 60,
    alignSelf: "center",
    alignItems: "center",
  },
  callControls: {
    position: "absolute",
    bottom: 50,
    left: 0,
    right: 0,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-around",
    paddingHorizontal: 40,
  },
  
  // Control buttons - Apple Watch style
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#F0F0F0",
    borderWidth: 1,
    borderColor: "#E0E0E0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  controlButtonActive: {
    backgroundColor: CoreColors.TurquoiseBlue,
  },
  controlLabel: {
    fontSize: 12,
    color: "#6B7280",
    textAlign: "center",
    marginTop: 4,
    fontWeight: "500",
  },
  controlLabelActive: {
    color: "#FFFFFF",
  },
  
  // End call buttons - Apple Watch style
  endCallButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "#FF3B30",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#FF3B30",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  endCallButtonLarge: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "#FF3B30",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#FF3B30",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },

  // Avatar styles - Apple Watch circular design with responsive sizing
  callingAvatar: {
    width: isSmallScreen ? 140 : 160,
    height: isSmallScreen ? 140 : 160,
    borderRadius: isSmallScreen ? 70 : 80,
    backgroundColor: "#F0F8FF",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 24,
    borderWidth: 3,
    borderColor: CoreColors.TurquoiseBlue + "30",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  endedAvatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: "#F0F8FF",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 24,
    borderWidth: 2,
    borderColor: CoreColors.TurquoiseBlue + "20",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  // Floating dock - iPhone/Apple Watch style
  floatingDock: {
    position: "absolute",
    bottom: isSmallScreen ? 50 : 70,
    left: 20,
    right: 20,
    alignItems: "center",
  },
  dockContainer: {
    flexDirection: "row",
    backgroundColor: "#FFFFFF",
    borderRadius: 30,
    paddingHorizontal: 20,
    paddingVertical: 15,
    alignItems: "center",
    justifyContent: "space-between",
    minWidth: screenWidth * 0.7,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
    borderWidth: 1.5,
    borderColor: "#E8E8E8",
  },

  // Dock button styles - clean design with borders, no glowing
  dockButton: {
    width: isSmallScreen ? 50 : 56,
    height: isSmallScreen ? 50 : 56,
    borderRadius: isSmallScreen ? 25 : 28,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    marginHorizontal: 8,
    borderWidth: 1.5,
    borderColor: "#E5E5E5",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  dockButtonActive: {
    backgroundColor: CoreColors.TurquoiseBlue,
    borderColor: CoreColors.TurquoiseBlue,
    shadowColor: "#000",
    shadowOpacity: 0.1,
  },
  dockEndCallButton: {
    width: isSmallScreen ? 56 : 64,
    height: isSmallScreen ? 56 : 64,
    borderRadius: isSmallScreen ? 28 : 32,
    backgroundColor: "#FF3B30",
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: 8,
    borderWidth: 1.5,
    borderColor: "#FF3B30",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  // Single FAB for calling screen
  floatingFABContainer: {
    position: "absolute",
    bottom: isSmallScreen ? 50 : 70,
    left: 0,
    right: 0,
    alignItems: "center",
  },
  endCallFAB: {
    width: isSmallScreen ? 64 : 72,
    height: isSmallScreen ? 64 : 72,
    borderRadius: isSmallScreen ? 32 : 36,
    backgroundColor: "#FF3B30",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#FF3B30",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 4,
  },

  // Patient name styles - Apple Watch style with responsive sizing
  callingPatientName: {
    fontSize: isSmallScreen ? 20 : 24,
    fontWeight: "600",
    color: "#1A1A1A",
    textAlign: "center",
    marginBottom: 8,
  },
  endedPatientName: {
    fontSize: 22,
    fontWeight: "700",
    color: "#1A1A1A",
    marginBottom: 8,
    textAlign: "center",
  },
});  