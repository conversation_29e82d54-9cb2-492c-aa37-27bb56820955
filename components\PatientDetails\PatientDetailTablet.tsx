import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  Dimensions,
} from 'react-native';
import {
  AntDesign,
  Feather,
  FontAwesome5,
  Ionicons,
  MaterialCommunityIcons,
  MaterialIcons,
} from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import moment from 'moment';

import Text from '@/components/Text';
import { AdaptiveLayout, TabletAwareContainer } from '@/components/Layout';
import { useOrientation } from '@/hooks/useOrientation';
import { useTabletTheme } from '@/hooks/useTabletTheme';
import {
  isTablet,
  isLargeTablet,
  getOrientation,
  spacing,
  typography,
  iconSizes,
} from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';

interface PatientData {
  patientId: string | number;
  patientName?: string;
  image?: any;
  address?: string;
  phone?: string;
  dob?: string;
  mrn?: string;
  programs?: any;
}

interface Feature {
  id: number;
  icon: {
    family: string;
    name: string;
    size: number;
  };
  title: string;
  subtitle: string;
  route: string;
  color: string;
  gradientColors: string[];
  priority: 'high' | 'medium' | 'low';
  category: 'clinical' | 'communication' | 'monitoring' | 'management';
}

interface PatientDetailTabletProps {
  patientData: PatientData;
  loading?: boolean;
  onFeaturePress?: (feature: Feature) => void;
  style?: ViewStyle;
}

// Enhanced features organized by category for tablet layout
const TABLET_FEATURES: Feature[] = [
  // Clinical Features
  {
    id: 1,
    icon: { family: 'FontAwesome5', name: 'heartbeat', size: 28 },
    title: 'Vitals Report',
    subtitle: 'Real-time health monitoring',
    route: '/patient/vitals',
    color: '#4ECDC4',
    gradientColors: ['#4ECDC4', '#44A08D'],
    priority: 'high',
    category: 'clinical',
  },
  {
    id: 2,
    icon: { family: 'MaterialCommunityIcons', name: 'pill', size: 28 },
    title: 'Medications',
    subtitle: 'Drug management & adherence',
    route: '/patient/medications',
    color: '#7A77FF',
    gradientColors: ['#7A77FF', '#6366F1'],
    priority: 'high',
    category: 'clinical',
  },
  {
    id: 6,
    icon: { family: 'FontAwesome5', name: 'notes-medical', size: 26 },
    title: 'Care Team Notes',
    subtitle: 'Clinical records & observations',
    route: '/patient/ccm',
    color: '#9381FF',
    gradientColors: ['#9381FF', '#8B5CF6'],
    priority: 'high',
    category: 'clinical',
  },
  
  // Monitoring Features
  {
    id: 7,
    icon: { family: 'AntDesign', name: 'warning', size: 28 },
    title: 'Critical Alerts',
    subtitle: 'Urgent notifications & emergencies',
    route: '/patient-critical-alerts',
    color: '#F25F5C',
    gradientColors: ['#F25F5C', '#EF4444'],
    priority: 'high',
    category: 'monitoring',
  },
  {
    id: 4,
    icon: { family: 'Ionicons', name: 'notifications', size: 28 },
    title: 'Alerts',
    subtitle: 'System notifications & reminders',
    route: '/patient/alerts',
    color: '#36B5E0',
    gradientColors: ['#36B5E0', '#3490DC'],
    priority: 'medium',
    category: 'monitoring',
  },
  {
    id: 3,
    icon: { family: 'MaterialIcons', name: 'location-on', size: 28 },
    title: 'GPS Tracking',
    subtitle: 'Location monitoring & geofencing',
    route: '/patient/map',
    color: '#FF6B6B',
    gradientColors: ['#FF6B6B', '#EE5A52'],
    priority: 'medium',
    category: 'monitoring',
  },
  
  // Management Features
  {
    id: 5,
    icon: { family: 'MaterialCommunityIcons', name: 'calendar-clock', size: 28 },
    title: 'Custom Reminders',
    subtitle: 'Scheduled tasks & appointments',
    route: '/patient/custom-alerts',
    color: '#FF9F1C',
    gradientColors: ['#FF9F1C', '#F59E0B'],
    priority: 'medium',
    category: 'management',
  },
  {
    id: 8,
    icon: { family: 'Feather', name: 'check-circle', size: 28 },
    title: 'Check In',
    subtitle: 'Patient status & wellness checks',
    route: '/patient-check-in',
    color: '#5FB49C',
    gradientColors: ['#5FB49C', '#10B981'],
    priority: 'medium',
    category: 'management',
  },
];

/**
 * Patient Profile Card for tablet layout
 */
const PatientProfileTablet: React.FC<{ patientData: PatientData }> = ({ patientData }) => {
  const tabletTheme = useTabletTheme();

  const formatDate = useCallback((dateString: string | undefined) => {
    if (!dateString) return 'Not provided';
    return moment(dateString, 'MM-DD-YYYY').format('MMMM D, YYYY');
  }, []);

  const formatPhone = useCallback((phone: string | undefined) => {
    if (!phone) return 'Not provided';
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return phone;
  }, []);

  const formatMRN = useCallback(
    (mrn: string | undefined) => {
      if (!mrn) return patientData.patientId?.toString() || 'N/A';
      return mrn.toString();
    },
    [patientData.patientId]
  );

  return (
    <View style={[
      styles.profileCard,
      {
        padding: tabletTheme.spacing.get('xl'),
        borderRadius: tabletTheme.borderRadius('large'),
        ...tabletTheme.shadow('medium'),
      }
    ]}>
      {/* Header */}
      <View style={styles.profileHeader}>
        <View style={styles.profileInfo}>
          <Text style={[
            styles.patientName,
            { fontSize: tabletTheme.typography.get('h2').fontSize }
          ]}>
            {patientData.patientName || 'Unknown Patient'}
          </Text>
          <View style={[styles.mrnBadge, { backgroundColor: CoreColors.TurquoiseBlue }]}>
            <Text style={styles.mrnText}>MRN: {formatMRN(patientData.mrn)}</Text>
          </View>
        </View>
      </View>

      {/* Patient Details Grid */}
      <View style={styles.detailsGrid}>
        <View style={styles.detailItem}>
          <View style={styles.detailIconContainer}>
            <MaterialCommunityIcons
              name="calendar-outline"
              size={isTablet() ? iconSizes.lg : iconSizes.md}
              color={CoreColors.TurquoiseBlue}
            />
          </View>
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Date of Birth</Text>
            <Text style={[
              styles.detailValue,
              { fontSize: tabletTheme.typography.get('body').fontSize }
            ]}>
              {formatDate(patientData.dob)}
            </Text>
          </View>
        </View>

        <View style={styles.detailItem}>
          <View style={styles.detailIconContainer}>
            <Ionicons
              name="call-outline"
              size={isTablet() ? iconSizes.lg : iconSizes.md}
              color={CoreColors.TurquoiseBlue}
            />
          </View>
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Phone Number</Text>
            <Text style={[
              styles.detailValue,
              { fontSize: tabletTheme.typography.get('body').fontSize }
            ]}>
              {formatPhone(patientData.phone)}
            </Text>
          </View>
        </View>

        {patientData.address && (
          <View style={[styles.detailItem, styles.detailItemFull]}>
            <View style={styles.detailIconContainer}>
              <Ionicons
                name="location-outline"
                size={isTablet() ? iconSizes.lg : iconSizes.md}
                color={CoreColors.TurquoiseBlue}
              />
            </View>
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Address</Text>
              <Text style={[
                styles.detailValue,
                { fontSize: tabletTheme.typography.get('body').fontSize }
              ]}>
                {patientData.address}
              </Text>
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

/**
 * Feature Grid Item for tablet layout
 */
const FeatureGridItem: React.FC<{
  feature: Feature;
  patientId: string;
  onPress?: (feature: Feature) => void;
}> = ({ feature, patientId, onPress }) => {
  const router = useRouter();
  const tabletTheme = useTabletTheme();

  const handlePress = useCallback(() => {
    if (onPress) {
      onPress(feature);
    } else {
      // Default navigation
      try {
        router.push({
          pathname: feature.route as any,
          params: { patientId },
        });
      } catch (error) {
        console.error('Navigation error:', error);
      }
    }
  }, [feature, patientId, onPress, router]);

  const renderIcon = useCallback(() => {
    const iconProps = {
      size: isTablet() ? iconSizes.xl : iconSizes.lg,
      color: '#FFFFFF',
    };

    switch (feature.icon.family) {
      case 'MaterialIcons':
        return <MaterialIcons name={feature.icon.name as any} {...iconProps} />;
      case 'MaterialCommunityIcons':
        return <MaterialCommunityIcons name={feature.icon.name as any} {...iconProps} />;
      case 'Ionicons':
        return <Ionicons name={feature.icon.name as any} {...iconProps} />;
      case 'FontAwesome5':
        return <FontAwesome5 name={feature.icon.name as any} {...iconProps} />;
      case 'Feather':
        return <Feather name={feature.icon.name as any} {...iconProps} />;
      case 'AntDesign':
        return <AntDesign name={feature.icon.name as any} {...iconProps} />;
      default:
        return <MaterialIcons name="dashboard" {...iconProps} />;
    }
  }, [feature.icon]);

  return (
    <TouchableOpacity
      style={[
        styles.featureCard,
        {
          minHeight: isLargeTablet() ? 140 : isTablet() ? 120 : 100,
          borderRadius: tabletTheme.borderRadius('medium'),
          ...tabletTheme.shadow('light'),
        }
      ]}
      onPress={handlePress}
      activeOpacity={0.9}
    >
      <LinearGradient
        colors={feature.gradientColors}
        style={styles.featureGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.featureContent}>
          <View style={[
            styles.featureIconContainer,
            {
              width: tabletTheme.touchTarget('spacious'),
              height: tabletTheme.touchTarget('spacious'),
            }
          ]}>
            {renderIcon()}
          </View>
          
          <View style={styles.featureTextContainer}>
            <Text style={[
              styles.featureTitle,
              { fontSize: tabletTheme.typography.get('h6').fontSize }
            ]}>
              {feature.title}
            </Text>
            <Text style={[
              styles.featureSubtitle,
              { fontSize: tabletTheme.typography.get('bodySmall').fontSize }
            ]}>
              {feature.subtitle}
            </Text>
          </View>

          {feature.priority === 'high' && (
            <View style={styles.priorityBadge}>
              <Text style={styles.priorityText}>!</Text>
            </View>
          )}
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

/**
 * Main Patient Detail Tablet Component
 */
const PatientDetailTablet: React.FC<PatientDetailTabletProps> = ({
  patientData,
  loading = false,
  onFeaturePress,
  style,
}) => {
  const { orientation } = useOrientation();
  const tabletTheme = useTabletTheme();

  // Group features by category for better organization
  const featuresByCategory = useMemo(() => {
    const grouped = TABLET_FEATURES.reduce((acc, feature) => {
      if (!acc[feature.category]) {
        acc[feature.category] = [];
      }
      acc[feature.category].push(feature);
      return acc;
    }, {} as Record<string, Feature[]>);

    return grouped;
  }, []);

  const categoryLabels = {
    clinical: 'Clinical Care',
    monitoring: 'Monitoring & Alerts',
    management: 'Care Management',
    communication: 'Communication',
  };

  // Render feature section
  const renderFeatureSection = useCallback((category: string, features: Feature[]) => (
    <View key={category} style={styles.featureSection}>
      <Text style={[
        styles.sectionTitle,
        { fontSize: tabletTheme.typography.get('h3').fontSize }
      ]}>
        {categoryLabels[category as keyof typeof categoryLabels] || category}
      </Text>
      
      <View style={[
        styles.featuresGrid,
        {
          gridTemplateColumns: orientation === 'landscape' && isLargeTablet() 
            ? 'repeat(3, 1fr)' 
            : orientation === 'landscape' && isTablet()
            ? 'repeat(2, 1fr)'
            : 'repeat(2, 1fr)',
        }
      ]}>
        {features.map((feature) => (
          <FeatureGridItem
            key={feature.id}
            feature={feature}
            patientId={patientData.patientId.toString()}
            onPress={onFeaturePress}
          />
        ))}
      </View>
    </View>
  ), [orientation, tabletTheme, patientData.patientId, onFeaturePress]);

  // Phone layout (single column)
  const phoneLayout = (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <PatientProfileTablet patientData={patientData} />
      {Object.entries(featuresByCategory).map(([category, features]) =>
        renderFeatureSection(category, features)
      )}
    </ScrollView>
  );

  // Tablet portrait layout (enhanced single column)
  const tabletPortraitLayout = (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <PatientProfileTablet patientData={patientData} />
      {Object.entries(featuresByCategory).map(([category, features]) =>
        renderFeatureSection(category, features)
      )}
    </ScrollView>
  );

  // Tablet landscape layout (side-by-side panels)
  const tabletLandscapeLayout = (
    <View style={styles.landscapeContainer}>
      {/* Left Panel - Patient Profile */}
      <View style={styles.leftPanel}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <PatientProfileTablet patientData={patientData} />
        </ScrollView>
      </View>

      {/* Right Panel - Features */}
      <View style={styles.rightPanel}>
        <ScrollView showsVerticalScrollIndicator={false}>
          {Object.entries(featuresByCategory).map(([category, features]) =>
            renderFeatureSection(category, features)
          )}
        </ScrollView>
      </View>
    </View>
  );

  return (
    <TabletAwareContainer style={[styles.wrapper, style]}>
      <AdaptiveLayout
        phoneLayout={phoneLayout}
        tabletPortraitLayout={tabletPortraitLayout}
        tabletLandscapeLayout={tabletLandscapeLayout}
        enableTransitions={true}
      />
    </TabletAwareContainer>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    backgroundColor: '#FAFBFC',
  } as ViewStyle,

  container: {
    flex: 1,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
  } as ViewStyle,

  landscapeContainer: {
    flex: 1,
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
  } as ViewStyle,

  leftPanel: {
    width: '35%',
    paddingRight: spacing.lg,
  } as ViewStyle,

  rightPanel: {
    flex: 1,
    paddingLeft: spacing.lg,
  } as ViewStyle,

  // Profile Card Styles
  profileCard: {
    backgroundColor: '#FFFFFF',
    marginBottom: spacing.xl,
  } as ViewStyle,

  profileHeader: {
    marginBottom: spacing.lg,
  } as ViewStyle,

  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  } as ViewStyle,

  patientName: {
    fontWeight: '700',
    color: CoreColors.DarkJungleGreen,
    flex: 1,
    marginRight: spacing.md,
  } as TextStyle,

  mrnBadge: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 8,
  } as ViewStyle,

  mrnText: {
    fontSize: typography.bodySmall.fontSize,
    fontWeight: '600',
    color: '#FFFFFF',
  } as TextStyle,

  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -spacing.sm,
  } as ViewStyle,

  detailItem: {
    width: '50%',
    paddingHorizontal: spacing.sm,
    marginBottom: spacing.lg,
    flexDirection: 'row',
    alignItems: 'flex-start',
  } as ViewStyle,

  detailItemFull: {
    width: '100%',
  } as ViewStyle,

  detailIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: CoreColors.TurquoiseBlue + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  } as ViewStyle,

  detailContent: {
    flex: 1,
  } as ViewStyle,

  detailLabel: {
    fontSize: typography.caption.fontSize,
    color: CoreColors.GrayBlue,
    marginBottom: spacing.xs,
    fontWeight: '500',
  } as TextStyle,

  detailValue: {
    color: CoreColors.DarkJungleGreen,
    fontWeight: '400',
    lineHeight: typography.body.lineHeight,
  } as TextStyle,

  // Feature Section Styles
  featureSection: {
    marginBottom: spacing.xl,
  } as ViewStyle,

  sectionTitle: {
    fontWeight: '700',
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.lg,
  } as TextStyle,

  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -spacing.sm,
  } as ViewStyle,

  featureCard: {
    width: '48%',
    marginHorizontal: '1%',
    marginBottom: spacing.md,
    overflow: 'hidden',
  } as ViewStyle,

  featureGradient: {
    flex: 1,
    padding: spacing.lg,
  } as ViewStyle,

  featureContent: {
    flex: 1,
    position: 'relative',
  } as ViewStyle,

  featureIconContainer: {
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
  } as ViewStyle,

  featureTextContainer: {
    flex: 1,
  } as ViewStyle,

  featureTitle: {
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: spacing.xs,
  } as TextStyle,

  featureSubtitle: {
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: typography.bodySmall.lineHeight,
  } as TextStyle,

  priorityBadge: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  } as ViewStyle,

  priorityText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '700',
  } as TextStyle,
});

export default PatientDetailTablet;