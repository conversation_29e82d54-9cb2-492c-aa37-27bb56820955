import { DefaultTheme, ThemeProvider as NavigationThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import 'react-native-reanimated';
import { View, Platform } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { CoreColors } from '@/constants/Colors';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import ReduxProvider from '@/services/ReduxProvider';
import { ensureOrganizationId } from '@/utils/getOrganizationId';
import ThemeProvider from '@/constants/ThemeProvider';
import { CCMContentProvider } from '@/context/CCMContentContext';
import { AlertsCountProvider } from '@/context/AlertsCountContext';
import NotificationProvider from '@/utils/NotificationManager';
import RouteProvider from '@/context/RouteContext';
import { initializeFirebaseApp } from '@/utils/firebaseConfig';

import ToastNotification from '@/components/ToastNotification';
// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded] = useFonts({
    // Legacy font
    SpaceMono: require('@/assets/fonts/SpaceMono-Regular.ttf'),
    // New Inter font family
    'Inter-Regular': require('@/assets/fonts/OpenSans-Regular.ttf'),
    'Inter-Medium': require('@/assets/fonts/OpenSans-Medium.ttf'),
    'Inter-SemiBold': require('@/assets/fonts/OpenSans-SemiBold.ttf'),
    'Inter-Bold': require('@/assets/fonts/OpenSans-Bold.ttf'),
    // DM Sans font family
    'DMSans-Regular': require('@/assets/fonts/DMSans-Regular.ttf'),
    'DMSans-Bold': require('@/assets/fonts/DMSans-Bold.ttf'),
  });

  // Ensure organization ID is set
  useEffect(() => {
    ensureOrganizationId();
  }, []);

  useEffect(() => {
    // Initialize Firebase early in the app lifecycle
    try {
      initializeFirebaseApp();
      console.log('Firebase initialized in app layout');
    } catch (error) {
      console.error('Error initializing Firebase in app layout:', error);
    }
    
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <ReduxProvider>
          <ThemeProvider>
            <RouteProvider>
              <AlertsCountProvider>
                <NotificationProvider>
                  <CCMContentProvider>
                    <NavigationThemeProvider value={DefaultTheme}>
                      <View style={{
                        flex: 1,
                        backgroundColor: '#FFFFFF',
                        overflow: 'visible', // Allow header to be visible
                        position: 'relative', // Establish stacking context
                      }}>
                        <Stack
                          screenOptions={{
                          // Using only supported properties for NativeStackNavigationOptions
                          headerStyle: {
                            backgroundColor: CoreColors.TurquoiseBlue,
                          },
                          headerTintColor: '#fff',
                          // Set headerShown to false for all screens since we're using our custom header
                          headerShown: false
                        }}
                      >
                        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
                        <Stack.Screen name="onboarding" options={{ headerShown: false }} />
                        <Stack.Screen name="auth" options={{ headerShown: false }} />
                        <Stack.Screen name="organization" options={{ headerShown: false, title: 'Select Organization' }} />
                        <Stack.Screen name="patient" options={{ headerShown: false }} />
                        <Stack.Screen name="profile" options={{ headerShown: false }} />
                        <Stack.Screen name="splash" options={{ headerShown: false }} />
                        <Stack.Screen name="validation" options={{ headerShown: false, title: 'Validation' }} />
                        <Stack.Screen name="billing" options={{ headerShown: false, title: 'Billing Information' }} />
                        <Stack.Screen name="+not-found" options={{ headerShown: false }} />
                      </Stack>

                      <StatusBar 
                        style="light" 
                        backgroundColor={CoreColors.TurquoiseBlue} 
                        translucent={Platform.OS === 'ios'}
                      />
                      </View>
                    </NavigationThemeProvider>
                  </CCMContentProvider>
                </NotificationProvider>
              </AlertsCountProvider>
            </RouteProvider>
          </ThemeProvider>
        </ReduxProvider>
        <ToastNotification />
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}
