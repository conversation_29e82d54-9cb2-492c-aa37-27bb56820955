import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Colors } from '@/constants';

interface CheckmarkIconProps {
  color?: string;
  size?: number;
}

const CheckmarkIcon: React.FC<CheckmarkIconProps> = ({ 
  color = Colors.TealBlue, 
  size = 18 
}) => {
  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <View style={[
        styles.shortLine, 
        { 
          backgroundColor: color,
          width: size * 0.3,
          height: size * 0.15,
          bottom: size * 0.3,
          left: size * 0.2,
          transform: [{ rotate: '45deg' }]
        }
      ]} />
      <View style={[
        styles.longLine, 
        { 
          backgroundColor: color,
          width: size * 0.6,
          height: size * 0.15,
          bottom: size * 0.4,
          right: size * 0.1,
          transform: [{ rotate: '-45deg' }]
        }
      ]} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  shortLine: {
    position: 'absolute',
    borderRadius: 1,
  },
  longLine: {
    position: 'absolute',
    borderRadius: 1,
  }
});

export default CheckmarkIcon;
