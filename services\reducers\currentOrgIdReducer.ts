import {SELECTED_ORG_ID, CURRENT_ORG_DETAILS} from '../actions/type';

const initialState = {
  orgId: null,
  orgDetails: null,
};

export default function currentOrgIdReducer(state = initialState, action: any) {
  switch (action.type) {
    case SELECTED_ORG_ID:
      return {
        ...state,
        orgId: action.data,
      };
    case CURRENT_ORG_DETAILS:
      return {
        ...state,
        orgDetails: action.data,
        orgId: action.data?.id || state.orgId,
      };
    default:
      return state;
  }
}
