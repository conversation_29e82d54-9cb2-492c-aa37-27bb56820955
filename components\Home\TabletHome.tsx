import React from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
} from 'react-native';
import { TabletAwareContainer } from '@/components/Layout/TabletAwareContainer';
import { ResponsiveGrid } from '@/components/Layout/ResponsiveGrid';
import { AccessibleText } from '@/components/Accessibility/AccessibleText';
import { isTablet, isLargeTablet, spacing, typography } from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';

interface TabletHomeProps {
  refreshing?: boolean;
  onRefresh?: () => void;
}

/**
 * Tablet-optimized Home screen component
 */
const TabletHome: React.FC<TabletHomeProps> = ({
  refreshing = false,
  onRefresh,
}) => {
  // Sample dashboard data - replace with actual data from Redux
  const dashboardItems = [
    {
      id: '1',
      title: 'Active Patients',
      value: '24',
      subtitle: '3 new this week',
      color: CoreColors.TurquoiseBlue,
    },
    {
      id: '2',
      title: 'Critical Alerts',
      value: '2',
      subtitle: 'Requires attention',
      color: CoreColors.RedOrange,
    },
    {
      id: '3',
      title: 'Scheduled Calls',
      value: '8',
      subtitle: 'Today',
      color: CoreColors.TealBlue,
    },
    {
      id: '4',
      title: 'Pending Tasks',
      value: '12',
      subtitle: '5 overdue',
      color: CoreColors.YellowOrange,
    },
  ];

  const renderDashboardCard = (item: typeof dashboardItems[0]) => (
    <View key={item.id} style={[styles.dashboardCard, { borderLeftColor: item.color }]}>
      <AccessibleText variant="h1" style={styles.cardValue}>
        {item.value}
      </AccessibleText>
      <AccessibleText variant="h3" style={styles.cardTitle}>
        {item.title}
      </AccessibleText>
      <AccessibleText variant="bodySmall" style={styles.cardSubtitle}>
        {item.subtitle}
      </AccessibleText>
    </View>
  );

  const recentActivities = [
    {
      id: '1',
      type: 'video_call',
      patient: 'John Smith',
      time: '2 hours ago',
      description: 'Completed video consultation',
    },
    {
      id: '2',
      type: 'alert',
      patient: 'Sarah Johnson',
      time: '4 hours ago',
      description: 'Critical alert resolved',
    },
    {
      id: '3',
      type: 'task',
      patient: 'Mike Davis',
      time: '6 hours ago',
      description: 'Care plan updated',
    },
  ];

  const renderActivityItem = (activity: typeof recentActivities[0]) => (
    <View key={activity.id} style={styles.activityItem}>
      <View style={styles.activityIcon}>
        <AccessibleText variant="caption" style={styles.activityType}>
          {activity.type.toUpperCase()}
        </AccessibleText>
      </View>
      <View style={styles.activityContent}>
        <AccessibleText variant="body" style={styles.activityPatient}>
          {activity.patient}
        </AccessibleText>
        <AccessibleText variant="bodySmall" style={styles.activityDescription}>
          {activity.description}
        </AccessibleText>
        <AccessibleText variant="caption" style={styles.activityTime}>
          {activity.time}
        </AccessibleText>
      </View>
    </View>
  );

  return (
    <TabletAwareContainer style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <AccessibleText variant="h1" style={styles.welcomeText}>
            Welcome Back
          </AccessibleText>
          <AccessibleText variant="body" style={styles.dateText}>
            {new Date().toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })}
          </AccessibleText>
        </View>

        {/* Dashboard Cards */}
        <View style={styles.section}>
          <AccessibleText variant="h2" style={styles.sectionTitle}>
            Dashboard Overview
          </AccessibleText>
          <ResponsiveGrid
            data={dashboardItems}
            renderItem={({ item }) => renderDashboardCard(item)}
            numColumns={isLargeTablet() ? 4 : 2}
            spacing={spacing.md}
          />
        </View>

        {/* Recent Activities */}
        <View style={styles.section}>
          <AccessibleText variant="h2" style={styles.sectionTitle}>
            Recent Activities
          </AccessibleText>
          <View style={styles.activitiesContainer}>
            {recentActivities.map(renderActivityItem)}
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <AccessibleText variant="h2" style={styles.sectionTitle}>
            Quick Actions
          </AccessibleText>
          <ResponsiveGrid
            data={[
              { id: '1', title: 'New Patient', icon: 'add' },
              { id: '2', title: 'Schedule Call', icon: 'calendar' },
              { id: '3', title: 'View Alerts', icon: 'alert' },
              { id: '4', title: 'Generate Report', icon: 'report' },
            ]}
            renderItem={({ item }) => (
              <View style={styles.quickActionCard}>
                <AccessibleText variant="body" style={styles.quickActionTitle}>
                  {item.title}
                </AccessibleText>
              </View>
            )}
            numColumns={isLargeTablet() ? 4 : 2}
            spacing={spacing.md}
          />
        </View>
      </ScrollView>
    </TabletAwareContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.md,
  },
  welcomeText: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.xs,
  },
  dateText: {
    color: CoreColors.SlateGray,
  },
  section: {
    paddingHorizontal: spacing.md,
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.md,
  },
  dashboardCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: isTablet() ? 12 : 8,
    padding: spacing.lg,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardValue: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.xs,
  },
  cardTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.xs,
  },
  cardSubtitle: {
    color: CoreColors.SlateGray,
  },
  activitiesContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: isTablet() ? 12 : 8,
    padding: spacing.md,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  activityItem: {
    flexDirection: 'row',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  activityIcon: {
    width: 60,
    height: 40,
    backgroundColor: CoreColors.TurquoiseBlue,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  activityType: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  activityContent: {
    flex: 1,
  },
  activityPatient: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.xs,
  },
  activityDescription: {
    color: CoreColors.SlateGray,
    marginBottom: spacing.xs,
  },
  activityTime: {
    color: CoreColors.SlateGray,
  },
  quickActionCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: isTablet() ? 12 : 8,
    padding: spacing.lg,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: isTablet() ? 100 : 80,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickActionTitle: {
    color: CoreColors.DarkJungleGreen,
    textAlign: 'center',
  },
});

export default TabletHome;