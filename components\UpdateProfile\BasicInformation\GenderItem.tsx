import React from 'react';
import {
  Image,
  ImageSourcePropType,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import Text from '@/components/Text';
import {Colors} from '@/constants';
import {useTheme} from '@/constants/Theme';
import Theme from '@/constants/Theme';
import scale from '@/utils/scale';

interface GenderItemProps {
  isChoose?: boolean;
  isLastItem?: boolean;
  icon: ImageSourcePropType;
  title?: string;
  onPress: () => void;
}

const GenderItem = (props: GenderItemProps) => {
  const {theme} = useTheme();
  return (
    <View style={[styles.container, !props.isLastItem && styles.notLastItem]}>
      <TouchableOpacity
        style={[
          styles.touch,
          props.isChoose && styles.touchActive,
          {borderColor: theme.innearColor},
        ]}
        activeOpacity={0.54}
        onPress={props.onPress}>
        <Image
          source={props.icon}
          style={[
            styles.icon,
            props.isChoose ? styles.activeIcon : styles.inactiveIcon,
            {
              tintColor: props.isChoose ? theme.text : theme.innearColor,
            },
          ]}
        />
      </TouchableOpacity>
      <Text
        size={13}
        lineHeight={22}
        bold={props.isChoose}
        color={props.isChoose ? Colors.DarkJungleGreen : Colors.GrayBlue}
        center
        marginTop={16}>
        {props.title}
      </Text>
    </View>
  );
};

export default GenderItem;

const styles = StyleSheet.create({
  container: {},
  notLastItem: {
    marginRight: 48,
  },
  touch: {
    width: scale(72),
    height: scale(72),
    borderRadius: 24,
    borderColor: Colors.Platinum,
    borderWidth: 1,
    ...Theme.center,
  },
  touchActive: {
    backgroundColor: Colors.DodgerBlue,
    borderColor: Colors.DodgerBlue,
  },
  icon: {
    width: 32,
    height: 32,
  },
  inactiveIcon: {
    tintColor: Colors.DarkNavy,
  },
  activeIcon: {
    tintColor: Colors.White,
  },
});
