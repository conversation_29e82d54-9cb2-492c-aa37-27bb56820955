/**
 * Formats a number to a more readable format using 'k' suffix for thousands
 * 
 * @param value The number to format (can be a number, number string, or undefined/null)
 * @param decimalPlaces The number of decimal places to show (default: 1)
 * @param threshold The minimum value before using 'k' formatting (default: 1000)
 * @returns Formatted number as string or number
 */
export default function formatWithKSuffix(
  value: number | string | undefined | null,
  decimalPlaces: number = 1,
  threshold: number = 1000
): string | number {
  // Handle null/undefined
  if (value === null || value === undefined) {
    return 0;
  }

  // Convert to number if it's a string
  const num = typeof value === 'string' ? parseFloat(value) : value;

  // Handle NaN
  if (isNaN(num)) {
    return 0;
  }

  // Format with 'k' suffix if above threshold
  if (Math.abs(num) >= threshold) {
    const sign = Math.sign(num);
    const absNum = Math.abs(num);
    const formattedNum = (absNum / 1000).toFixed(decimalPlaces);
    
    // Remove trailing zeros after decimal point if needed
    const cleanedNum = 
      decimalPlaces > 0 && formattedNum.endsWith('0'.repeat(decimalPlaces)) 
      ? (absNum / 1000).toFixed(0) 
      : formattedNum;
      
    return sign * parseFloat(cleanedNum) + 'k';
  }
  
  // Return original number for small values
  return Math.sign(num) * Math.abs(num);
}
