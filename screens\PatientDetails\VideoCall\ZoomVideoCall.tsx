import { Colors } from "@/constants";
import { CoreColors } from "@/constants/Colors";
import { MaterialIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import {
  EventType,
  VideoAspect,
  ZoomVideoSdkUser,
  ZoomVideoSdkUserType,
  ZoomView,
  useZoom,
} from "@zoom/react-native-videosdk";
import React, { useEffect, useRef, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  EmitterSubscription,
  FlatList,
  Modal,
  PermissionsAndroid,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Animated,
  Dimensions,
  Linking,
} from "react-native";
import { PanGestureHandler, State } from "react-native-gesture-handler";
import Container from "@/components/Layout/Container";
import NavigationHeader from "@/components/NavigationHeader";
import { Camera } from "expo-camera";
import { Audio } from "expo-av";

interface ZoomVideoCallProps {
  patientId: string | number;
  patientName: string;
  accessToken: string;
  meetingNumber: string;
  password: string;
  userName?: string;
  onCallEnd?: () => void;
  program?: string;
  onProgramChange?: (program: string) => void;
  programOptions?: Array<{programName: string, programId: string, mins: number, programActivated: boolean}>;
}

// Permission status types
interface PermissionStatus {
  camera: boolean;
  microphone: boolean;
  canProceed: boolean;
}

const ZoomVideoCall: React.FC<ZoomVideoCallProps> = ({
  patientName,
  accessToken,
  meetingNumber,
  password,
  userName = "Care Manager",
  onCallEnd,
  program = "",
  onProgramChange,
  programOptions = [],
}) => {
  const navigation = useNavigation();
  const zoom = useZoom();
  const listeners = useRef<EmitterSubscription[]>([]);

  // State variables
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isFrontCamera, setIsFrontCamera] = useState(true); // Add state for camera position
  const [users, setUsers] = useState<ZoomVideoSdkUser[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [showPreCallScreen, setShowPreCallScreen] = useState(true); // Always start with pre-call screen
  const [selectedProgram, setSelectedProgram] = useState<string>(program);
  const [hasRemoteUserEverJoined, setHasRemoteUserEverJoined] = useState(false); // Track if remote user has joined
  
  // Permission state
  const [permissionStatus, setPermissionStatus] = useState<PermissionStatus>({
    camera: false,
    microphone: false,
    canProceed: false,
  });
  const [isCheckingPermissions, setIsCheckingPermissions] = useState(false);
  
  // Movable video position state
  const screenDimensions = Dimensions.get('window');
  const translateX = useRef(new Animated.Value(screenDimensions.width - 140)).current; // Start from right
  const translateY = useRef(new Animated.Value(120)).current; // Start below header
  const lastOffset = useRef({ x: screenDimensions.width - 140, y: 120 });  
  // Map program options to the format needed for the dropdown
  const programCategories = programOptions.map(program => ({
    label: program.programName,
    value: program.programId,
    activated: program.programActivated
  }));

  // Comprehensive permission check function
  const checkAndRequestPermissions = async (): Promise<PermissionStatus> => {
    setIsCheckingPermissions(true);
    
    try {
      let cameraPermission = false;
      let microphonePermission = false;

      if (Platform.OS === 'android') {
        // Android permission handling
        console.log('Checking Android permissions...');
        
        // Check existing permissions first
        const cameraStatus = await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.CAMERA);
        const audioStatus = await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.RECORD_AUDIO);
        
        console.log('Existing Android permissions - Camera:', cameraStatus, 'Audio:', audioStatus);
        
        if (cameraStatus && audioStatus) {
          cameraPermission = true;
          microphonePermission = true;
        } else {
          // Request permissions that are not granted
          const permissionsToRequest: Array<keyof typeof PermissionsAndroid.PERMISSIONS> = [];
          if (!cameraStatus) permissionsToRequest.push('CAMERA');
          if (!audioStatus) permissionsToRequest.push('RECORD_AUDIO');
          
          const granted = await PermissionsAndroid.requestMultiple(
            permissionsToRequest.map(permission => PermissionsAndroid.PERMISSIONS[permission])
          );
          
          cameraPermission = granted[PermissionsAndroid.PERMISSIONS.CAMERA] === PermissionsAndroid.RESULTS.GRANTED;
          microphonePermission = granted[PermissionsAndroid.PERMISSIONS.RECORD_AUDIO] === PermissionsAndroid.RESULTS.GRANTED;
          
          console.log('Android permission request results:', granted);
        }
      } else {
        // iOS permission handling using expo-camera and expo-av
        console.log('Checking iOS permissions...');
        
        try {
          // Check camera permissions
          console.log('Requesting camera permissions...');
          const cameraResult = await Camera.requestCameraPermissionsAsync();
          cameraPermission = cameraResult.status === 'granted';
          console.log('Camera permission result:', cameraResult);
          
          // Check microphone permissions
          console.log('Requesting microphone permissions...');
          const audioResult = await Audio.requestPermissionsAsync();
          microphonePermission = audioResult.status === 'granted';
          console.log('Microphone permission result:', audioResult);
        } catch (error) {
          console.error('Error requesting iOS permissions:', error);
          // Fallback for older expo versions or permission issues
          cameraPermission = false;
          microphonePermission = false;
        }
      }

      const canProceed = cameraPermission && microphonePermission;
      
      const status: PermissionStatus = {
        camera: cameraPermission,
        microphone: microphonePermission,
        canProceed,
      };
      
      console.log('Final permission status:', status);
      setPermissionStatus(status);
      
      return status;
    } catch (error) {
      console.error('Error checking permissions:', error);
      const status: PermissionStatus = {
        camera: false,
        microphone: false,
        canProceed: false,
      };
      setPermissionStatus(status);
      return status;
    } finally {
      setIsCheckingPermissions(false);
    }
  };

  // Show permission denied alert with options
  const showPermissionDeniedAlert = (deniedPermissions: string[]) => {
    const permissionText = deniedPermissions.join(' and ');
    
    Alert.alert(
      "Permissions Required",
      `${permissionText} ${deniedPermissions.length > 1 ? 'permissions are' : 'permission is'} required for video calls. Please grant ${deniedPermissions.length > 1 ? 'these permissions' : 'this permission'} to continue.`,
      [
        {
          text: "Cancel",
          style: "cancel",
          onPress: () => {
            console.log('User cancelled permission request');
          }
        },
        {
          text: "Retry",
          onPress: async () => {
            console.log('User chose to retry permissions');
            await checkAndRequestPermissions();
          }
        },
        {
          text: "Settings",
          onPress: () => {
            console.log('Opening device settings');
            Linking.openSettings();
          }
        }
      ]
    );
  };

  // Initial permission check on component mount
  useEffect(() => {
    checkAndRequestPermissions();
  }, []);

  // Clean up listeners when component unmounts
  useEffect(() => {
    return () => {
      if (listeners.current && listeners.current.length > 0) {
        listeners.current.forEach((listener) => {
          try {
            if (listener && typeof listener.remove === "function") {
              listener.remove();
            }
          } catch (err) {
            console.warn("Error removing listener:", err);
          }
        });
        listeners.current = [];
      }

      // If connected, try to leave the session
      if (isConnected && zoom) {
        try {
          zoom.leaveSession(false);
        } catch (err) {
          console.warn("Error leaving session:", err);
        }
      }
    };
  }, [zoom, isConnected]);

  // Auto-connect when we receive accessToken and meetingNumber from API
  useEffect(() => {
    if (isConnecting && accessToken && meetingNumber && selectedProgram && !isConnected) {
      console.log("Auto-connecting: Received connection info from API");
      // Call connectToCall again now that we have the credentials
      connectToCall();
    }
  }, [accessToken, meetingNumber, isConnecting, selectedProgram, isConnected]);

  // Set up event listeners
  const setupEventListeners = () => {
    // Clean up any existing listeners first
    if (listeners.current.length > 0) {
      listeners.current.forEach((listener) => {
        try {
          listener.remove();
        } catch (err) {
          console.warn("Error removing listener:", err);
        }
      });
      listeners.current = [];
    }

    // Session join event
    const sessionJoinListener = zoom.addListener(
      EventType.onSessionJoin,
      async () => {
        console.log("Session joined successfully");
        setIsConnecting(false);
        setIsConnected(true);

        try {
          if (!zoom.session) {
            console.error("Session object not available");
            return;
          }

          // Get the list of users
          const mySelf = await zoom.session.getMySelf();
          const remoteUsers = await zoom.session.getRemoteUsers();
          setUsers([mySelf, ...remoteUsers]);
          console.log("Remote Users:", remoteUsers);

          // Add listeners for user join/leave events
          const userJoinListener = zoom.addListener(
            EventType.onUserJoin,
            async (event) => {
              const { remoteUsers } = event;
              const mySelf = await zoom.session.getMySelf();
              const remote = remoteUsers.map(
                (user: ZoomVideoSdkUserType) => new ZoomVideoSdkUser(user)
              );
              setUsers([mySelf, ...remote]);
              
              // Mark that a remote user has joined
              if (remote.length > 0) {
                setHasRemoteUserEverJoined(true);
              }
            }
          );

          const userLeaveListener = zoom.addListener(
            EventType.onUserLeave,
            async (event) => {
              const { remoteUsers } = event;
              const mySelf = await zoom.session.getMySelf();
              const remote = remoteUsers.map(
                (user: ZoomVideoSdkUserType) => new ZoomVideoSdkUser(user)
              );
              setUsers([mySelf, ...remote]);
            }
          );

          // Add listener for session ended event
          const sessionEndListener = zoom.addListener(
            EventType.onSessionLeave,
            () => {
              console.log("Session ended");
              setIsConnected(false);
              setUsers([]);
              setShowPreCallScreen(true);
              // Don't reset hasRemoteUserEverJoined here - let it show "user left" if someone had joined
              if (onCallEnd) {
                onCallEnd();
              }
            }
          );

          // Add listeners for audio/video status changes
          const audioStatusChangedListener = zoom.addListener(
            EventType.onUserAudioStatusChanged,
            async (data) => {
              // Update audio state if it's the current user
              if (zoom.session) {
                const mySelf = await zoom.session.getMySelf();
                if (data.userId === mySelf.userId) {
                  setIsAudioEnabled(!data.isMuted);
                }
              }
            }
          );

          const videoStatusChangedListener = zoom.addListener(
            EventType.onUserVideoStatusChanged,
            async (data) => {
              // Update video state if it's the current user
              if (zoom.session) {
                const mySelf = await zoom.session.getMySelf();
                if (data.userId === mySelf.userId) {
                  setIsVideoEnabled(data.isOn);
                }
              }
            }
          );

          // Store all listeners for cleanup
          listeners.current = [
            sessionJoinListener,
            userJoinListener,
            userLeaveListener,
            sessionEndListener,
            audioStatusChangedListener,
            videoStatusChangedListener,
          ];
        } catch (err) {
          console.error("Error setting up event listeners:", err);
        }
      }
    );

    // Session error event
    const sessionErrorListener = zoom.addListener(
      EventType.onError,
      (error) => {
        console.error("Zoom session error:", error);
        setError(`Error: ${error.error}`);
        setIsConnecting(false);
      }
    );

    listeners.current.push(sessionJoinListener, sessionErrorListener);
  };

  // Connect to the call - streamlined approach with automatic progression
  const connectToCall = async () => {
    // Check if program is selected
    if (!selectedProgram) {
      setError("Please select a program before starting the call");
      return;
    }
    
    try {
      console.log("Initiating call with program:", selectedProgram);
      
      // STEP 1: Check permissions before proceeding
      console.log("Checking permissions before starting video call...");
      const currentPermissions = await checkAndRequestPermissions();
      
      if (!currentPermissions.canProceed) {
        console.log("Permissions not granted, cannot proceed with video call");
        
        // Determine which permissions are missing
        const deniedPermissions: string[] = [];
        if (!currentPermissions.camera) deniedPermissions.push("Camera");
        if (!currentPermissions.microphone) deniedPermissions.push("Microphone");
        
        // Show permission denied alert
        showPermissionDeniedAlert(deniedPermissions);
        
        // Stop here - don't proceed with the video call
        return;
      }
      
      console.log("All permissions granted - proceeding with video call");
      
      // If we don't have connection info yet, we need to get it from the API
      if (!accessToken || !meetingNumber) {
        console.log("Missing connection info - calling API first");
        
        // Show loading state while waiting for API call
        setIsConnecting(true);
        setError(null);
        
        // Tell parent to make API call and get Zoom credentials
        if (onProgramChange) {
          onProgramChange(selectedProgram);
        }
        
        // Return here - the useEffect below will handle the actual connection
        // when accessToken and meetingNumber become available
        console.log("Waiting for connection information from API");
        return;
      }
      
      // If we have connection info, proceed with call
      console.log("Connection info available - proceeding with call");
      
      // Set UI state
      setIsConnecting(true);
      setError(null);
      setShowPreCallScreen(false);

      // Validate inputs
      if (!zoom) {
        throw new Error("Video call service unavailable");
      }

      // Set up event listeners
      setupEventListeners();

      // CRITICAL: Use the exact meeting number as provided by the API
      // The "tpc" field in the JWT token must match exactly with the sessionName
      // DO NOT modify the meeting number at all
      console.log(`Connecting to meeting: ${meetingNumber}`);

      // Add a delay before joining to ensure SDK is ready
      await new Promise((resolve) => setTimeout(resolve, 1500)); // Increased delay to 1.5 seconds

      // Prepare session configuration - following Zoom documentation exactly
      const sessionConfig = {
        sessionName: meetingNumber, // Must be EXACTLY the same as in the JWT token's "tpc" field
        sessionPassword: password || "", // Empty string if no password
        userName: userName || "Care Manager",
        token: accessToken,
        sessionIdleTimeoutMins: 40,
        audioOptions: {
          connect: true,
          mute: false,
          autoAdjustSpeakerVolume: true,
        },
        videoOptions: {
          localVideoOn: true,
          videoQuality: 0, // Use default quality
        },
        renderMode: 0, // Default render mode
      };

      console.log(
        "Joining session with config:",
        JSON.stringify({
          sessionName: sessionConfig.sessionName,
          userName: sessionConfig.userName,
          // Don't log the token for security reasons
        })
      );

      // Join the session - using a try/catch inside to get more detailed error info
      try {
        const result = await zoom.joinSession(sessionConfig);
        console.log("Join session result:", result);
      } catch (joinError) {
        console.error("Specific join error:", joinError);
        throw joinError; // Re-throw to be caught by the outer catch
      }

      console.log("Join session call completed");
    } catch (err) {
      console.error("Error connecting to call:", err);

      // Handle error and update UI
      let errorMessage = "Failed to connect to call";
      if (err instanceof Error) {
        errorMessage = `${err.message}`;
        // Log additional details for debugging
        console.error("Error details:", JSON.stringify(err));
      }

      setError(errorMessage);
      setIsConnecting(false);
      setShowPreCallScreen(true);

      // Clean up listeners on error
      if (listeners.current.length > 0) {
        listeners.current.forEach((listener) => {
          try {
            listener.remove();
          } catch (e) {
            // Ignore
          }
        });
        listeners.current = [];
      }
    }
  };

  // End the call
  const handleEndCall = async () => {
    if (!zoom) return;

    try {
      if (listeners.current.length > 0) {
        listeners.current.forEach((listener) => {
          try {
            listener.remove();
          } catch (err) {
            console.warn("Error removing listener:", err);
          }
        });
        listeners.current = [];
      }
      if (zoom.session) {
        zoom.leaveSession(true);
      }
      // Reset states when user intentionally ends call
      setIsConnected(false);
      setUsers([]);
      setShowPreCallScreen(true);
      setHasRemoteUserEverJoined(false); // Reset only when user ends call

      // Call the onCallEnd callback if provided
      if (onCallEnd) {
        onCallEnd();
      }
    } catch (err) {
      console.error("Error ending call:", err);
      // Even if there's an error, try to clean up and navigate back
      if (onCallEnd) {
        onCallEnd();
      }
      navigation.goBack();
    }
  };

  // Toggle audio
  const toggleAudio = async () => {
    try {
      if (!zoom || !zoom.audioHelper) {
        return;
      }

      // Toggle the state first for immediate UI feedback
      setIsAudioEnabled(!isAudioEnabled);

      // If we're in a session, actually toggle the audio
      if (isConnected && zoom.session) {
        try {
          const mySelf = await zoom.session.getMySelf();
          if (!mySelf) return;

          const userId = mySelf.userId;

          if (isAudioEnabled) {
            // Currently enabled, so mute it
            await zoom.audioHelper.muteAudio(userId);
          } else {
            // Currently disabled, so unmute it
            await zoom.audioHelper.unmuteAudio(userId);
          }
        } catch (err) {
          console.error("Error toggling audio in session:", err);
          // Revert state if there was an error
          setIsAudioEnabled(isAudioEnabled);
        }
      }
    } catch (err) {
      console.error("Error toggling audio:", err);
      // Revert state if there was an error
      setIsAudioEnabled(isAudioEnabled);
    }
  };

  // Toggle video
  const toggleVideo = async () => {
    try {
      if (!zoom || !zoom.videoHelper) {
        return;
      }

      // Toggle the state first for immediate UI feedback
      setIsVideoEnabled(!isVideoEnabled);

      // If we're in a session, actually toggle the video
      if (isConnected) {
        try {
          if (isVideoEnabled) {
            // Currently enabled, so stop it
            await zoom.videoHelper.stopVideo();
          } else {
            // Currently disabled, so start it
            await zoom.videoHelper.startVideo();
          }
        } catch (err) {
          console.error("Error toggling video in session:", err);
          // Revert state if there was an error
          setIsVideoEnabled(isVideoEnabled);
        }
      }
    } catch (err) {
      console.error("Error toggling video:", err);
      // Revert state if there was an error
      setIsVideoEnabled(isVideoEnabled);
    }
  };

  // Add camera flip function
  const toggleCamera = async () => {
    try {
      if (!zoom || !zoom.videoHelper) {
        return;
      }

      // Toggle the state first for immediate UI feedback
      setIsFrontCamera(!isFrontCamera);

      // If we're in a session, actually switch the camera
      if (isConnected) {
        try {
          await zoom.videoHelper.switchCamera();
        } catch (err) {
          console.error("Error switching camera:", err);
          // Revert state if there was an error
          setIsFrontCamera(!isFrontCamera);
        }
      }
    } catch (err) {
      console.error("Error toggling camera:", err);
      // Revert state if there was an error
      setIsFrontCamera(!isFrontCamera);
    }
  };

  // Pan gesture handlers for movable video
  const onGestureEvent = Animated.event(
    [
      {
        nativeEvent: {
          translationX: translateX,
          translationY: translateY,
        },
      },
    ],
    { useNativeDriver: false }
  );

  const onHandlerStateChange = (event: any) => {
    if (event.nativeEvent.oldState === State.ACTIVE) {
      const { translationX: deltaX, translationY: deltaY } = event.nativeEvent;
      
      // Calculate new position based on last known position plus delta
      let newPosX = lastOffset.current.x + deltaX;
      let newPosY = lastOffset.current.y + deltaY;
      
      // Keep within screen bounds with better margins
      const videoWidth = 120;
      const videoHeight = 160;
      const headerHeight = 100; // Account for header
      const bottomControlsHeight = 160; // Account for floating controls with more space
      const sideMargin = 20;
      
      const maxX = screenDimensions.width - videoWidth - sideMargin;
      const maxY = screenDimensions.height - videoHeight - bottomControlsHeight;
      
      newPosX = Math.max(sideMargin, Math.min(newPosX, maxX));
      newPosY = Math.max(headerHeight, Math.min(newPosY, maxY));
      
      // Update last offset
      lastOffset.current = { x: newPosX, y: newPosY };
      
      // Set the final position directly
      translateX.setValue(newPosX);
      translateY.setValue(newPosY);
      
      // Reset the translation values for next gesture
      translateX.setOffset(newPosX);
      translateY.setOffset(newPosY);
      translateX.setValue(0);
      translateY.setValue(0);
    }
  };

  // Render program radio buttons
  const renderProgramRadioButtons = () => {
    return (
      <View style={styles.radioButtonsContainer}>
        {programCategories.map((item) => (
          <TouchableOpacity
            key={item.value}
            style={[styles.radioButtonRow, !item.activated && styles.disabledRadioButton]}
            onPress={() => {
              if (item.activated) {
                setSelectedProgram(item.value);
                setError(null);
              }
            }}
            disabled={!item.activated}
          >
            <View style={[styles.radioButton, selectedProgram === item.value && styles.radioButtonSelected]}>
              {selectedProgram === item.value && <View style={styles.radioButtonInner} />}
            </View>
            <Text style={[styles.radioButtonText, !item.activated && styles.disabledRadioButtonText]}>
              {item.label}
              {!item.activated && ' - Not Activated'}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  // Render pre-call screen
  if (showPreCallScreen && !isConnecting && !isConnected && !error) {
    return (
      <Container style={styles.preCallContainer}>
        <NavigationHeader 
          title="Video Call" 
          showBackButton={true}
          showLogo={false}
        />
        
        {/* Main Content */}
        <View style={styles.preCallContent}>
          {/* Patient Info Card */}
          <View style={styles.patientCard}>
            <View style={styles.avatarContainer}>
              <MaterialIcons name="person" size={32} color={CoreColors.TurquoiseBlue} />
            </View>
            <Text style={styles.patientName}>{patientName}</Text>
            <Text style={styles.callSubtitle}>Ready to start your video call</Text>
          </View>

          {/* Program Selection */}
          <View style={styles.preCallProgramSelector}>
            <Text style={styles.preCallProgramLabel}>Select Program Category</Text>
            <View style={styles.preCallRadioContainer}>
              {programCategories.map((item) => (
                <TouchableOpacity
                  key={item.value}
                  style={[
                    styles.preCallRadioButton,
                    selectedProgram === item.value && styles.preCallRadioButtonSelected,
                    !item.activated && styles.preCallRadioButtonDisabled
                  ]}
                  onPress={() => {
                    if (item.activated) {
                      setSelectedProgram(item.value);
                      setError(null);
                    }
                  }}
                  disabled={!item.activated}
                >
                  <View style={[
                    styles.preCallRadioCircle,
                    selectedProgram === item.value && styles.preCallRadioCircleSelected
                  ]}>
                    {selectedProgram === item.value && (
                      <MaterialIcons name="check" size={14} color="white" />
                    )}
                  </View>
                  <View style={styles.preCallRadioTextContainer}>
                    <Text style={[
                      styles.preCallRadioText,
                      !item.activated && styles.preCallRadioTextDisabled
                    ]}>
                      {item.label}
                    </Text>
                    {!item.activated && (
                      <Text style={styles.preCallRadioSubtext}>Not Available</Text>
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </View>
            {error && <Text style={styles.preCallErrorText}>{error}</Text>}
          </View>

          {/* Pre-call Settings */}
          <View style={styles.preCallSettings}>
            <Text style={styles.preCallSettingsTitle}>Call Settings</Text>
            <View style={styles.preCallSettingsGrid}>
              <TouchableOpacity
                style={styles.preCallSettingCard}
                onPress={toggleAudio}
              >
                <View style={[
                  styles.preCallSettingIcon,
                  isAudioEnabled ? styles.preCallSettingIconActive : styles.preCallSettingIconInactive
                ]}>
                  <MaterialIcons
                    name={isAudioEnabled ? "mic" : "mic-off"}
                    size={22}
                    color={isAudioEnabled ? "white" : "#666"}
                  />
                </View>
                <Text style={styles.preCallSettingText}>
                  {isAudioEnabled ? "Mic On" : "Mic Off"}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.preCallSettingCard}
                onPress={toggleVideo}
              >
                <View style={[
                  styles.preCallSettingIcon,
                  isVideoEnabled ? styles.preCallSettingIconActive : styles.preCallSettingIconInactive
                ]}>
                  <MaterialIcons
                    name={isVideoEnabled ? "videocam" : "videocam-off"}
                    size={22}
                    color={isVideoEnabled ? "white" : "#666"}
                  />
                </View>
                <Text style={styles.preCallSettingText}>
                  {isVideoEnabled ? "Video On" : "Video Off"}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Footer with Join Button */}
        <View style={styles.preCallFooter}>
          <TouchableOpacity
            style={[
              styles.preCallJoinButton,
              !selectedProgram && styles.preCallJoinButtonDisabled
            ]}
            onPress={connectToCall}
            disabled={!selectedProgram}
          >
            <MaterialIcons 
              name="videocam" 
              size={24} 
              color="white" 
              style={styles.preCallJoinButtonIcon}
            />
            <Text style={styles.preCallJoinButtonText}>Join Call</Text>
          </TouchableOpacity>
        </View>
      </Container>
    );
  }

  // Render loading state
  if (isConnecting) {
    return (
      <Container style={styles.connectingContainer}>
        <NavigationHeader 
          title="Connecting..." 
          showBackButton={true}
          showLogo={false}
        />
        
        <View style={styles.connectingContent}>
          <View style={styles.connectingAnimation}>
            <ActivityIndicator size="large" color={CoreColors.TurquoiseBlue} />
            <Text style={styles.connectingText}>Connecting to call...</Text>
            <Text style={styles.connectingSubtext}>Please wait while we establish the connection</Text>
          </View>
        </View>
      </Container>
    );
  }

  // Render error state
  if (error) {
    return (
      <View style={styles.container}>
        <View style={styles.statusBar}>
          <Text style={styles.welcome}>Connection Error</Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={connectToCall}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.endCallButton}
            onPress={handleEndCall}
          >
            <MaterialIcons name="call-end" size={28} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Render connected call state - Modern white UI with movable video
  return (
    <View style={styles.videoCallContainer}>
      {/* Top Header */}
      <View style={styles.videoCallHeader}>
        <TouchableOpacity 
          style={styles.backButtonCall}
          onPress={() => navigation.goBack()}
        >
          <MaterialIcons name="arrow-back" size={24} color="#1F2937" />
        </TouchableOpacity>
        <View style={styles.callStatusContainer}>
          <View style={styles.callStatusDot} />
          <Text style={styles.callStatusText}>
            {isConnected ? "Connected" : "Connecting..."}
          </Text>
        </View>
        <View style={styles.callTimeContainer}>
          <MaterialIcons name="access-time" size={16} color="#6B7280" />
          <Text style={styles.callTimeText}>00:00</Text>
        </View>
      </View>

      {/* Main Video Area */}
      <View style={styles.videoCallMainArea}>
        {/* Remote Video - Full Screen */}
        <View style={styles.remoteVideoArea}>
          {users.length > 1 ? (
            users.slice(1).map((user) => (
              <View key={user.userId} style={styles.remoteVideoView}>
                <ZoomView
                  style={styles.zoomView}
                  userId={user.userId}
                  fullScreen={true}
                  videoAspect={VideoAspect.PanAndScan}
                />
                {/* Patient Info Overlay */}
                <View style={styles.patientInfoOverlay}>
                  <View style={styles.patientInfoCard}>
                    <MaterialIcons name="person" size={20} color="#FFFFFF" />
                    <Text style={styles.patientInfoText}>
                      {user.userName || patientName}
                    </Text>
                  </View>
                </View>
              </View>
            ))
          ) : hasRemoteUserEverJoined ? (
            // Show "Patient left" message only if someone had joined before
            <View style={styles.patientLeftContainer}>
              <View style={styles.patientLeftAnimation}>
                <View style={styles.patientLeftIcon}>
                  <MaterialIcons name="person-off" size={48} color="#EF4444" />
                </View>
                <Text style={styles.patientLeftMainText}>
                  {patientName} left the call
                </Text>
                <Text style={styles.patientLeftSubText}>
                  The patient has disconnected from the video call
                </Text>
              </View>
            </View>
          ) : (
            // Show waiting message when no one has joined yet
            <View style={styles.waitingForPatientContainer}>
              <View style={styles.waitingAnimation}>
                <View style={styles.avatarPlaceholder}>
                  <MaterialIcons name="person" size={48} color="#6B7280" />
                </View>
                <Text style={styles.waitingMainText}>
                  Waiting for {patientName}
                </Text>
                <Text style={styles.waitingSubText}>
                  They will join the call shortly
                </Text>
                <View style={styles.loadingDots}>
                  <View style={[styles.dot, styles.dot1]} />
                  <View style={[styles.dot, styles.dot2]} />
                  <View style={[styles.dot, styles.dot3]} />
                </View>
              </View>
            </View>
          )}
        </View>

        {/* Movable Local Video - Picture in Picture */}
        {users.length > 0 && users[0] && (
          <PanGestureHandler
            onGestureEvent={onGestureEvent}
            onHandlerStateChange={onHandlerStateChange}
          >
            <Animated.View
              style={[
                styles.movableLocalVideo,
                {
                  transform: [
                    { translateX: translateX },
                    { translateY: translateY },
                  ],
                },
              ]}
            >
              <TouchableOpacity 
                style={styles.localVideoFrame}
                onPress={toggleCamera}
                activeOpacity={0.8}
              >
                <ZoomView
                  style={styles.localVideoView}
                  userId={users[0].userId}
                  fullScreen={false}
                  videoAspect={VideoAspect.PanAndScan}
                />
                <View style={styles.localVideoOverlay}>
                  <MaterialIcons name="flip-camera-ios" size={16} color="#FFFFFF" />
                </View>
                <View style={styles.moveIndicator}>
                  <MaterialIcons name="drag-indicator" size={20} color="#FFFFFF" />
                </View>
              </TouchableOpacity>
            </Animated.View>
          </PanGestureHandler>
        )}
      </View>

      {/* Floating Navigation Bar Controls */}
      <View style={styles.floatingControlsContainer}>
        <View style={styles.floatingNavBar}>
          {/* Audio Control */}
          <TouchableOpacity
            style={[
              styles.floatingControlButton,
              isAudioEnabled ? styles.controlActive : styles.controlInactive,
            ]}
            onPress={toggleAudio}
          >
            <MaterialIcons
              name={isAudioEnabled ? "mic" : "mic-off"}
              size={24}
              color="#FFFFFF"
            />
            <Text style={styles.floatingControlLabel}>
              {isAudioEnabled ? "Mute" : "Unmute"}
            </Text>
          </TouchableOpacity>

          {/* Video Control */}
          <TouchableOpacity
            style={[
              styles.floatingControlButton,
              isVideoEnabled ? styles.controlActive : styles.controlInactive,
            ]}
            onPress={toggleVideo}
          >
            <MaterialIcons
              name={isVideoEnabled ? "videocam" : "videocam-off"}
              size={24}
              color="#FFFFFF"
            />
            <Text style={styles.floatingControlLabel}>
              {isVideoEnabled ? "Stop Video" : "Start Video"}
            </Text>
          </TouchableOpacity>

          {/* Camera Flip Control */}
          <TouchableOpacity
            style={[styles.floatingControlButton, styles.controlNeutral]}
            onPress={toggleCamera}
          >
            <MaterialIcons name="flip-camera-ios" size={24} color="#FFFFFF" />
            <Text style={styles.floatingControlLabel}>Flip</Text>
          </TouchableOpacity>

          {/* End Call Control */}
          <TouchableOpacity
            style={[styles.floatingControlButton, styles.endCallControl]}
            onPress={() => {
              handleEndCall();
              if (onCallEnd) {
                onCallEnd();
              }
              navigation.goBack();
            }}
          >
            <MaterialIcons name="call-end" size={24} color="#FFFFFF" />
            <Text style={styles.floatingControlLabel}>End</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000", // Changed to black background like Twilio
  },
  safe: {
    flex: 1,
  },
  // Pre-call screen styles - Clean white mode
  preCallContainer: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  preCallContent: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  patientCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    padding: 24,
    alignItems: "center",
    marginBottom: 32,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 1,
    borderColor: "#F0F0F0",
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#F0F8FF",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 16,
    borderWidth: 2,
    borderColor: CoreColors.TurquoiseBlue + "20",
  },
  patientName: {
    fontSize: 22,
    fontWeight: "700",
    color: "#1A1A1A",
    marginBottom: 8,
    textAlign: "center",
  },
  callSubtitle: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
    lineHeight: 22,
  },
  preCallProgramSelector: {
    marginBottom: 32,
  },
  preCallProgramLabel: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1A1A1A",
    marginBottom: 16,
  },
  preCallRadioContainer: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#E5E7EB",
    overflow: "hidden",
  },
  preCallRadioButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#F3F4F6",
    backgroundColor: "#FFFFFF",
  },
  preCallRadioButtonSelected: {
    backgroundColor: "#F0F8FF",
    borderBottomColor: CoreColors.TurquoiseBlue + "20",
  },
  preCallRadioButtonDisabled: {
    backgroundColor: "#F9FAFB",
    opacity: 0.6,
  },
  preCallRadioCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#D1D5DB",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  preCallRadioCircleSelected: {
    backgroundColor: CoreColors.TurquoiseBlue,
    borderColor: CoreColors.TurquoiseBlue,
  },
  preCallRadioTextContainer: {
    flex: 1,
  },
  preCallRadioText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#1F2937",
    marginBottom: 2,
  },
  preCallRadioTextDisabled: {
    color: "#9CA3AF",
  },
  preCallRadioSubtext: {
    fontSize: 14,
    color: "#6B7280",
    fontStyle: "italic",
  },
  preCallErrorText: {
    fontSize: 14,
    color: "#EF4444",
    textAlign: "center",
    marginTop: 12,
    fontWeight: "500",
  },
  preCallSettings: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 20,
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  preCallSettingsTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1A1A1A",
    marginBottom: 16,
  },
  preCallSettingsGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 16,
  },
  preCallSettingCard: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    paddingHorizontal: 12,
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#E5E7EB",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  preCallSettingIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  preCallSettingIconActive: {
    backgroundColor: CoreColors.TurquoiseBlue,
  },
  preCallSettingIconInactive: {
    backgroundColor: "#F3F4F6",
    borderWidth: 1,
    borderColor: "#D1D5DB",
  },
  preCallSettingText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#374151",
    textAlign: "center",
  },
  preCallFooter: {
    padding: 24,
    paddingBottom: Platform.OS === "ios" ? 34 : 24,
    backgroundColor: "#FFFFFF",
    borderTopWidth: 1,
    borderTopColor: "#E5E7EB",
  },
  preCallJoinButton: {
    backgroundColor: CoreColors.TurquoiseBlue,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 16,
    paddingHorizontal: 24,
    shadowColor: CoreColors.TurquoiseBlue,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  preCallJoinButtonDisabled: {
    backgroundColor: "#D1D5DB",
    shadowOpacity: 0,
    elevation: 0,
  },
  preCallJoinButtonIcon: {
    marginRight: 8,
  },
  preCallJoinButtonText: {
    fontSize: 18,
    fontWeight: "600",
    color: "#FFFFFF",
  },
  // Video Call Screen Styles - Modern White UI
  videoCallContainer: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  videoCallHeader: {
    height: 80,
    backgroundColor: "#FFFFFF",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingTop: Platform.OS === "ios" ? 44 : 20,
    borderBottomWidth: 1,
    borderBottomColor: "#E5E7EB",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 10,
  },
  backButtonCall: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#F3F4F6",
  },
  callStatusContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
  },
  callStatusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#10B981",
    marginRight: 8,
  },
  callStatusText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#1F2937",
  },
  callTimeContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F3F4F6",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  callTimeText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#6B7280",
    marginLeft: 6,
  },
  videoCallMainArea: {
    flex: 1,
    position: "relative",
    backgroundColor: "#FFFFFF",
  },
  remoteVideoArea: {
    flex: 1,
    backgroundColor: "#000000",
  },
  remoteVideoView: {
    width: "100%",
    height: "100%",
    position: "relative",
    backgroundColor: "#000",
  },
  patientInfoOverlay: {
    position: "absolute",
    top: 20,
    left: 20,
    zIndex: 5,
  },
  patientInfoCard: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  patientInfoText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF",
    marginLeft: 8,
  },
  waitingForPatientContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 40,
  },
  // Connecting screen styles
  connectingContainer: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  connectingContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 40,
  },
  connectingAnimation: {
    alignItems: "center",
  },
  connectingText: {
    fontSize: 20,
    fontWeight: "600",
    color: "#1F2937",
    textAlign: "center",
    marginTop: 20,
    marginBottom: 8,
  },
  connectingSubtext: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
    lineHeight: 22,
  },
  // Patient left styles
  patientLeftContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    paddingHorizontal: 40,
  },
  patientLeftAnimation: {
    alignItems: "center",
  },
  patientLeftIcon: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: "#FFFFFF",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 24,
    borderWidth: 2,
    borderColor: "#FCA5A5",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  patientLeftMainText: {
    fontSize: 24,
    fontWeight: "700",
    color: "#1F2937",
    textAlign: "center",
    marginBottom: 8,
  },
  patientLeftSubText: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
    lineHeight: 22,
  },
  waitingAnimation: {
    alignItems: "center",
  },
  avatarPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: "#FFFFFF",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 24,
    borderWidth: 2,
    borderColor: "#E5E7EB",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  waitingMainText: {
    fontSize: 24,
    fontWeight: "700",
    color: "#1F2937",
    textAlign: "center",
    marginBottom: 8,
  },
  waitingSubText: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
    marginBottom: 32,
    lineHeight: 22,
  },
  loadingDots: {
    flexDirection: "row",
    alignItems: "center",
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: CoreColors.TurquoiseBlue,
    marginHorizontal: 4,
  },
  dot1: {
    opacity: 0.4,
  },
  dot2: {
    opacity: 0.7,
  },
  dot3: {
    opacity: 1,
  },
  movableLocalVideo: {
    position: "absolute",
    width: 120,
    height: 160,
    borderRadius: 16,
    overflow: "hidden",
    zIndex: 100, // Increased z-index to ensure it stays above everything
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 15,
  },
  localVideoFrame: {
    width: "100%",
    height: "100%",
    borderRadius: 16,
    overflow: "hidden",
    backgroundColor: "#1F2937",
    borderWidth: 3,
    borderColor: "#FFFFFF",
  },
  localVideoView: {
    width: "100%",
    height: "100%",
  },
  localVideoOverlay: {
    position: "absolute",
    bottom: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    justifyContent: "center",
    alignItems: "center",
  },
  moveIndicator: {
    position: "absolute",
    top: 8,
    left: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    justifyContent: "center",
    alignItems: "center",
  },
  // Floating Navigation Bar Styles
  floatingControlsContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
    paddingBottom: Platform.OS === "ios" ? 34 : 20,
    paddingTop: 20,
    alignItems: "center",
    zIndex: 50,
  },
  floatingNavBar: {
    backgroundColor: "#FFFFFF",
    borderRadius: 25,
    flexDirection: "row",
    paddingHorizontal: 12,
    paddingVertical: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  floatingControlButton: {
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginHorizontal: 4,
    minWidth: 70,
  },
  floatingControlLabel: {
    fontSize: 10,
    fontWeight: "600",
    color: "#FFFFFF",
    marginTop: 4,
    textAlign: "center",
  },
  controlActive: {
    backgroundColor: CoreColors.TurquoiseBlue,
  },
  controlInactive: {
    backgroundColor: "#EF4444",
  },
  controlNeutral: {
    backgroundColor: "#6B7280",
  },
  endCallControl: {
    backgroundColor: "#EF4444",
  },
  // Status bar styles - exactly matching Twilio
  statusBar: {
    height: 44, // Increased height to match Twilio
    backgroundColor: "#1A1A1A", // Darker background like Twilio
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#333",
  },
  welcome: {
    fontSize: 17, // Slightly larger font like Twilio
    color: "#FFFFFF",
    fontWeight: "600",
    textAlign: "center",
  },
  connectionIndicator: {
    marginLeft: 8,
    justifyContent: "center",
  },
  connectionDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#4CAF50",
  },
  // Call container styles
  callContainer: {
    flex: 1,
    backgroundColor: "#000", // Black background like Twilio
  },
  // Remote video styles - exactly matching Twilio
  remoteVideoContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
    backgroundColor: "#000",
    width: "100%",
    height: "100%",
  },
  remoteVideo: {
    width: "100%",
    height: "100%",
    backgroundColor: "#000",
    position: "relative",
  },
  // Local video styles - exactly matching Twilio
  localVideoContainer: {
    position: "absolute",
    right: 16,
    bottom: 100,
    width: 100, // Slightly smaller like Twilio
    height: 150, // Adjusted height to match width ratio
    borderRadius: 8, // Smaller radius like Twilio
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.5,
    shadowRadius: 4,
    elevation: 5,
    zIndex: 10,
    borderWidth: 2,
    borderColor: "#333",
  },
  localVideo: {
    width: "100%",
    height: "100%",
  },
  zoomView: {
    width: "100%",
    height: "100%",
    backgroundColor: "#000",
    ...(Platform.OS === "ios"
      ? {
          overflow: "hidden",
          backfaceVisibility: "hidden",
          opacity: 0.99,
        }
      : {}),
  },
  nameTag: {
    position: "absolute",
    bottom: 16,
    left: 16,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    padding: 8,
    borderRadius: 4,
  },
  nameTagText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "500",
  },
  // Waiting container styles - exactly matching Twilio
  waitingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
    backgroundColor: "#000",
  },
  waitingText: {
    fontSize: 20,
    fontWeight: "600",
    color: "#FFFFFF",
    textAlign: "center",
    marginTop: 20,
    marginBottom: 10,
  },
  waitingSubtext: {
    fontSize: 16,
    color: "#999",
    textAlign: "center",
    marginBottom: 15,
  },
  programSelectorContainer: {
    width: '100%',
    marginBottom: 20,
    alignItems: 'center',
  },
  programLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: "#FFF",
    marginBottom: 12,
    textAlign: 'center',
  },
  radioButtonsContainer: {
    width: '90%',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 8,
    padding: 10,
    marginBottom: 15,
  },
  radioButtonRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  radioButtonSelected: {
    borderColor: CoreColors.TurquoiseBlue,
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: CoreColors.TurquoiseBlue,
  },
  radioButtonText: {
    fontSize: 16,
    color: '#fff',
    flex: 1,
  },
  disabledRadioButton: {
    opacity: 0.5,
  },
  disabledRadioButtonText: {
    color: '#aaa',
  },
  errorMessageText: {
    fontSize: 14,
    color: '#DC3545',
    textAlign: 'center',
    marginBottom: 10,
  },
  disabledButton: {
    backgroundColor: '#cccccc',
    opacity: 0.7,
  },
  // Loading container styles
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#000",
  },
  // Error container styles
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
    backgroundColor: "#000",
  },
  // Controls styles - exactly matching Twilio
  optionsContainer: {
    position: "absolute",
    bottom: 0,
    height: 90, // Increased height like Twilio
    backgroundColor: "#1A1A1A",
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    justifyContent: "space-evenly",
    paddingHorizontal: 20,
    borderTopWidth: 1,
    borderTopColor: "#333",
  },
  controlButton: {
    width: 50, // Slightly smaller like Twilio
    height: 50,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: 10,
  },
  activeButton: {
    backgroundColor: "#333",
  },
  inactiveButton: {
    backgroundColor: "#333",
    borderWidth: 1,
    borderColor: "#666",
  },
  endCallButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "#DC3545", // Brighter red like Twilio
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: 10,
  },
  centerCallButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: "#4CAF50",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  buttonText: {
    color: "#FFFFFF",
    fontWeight: "600",
  },
  statusText: {
    color: "#FFFFFF",
    marginTop: 20,
    fontSize: 16,
  },
  errorText: {
    color: "#DC3545",
    marginBottom: 20,
    fontSize: 16,
    textAlign: "center",
    padding: 20,
  },
  retryButton: {
    backgroundColor: "#333",
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  retryButtonText: {
    color: "#FFFFFF",
    fontWeight: "600",
    fontSize: 16,
  },
});

export default ZoomVideoCall;