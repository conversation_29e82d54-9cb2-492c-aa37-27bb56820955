import React, { memo } from 'react';
import { StyleSheet, View, Image, ImageSourcePropType, Dimensions } from 'react-native';
import { Colors, Constants } from '@/constants';
import Text from '../Text';
import { LinearGradient } from 'expo-linear-gradient';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

interface OnboardingPageProps {
  id: number;
  image: ImageSourcePropType;
  title: string;
  desc: string;
  isFirstItem?: boolean;
  isLastItem?: boolean;
}

const OnboardingPage = memo(({
  image,
  title,
  desc,
  isFirstItem,
  isLastItem,
}: OnboardingPageProps) => {
  return (
    <View style={styles.container}>
      <View style={styles.imageContainer}>
        <Image source={image} style={styles.image} resizeMode="cover" />
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.5)']}
          style={styles.gradient}
        >
          <View style={styles.textContainer}>
            <Text 
              style={[styles.title, { fontFamily: 'DMSans-Bold' }]} 
              center 
              white 
              bold
            >
              {title}
            </Text>
            <Text 
              style={[styles.desc, { fontFamily: 'DMSans-Regular' }]} 
              center 
              white
            >
              {desc}
            </Text>
          </View>
        </LinearGradient>
      </View>
    </View>
  );
});

export default OnboardingPage;

const styles = StyleSheet.create({
  container: {
    width: Constants.width,
    flex: 1,
  },
  imageContainer: {
    width: Constants.width,
    height: SCREEN_HEIGHT * 0.7,
    overflow: 'hidden',
    borderBottomLeftRadius: 32,
    borderBottomRightRadius: 32,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  gradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '45%',
    justifyContent: 'flex-end',
    borderBottomLeftRadius: 32,
    borderBottomRightRadius: 32,
  },
  textContainer: {
    paddingHorizontal: 24,
    paddingBottom: 32,
    paddingTop: 40,
  },
  title: {
    fontSize: 32,
    lineHeight: 38,
    marginBottom: 12,
    letterSpacing: -0.5,
  },
  desc: {
    fontSize: 16,
    lineHeight: 24,
    opacity: 0.95,
    textAlign: 'center',
    letterSpacing: 0.1,
  },
});
