import React, { useEffect } from 'react';
import { StyleSheet, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams } from 'expo-router';
import { useDispatch, useSelector } from 'react-redux';

import PatientMapViewScreen from '@/screens/PatientDetails/PatinetsMapView';
import { setCurrentPatientId, setCurrentPatientName } from '@/services/actions/patientActions';
import { apiPostWithToken } from '@/services/apis/apiManager';
import URLS from '@/services/config/config';

export default function PatientMapViewRoute() {
  const { id } = useLocalSearchParams();
  const dispatch = useDispatch();

  // Check if we already have the patient name in Redux
  const patientName = useSelector((state: any) => state?.currentPatientNameReducer?.patientName);
  const patientId = useSelector((state: any) => state?.currentPatientIdReducer?.patientId);

  // Fetch patient details if needed
  useEffect(() => {
    // Validate the ID
    if (!id) {
      console.error('No patient ID provided in route params');
      Alert.alert('Error', 'No patient ID provided. Please try again.');
      return;
    }

    console.log('PatientMapViewRoute: Using patient ID:', id);
    dispatch(setCurrentPatientId(id as string));

    // Only fetch patient details if we don't have the name already
    if (!patientName) {
      fetchPatientDetails(id as string);
    }
  }, [id, dispatch, patientName]);

  // Function to fetch patient details and set the name
  const fetchPatientDetails = async (patientId: string) => {
    try {
      console.log('Fetching patient details for ID:', patientId);
      const response = await apiPostWithToken(
        { patientId: patientId },
        URLS.caregiverUrl + 'getPatientDetails'
      );

      if (response?.status === 200 && response?.data?.status === 'Success') {
        const patientData = response?.data?.data;
        if (patientData?.patientName) {
          console.log('Setting patient name:', patientData.patientName);
          dispatch(setCurrentPatientName(patientData.patientName));
        }
      } else {
        console.error('Failed to fetch patient details:', response);
        Alert.alert('Error', 'Failed to fetch patient details. Please try again.');
      }
    } catch (error) {
      console.error('Error fetching patient details:', error);
      Alert.alert('Error', 'An error occurred while fetching patient details.');
    }
  };

  // Make sure we have a valid ID to pass to the map component
  const validPatientId = id || patientId;

  if (!validPatientId) {
    // Show alert and return empty container
    Alert.alert('Error', 'No patient ID available. Please go back and try again.');
    return (
      <SafeAreaView style={styles.container}>
        {/* Empty container */}
      </SafeAreaView>
    );
  }

  return (
    // Explicitly exclude the top and bottom edges to prevent double padding with the header and remove bottom safe area
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      <PatientMapViewScreen patientId={validPatientId as string} />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
