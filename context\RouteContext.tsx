import React, { createContext, useContext, useState, useEffect } from 'react';
import { usePathname } from 'expo-router';

// Define restricted routes where notifications should not be shown
const RESTRICTED_ROUTES = [
  '/auth/login',
  '/auth/signup',
  '/auth/forgot-password',
  '/auth/recovery-password',
  '/auth/change-password-successful',
  '/auth/verify-phone',
  '/auth/signup-successful',
  '/auth/password-expire',
  '/organization',
  '/org',
  '/simple-org',
  '/splash',
  '/onboarding'
];

interface RouteContextType {
  currentRoute: string;
  isRestrictedRoute: boolean;
  hasSelectedOrganization: boolean;
  setHasSelectedOrganization: (value: boolean) => void;
}

const RouteContext = createContext<RouteContextType>({
  currentRoute: '',
  isRestrictedRoute: true,
  hasSelectedOrganization: false,
  setHasSelectedOrganization: () => {},
});

export const useRouteContext = () => useContext(RouteContext);

export const RouteProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const pathname = usePathname();
  const [currentRoute, setCurrentRoute] = useState(pathname);
  const [hasSelectedOrganization, setHasSelectedOrganization] = useState(false);
  
  // Update current route when pathname changes
  useEffect(() => {
    setCurrentRoute(pathname);
  }, [pathname]);
  
  // Check if current route is in the restricted list
  const isRestrictedRoute = RESTRICTED_ROUTES.some(route => 
    currentRoute === route || currentRoute.startsWith(route + '/')
  );
  
  const value = {
    currentRoute,
    isRestrictedRoute,
    hasSelectedOrganization,
    setHasSelectedOrganization,
  };
  
  return (
    <RouteContext.Provider value={value}>
      {children}
    </RouteContext.Provider>
  );
};

export default RouteProvider;
