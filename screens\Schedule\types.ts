/**
 * Task interface
 * Defines the structure of a task in the calendar
 * Updated to match the new API structure
 */
export interface Task {
  id: string;
  title: string;
  description: string;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  start: string; // Date string in MM-DD-YYYY hh:mm A format
  end: string; // Date string in MM-DD-YYYY hh:mm A format
  patientId: string;
  userId?: number;      // Caregiver ID
  patientName?: string; // Called 'patinetName' in the API (typo in the original)
  className?: string;   // CSS class for styling in the calendar
  timezone: string;     // Timezone for the task
}

/**
 * API Response interface
 * Standard response structure from the backend
 */
export interface ApiResponse<T = any> {
  data?: T;
  success?: boolean;
  responseCode?: number;
  responsecode?: number;
  messages?: string[];
  error?: {
    message: string;
    status?: number;
    details?: any;
  };
  status?: number;
  message?: string; // backward compatibility
}

/**
 * Patient interface for task assignment
 * Simplified version of PatientItemProps for task creation
 */
export interface Patient {
  patientId: number;
  patientName: string;
  mrn?: string;
  phone?: string;
  address?: string;
}

/**
 * Calendar Task API Response
 * Structure of the response from getCalendarTasks API
 */
export interface CalendarTasksResponse {
  data: CalendarTaskData[];
  success: boolean;
}

/**
 * Calendar Task Data
 * Individual task data from the API
 */
export interface CalendarTaskData {
  id: string;
  title: string;
  description: string;
  start: string;
  end: string;
  className: string;
  patientId: string;
  userId: string;
  priority: string;
  patinetName: string; // Note: API has typo
}
