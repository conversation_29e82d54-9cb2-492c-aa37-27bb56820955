import { getUnreadMessages, UnreadMessagesRequest, UnreadMessagesResponse } from '../apis/apiManager';

/**
 * Redux action types for unread messages
 */
export const FETCH_UNREAD_MESSAGES_REQUEST = 'FETCH_UNREAD_MESSAGES_REQUEST';
export const FETCH_UNREAD_MESSAGES_SUCCESS = 'FETCH_UNREAD_MESSAGES_SUCCESS';
export const FETCH_UNREAD_MESSAGES_FAILURE = 'FETCH_UNREAD_MESSAGES_FAILURE';

export interface FetchUnreadMessagesRequestAction {
  type: typeof FETCH_UNREAD_MESSAGES_REQUEST;
}

export interface FetchUnreadMessagesSuccessAction {
  type: typeof FETCH_UNREAD_MESSAGES_SUCCESS;
  payload: UnreadMessagesResponse;
}

export interface FetchUnreadMessagesFailureAction {
  type: typeof FETCH_UNREAD_MESSAGES_FAILURE;
  payload: string;
}

export type UnreadMessagesActionTypes = 
  | FetchUnreadMessagesRequestAction
  | FetchUnreadMessagesSuccessAction
  | FetchUnreadMessagesFailureAction;

/**
 * Redux action creators
 */
export const fetchUnreadMessagesRequest = (): FetchUnreadMessagesRequestAction => ({
  type: FETCH_UNREAD_MESSAGES_REQUEST,
});

export const fetchUnreadMessagesSuccess = (data: UnreadMessagesResponse): FetchUnreadMessagesSuccessAction => ({
  type: FETCH_UNREAD_MESSAGES_SUCCESS,
  payload: data,
});

export const fetchUnreadMessagesFailure = (error: string): FetchUnreadMessagesFailureAction => ({
  type: FETCH_UNREAD_MESSAGES_FAILURE,
  payload: error,
});

/**
 * Async action to fetch unread messages
 * Uses the centralized API from apiManager.ts
 */
export const fetchUnreadMessages = (params: UnreadMessagesRequest) => {
  return async (dispatch: any) => {
    try {
      dispatch(fetchUnreadMessagesRequest());

      // Use the centralized API from apiManager.ts
      const response = await getUnreadMessages(params);

      if (response.error) {
        dispatch(fetchUnreadMessagesFailure(response.error.message));
        return null;
      }

      if (!response.data) {
        dispatch(fetchUnreadMessagesFailure('No data received from unread messages API'));
        return null;
      }

      dispatch(fetchUnreadMessagesSuccess(response.data));
      return response.data;
    } catch (error: any) {
      dispatch(fetchUnreadMessagesFailure(error.message || 'Unknown error occurred'));
      return null;
    }
  };
}; 