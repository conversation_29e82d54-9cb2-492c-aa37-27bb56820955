import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ViewStyle,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import Text from '@/components/Text';
import {
  isTablet,
  isLargeTablet,
  spacing,
  typography,
  iconSizes,
  tabletLayout,
} from '@/utils/responsive';
import { useOrientation } from '@/hooks/useOrientation';
import { CoreColors } from '@/constants/Colors';

interface NavigationItem {
  id: string;
  label: string;
  icon: keyof typeof Ionicons.glyphMap;
  route?: string;
  badge?: number;
  onPress?: () => void;
  children?: NavigationItem[];
}

interface SidebarNavigationProps {
  items: NavigationItem[];
  activeItemId?: string;
  onItemPress?: (item: NavigationItem) => void;
  width?: number;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  style?: ViewStyle;
  headerComponent?: React.ReactNode;
  footerComponent?: React.ReactNode;
}

/**
 * Sidebar Navigation component optimized for large tablet layouts
 * Provides hierarchical navigation with collapsible sections
 */
const SidebarNavigation: React.FC<SidebarNavigationProps> = ({
  items,
  activeItemId,
  onItemPress,
  width,
  collapsible = true,
  defaultCollapsed = false,
  style,
  headerComponent,
  footerComponent,
}) => {
  const { orientation } = useOrientation();
  const [collapsed, setCollapsed] = useState(defaultCollapsed);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  // Calculate sidebar width
  const getSidebarWidth = () => {
    if (width) return width;
    
    if (collapsed) {
      return isLargeTablet() ? 80 : 72;
    }
    
    if (isLargeTablet()) {
      return tabletLayout.sidebarWidth || 280;
    } else if (isTablet()) {
      return 240;
    }
    
    return 200;
  };

  const sidebarWidth = getSidebarWidth();

  // Toggle sidebar collapse
  const toggleCollapse = () => {
    if (collapsible) {
      setCollapsed(!collapsed);
    }
  };

  // Toggle section expansion
  const toggleSection = (itemId: string) => {
    const newExpandedSections = new Set(expandedSections);
    if (newExpandedSections.has(itemId)) {
      newExpandedSections.delete(itemId);
    } else {
      newExpandedSections.add(itemId);
    }
    setExpandedSections(newExpandedSections);
  };

  // Handle item press
  const handleItemPress = (item: NavigationItem) => {
    if (item.children && item.children.length > 0) {
      toggleSection(item.id);
    } else {
      onItemPress?.(item);
      item.onPress?.();
    }
  };

  // Render navigation item
  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {
    const isActive = activeItemId === item.id;
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedSections.has(item.id);
    const isSubItem = level > 0;

    return (
      <View key={item.id}>
        <TouchableOpacity
          style={[
            styles.navigationItem,
            {
              paddingLeft: collapsed ? spacing.sm : spacing.md + (level * spacing.lg),
              backgroundColor: isActive ? CoreColors.TurquoiseBlue + '15' : 'transparent',
              borderRightWidth: isActive ? 3 : 0,
              borderRightColor: CoreColors.TurquoiseBlue,
            },
            isSubItem && styles.subNavigationItem,
          ]}
          onPress={() => handleItemPress(item)}
          accessibilityLabel={item.label}
          accessibilityRole="button"
        >
          <View style={styles.itemContent}>
            <View style={styles.itemLeft}>
              <Ionicons
                name={item.icon}
                size={collapsed ? iconSizes.lg : iconSizes.md}
                color={isActive ? CoreColors.TurquoiseBlue : CoreColors.DarkJungleGreen}
                style={styles.itemIcon}
              />
              
              {!collapsed && (
                <Text
                  style={[
                    styles.itemLabel,
                    {
                      color: isActive ? CoreColors.TurquoiseBlue : CoreColors.DarkJungleGreen,
                      fontSize: isSubItem ? typography.bodySmall.fontSize : typography.body.fontSize,
                      fontWeight: isActive ? '600' : '400',
                    },
                  ]}
                  numberOfLines={1}
                >
                  {item.label}
                </Text>
              )}
            </View>

            <View style={styles.itemRight}>
              {item.badge && item.badge > 0 && !collapsed && (
                <View style={styles.badge}>
                  <Text style={styles.badgeText}>
                    {item.badge > 99 ? '99+' : item.badge.toString()}
                  </Text>
                </View>
              )}
              
              {hasChildren && !collapsed && (
                <Ionicons
                  name={isExpanded ? 'chevron-down' : 'chevron-forward'}
                  size={iconSizes.sm}
                  color={CoreColors.GrayBlue}
                  style={styles.chevronIcon}
                />
              )}
            </View>
          </View>
        </TouchableOpacity>

        {/* Render children if expanded */}
        {hasChildren && isExpanded && !collapsed && (
          <View style={styles.childrenContainer}>
            {item.children!.map(child => renderNavigationItem(child, level + 1))}
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={[
      styles.container,
      {
        width: sidebarWidth,
      },
      style,
    ]}>
      {/* Header */}
      {headerComponent && (
        <View style={styles.header}>
          {headerComponent}
        </View>
      )}

      {/* Collapse toggle button */}
      {collapsible && isTablet() && (
        <TouchableOpacity
          style={styles.collapseButton}
          onPress={toggleCollapse}
          accessibilityLabel={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          <Ionicons
            name={collapsed ? 'chevron-forward' : 'chevron-back'}
            size={iconSizes.md}
            color={CoreColors.GrayBlue}
          />
        </TouchableOpacity>
      )}

      {/* Navigation items */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {items.map(item => renderNavigationItem(item))}
      </ScrollView>

      {/* Footer */}
      {footerComponent && (
        <View style={styles.footer}>
          {footerComponent}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FAFBFC',
    borderRightWidth: 1,
    borderRightColor: '#E5E7EB',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 2, height: 0 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
      },
      android: {
        elevation: 2,
      },
    }),
  },

  header: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },

  collapseButton: {
    alignSelf: 'flex-end',
    padding: spacing.sm,
    margin: spacing.sm,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },

  scrollView: {
    flex: 1,
  },

  scrollContent: {
    paddingVertical: spacing.sm,
  },

  navigationItem: {
    paddingVertical: spacing.sm,
    paddingRight: spacing.md,
    marginHorizontal: spacing.xs,
    borderRadius: 8,
  },

  subNavigationItem: {
    marginLeft: spacing.md,
  },

  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  itemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  itemIcon: {
    marginRight: spacing.sm,
  },

  itemLabel: {
    flex: 1,
    fontSize: typography.body.fontSize,
    lineHeight: typography.body.lineHeight,
  },

  itemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  badge: {
    backgroundColor: CoreColors.TurquoiseBlue,
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginRight: spacing.xs,
    minWidth: 20,
    alignItems: 'center',
  },

  badgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
    textAlign: 'center',
  },

  chevronIcon: {
    marginLeft: spacing.xs,
  },

  childrenContainer: {
    marginTop: spacing.xs,
  },

  footer: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
});

export default SidebarNavigation;