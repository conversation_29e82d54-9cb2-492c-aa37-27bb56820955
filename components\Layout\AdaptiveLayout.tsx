import React, { useState, useEffect } from 'react';
import {
  View,
  Dimensions,
  Animated,
  ViewStyle,
} from 'react-native';

import {
  isTablet,
  isLargeTablet,
  getOrientation,
  getDeviceType,
  SCREEN_BREAKPOINTS,
} from '@/utils/responsive';

interface AdaptiveLayoutProps {
  children?: React.ReactNode;
  phoneLayout?: React.ReactNode;
  tabletPortraitLayout?: React.ReactNode;
  tabletLandscapeLayout?: React.ReactNode;
  largeTabletLayout?: React.ReactNode;
  breakpoint?: number;
  enableTransitions?: boolean;
  transitionDuration?: number;
  style?: ViewStyle;
  onLayoutChange?: (layout: 'phone' | 'tablet-portrait' | 'tablet-landscape' | 'large-tablet') => void;
}

/**
 * AdaptiveLayout component that switches between different layouts based on screen size and orientation
 * Provides smooth transitions and conditional rendering for optimal user experience
 */
const AdaptiveLayout: React.FC<AdaptiveLayoutProps> = ({
  children,
  phoneLayout,
  tabletPortraitLayout,
  tabletLandscapeLayout,
  largeTabletLayout,
  breakpoint = SCREEN_BREAKPOINTS.lg,
  enableTransitions = true,
  transitionDuration = 300,
  style,
  onLayoutChange,
}) => {
  const [screenData, setScreenData] = useState(() => {
    const { width, height } = Dimensions.get('window');
    return { width, height };
  });
  
  const [fadeAnim] = useState(new Animated.Value(1));
  const [currentLayout, setCurrentLayout] = useState<'phone' | 'tablet-portrait' | 'tablet-landscape' | 'large-tablet'>('phone');

  // Listen for dimension changes
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenData({ width: window.width, height: window.height });
    });

    return () => subscription?.remove?.();
  }, []);

  // Determine current layout based on screen dimensions
  const determineLayout = () => {
    const deviceType = getDeviceType();
    const orientation = getOrientation();
    
    if (deviceType === 'extra-large-tablet' || deviceType === 'large-tablet') {
      return 'large-tablet';
    } else if (deviceType === 'tablet') {
      return orientation === 'landscape' ? 'tablet-landscape' : 'tablet-portrait';
    } else {
      return 'phone';
    }
  };

  // Update layout when screen dimensions change
  useEffect(() => {
    const newLayout = determineLayout();
    
    if (newLayout !== currentLayout) {
      if (enableTransitions) {
        // Fade out, change layout, then fade in
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: transitionDuration / 2,
          useNativeDriver: true,
        }).start(() => {
          setCurrentLayout(newLayout);
          onLayoutChange?.(newLayout);
          
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: transitionDuration / 2,
            useNativeDriver: true,
          }).start();
        });
      } else {
        setCurrentLayout(newLayout);
        onLayoutChange?.(newLayout);
      }
    }
  }, [screenData, currentLayout, enableTransitions, transitionDuration, fadeAnim, onLayoutChange]);

  // Get the appropriate layout content
  const getLayoutContent = () => {
    switch (currentLayout) {
      case 'large-tablet':
        return largeTabletLayout || tabletLandscapeLayout || tabletPortraitLayout || phoneLayout || children;
      
      case 'tablet-landscape':
        return tabletLandscapeLayout || tabletPortraitLayout || phoneLayout || children;
      
      case 'tablet-portrait':
        return tabletPortraitLayout || phoneLayout || children;
      
      case 'phone':
      default:
        return phoneLayout || children;
    }
  };

  const containerStyle: ViewStyle = {
    flex: 1,
    ...style,
  };

  if (enableTransitions) {
    return (
      <Animated.View style={[containerStyle, { opacity: fadeAnim }]}>
        {getLayoutContent()}
      </Animated.View>
    );
  }

  return (
    <View style={containerStyle}>
      {getLayoutContent()}
    </View>
  );
};

export default AdaptiveLayout;