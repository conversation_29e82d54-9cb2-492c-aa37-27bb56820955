import {useIsFocused, useNavigation} from '@react-navigation/native';
import {useRouter} from 'expo-router';
import React, {memo, useCallback, useEffect, useRef, useState} from 'react';
import {Alert, Image, ScrollView, StyleSheet, View, Animated, TouchableOpacity, TextInput, Modal} from 'react-native';
import {useSelector} from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import ButtonIcon from '@/components/Buttons/ButtonIcon';
import Container from '@/components/Layout/Container';
import Loader from '@/components/Loader/Loader';
import MedicationItem from '@/components/MedicationItem';
import Text from '@/components/Text';
import {Routes, Colors} from '@/constants';
import {MedicationsProps} from '@/models';
import {apiPostWithToken} from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import Theme from '@/constants/Theme';
import scale from '@/utils/scale';
import EditMedication from './editMedication';
import { getBottomTabSafePadding } from '@/utils/layoutHelper';

const MedicationPage = memo((props: any) => {
  const scrollRef = useRef<any>();
  const [medicationList, setMedicationList] = useState<any[] | []>();
  const [filteredMedicationList, setFilteredMedicationList] = useState<any[] | []>();
  const [loader, setLoader] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  // Edit medication states
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingMedication, setEditingMedication] = useState<MedicationsProps | null>(null);

  // Get both navigation methods for compatibility
  const {navigate} = useNavigation() as any;
  const router = useRouter();

  const getMedicationByPatientId = async (patientId: number) => {
    setLoader(true);
    const response = await apiPostWithToken(
      {patientId: patientId},
      URLS.caregiverUrl + 'medicationInfoByPatient',
    );
    
    if (response?.status == 200) {
      setLoader(false);
      const medicationsList = response?.data
        ?.medicationLists as MedicationsProps[];
      setMedicationList(medicationsList);
      setFilteredMedicationList(medicationsList); // Initialize filtered list
      console.log('medicationsList', medicationsList?.length);
      onScrollToEnd();
    } else {
      setLoader(false);
      const errorMessage = response?.response?.data?.responseMessage
        ? response?.response?.data?.responseMessage
        : response.message === 'Network Error'
        ? 'Network error. Please check your data connection.'
        : response.message;
      Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
    }
  };

  // Filter medications based on search query
  const filterMedications = useCallback((query: string) => {
    if (!medicationList) return;
    
    if (!query.trim()) {
      setFilteredMedicationList(medicationList);
      return;
    }

    const filtered = medicationList.filter(medication => 
      medication.medicineName.toLowerCase().includes(query.toLowerCase()) ||
      medication.strength.toLowerCase().includes(query.toLowerCase()) ||
      medication.color.toLowerCase().includes(query.toLowerCase())
    );
    
    setFilteredMedicationList(filtered);
  }, [medicationList]);

  // Handle search input changes
  useEffect(() => {
    filterMedications(searchQuery);
  }, [searchQuery, filterMedications]);

  // Get patient ID from props or Redux
  const reduxPatientId = useSelector(
    (state: any) => state?.currentPatientIdReducer?.patientId,
  );

  // Try to get patient ID from URL if it's not in props
  let urlPatientId = '';
  if (typeof window !== 'undefined' && window.location) {
    try {
      if (window.location.search) {
        const urlParams = new URLSearchParams(window.location.search);
        const idParam = urlParams.get('patientId');
        if (idParam) {
          urlPatientId = idParam;
        }
      }
    } catch (error) {
      console.warn('Error parsing URL params in Medications component:', error);
    }
  }

  // Use directPatientId, patientId from props, URL, or Redux (in that order of preference)
  let effectivePatientId = props.directPatientId || props.patientId || urlPatientId || reduxPatientId;

  // Make sure we're not using invalid patient IDs
  if (effectivePatientId === 'index' || effectivePatientId === 'undefined') {
    // Use Redux ID instead if available
    if (reduxPatientId && reduxPatientId !== 'index' && reduxPatientId !== 'undefined') {
      effectivePatientId = reduxPatientId;
    } else {
      // Clear the ID to show error state
      effectivePatientId = '';
    }
  }

  // Track patient ID errors
  const [patientIdError, setPatientIdError] = useState<string | null>(null);

  // Component loaded status tracking to avoid duplicate logs
  const hasLoadedRef = React.useRef(false);

  // Log the patient ID for debugging - but only once
  useEffect(() => {
    if (!hasLoadedRef.current) {
      console.log('Medications component - Patient ID sources:', {
        directPatientId: props.directPatientId,
        propsPatientId: props.patientId,
        urlPatientId,
        reduxPatientId,
        effectivePatientId,
        windowLocation: typeof window !== 'undefined' ? window.location?.href : 'not available',
        allProps: Object.keys(props)
      });
      hasLoadedRef.current = true;
    }

    // Show warning if patient ID is invalid
    if (!effectivePatientId) {
      setPatientIdError('Missing patient ID - Please return to patient list and try again');
      console.error('Medications component - No patient ID available from any source');
    } else if (effectivePatientId === 'index' || effectivePatientId === 'undefined') {
      setPatientIdError(`Invalid patient ID "${effectivePatientId}" - Please return to patient list`);
      console.error('Medications component - Invalid patient ID:', effectivePatientId);
    } else {
      setPatientIdError(null);
    }
  }, [props.directPatientId, props.patientId, urlPatientId, reduxPatientId, effectivePatientId]);

  const isFocused = useIsFocused();
  useEffect(() => {
    if (effectivePatientId) {
      getMedicationByPatientId(effectivePatientId);
    } else {
      console.warn('Medications: No patient ID available');
    }
  }, [effectivePatientId, isFocused]);

  const onScrollToEnd = useCallback(() => {
    setTimeout(() => {
      scrollRef?.current?.scrollToEnd();
    }, 200);
  }, [scrollRef]);

  const patientName = useSelector(
    (state: any) => state?.currentPatientNameReducer?.patientName,
  );
  useEffect(() => {
    // Only set navigation options if using React Navigation
    if (props.navigation && props.navigation.setOptions) {
      props.navigation.setOptions({
        headerTitle: () => (
          <View
            style={{
              marginBottom: 0,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Text medium size={16} color={'#000'}>
              {patientName}
              {' Medications'}
            </Text>
          </View>
        ),
      });
    }
  }, [props.navigation, patientName]);

  const onDelete = () => {
    console.log('On Delete Callback...');
    if (effectivePatientId) {
      getMedicationByPatientId(effectivePatientId);
    }
  };

  // Handle edit medication
  const onEdit = (medication: MedicationsProps) => {
    console.log('On Edit Callback...', medication);
    setEditingMedication(medication);
    setShowEditModal(true);
  };

  // Handle edit success
  const onEditSuccess = () => {
    setShowEditModal(false);
    setEditingMedication(null);
    if (effectivePatientId) {
      getMedicationByPatientId(effectivePatientId);
    }
  };

  // Handle edit cancel
  const onEditCancel = () => {
    setShowEditModal(false);
    setEditingMedication(null);
  };

  const clearSearch = () => {
    setSearchQuery('');
  };

  const displayedMedications = filteredMedicationList || [];
  const totalMedications = medicationList?.length || 0;

  const renderFloatingActionButton = () => {
    return (
      <View style={styles.fabContainer}>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => {
            // Only try to navigate if we have a valid patient ID
            if (!effectivePatientId) {
              Alert.alert('Error', 'Missing patient ID. Cannot add medication.');
              return;
            }

            console.log('Navigating to add medication with patient ID:', effectivePatientId);

            try {
              // Use Expo Router for navigation
              router.push({
                pathname: '/patient/medication/add' as any,
                params: { patientId: effectivePatientId }
              });
            } catch (error) {
              console.error('Error navigating to add medication:', error);

              // Try alternate navigation method
              try {
                // Try a simpler navigation approach
                router.push(`/patient/medication/add?patientId=${effectivePatientId}` as any);
              } catch (fallbackError) {
                console.error('Even fallback navigation failed:', fallbackError);
                // Last resort: use React Navigation
                navigate(Routes.AddMedication, { patientId: effectivePatientId });
              }
            }
          }}
        >
          <View style={styles.addButtonInner}>
            <Ionicons name="add" size={28} color={Colors.White} />
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <Container style={styles.container}>
      <Loader modalVisible={loader || props.loading} />

      {patientIdError ? (
        // Show error message if patient ID is invalid
        <View style={styles.errorContainer}>
          <Text size={16} color="red" center>
            {patientIdError}
          </Text>
        </View>
      ) : (
        <>
          {/* Header with title */}
          <View style={styles.headerContainer}>
            <View style={styles.headerContent}>
              <View style={styles.headerLeft}>

                {searchQuery && displayedMedications.length > 0 && (
                  <Text size={16} lineHeight={20} color={Colors.GrayBlue} marginTop={4}>
                    {displayedMedications.length} result{displayedMedications.length !== 1 ? 's' : ''}
                  </Text>
                )}
              </View>
            </View>
          </View>

          {/* Search Bar */}
          <View style={styles.searchContainer}>
            <View style={styles.searchBar}>
              <Ionicons name="search-outline" size={20} color={Colors.GrayBlue} style={styles.searchIcon} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search medications..."
                placeholderTextColor={Colors.GrayBlue}
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={clearSearch} style={styles.clearButton}>
                  <Ionicons name="close-circle" size={20} color={Colors.GrayBlue} />
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Medications List */}
          {totalMedications === 0 ? (
            <View style={styles.emptyContainer}>
              <View style={styles.emptyCard}>
                <Ionicons name="medical-outline" size={80} color={Colors.GrayBlue} style={styles.emptyIcon} />
                <Text size={20} lineHeight={24} bold color={Colors.DarkJungleGreen} marginTop={24}>
                  No Medications Found
                </Text>
                <Text size={16} lineHeight={22} color={Colors.GrayBlue} marginTop={8} center>
                  This patient doesn't have any medications yet.
                </Text>
                <Text size={14} lineHeight={20} color={Colors.GrayBlue} marginTop={4} center>
                  Tap the + button below to add the first medication.
                </Text>
              </View>
            </View>
          ) : displayedMedications.length === 0 ? (
            <View style={styles.emptyContainer}>
              <View style={styles.emptyCard}>
                <Ionicons name="search-outline" size={80} color={Colors.GrayBlue} style={styles.emptyIcon} />
                <Text size={20} lineHeight={24} bold color={Colors.DarkJungleGreen} marginTop={24}>
                  No Results Found
                </Text>
                <Text size={16} lineHeight={22} color={Colors.GrayBlue} marginTop={8} center>
                  No medications match your search "{searchQuery}"
                </Text>
                <TouchableOpacity onPress={clearSearch} style={styles.clearSearchButton}>
                  <Text size={14} lineHeight={20} color={Colors.TealBlue} marginTop={8}>
                    Clear Search
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <ScrollView
              ref={scrollRef}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.scrollContent}>
              <View style={styles.medicationList}>
                {displayedMedications.map(item => (
                  <MedicationItem
                    {...item}
                    key={item.medicineId.toString()}
                    onDelete={onDelete}
                    onEdit={() => onEdit(item)}
                  />
                ))}
              </View>
            </ScrollView>
          )}

          {renderFloatingActionButton()}

          {/* Edit Medication Modal */}
          <Modal
            visible={showEditModal}
            animationType="slide"
            transparent={false}
            onRequestClose={onEditCancel}
          >
            {editingMedication && (
              <EditMedication
                medication={editingMedication}
                patientId={effectivePatientId}
                onClose={onEditCancel}
                onSuccess={onEditSuccess}
              />
            )}
          </Modal>
        </>
      )}
    </Container>
  );
});

export default MedicationPage;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.Snow,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyCard: {
    backgroundColor: Colors.White,
    borderRadius: 20,
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
    ...Theme.shadow,
    maxWidth: 320,
    width: '100%',
  },
  emptyIcon: {
    opacity: 0.7,
  },
  clearSearchButton: {
    marginTop: 8,
  },
  headerContainer: {
    backgroundColor: Colors.White,
    paddingHorizontal: 24,
    paddingTop: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.Snow,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerLeft: {
    flex: 1,
  },
  searchContainer: {
    backgroundColor: Colors.White,
    paddingHorizontal: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.Snow,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.Snow,
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 48,
    borderWidth: 1,
    borderColor: Colors.Platinum,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.DarkJungleGreen,
    padding: 0, // Remove default padding
  },
  clearButton: {
    marginLeft: 8,
    padding: 4,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: getBottomTabSafePadding() + 80, // Space for FAB + tab bar
  },
  medicationList: {
    // Removed gap since cards now have their own marginBottom
  },
  fabContainer: {
    position: 'absolute',
    right: 24,
    bottom: 24,
    zIndex: 999,
  },
  addButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.TealBlue,
    ...Theme.shadow,
    shadowColor: Colors.TealBlue,
    shadowOpacity: 0.3,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 4 },
    elevation: 8,
  },
  addButtonInner: {
    width: '100%',
    height: '100%',
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
