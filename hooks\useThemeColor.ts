/**
 * Only light mode is supported
 */

import Colors, { CoreColors } from '@/constants/Colors';

// Extended color types to support additional UI elements
type ExtendedColorNames = keyof typeof CoreColors | 'text' | 'background' | 'primary' | 'secondary' | 'accent';

export function useThemeColor(
  props: { light?: string; dark?: string },
  colorName: ExtendedColorNames
) {
  // Always use light theme colors
  if (props.light) {
    return props.light;
  } else {
    // Map extended color names to actual colors
    switch(colorName) {
      case 'text':
        return Colors.DarkJungleGreen;
      case 'background':
        return Colors.Snow;
      case 'primary':
        return Colors.TiffanyBlue;
      case 'secondary':
        return Colors.GrayBlue;
      case 'accent':
        return Colors.BlueCrayola;
      default:
        // Use the direct Colors object for standard colors
        return Colors[colorName as keyof typeof CoreColors];
    }
  }
}
