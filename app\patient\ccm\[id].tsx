import React from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams } from 'expo-router';
import { Stack } from 'expo-router';

import CarePlanDetailsScreen from '@/screens/PatientDetails/CCMReports/carePlanDetails';

export default function CarePlanDetailsRoute() {
  const { id } = useLocalSearchParams();

  return (
    <>
      <Stack.Screen options={{ title: 'Care Plan Details' }} />
      <SafeAreaView style={styles.container} edges={['left', 'right']}>
        <CarePlanDetailsScreen
          patientId={id as string}
          planId={id as string}
        />
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});