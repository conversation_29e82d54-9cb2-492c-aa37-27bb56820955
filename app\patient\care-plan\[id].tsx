import React from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams } from 'expo-router';
import { Stack } from 'expo-router';

import CarePlanDetailsScreen from '@/screens/PatientDetails/CCMReports/carePlanDetails';

export default function CarePlanDetailsRoute() {
  const params = useLocalSearchParams();
  const { id, carePlanId, latestVitals } = params;

  // Parse latestVitals if it's a string
  let parsedVitals = [];
  if (latestVitals && typeof latestVitals === 'string') {
    try {
      parsedVitals = JSON.parse(latestVitals);
    } catch (error) {
      console.error('Error parsing latestVitals:', error);
    }
  }

  console.log('CarePlanDetailsRoute params:', { id, carePlanId, latestVitals: parsedVitals });

  // Make sure we're not passing 'index' as a parameter
  const effectivePatientId = id === 'index' ? undefined : id;
  const effectiveCarePlanId = carePlanId === 'index' ? undefined : carePlanId;

  return (
    <>
      <Stack.Screen options={{ title: 'Care Plan Details' }} />
      <SafeAreaView style={styles.container}>
        <CarePlanDetailsScreen
          patientId={effectivePatientId as string}
          planId={effectiveCarePlanId as string}
          latestVitals={parsedVitals}
        />
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
