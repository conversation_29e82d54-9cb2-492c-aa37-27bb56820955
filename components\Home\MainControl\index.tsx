import { SOURCE_ICON } from "@/assets/images";
import GenericModal from "@/components/SuccessFailModal/GenericModal";
import Text from "@/components/Text";
import { Colors } from "@/constants";
import { userLogOut } from "@/services/actions/logOutActions";
import { resetCredentials } from "@/services/secure-storage";
import {
  cardDimensions,
  grid,
  layout,
  spacing,
  typography,
} from "@/utils/responsive";
import {
  Feather,
  FontAwesome5,
  Ionicons,
  MaterialCommunityIcons,
  MaterialIcons,
} from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import React, { memo, useRef, useState, useEffect } from "react";
import {
  Animated,
  Dimensions,
  Easing,
  Image,
  Modal,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useDispatch, useSelector } from "react-redux";
import { useAlertsCount } from "@/context/AlertsCountContext";
import useOrganization from "@/hooks/useOrganization";
import * as Haptics from "expo-haptics";
import FCMNotificationIcon from "@/components/FCMNotificationIcon";

const { width: screenWidth, height: screenHeight } = Dimensions.get("window");

// Enhanced features with modern vector icons and creative designs
const dashboardFeatures = [
  {
    id: 1,
    title: "Critical Alerts",
    subtitle: "Urgent notifications",
    route: "/critical-alerts",
    iconFamily: "MaterialIcons",
    iconName: "crisis-alert",
    color: "#FF4757",
    gradientColors: ["#FF4757", "#FF3838"],
    priority: "high",
    animation: "flash",
    showNotificationBubble: true, // Enable notification bubble
    notificationKey: "critical", // Key to identify the notification type
  },
  {
    id: 2,
    title: "Patients",
    subtitle: "Manage care recipients",
    route: "/(tabs)/patients",
    iconFamily: "MaterialCommunityIcons",
    iconName: "account-heart",
    color: Colors.TurquoiseBlue,
    gradientColors: [Colors.TurquoiseBlue, "#1DD1A1"],
    priority: "high",
    animation: "pulse",
  },
  {
    id: 3,
    title: "Schedule",
    subtitle: "Appointments & events",
    route: "/schedule",
    iconFamily: "MaterialCommunityIcons",
    iconName: "calendar-clock",
    color: "#00D2D3",
    gradientColors: ["#00D2D3", "#0ABDE3"],
    priority: "medium",
    animation: "scale",
  },
  {
    id: 4,
    title: "Tasks",
    subtitle: "Daily assignments",
    route: "/tasks",
    iconFamily: "MaterialCommunityIcons",
    iconName: "clipboard-check",
    color: "#5F27CD",
    gradientColors: ["#5F27CD", "#7B1FA2"],
    priority: "medium",
    animation: "bounce",
  },
  {
    id: 5,
    title: "Alerts",
    subtitle: "System notifications",
    route: "/alerts",
    iconFamily: "Ionicons",
    iconName: "notifications",
    color: "#FF9500",
    gradientColors: ["#FF9500", "#FF6B35"],
    priority: "medium",
    animation: "shake",
    showNotificationBubble: false, // Enable notification bubble
    notificationKey: "alerts", // Key to identify the notification type
  },
  {
    id: 6,
    title: "Summary",
    subtitle: "Patient overview",
    route: "/summary",
    iconFamily: "MaterialCommunityIcons",
    iconName: "file-document",
    color: "#A855F7",
    gradientColors: ["#A855F7", "#8B5CF6"],
    priority: "low",
    animation: "rotate",
  },

];

// Enhanced icons for icons not in standard vector icon libraries
const getCustomIcon = (iconName: string, size: number, color: string) => {
  switch (iconName) {
    case "brain":
      return <FontAwesome5 name="brain" size={size} color={color} />;
    case "chart-line":
      return <FontAwesome5 name="chart-line" size={size} color={color} />;
    case "lightbulb":
      return <FontAwesome5 name="lightbulb" size={size} color={color} />;
    case "clipboard-list":
      return <FontAwesome5 name="clipboard-list" size={size} color={color} />;
    case "clipboard-check":
      return <MaterialCommunityIcons name="clipboard-check" size={size} color={color} />;
    case "account-heart":
      return <MaterialCommunityIcons name="account-heart" size={size} color={color} />;
    case "calendar-clock":
      return <MaterialCommunityIcons name="calendar-clock" size={size} color={color} />;
    case "file-document":
      return <MaterialCommunityIcons name="file-document" size={size} color={color} />;
    case "account-multiple":
      return <MaterialCommunityIcons name="account-multiple" size={size} color={color} />;
    case "heart-pulse":
      return <MaterialCommunityIcons name="heart-pulse" size={size} color={color} />;
    case "school":
      return <MaterialCommunityIcons name="school" size={size} color={color} />;
    case "crisis-alert":
      return <MaterialIcons name="crisis-alert" size={size} color={color} />;
    default:
      return <Ionicons name={iconName as any} size={size} color={color} />;
  }
};

// Individual dashboard item component with enhanced animations and creative designs
const DashboardGridItem = memo(
  ({ item, style, notificationCount }: { item: any; style?: any; notificationCount?: number }) => {
    const router = useRouter();
    const scaleAnim = useRef(new Animated.Value(1)).current;

    const handlePressIn = () => {
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 150,
        useNativeDriver: true,
        easing: Easing.bezier(0.4, 0.0, 0.2, 1),
      }).start();
    };

    const handlePressOut = () => {
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 4,
        tension: 100,
        useNativeDriver: true,
      }).start();
    };

    const handlePress = () => {
      router.push(item.route as any);
    };

    const renderIcon = () => {
      const iconProps = {
        size: 28,
        color: "#FFFFFF",
      };

      switch (item.iconFamily) {
        case "MaterialIcons":
          return <MaterialIcons name={item.iconName} {...iconProps} />;
        case "MaterialCommunityIcons":
          return <MaterialCommunityIcons name={item.iconName} {...iconProps} />;
        case "Ionicons":
          return <Ionicons name={item.iconName} {...iconProps} />;
        case "FontAwesome5":
          return <FontAwesome5 name={item.iconName} {...iconProps} />;
        case "Feather":
          return <Feather name={item.iconName} {...iconProps} />;
        default:
          return <MaterialIcons name="dashboard" {...iconProps} />;
      }
    };

    return (
      <TouchableOpacity
        activeOpacity={1}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        style={style}
      >
        <Animated.View
          style={[
            styles.gridItem,
            {
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <LinearGradient
            colors={[item.color, item.color]}
            style={styles.gridItemGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            {/* Notification bubble */}
            {typeof notificationCount === 'number' && notificationCount > 0 && (
              <View style={styles.notificationBubble}>
                <Text style={styles.notificationText}>
                  {notificationCount > 99 ? '99+' : String(notificationCount)}
                </Text>
              </View>
            )}

            {/* Icon container with enhanced styling */}
            <View style={styles.gridIconContainer}>
              <View style={styles.iconBackground}>{renderIcon()}</View>
            </View>

            {/* Content with improved typography */}
            <View style={styles.gridItemContent}>
              <Text style={styles.gridItemTitle} numberOfLines={1}>
                {item.title}
              </Text>
              <Text style={styles.gridItemSubtitle} numberOfLines={1}>
                {item.subtitle}
              </Text>
            </View>
          </LinearGradient>
        </Animated.View>
      </TouchableOpacity>
    );
  }
);

// Logout Button Component
const LogoutButton = memo(() => {
  const router = useRouter();
  const dispatch = useDispatch();
  const [visibleModal, setVisibleModal] = useState<boolean>(false);

  const handleLogout = () => {
    setVisibleModal(true);
  };

  const closeModal = () => {
    setVisibleModal(false);
  };

  const confirmLogout = () => {
    setVisibleModal(false);
    setTimeout(() => {
      dispatch(userLogOut());
      resetCredentials();
      router.replace("/auth/login");
    }, 500);
  };

  return (
    <>
      <TouchableOpacity
        style={styles.logoutButtonContainer}
        onPress={handleLogout}
        activeOpacity={0.8}
      >
        <View style={styles.logoutButtonWrapper}>
          <Ionicons
            name="log-out-outline"
            size={22}
            color={Colors.TurquoiseBlue}
          />
        </View>
      </TouchableOpacity>

      <Modal
        visible={visibleModal}
        onRequestClose={closeModal}
        transparent
        animationType="fade"
      >
        <GenericModal
          close={closeModal}
          open={confirmLogout}
          title="Confirm Logout"
          confirmText="Logout"
          cancelText="Cancel"
          message="Are you sure you want to logout?"
          icon={
            <Ionicons
              name="log-out-outline"
              size={60}
              color={Colors.TurquoiseBlue}
              style={{ marginBottom: 10 }}
            />
          }
        />
      </Modal>
    </>
  );
});

// Header component with enhanced profile photo section and dynamic safe area handling
const AppHeader = memo(() => {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { orgName } = useOrganization();
  const [shouldAnimate, setShouldAnimate] = useState(false);
  const scrollAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  // Get user details from Redux
  const userProfileDetails = useSelector(
    (state: any) => state?.userProfileReducer?.data
  );

  const caregiverName =
    userProfileDetails?.firstName || userProfileDetails?.name || "Care Manager";

  // Calculate optimal font size based on title length
  const getTitleFontSize = () => {
    if (!orgName) return screenHeight < 600 ? 16 : screenHeight < 700 ? 18 : 20;
    
    const baseSize = screenHeight < 600 ? 16 : screenHeight < 700 ? 18 : 20;
    
    if (orgName.length > 25) {
      return Math.max(baseSize - 4, 14);
    } else if (orgName.length > 20) {
      return Math.max(baseSize - 3, 15);
    } else if (orgName.length > 15) {
      return Math.max(baseSize - 2, 16);
    }
    
    return baseSize;
  };

  // Set up marquee animation for long titles
  useEffect(() => {
    if (orgName && orgName.length > 20) {
      setShouldAnimate(true);
      
      // Start the animation
      const startAnimation = () => {
        Animated.sequence([
          // Wait a moment before starting
          Animated.delay(1000),
          // Move text left slowly
          Animated.timing(scrollAnim, {
            toValue: -20,
            duration: 2000,
            useNativeDriver: true,
            easing: Easing.linear,
          }),
          // Hold at the end
          Animated.delay(800),
          // Move back to original position
          Animated.timing(scrollAnim, {
            toValue: 0,
            duration: 2000,
            useNativeDriver: true,
            easing: Easing.linear,
          }),
          // Hold before repeating
          Animated.delay(2000),
        ]).start(() => {
          // Repeat the animation
          if (shouldAnimate) startAnimation();
        });
      };
      
      startAnimation();
    } else {
      setShouldAnimate(false);
      scrollAnim.setValue(0);
    }
    
    return () => {
      setShouldAnimate(false);
    };
  }, [orgName]);

  // Get current time for greeting
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good Morning";
    if (hour < 17) return "Good Afternoon";
    return "Good Evening";
  };

  // Dynamic header calculations based on device and safe area
  const getHeaderPadding = () => {
    const statusBarHeight =
      Platform.OS === "android" ? StatusBar.currentHeight || 0 : 0;
    const safeAreaTop = insets.top;
    const isSmallScreen = screenHeight < 700;
    const isVerySmallScreen = screenHeight < 600;

    // Base padding for different scenarios
    let topPadding = safeAreaTop;
    let bottomPadding = 12;

    if (Platform.OS === "ios") {
      // iOS devices - use safe area insets
      if (safeAreaTop > 40) {
        // iPhone with notch (X, 11, 12, 13, 14, 15 series)
        topPadding = safeAreaTop + (isSmallScreen ? 0 : 5);
      } else if (safeAreaTop > 20) {
        // iPhone without notch but with status bar
        topPadding = safeAreaTop + (isSmallScreen ? 5 : 8);
      } else {
        // Older iPhones or simulators
        topPadding = Math.max(safeAreaTop, 20) + (isSmallScreen ? 5 : 8);
      }
    } else {
      // Android devices - handle various scenarios
      if (safeAreaTop > 30) {
        // Android with notch/punch hole
        topPadding = safeAreaTop + (isSmallScreen ? 0 : 5);
      } else {
        // Android without notch
        topPadding =
          Math.max(safeAreaTop, statusBarHeight) + (isSmallScreen ? 5 : 8);
      }
    }

    // Adjust bottom padding based on screen size
    if (isVerySmallScreen) {
      bottomPadding = 6;
    } else if (isSmallScreen) {
      bottomPadding = 8;
    }

    return {
      paddingTop: Math.max(topPadding, 15), // Reduced minimum padding from 20px to 15px
      paddingBottom: bottomPadding,
    };
  };

  const headerPadding = getHeaderPadding();

  // Debug logging for development
  if (__DEV__) {
    console.log("Header Debug Info:", {
      platform: Platform.OS,
      screenHeight,
      safeAreaInsets: insets,
      statusBarHeight:
        Platform.OS === "android" ? StatusBar.currentHeight : "N/A",
      calculatedPadding: headerPadding,
    });
  }

  // Handle title press with subtle feedback
  const handleTitlePress = () => {
    // Provide haptic feedback if available
    if (Platform.OS === 'ios' && 'impactAsync' in Haptics) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    
    // Scale animation for touch feedback
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
    
    // Here you could add navigation to org details or toggle views
    // router.push('/organization-details');
  };

  return (
    <View style={[styles.appHeader, headerPadding]}>
      <View style={styles.headerTopRow}>
        {/* App Logo */}
        <View style={styles.appIconContainer}>
          <Image
            style={styles.appLogo}
            source={SOURCE_ICON.logoHeader}
            resizeMode="contain"
          />
        </View>

        {/* App Name */}
        <View style={styles.appNameContainer}>
          <TouchableOpacity 
            activeOpacity={0.8}
            onPress={handleTitlePress}
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel={`Organization: ${orgName}`}
            accessibilityHint="Double tap to view organization details"
          >
            <Animated.View
              style={{
                transform: [
                  { translateX: shouldAnimate ? scrollAnim : 0 },
                  { scale: scaleAnim }
                ]
              }}
            >
              <Text 
                style={[styles.appName, { fontSize: getTitleFontSize() }]}
                numberOfLines={1}
              >
                {orgName}
              </Text>
            </Animated.View>
          </TouchableOpacity>
        </View>

        {/* Enhanced Profile Photo Section */}
        <LogoutButton />
      </View>

      {/* Left-aligned Greeting and Name */}
      <View style={styles.userInfoSection}>
        <View style={styles.greetingRow}>
          <Text style={styles.greetingTextLarge}>{getGreeting()} 👋</Text>
          <FCMNotificationIcon color="#6B7280" size={20} />
        </View>
        <Text style={styles.caregiverNameLarge}>{caregiverName}</Text>
      </View>
    </View>
  );
});

const MainControl = memo(() => {
  // Get alerts count data for notification bubbles
  const { criticalCount, alertsCount } = useAlertsCount();

  // Get the notification count for a specific feature
  const getNotificationCount = (feature: any) => {
    if (!feature?.showNotificationBubble) return 0;
    
    switch (feature.notificationKey) {
      case "critical":
        const criticalNumber = typeof criticalCount === 'number' ? criticalCount : 0;
        return criticalNumber;
      case "alerts":
        const alertsNumber = typeof alertsCount === 'number' ? alertsCount : 0;
        return alertsNumber;
      default:
        return 0;
    }
  };

  // Split features into pairs for 2-column layout
  const renderDashboardGrid = () => {
    const pairs = [];
    for (let i = 0; i < dashboardFeatures.length; i += 2) {
      pairs.push(dashboardFeatures.slice(i, i + 2));
    }

    return pairs.map((pair, rowIndex) => (
      <View key={rowIndex} style={styles.gridRow}>
        {pair.map((item) => (
          <DashboardGridItem
            key={item.id}
            item={item}
            style={styles.gridItemWrapper}
            notificationCount={getNotificationCount(item)}
          />
        ))}
        {/* If odd number of items, add spacer for last row */}
        {pair.length === 1 && <View style={styles.gridItemWrapper} />}
      </View>
    ));
  };

  return (
    <View style={styles.container}>
      {/* App Header */}
      <AppHeader />

      {/* Main Dashboard Grid - 2 Columns with ScrollView for small screens */}
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        bounces={true}
      >
        <View style={styles.dashboardGrid}>{renderDashboardGrid()}</View>
      </ScrollView>
    </View>
  );
});

export default MainControl;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FAFBFC",
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
    // Ensure content doesn't get compressed on short screens
    minHeight: screenHeight < 600 ? 500 : screenHeight < 700 ? 600 : 700,
  },

  // App Header Styles - Dynamic padding handled by component
  appHeader: {
    paddingHorizontal: 20,
    backgroundColor: "#FAFBFC",
  },
  headerTopRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: screenHeight < 600 ? 10 : screenHeight < 700 ? 12 : 16,
  },
  appIconContainer: {
    width: screenHeight < 600 ? 36 : 44,
    height: screenHeight < 600 ? 36 : 44,
    borderRadius: screenHeight < 600 ? 18 : 22,
    backgroundColor: "#FFFFFF",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  appLogo: {
    width: screenHeight < 600 ? 22 : 28,
    height: screenHeight < 600 ? 22 : 28,
  },
  appNameContainer: {
    flex: 1,
    alignItems: "center",
    paddingHorizontal: 8,
    justifyContent: "center",
  },
  appName: {
    fontSize: screenHeight < 600 ? 16 : screenHeight < 700 ? 18 : 20,
    fontWeight: "700",
    fontFamily: "DMSans-Bold",
    color: "#1A1D29",
    textAlign: "center",
    maxWidth: screenWidth * 0.65, // Limit width to prevent overflow
    textShadowColor: 'rgba(0, 0, 0, 0.05)',
    textShadowOffset: { width: 0, height: 0.5 },
    textShadowRadius: 1,
  },

  // Logout button styles
  logoutButtonContainer: {
    padding: 4,
  },
  logoutButtonWrapper: {
    width: screenHeight < 600 ? 36 : 44,
    height: screenHeight < 600 ? 36 : 44,
    borderRadius: screenHeight < 600 ? 18 : 22,
    backgroundColor: "#FFFFFF",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    position: "relative",
  },

  // User Info Section (left-aligned) - More compact
  userInfoSection: {
    alignItems: "flex-start",
  },
  greetingRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: screenHeight < 600 ? 3 : screenHeight < 700 ? 4 : 6,
  },
  greetingTextLarge: {
    fontSize: screenHeight < 600 ? 14 : screenHeight < 700 ? 16 : 18,
    color: "#6B7280",
    marginRight: 8,
    fontWeight: "500",
  },
  caregiverNameLarge: {
    fontSize: screenHeight < 600 ? 24 : screenHeight < 700 ? 28 : 30,
    fontWeight: "bold",
    color: "#1A1D29",
    marginBottom: screenHeight < 600 ? 6 : screenHeight < 700 ? 8 : 6,
    letterSpacing: -0.5,
  },

  // Inline Critical Alerts - More compact
  inlineCriticalAlert: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFF5F5",
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#FED7D7",
  },

  // Dashboard Grid Styles - Responsive design for all screen sizes
  dashboardGrid: {
    flex: 1,
    ...grid.container,
    paddingBottom: spacing.lg,
    // Ensure minimum space for grid content
    minHeight: layout.safeContentHeight,
  },
  dashboardTitle: {
    ...typography.h3,
    color: "#1A1D29",
    marginBottom: spacing.sm,
  },
  gridRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: grid.row.marginBottom,
  },
  gridItemWrapper: {
    flex: 1,
    ...grid.col,
    minHeight: cardDimensions.minHeight + 20,
  },
  gridItem: {
    // Use fixed aspect ratio that works well across all screen sizes
    aspectRatio: 1.15,
    borderRadius: cardDimensions.borderRadius,
    backgroundColor: "#FFFFFF",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 3,
    overflow: "hidden",
    // Ensure minimum height for readability
    minHeight: cardDimensions.minHeight,
  },
  gridItemGradient: {
    flex: 1,
    padding: cardDimensions.padding - 4,
    justifyContent: "space-between",
  },
  gridIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
  },
  gridItemContent: {
    marginTop: 6,
  },
  gridItemTitle: {
    fontSize: screenHeight < 600 ? 14 : screenHeight < 700 ? 16 : 18,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 2,
    letterSpacing: -0.3,
    lineHeight: screenHeight < 600 ? 16 : screenHeight < 700 ? 18 : 20,
  },
  gridItemSubtitle: {
    fontSize: screenHeight < 600 ? 9 : screenHeight < 700 ? 10 : 11,
    color: "rgba(255, 255, 255, 0.8)",
    fontWeight: "800",
    lineHeight: screenHeight < 600 ? 11 : screenHeight < 700 ? 12 : 13,
  },
  iconBackground: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 18,
    padding: 6,
    justifyContent: "center",
    alignItems: "center",
  },
  notificationBubble: {
    position: "absolute",
    top: 12,
    right: 12,
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 8,
    zIndex: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  notificationText: {
    fontSize: 12,
    fontWeight: "800",
    color: "#EF4444",
    letterSpacing: -0.2,
  },
});
