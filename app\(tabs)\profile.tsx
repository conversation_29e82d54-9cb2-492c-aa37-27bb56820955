import React from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import UserProfileScreen from '@/screens/UserProfile';

export default function ProfileRoute() {
  return (
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      <UserProfileScreen />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    marginTop: 0,
    paddingTop: 0, // The Container component will handle proper padding
    paddingBottom: 0,
    position: 'relative',
    zIndex: 1,
  },
});