import {useRoute} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {
  Alert,
  Image,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import {getBottomSpace} from 'react-native-iphone-x-helper';
import {useSelector} from 'react-redux';
import Container from '@/components/Layout/Container';
import Loader from '@/components/Loader/Loader';
import Text from '@/components/Text';
import {Colors} from '@/constants';
import {apiPostWithToken} from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import Theme from '@/constants/Theme';
import scale from '@/utils/scale';

interface ItemProps {
  carePlanId: string;
  enabledOrDiable: boolean;
  notes: string;
  lastUpdated: any;
  patientId: string;
  patientCarePlanId: string;
  updatedDate: string;
  carePlanName: string;
}

const CarePlanDetails = (props: any) => {
  const [carePlanDetailsList, setCarePlanDetailsList] = useState<
    ItemProps[] | []
  >();
  const [loader, setLoader] = useState(false);
  const route = useRoute();

  const [vitals, setVitals] = useState<any[]>([]);

  const patientName = useSelector(
    (state: any) => state?.currentPatientNameReducer?.patientName,
  );

  useEffect(() => {
    // Only set navigation options if using React Navigation
    if (props.navigation && props.navigation.setOptions) {
      props.navigation.setOptions({
        headerTitle: () => (
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Text medium size={16} color={'#000'}>
              {patientName}
              {' CCM'}
            </Text>
          </View>
        ),
      });
    }

    console.log('Params Data:', JSON.stringify(route?.params));

    // Get params from either route or props
    let params: any = {};

    // First try to get from route params
    if (route?.params) {
      params = { ...route.params };

      // Clean up parameters that shouldn't be sent to the API
      delete params.__EXPO_ROUTER_key;
      delete params.id; // We'll use patientId instead
    }

    // Then use props as fallback
    if (!params.patientId && props.patientId) {
      params.patientId = props.patientId;
    }

    if (!params.carePlanId && props.planId) {
      params.carePlanId = props.planId;
    }

    // Handle latestVitals from props
    if (props.latestVitals && !params.latestVitals) {
      params.latestVitals = props.latestVitals;
    }

    // Make sure we're not using 'index' as a patientId
    if (params.patientId === 'index') {
      // Try to get from Redux instead
      const reduxPatientId = useSelector(
        (state: any) => state?.currentPatientIdReducer?.patientId
      );
      if (reduxPatientId) {
        params.patientId = reduxPatientId;
      }
    }

    // Remove carePlanId if it's 'index'
    if (params.carePlanId === 'index') {
      delete params.carePlanId;
    }

    console.log('Final params for API call:', params);

    // Only proceed if we have the required parameters and they're not 'index'
    if (params.patientId && params.patientId !== 'index') {
      getCarePlanDetailsByPatientIdAndCarePlanId(params);
    } else {
      console.error('Missing or invalid required parameters for CarePlanDetails');
    }
  }, [props.navigation, patientName, props.patientId, props.planId]);

  const getCarePlanDetailsByPatientIdAndCarePlanId = async (params: any) => {
    setLoader(true);

    // Create a clean copy of params for the API call
    const apiParams = { ...params };

    // If carePlanId is missing, use a default value or remove it
    if (!apiParams.carePlanId) {
      // Option 1: Remove it completely if the API doesn't require it
      delete apiParams.carePlanId;

      // Option 2: Use a default value if the API requires it
      // apiParams.carePlanId = '1'; // Use an appropriate default value
    }

    console.log('Sending API params:', apiParams);

    const response = await apiPostWithToken(
      apiParams,
      URLS.caregiverUrl + 'patientCarePlanDetails',
    );
    if (response?.status == 200) {
      setLoader(false);
      console.log('Data:', JSON.stringify(response?.data?.items));
      let data = response?.data?.items as ItemProps[];
      console.log('Params:', params?.latestVitals);

      // Ensure latestVitals is always an array
      if (Array.isArray(params?.latestVitals)) {
        setVitals(params?.latestVitals);
      } else if (params?.latestVitals) {
        // If it's not an array but exists, try to convert it
        try {
          const vitalsArray = Array.isArray(params.latestVitals)
            ? params.latestVitals
            : typeof params.latestVitals === 'string'
              ? JSON.parse(params.latestVitals)
              : [params.latestVitals];

          console.log('Converted vitals to array:', vitalsArray);
          setVitals(Array.isArray(vitalsArray) ? vitalsArray : []);
        } catch (error) {
          console.error('Failed to parse vitals data:', error);
          setVitals([]); // Set empty array on error
        }
      } else {
        // No vitals data
        setVitals([]);
      }

      setCarePlanDetailsList(data);
    } else {
      setLoader(false);
      const errorMessage = response?.response?.data?.responseMessage
        ? response?.response?.data?.responseMessage
        : response.message === 'Network Error'
        ? 'Network error. Please check your data connection.'
        : response.message;
      Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
    }
  };

  return (
    <Container style={styles.container}>
      <Loader modalVisible={loader} />
      {!Array.isArray(vitals) || vitals.length === 0 ? (
        <View style={{height: '100%', ...Theme.center}}>
          <Image
            source={require('images/Consults/img_no_upcoming.png')}
            style={{width: scale(160, true), height: scale(160, true)}}
          />
          <Text size={15} lineHeight={24} marginTop={scale(56, true)}>
            No Details Found!!
          </Text>
        </View>
      ) : (
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            paddingTop: 15, // Reduced from 25 to match patient details page
            paddingHorizontal: 10,
          }}>
          <View style={{paddingBottom: 32}}>
            {Array.isArray(vitals) && vitals.map(item => (
              <View key={item.vitalType + item.vitalData}>
                <TouchableOpacity
                  style={{
                    padding: 15,
                    borderRadius: 5,
                    width: '100%',
                    ...Theme.flexRow,
                    marginBottom: 10,
                    opacity: 0.75,
                    backgroundColor: Colors.MediumTurquoise,
                  }}
                  onPress={() => {}}>
                  <View style={{margin: 2}}>
                    <Text bold size={16} lineHeight={24}>
                      {item.vitalType} {': '}
                      {item.vitalData}
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            ))}

            {carePlanDetailsList?.map(item => (
              <View key={item.patientCarePlanId}>
                <TouchableOpacity
                  style={{
                    padding: 15,
                    borderRadius: 5,
                    width: '100%',
                    ...Theme.flexRow,
                    marginBottom: 10,
                    opacity: 0.75,
                    backgroundColor: Colors.MediumTurquoise,
                  }}
                  onPress={() => {}}>
                  <View style={{margin: 2}}>
                    <Text bold size={16} lineHeight={24}>
                      {item.notes}
                    </Text>
                    <Text bold size={12} lineHeight={24}>
                      {'Last Update:'} {item.updatedDate}
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </ScrollView>
      )}
    </Container>
  );
};

export default CarePlanDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  contentContainerStyle: {
    paddingHorizontal: 24,
    paddingTop: 15,
    paddingBottom: getBottomSpace(),
  },
});
