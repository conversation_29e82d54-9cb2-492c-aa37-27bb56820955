import {
  FETCH_FTASKS_LIST,
  FETCH_FTASKS_LIST_FAILURE,
  FETCH_FTASKS_LIST_SUCCESS,
} from './type';

export const fetchFtasksList = (params: Object) => {
  return {
    type: FETCH_FTASKS_LIST,
    payload: params,
  };
};

export const fetchFtasksListSuccess = (data: Object) => {
  return {
    type: FETCH_FTASKS_LIST_SUCCESS,
    data,
  };
};

export const fetchFtasksListFailure = (msg: String) => {
  return {
    type: FETCH_FTASKS_LIST_FAILURE,
    msg,
  };
};
