import React from 'react';
import { Text, TextStyle, TextProps } from 'react-native';
import { isTablet, isLargeTablet, typography } from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';

interface AccessibleTextProps extends TextProps {
  variant?: 'h1' | 'h2' | 'h3' | 'body' | 'bodySmall' | 'caption';
  color?: string;
  bold?: boolean;
  semibold?: boolean;
  italic?: boolean;
  underline?: boolean;
  align?: 'auto' | 'left' | 'right' | 'center' | 'justify';
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: 'header' | 'text' | 'link' | 'none';
  style?: TextStyle;
}

/**
 * Accessible Text component optimized for tablets
 */
const AccessibleText: React.FC<AccessibleTextProps> = ({
  children,
  variant = 'body',
  color,
  bold = false,
  semibold = false,
  italic = false,
  underline = false,
  align,
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole,
  style,
  ...props
}) => {
  // Get text styles based on variant
  const getTextStyles = () => {
    const baseStyle = typography[variant];
    
    // Tablet-specific font size adjustments
    let fontSize = baseStyle.fontSize;
    if (isLargeTablet()) {
      fontSize = fontSize * 1.2; // 20% larger for large tablets
    } else if (isTablet()) {
      fontSize = fontSize * 1.1; // 10% larger for tablets
    }
    
    const textStyle: TextStyle = {
      ...baseStyle,
      fontSize,
      color: color || CoreColors.DarkJungleGreen,
      textAlign: align,
      fontWeight: bold ? '700' : semibold ? '600' : baseStyle.fontWeight,
      fontStyle: italic ? 'italic' : 'normal',
      textDecorationLine: underline ? 'underline' : 'none',
    };
    
    return textStyle;
  };
  
  // Determine accessibility role based on variant
  const getAccessibilityRole = () => {
    if (accessibilityRole) return accessibilityRole;
    
    switch (variant) {
      case 'h1':
      case 'h2':
      case 'h3':
        return 'header';
      default:
        return 'text';
    }
  };
  
  const textStyles = getTextStyles();
  
  return (
    <Text
      style={[textStyles, style]}
      accessible={true}
      accessibilityLabel={accessibilityLabel}
      accessibilityHint={accessibilityHint}
      accessibilityRole={getAccessibilityRole()}
      {...props}
    >
      {children}
    </Text>
  );
};

export default AccessibleText;