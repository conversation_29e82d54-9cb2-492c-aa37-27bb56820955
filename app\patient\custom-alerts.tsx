import React from 'react';
import { Stack } from 'expo-router';
import { useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';

import CustomAlertsScreen from '@/screens/PatientDetails/CustomAlerts';

export default function CustomAlertsRoute() {
  const params = useLocalSearchParams();
  const { id, patientId } = params;

  // Use patientId if available, otherwise use id
  const effectivePatientId = patientId || id;

  // Log the params for debugging
  console.log('CustomAlerts Route - Params:', params, 'Using patientId:', effectivePatientId);

  return (
    <>
      <Stack.Screen options={{ title: 'Custom Alerts' }} />
      <SafeAreaView style={{ flex: 1 }} edges={['left', 'right']}>
        <CustomAlertsScreen patientId={effectivePatientId as string} />
      </SafeAreaView>
    </>
  );
}
