import Container from "@/components/Layout/Container";
import Text from "@/components/Text";
import { useTheme } from "@/constants";
import { CoreColors } from "@/constants/Colors";
import { useRoute, useNavigation } from "@react-navigation/native";
import { MaterialIcons, Ionicons, FontAwesome5, AntDesign, MaterialCommunityIcons } from "@expo/vector-icons";
import moment from "moment";
import React, { useState, useRef, useEffect, useLayoutEffect, useCallback } from "react";
import { useCCMContentContext } from "@/context/CCMContentContext";
import { 
  FlatList, 
  StyleSheet, 
  View, 
  Platform,
  TouchableOpacity,
  Animated,
  Modal,
  SafeAreaView,
  Dimensions,
  StatusBar,
  TextInput,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
  Pressable,
  ScrollView,
  Alert,
  ActivityIndicator,
  RefreshControl
} from "react-native";
import { useLocalSearchParams } from "expo-router";
import { useSelector } from "react-redux";
import { apiPostWithToken } from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import NavigationHeader from '@/components/NavigationHeader';
import AlertsDatePicker from '@/components/DatePickerButtons/alerts-date-picker';
import Loader from '@/components/Loader/Loader';
import { Colors } from '@/constants';
import { ExpoSpeechRecognitionModule, useSpeechRecognitionEvent } from "expo-speech-recognition";
import DateTimePickerModal from 'react-native-modal-datetime-picker';

// Enhanced API encounter interface matching the response structure
interface APIEncounter {
  patientId: string;
  encounterId: string;
  duration: number;
  encounterReason: string;
  encounterDescription: string;
  encounterDateTime: string;
  encounterEndDateTime: string;
  addedByUser: string;
  enMins: string;
  review: string;
  rpmMins: string;
  ccmMins: string;
  pcmMins: string;
}

// Enhanced Review types for all program types
const REVIEW_TYPES = [
  { id: 'rpm', label: 'RPM', color: '#4ECDC4', fullName: 'Remote Patient Monitoring' },
  { id: 'ccm', label: 'CCM', color: '#9381FF', fullName: 'Chronic Care Management' },
  { id: 'pcm', label: 'PCM', color: '#7A77FF', fullName: 'Principal Care Management' },
  { id: 'rtm', label: 'RTM', color: '#36B5E0', fullName: 'Remote Therapeutic Monitoring' },
  { id: 'homehealth', label: 'Home Health', color: '#5FB49C', fullName: 'Home Health Services' },
  { id: 'hospice', label: 'Hospice', color: '#FF9F1C', fullName: 'Hospice Care' }
];

// Standard encounter reason codes as provided
const reasonCodeMap: Record<string, string> = {
  "Critical Alert": "EC_001",
  "Data Review": "EC_002",
  "Phone Call": "EC_003",
  "RPM Review": "EC_004",
  "CCM Review": "EC_005",
  "PCM Review": "EC_006",
  "EMR/Chart review": "EC_007",
  "BPM Device Documentation/Training": "EC_008",
  "Glucometer Device education Template": "EC_009",
  "Weigh scale device education template for CHF": "EC_010",
  "Weigh scale device education template for ESRD, volume overload": "EC_011",
  "Introduction to RPM": "EC_012",
  "Introduction to CCM": "EC_013",
  "Introduction to PCM": "EC_014",
  "Introduction to RTM": "EC_015",
  "Introduction to PCM & RPM": "EC_016",
  "Introduction to PCM & RTM": "EC_017",
  "Introduction to CCM & RPM": "EC_018",
  "Other": "EC_019",
  "Text Message": "EC_020",
  "Appointment": "EC_021",
  "Home Visits": "EC_022",
};

// Convert reasonCodeMap to array for dropdown options
const ENCOUNTER_REASON_OPTIONS = Object.entries(reasonCodeMap).map(
  ([label, code]) => ({ label, code })
);

interface CCMPlaceholderScreenProps {
  navigation?: any;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const CCMPlaceholderScreen = (props: any) => {
  const route = useRoute();
  const navigation = useNavigation();
  const params = useLocalSearchParams();
  const patientId = params.patientId as string || params.id as string;
  
  const routeParams = (route.params as any) || {};
  const tileIndex = routeParams?.tileIndex || 0;
  const title = routeParams?.title || 'CCM Encounters';
  
  // Extract program type from title or route params
  const extractProgramType = () => {
    // First try the type parameter from route params
    if (routeParams?.type) {
      return routeParams.type;
    }
    
    // Extract from title with better mapping
    const titlePart = title.split(' - ')[0];
    const titleToTypeMap: Record<string, string> = {
      'RPM': 'rpm',
      'CCM': 'ccm', 
      'PCM': 'pcm',
      'RTM': 'rtm',
      'Home Health': 'homehealth',
      'Hospice': 'hospice'
    };
    
    // Try exact match first
    if (titleToTypeMap[titlePart]) {
      return titleToTypeMap[titlePart];
    }
    
    // Fallback to the old logic
    return titlePart.toLowerCase().replace(' ', '');
  };
  
  const programType = extractProgramType();
  const currentProgram = REVIEW_TYPES.find(p => p.id === programType) || REVIEW_TYPES.find(p => p.id === 'ccm') || REVIEW_TYPES[0];
  
  const { theme } = useTheme();
  const { setCcmContentTitle, setDynamicTitle } = useCCMContentContext();

  // State management
  const [encountersData, setEncountersData] = useState<APIEncounter[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showAddEncounterModal, setShowAddEncounterModal] = useState(false);
  const [showEditEncounterModal, setShowEditEncounterModal] = useState(false);
  const [editingEncounter, setEditingEncounter] = useState<APIEncounter | null>(null);
  
  // Date range state like other screens
  const [fromDate, setFromDate] = useState<Date>();
  const [toDate, setToDate] = useState<Date>();
  
  // Get user data from Redux store - using the same pattern as other screens with fallback
  const userId = useSelector((state: any) => state?.loginReducer?.data?.userId);
  const orgId = useSelector((state: any) => state?.currentOrgIdReducer?.orgId);
  
  // Initialize date range on component mount - from 1st May to current date
  useEffect(() => {
    const today = new Date();
    setToDate(today);
    
    // Set start date to May 1st of the current year
    const currentYear = today.getFullYear();
    const mayFirst = new Date(currentYear, 4, 1); // Month is 0-indexed, so 4 = May
    
    // If we're before May 1st this year, use May 1st of previous year
    if (today < mayFirst) {
      setFromDate(new Date(currentYear - 1, 4, 1));
    } else {
      setFromDate(mayFirst);
    }
  }, []);
  
  // Animation refs
  const fabScale = useRef(new Animated.Value(1)).current;
  const fabOpacity = useRef(new Animated.Value(1)).current;

  // Set navigation title
  useLayoutEffect(() => {
    const mainTitle = currentProgram.label;
    setCcmContentTitle(mainTitle);
    setDynamicTitle(mainTitle);
    navigation.setOptions({ title: mainTitle });
  }, [navigation, currentProgram, setCcmContentTitle, setDynamicTitle]);

  // Clean up title on unmount
  useEffect(() => {
    return () => {
      setCcmContentTitle('');
      setDynamicTitle('');
    };
  }, [setCcmContentTitle, setDynamicTitle]);

  // Check if user is authenticated
  useEffect(() => {
    if (!userId) {
      console.log('User not authenticated, userId is undefined');
      Alert.alert(
        'Authentication Required',
        'Please log in again to access encounters.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    }
  }, [userId, navigation]);

  // API Functions
  const fetchEncounters = async (showLoader = true) => {
    console.log('fetchEncounters called with:', { showLoader, userId, patientId, programType, fromDate, toDate });
    
    if (showLoader) setLoading(true);
    
    if (!userId || !patientId || !fromDate || !toDate) {
      console.log('Missing required data:', { userId, patientId, fromDate, toDate });
      setLoading(false);
      setRefreshing(false);
      return;
    }
    
    // Store current data in case refresh fails
    const currentData = encountersData;
    
    try {
      const payload = {
        userId: String(userId),
        patientId: String(patientId),
        startDate: moment(fromDate).format('YYYY-MM-DD'),
        endDate: moment(toDate).format('YYYY-MM-DD'),
        program: programType
      };
      
      console.log('API call payload:', payload);
      console.log('API endpoint:', URLS.caregiverUrl + 'getEncounters');
      
      const response = await apiPostWithToken(
        payload,
        URLS.caregiverUrl + 'getEncounters'
      );

      console.log('API response:', response);
      
      setLoading(false);
      setRefreshing(false);
      
      const responseData = response?.data || response?.response?.data;
      const responseStatus = response?.status || response?.response?.status;

      console.log('Processed response:', { responseStatus, responseData });

      if (responseStatus === 200 && responseData) {
        if (responseData.success === true) {
          if (responseData.data && Array.isArray(responseData.data) && responseData.data.length > 0) {
            console.log('Raw encounters data:', responseData.data);
            // Filter encounters by the current program type if needed
            const filteredData = responseData.data.filter((encounter: APIEncounter) => 
              encounter.review === programType || !encounter.review
            );
            console.log('Filtered encounters data for program', programType, ':', filteredData);
            setEncountersData(filteredData);
          } else {
            console.log('No encounters found for this user and program type');
            setEncountersData([]);
          }
        } else if (responseData.success === false) {
          console.log('API returned success: false - no data available');
          setEncountersData([]);
          // This is not an error - just no data available
        } else {
          console.log('Unexpected response structure:', responseData);
          setEncountersData([]);
        }
      } else {
        console.log('API call failed with status:', responseStatus);
        // If this is a refresh (not initial load), keep existing data on error
        if (!showLoader && currentData.length > 0) {
          console.log('Refresh failed, keeping existing data');
          Alert.alert("Warning", "Failed to refresh encounters. Showing cached data.", [{ text: "Dismiss" }]);
        } else {
          setEncountersData([]);
          let errorMessage = "Failed to fetch encounters";
          if (response?.error?.message) {
            errorMessage = response.error.message;
          } else if (response?.response?.data?.responseMessage) {
            errorMessage = response.response.data.responseMessage;
          } else if (response?.message) {
            errorMessage = response.message === "Network Error"
                ? "Network error. Please check your data connection."
                : response.message;
          }
          console.log('Error message:', errorMessage);
          Alert.alert("Error", errorMessage, [{ text: "Dismiss" }]);
        }
      }
    } catch (error) {
      console.error('fetchEncounters error:', error);
      setLoading(false);
      setRefreshing(false);
      
      // If this is a refresh (not initial load), keep existing data on error
      if (!showLoader && currentData.length > 0) {
        console.log('Refresh failed due to exception, keeping existing data');
        Alert.alert("Warning", "Failed to refresh encounters. Showing cached data.", [{ text: "Dismiss" }]);
      } else {
        setEncountersData([]);
        Alert.alert("Error", "An unexpected error occurred while fetching encounters. Please try again.", [{ text: "Dismiss" }]);
      }
    }
  };

  const saveEncounter = async (encounterData: any) => {
    if (!userId) {
      Alert.alert('Error', 'User authentication required');
      return false;
    }
    
    try {
      // Ensure exact API format matching the curl example
      const payload = {
        patientId: String(patientId),
        encounterReason: encounterData.reason,
        encounterDescription: encounterData.description,
        encounterDateTime: encounterData.dateTime, // Format: "2025-05-24 13:10:00"
        review: programType,
        duration: parseInt(encounterData.duration), // Ensure it's a number
        userId: String(userId),
        ...(encounterData.encounterId && { encounterId: encounterData.encounterId })
      };

      console.log('Save encounter payload:', payload);

      const response = await apiPostWithToken(
        payload,
        URLS.caregiverUrl + 'saveEncounter'
      );

      console.log('Save encounter response:', response);
      
      const responseData = response?.data || response?.response?.data;
      const responseStatus = response?.status || response?.response?.status;

      console.log('Save encounter processed response:', { responseStatus, responseData });

      if (responseStatus === 200 && responseData) {
        if (responseData.success === true) {
          console.log('Encounter saved successfully:', responseData.messages);
          // Add a small delay to ensure the server has processed the update
          setTimeout(() => {
            fetchEncounters(false);
          }, 500);
          return true;
        } else {
          // API returned success: false
          let errorMessage = "Failed to save encounter";
          if (responseData.messages && responseData.messages.length > 0) {
            errorMessage = responseData.messages.join(', ');
          }
          console.log('Save failed with message:', errorMessage);
          Alert.alert("Error", errorMessage, [{ text: "Dismiss" }]);
          return false;
        }
      } else {
        console.log('API call failed with status:', responseStatus);
        Alert.alert("Error", "An unexpected error occurred while saving encounter. Please try again.", [{ text: "Dismiss" }]);
        return false;
      }
    } catch (error) {
      console.error('saveEncounter error:', error);
      Alert.alert("Error", "An unexpected error occurred while saving encounter. Please try again.", [{ text: "Dismiss" }]);
      return false;
    }
  };

  const deleteEncounter = async (encounterId: string) => {
    if (!userId) {
      Alert.alert('Error', 'User authentication required');
      return false;
    }
    
    try {
      const payload = { encounterId };
      console.log('Delete encounter payload:', payload);

      const response = await apiPostWithToken(
        payload,
        URLS.caregiverUrl + 'deleteEncounter'
      );

      console.log('Delete encounter response:', response);

      const responseData = response?.data || response?.response?.data;
      const responseStatus = response?.status || response?.response?.status;

      console.log('Delete encounter processed response:', { responseStatus, responseData });

      if (responseStatus === 200 && responseData) {
        if (responseData.success === true) {
          console.log('Encounter deleted successfully:', responseData.messages);
          fetchEncounters(false); // Refresh the list
          return true;
        } else {
          // API returned success: false
          let errorMessage = "Failed to delete encounter";
          if (responseData.messages && responseData.messages.length > 0) {
            errorMessage = responseData.messages.join(', ');
          }
          console.log('Delete failed with message:', errorMessage);
          Alert.alert("Error", errorMessage, [{ text: "Dismiss" }]);
          return false;
        }
      } else {
        console.log('API call failed with status:', responseStatus);
        Alert.alert("Error", "An unexpected error occurred while deleting encounter. Please try again.", [{ text: "Dismiss" }]);
        return false;
      }
    } catch (error) {
      console.error('deleteEncounter error:', error);
      Alert.alert("Error", "An unexpected error occurred while deleting encounter. Please try again.", [{ text: "Dismiss" }]);
      return false;
    }
  };

  // Load encounters on component mount and when credentials are available
  useEffect(() => {
    console.log('useEffect triggered for fetchEncounters:', { patientId, userId, fromDate, toDate });
    if (patientId && userId && fromDate && toDate) {
      console.log('Calling fetchEncounters...');
      fetchEncounters();
    } else {
      console.log('Not calling fetchEncounters - missing data');
    }
  }, [patientId, userId, fromDate, toDate]);

  // Get display info for encounter review type
  const getReviewTypeInfo = (reviewType: string) => {
    const type = REVIEW_TYPES.find(t => t.id === reviewType) || currentProgram;
        return {
      label: type.label,
      color: type.color,
      backgroundColor: `${type.color}15`
    };
  };

  // Format date for display
  const formatDateTime = (dateTimeStr: string) => {
    try {
      const date = moment(dateTimeStr, 'MM-DD-YYYY HH:mm:ss');
      if (date.isValid()) {
        return {
          date: date.format('MMM DD, YYYY'),
          time: date.format('h:mm A')
        };
      }
      return { date: 'Invalid date', time: '' };
    } catch {
      return { date: 'Invalid date', time: '' };
    }
  };

  // Enhanced encounter card component
  const renderEncounterItem = ({ item }: { item: APIEncounter }) => {
    const reviewInfo = getReviewTypeInfo(item.review);
    const dateTime = formatDateTime(item.encounterDateTime);
    const programInfo = REVIEW_TYPES.find(p => p.id === item.review) || currentProgram;
    
    const handleEdit = () => {
      setEditingEncounter(item);
      setShowEditEncounterModal(true);
    };

    const handleDelete = () => {
      Alert.alert(
        'Delete Encounter',
        'Are you sure you want to delete this encounter? This action cannot be undone.',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Delete', 
            style: 'destructive',
            onPress: () => deleteEncounter(item.encounterId)
          }
        ]
      );
    };

    return (
      <View style={styles.encounterCard}>
        {/* Header */}
        <View style={styles.encounterHeader}>
          <View style={styles.encounterTypeContainer}>
            <View style={[styles.encounterTypeIcon, { backgroundColor: reviewInfo.backgroundColor }]}>
              <Text style={[styles.encounterTypeLabel, { color: reviewInfo.color }]}>
                {reviewInfo.label}
              </Text>
            </View>
            <View style={styles.encounterMetadata}>
              <Text style={styles.encounterReason}>{item.encounterReason}</Text>
              <View style={styles.encounterDateContainer}>
                <Ionicons name="calendar-outline" size={14} color="#6B7280" />
                <Text style={styles.encounterDate}>{dateTime.date}</Text>
                <Ionicons name="time-outline" size={14} color="#6B7280" />
                <Text style={styles.encounterTime}>{dateTime.time}</Text>
              </View>
              <Text style={styles.programFullName}>{programInfo.fullName}</Text>
            </View>
          </View>
          
          <View style={styles.durationBadge}>
            <Ionicons name="stopwatch-outline" size={16} color="#059669" />
            <Text style={styles.durationText}>{item.duration}min</Text>
            </View>
        </View>

        {/* Content */}
        <Text style={styles.encounterContent} numberOfLines={3}>
          {item.encounterDescription}
        </Text>

        {/* Footer with actions and metadata */}
        <View style={styles.encounterFooter}>
          <View style={styles.encounterMeta}>
            <Text style={styles.addedBy}>Added by {item.addedByUser}</Text>
          </View>
        <View style={styles.encounterActions}>
            <TouchableOpacity style={styles.actionButton} onPress={handleEdit}>
              <Ionicons name="create-outline" size={18} color="#4F46E5" />
          </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton} onPress={handleDelete}>
              <Ionicons name="trash-outline" size={18} color="#DC2626" />
          </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  // Empty state component
  const renderEmptyState = () => {
    return (
    <View style={styles.emptyState}>
      <View style={styles.emptyStateIcon}>
        <MaterialIcons name="note-add" size={64} color="#D1D5DB" />
      </View>
        <Text style={styles.emptyStateTitle}>No {currentProgram.label} encounters found</Text>
      <Text style={styles.emptyStateSubtitle}>
          Start documenting {currentProgram.fullName} encounters by adding your first entry
      </Text>
      <TouchableOpacity 
          style={[styles.emptyStateButton, { backgroundColor: currentProgram.color }]}
        onPress={() => setShowAddEncounterModal(true)}
      >
          <Text style={styles.emptyStateButtonText}>Add {currentProgram.label} Encounter</Text>
      </TouchableOpacity>
    </View>
  );
  };

  // Pull to refresh handler
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchEncounters(false);
  }, []);

  // FAB press handler
  const handleFabPress = () => {
    Animated.sequence([
      Animated.parallel([
        Animated.timing(fabScale, {
          toValue: 0.9,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(fabOpacity, {
          toValue: 0.8,
          duration: 100,
          useNativeDriver: true,
        })
      ]),
      Animated.parallel([
        Animated.spring(fabScale, {
          toValue: 1,
          friction: 3,
          useNativeDriver: true,
        }),
        Animated.timing(fabOpacity, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        })
      ])
    ]).start();

    setShowAddEncounterModal(true);
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <Container style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor={Colors.TurquoiseBlue} />
        <NavigationHeader 
          title={`${currentProgram.label} Encounters`}
          showBackButton={true}
          showLogo={false}
          showRightLogo={true}
        />
        <Loader modalVisible={loading} />
        
        <View style={styles.contentContainer}>
          {/* Date Range Selector */}
          <View style={styles.datePickersContainer}>
            <View style={styles.datePickerWrapper}>
              <AlertsDatePicker
                date={fromDate}
                onDateChange={(date) => setFromDate(date)}
                label="Start Date"
              />
            </View>
            <View style={styles.dateSeparator}>
              <Ionicons name="arrow-forward" size={18} color={Colors.GrayBlue} />
            </View>
            <View style={styles.datePickerWrapper}>
              <AlertsDatePicker
                date={toDate}
                onDateChange={(date) => setToDate(date)}
                label="End Date"
              />
            </View>
          </View>

          {/* Program Info Header */}
          <View style={styles.programHeader}>
            <View style={styles.programIconContainer}>
            <View style={[
                styles.programIcon,
                { backgroundColor: currentProgram.color }
            ]}>
              {(() => {
                const iconInfo = getCategoryIcon(title);
                const iconProps = { size: 24, color: "#FFFFFF" };
                
                switch (iconInfo.family) {
                  case 'MaterialCommunityIcons':
                    return <MaterialCommunityIcons name={iconInfo.name as any} {...iconProps} />;
                  case 'FontAwesome5':
                    return <FontAwesome5 name={iconInfo.name as any} {...iconProps} />;
                  case 'MaterialIcons':
                  default:
                    return <MaterialIcons name={iconInfo.name as any} {...iconProps} />;
                }
              })()}
            </View>
              <View style={styles.programInfo}>
                <Text style={styles.programName}>{currentProgram.fullName}</Text>
                <Text style={styles.encounterCount}>
                  {loading ? 'Loading...' : `${encountersData.length} encounters`}
                </Text>
            </View>
          </View>
        </View>

        {/* Encounters List */}
          {!loading && (
        <FlatList
          data={encountersData}
              keyExtractor={(item) => item.encounterId}
          renderItem={renderEncounterItem}
          contentContainerStyle={[
            styles.listContainer,
            encountersData.length === 0 && styles.emptyListContainer
          ]}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={renderEmptyState}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={onRefresh}
                  colors={[currentProgram.color]}
                  tintColor={currentProgram.color}
                />
              }
            />
          )}

        {/* Floating Action Button */}
          {!loading && (
        <Animated.View
          style={[
            styles.fab,
            {
              transform: [{ scale: fabScale }],
              opacity: fabOpacity,
            }
          ]}
        >
          <TouchableOpacity
                style={[styles.fabButton, { backgroundColor: currentProgram.color }]}
            onPress={handleFabPress}
            activeOpacity={0.8}
          >
            <MaterialIcons name="add" size={28} color="#FFFFFF" />
          </TouchableOpacity>
        </Animated.View>
          )}
        </View>

        {/* Add Encounter Modal */}
        <EncounterModal
          visible={showAddEncounterModal}
          onClose={() => setShowAddEncounterModal(false)}
          onSave={saveEncounter}
          patientId={patientId}
          title={`Add ${currentProgram.label} Encounter`}
          programType={programType}
          programInfo={currentProgram}
        />

        {/* Edit Encounter Modal */}
        <EncounterModal
          visible={showEditEncounterModal}
          onClose={() => {
            setShowEditEncounterModal(false);
            setEditingEncounter(null);
          }}
          onSave={saveEncounter}
          patientId={patientId}
          title={`Edit ${currentProgram.label} Encounter`}
          encounter={editingEncounter}
          programType={programType}
          programInfo={currentProgram}
        />
      </Container>
    </TouchableWithoutFeedback>
  );
};

// Enhanced Encounter Modal Component
interface EncounterModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (encounter: any) => Promise<boolean>;
  patientId: string;
  title: string;
  encounter?: APIEncounter | null;
  programType: string;
  programInfo: any;
}

const EncounterModal: React.FC<EncounterModalProps> = ({ 
  visible, 
  onClose, 
  onSave, 
  patientId,
  title,
  encounter,
  programType,
  programInfo
}) => {
  const [formData, setFormData] = useState({
    reason: '',
    description: '',
    duration: '',
    review: programType,
    dateTime: moment().format('YYYY-MM-DD HH:mm:ss')
  });
  const [saving, setSaving] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showReasonDropdown, setShowReasonDropdown] = useState(false);
  
  // Speech recognition state
  const [isListening, setIsListening] = useState(false);
  const [partialResults, setPartialResults] = useState<string>("");
  const [finalTranscript, setFinalTranscript] = useState("");

  // Enhanced speech recognition setup for real-time feedback
  useSpeechRecognitionEvent("start", () => {
    setIsListening(true);
    setPartialResults("");
    setFinalTranscript("");
  });
  
  useSpeechRecognitionEvent("end", () => {
    setIsListening(false);
    
    // When recognition ends, add the final transcript to description if it exists
    if (finalTranscript) {
      setFormData(prevData => {
        const separator = prevData.description.length > 0 ? ' ' : '';
        return {
          ...prevData,
          description: prevData.description + separator + finalTranscript
        };
      });
      
      // Clear the transcripts after adding to description
      setFinalTranscript("");
      setPartialResults("");
    }
  });
  
  useSpeechRecognitionEvent("result", (event) => {
    if (event.results && event.results.length > 0) {
      const result = event.results[0];
      const transcript = result.transcript.trim();
      
      if (transcript) {
        // For real-time feedback, always update partial results
        // When the recognition is stable, it will be added to description in the end event
        setPartialResults(transcript);
        setFinalTranscript(transcript);
      }
    }
  });
  
  useSpeechRecognitionEvent("error", (event) => {
    console.log("Speech recognition error:", event.error, event.message);
    setIsListening(false);
  });

  // Function to toggle speech recognition with enhanced settings
  const toggleSpeechToText = async () => {
    try {
      if (isListening) {
        ExpoSpeechRecognitionModule.stop();
      } else {
        // Request permissions if needed and start listening
        const permissionResult = await ExpoSpeechRecognitionModule.requestPermissionsAsync();
        if (permissionResult.granted) {
          // Clear any existing partial results
          setPartialResults("");
          setFinalTranscript("");
          
          // Start speech recognition with enhanced settings
          ExpoSpeechRecognitionModule.start({
            lang: "en-US",
            interimResults: true,  // Enable interim results for real-time feedback
            continuous: true,      // Enable continuous recognition
            maxAlternatives: 1     // Only get the best match
          });
        } else {
          console.warn("Speech recognition permission not granted");
          Alert.alert("Permission Required", "Please enable microphone permissions to use voice input.");
        }
      }
    } catch (error) {
      console.log("Error with speech recognition:", error);
      Alert.alert("Error", "Voice recognition is not available on this device.");
    }
  };

  // Initialize form data when editing
  useEffect(() => {
    if (encounter) {
      // Convert API date format "05-24-2025 13:15:00" to our format "2025-05-24 13:15:00"
      let formattedDateTime = encounter.encounterDateTime;
      if (encounter.encounterDateTime) {
        const parsedDate = moment(encounter.encounterDateTime, 'MM-DD-YYYY HH:mm:ss');
        if (parsedDate.isValid()) {
          formattedDateTime = parsedDate.format('YYYY-MM-DD HH:mm:ss');
        }
      }
      
      setFormData({
        reason: encounter.encounterReason,
        description: encounter.encounterDescription,
        duration: encounter.duration.toString(),
        review: encounter.review,
        dateTime: formattedDateTime
      });
    } else {
      setFormData({
        reason: '',
        description: '',
        duration: '',
        review: programType,
        dateTime: moment().format('YYYY-MM-DD HH:mm:ss')
      });
    }
    
    // Reset speech recognition states when modal opens/closes
    if (!visible) {
      setPartialResults("");
      setFinalTranscript("");
      setIsListening(false);
      // Stop any ongoing speech recognition
      try {
        ExpoSpeechRecognitionModule.stop();
      } catch (error) {
        // Ignore errors when stopping
      }
    }
  }, [encounter, visible, programType]);

  const handleSave = async () => {
    if (!formData.reason.trim() || !formData.description.trim() || !formData.duration) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    setSaving(true);
    const encounterData = {
      ...formData,
      duration: parseInt(formData.duration),
      dateTime: moment(formData.dateTime).format('YYYY-MM-DD HH:mm:ss'),
      ...(encounter && { encounterId: encounter.encounterId })
    };

    console.log('Modal encounter data being saved:', encounterData);

    try {
      const success = await onSave(encounterData);
      
      if (success) {
        // Add a small delay to ensure the parent component refresh completes
        setTimeout(() => {
          setSaving(false);
          onClose();
        }, 800);
        } else {
        setSaving(false);
      }
    } catch (error) {
      console.error('Error in handleSave:', error);
      setSaving(false);
      Alert.alert('Error', 'An unexpected error occurred while saving.');
    }
  };

  const formatDateTimeForDisplay = (dateTimeStr: string) => {
    return moment(dateTimeStr).format('MMM DD, YYYY [at] h:mm A');
  };

  // Get encounter reasons - now using standardized reason codes
  const getEncounterReasons = (): { label: string; code: string }[] => {
    return ENCOUNTER_REASON_OPTIONS;
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <SafeAreaView style={styles.modalContainer}>
          <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
          
          {/* Modal Header */}
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={onClose} style={styles.modalCloseButton}>
              <Text style={styles.modalCloseText}>Cancel</Text>
            </TouchableOpacity>
            <View style={styles.modalTitleContainer}>
              <Text style={styles.modalTitle}>{title}</Text>
              <Text style={styles.modalSubtitle}>
                {programInfo.fullName}
              </Text>
            </View>
            <TouchableOpacity 
              onPress={handleSave}
              style={[
                styles.modalSaveButton,
                (!formData.reason.trim() || !formData.description.trim() || !formData.duration || saving) && styles.modalSaveButtonDisabled
              ]}
              disabled={!formData.reason.trim() || !formData.description.trim() || !formData.duration || saving}
            >
              {saving ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
              <Text style={[
                styles.modalSaveText,
                  (!formData.reason.trim() || !formData.description.trim() || !formData.duration) && styles.modalSaveTextDisabled
              ]}>Save</Text>
              )}
            </TouchableOpacity>
          </View>

          <KeyboardAvoidingView
            style={{ flex: 1 }}
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            keyboardVerticalOffset={Platform.OS === "ios" ? 100 : 20}
          >
            <ScrollView 
              style={{ flex: 1 }}
              contentContainerStyle={{ flexGrow: 1 }}
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled"
            >
              <View style={styles.modalContent}>
                {/* Date and Time */}
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>Date & Time</Text>
                      <TouchableOpacity
                    style={styles.dateTimeButton}
                    onPress={() => setShowDatePicker(true)}
                  >
                    <Ionicons name="calendar-outline" size={20} color="#6B7280" />
                    <Text style={styles.dateTimeText}>
                      {formatDateTimeForDisplay(formData.dateTime)}
                          </Text>
                      </TouchableOpacity>
                </View>

                {/* Encounter Reason */}
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>
                    Encounter Reason * 
                    <Text style={styles.modalSectionSubtitle}>
                      ({programInfo.label} specific)
                    </Text>
                  </Text>
                  <TouchableOpacity
                    style={styles.dropdownButton}
                    onPress={() => setShowReasonDropdown(!showReasonDropdown)}
                  >
                    <Text style={[
                      styles.dropdownButtonText,
                      !formData.reason && styles.dropdownPlaceholderText
                    ]}>
                      {formData.reason || 'Select encounter reason'}
                    </Text>
                    <Ionicons 
                      name={showReasonDropdown ? "chevron-up" : "chevron-down"} 
                      size={20} 
                      color="#6B7280" 
                    />
                  </TouchableOpacity>
                  
                  {showReasonDropdown && (
                    <View style={styles.dropdownList}>
                      <ScrollView 
                        style={styles.dropdownScrollView}
                        showsVerticalScrollIndicator={true}
                        nestedScrollEnabled={true}
                      >
                        {getEncounterReasons().map((reason) => (
                          <TouchableOpacity
                            key={reason.code}
                            style={[
                              styles.dropdownItem,
                              formData.reason === reason.label && styles.dropdownItemSelected
                            ]}
                            onPress={() => {
                              console.log('Selected reason:', reason.label);
                              setFormData(prev => ({ ...prev, reason: reason.label }));
                              setShowReasonDropdown(false);
                            }}
                          >
                            <Text style={[
                              styles.dropdownItemText,
                              formData.reason === reason.label && styles.dropdownItemTextSelected
                            ]}>
                              {reason.label}
                            </Text>
                            {formData.reason === reason.label && (
                              <Ionicons name="checkmark" size={20} color={CoreColors.TurquoiseBlue} />
                            )}
                          </TouchableOpacity>
                        ))}
                      </ScrollView>
                    </View>
                  )}
                  
                </View>

                {/* Duration */}
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>Duration (minutes) *</Text>
                  <TextInput
                    style={styles.durationInput}
                    value={formData.duration}
                    onChangeText={(text) => setFormData(prev => ({ ...prev, duration: text.replace(/[^0-9]/g, '') }))}
                    placeholder="Enter duration in minutes"
                    placeholderTextColor="#9CA3AF"
                    keyboardType="numeric"
                    maxLength={3}
                  />
                </View>

                {/* Description */}
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>
                    Description * 
                    <Text style={styles.modalSectionSubtitle}>
                      (Detailed {programInfo.label} notes)
                    </Text>
                  </Text>
                  <View style={styles.textInputContainer}>
                    <TextInput
                      style={styles.textInput}
                      multiline
                      value={formData.description}
                      onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
                      placeholder={`Enter encounter details or use voice-to-text...`}
                      placeholderTextColor="#9CA3AF"
                      textAlignVertical="top"
                      scrollEnabled={true}
                      blurOnSubmit={false}
                      maxLength={1000}
                    />
                    
                    {/* Show partial results during speech recognition */}
                    {partialResults.length > 0 && (
                      <View style={styles.partialResultsContainer}>
                        <Text style={styles.partialResultsText}>
                          Listening: {partialResults}
                        </Text>
                      </View>
                    )}
                  </View>
                  
                  {/* Voice to Text and Character Count */}
                  <View style={styles.textInputFooter}>
                    <TouchableOpacity
                      style={[
                        styles.micButton,
                        isListening && styles.micButtonActive
                      ]}
                      onPress={toggleSpeechToText}
                      activeOpacity={0.7}
                    >
                      <Ionicons 
                        name={isListening ? "mic" : "mic-outline"} 
                        size={20} 
                        color={isListening ? "#FFFFFF" : CoreColors.TurquoiseBlue} 
                      />
                      <Text style={[
                        styles.micButtonText,
                        isListening && styles.micButtonTextActive
                      ]}>
                        {isListening ? "Stop" : "Voice"}
                      </Text>
                    </TouchableOpacity>
                    
                    <Text style={styles.characterCount}>
                      {formData.description.length}/1000
                    </Text>
                  </View>
                </View>
              </View>
            </ScrollView>
          </KeyboardAvoidingView>
        </SafeAreaView>
      </TouchableWithoutFeedback>
      
      {/* Date Time Picker Modal */}
      <DateTimePickerModal
        isVisible={showDatePicker}
        mode="datetime"
        onConfirm={(date) => {
          setFormData(prev => ({
            ...prev,
            dateTime: moment(date).format('YYYY-MM-DD HH:mm:ss')
          }));
          setShowDatePicker(false);
        }}
        onCancel={() => setShowDatePicker(false)}
        date={moment(formData.dateTime).toDate()}
        maximumDate={new Date()}
        minimumDate={moment().subtract(1, 'year').toDate()}
      />
    </Modal>
  );
};

// Helper functions
const getCategoryColor = (title: string) => {
  const category = title.split(' - ')[0];
  switch (category) {
    case 'RPM': return '#4ECDC4';
    case 'CCM': return '#9381FF';
    case 'PCM': return '#7A77FF';
    case 'RTM': return '#36B5E0';
    case 'Home Health': return '#5FB49C';
    case 'Hospice': return '#FF9F1C';
    default: return CoreColors.TurquoiseBlue;
  }
};

const getCategoryIcon = (title: string) => {
  const category = title.split(' - ')[0];
  switch (category) {
    case 'RPM': 
      return { family: 'MaterialCommunityIcons', name: 'heart-pulse' };
    case 'CCM': 
      return { family: 'FontAwesome5', name: 'notes-medical' };
    case 'PCM': 
      return { family: 'MaterialCommunityIcons', name: 'account-heart' };
    case 'RTM': 
      return { family: 'MaterialCommunityIcons', name: 'chart-line' };
    case 'Home Health': 
      return { family: 'FontAwesome5', name: 'home' };
    case 'Hospice': 
      return { family: 'MaterialCommunityIcons', name: 'hand-heart' };
    default: 
      return { family: 'MaterialIcons', name: 'note' };
  }
};

export default CCMPlaceholderScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8fafc",
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
    marginTop: 10, // Matches other screens spacing
  },
  datePickersContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  datePickerWrapper: {
    width: "46%",
  },
  dateSeparator: {
    width: "8%",
    alignItems: "center",
    justifyContent: "center",
    paddingTop: 20,
  },
  programHeader: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 2,
  },
  programIconContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  programIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  programInfo: {
    flex: 1,
  },
  programName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#14142B",
    marginBottom: 2,
  },
  encounterCount: {
    fontSize: 14,
    color: "#6B7280",
  },
  listContainer: {
    paddingBottom: 100,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  encounterCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 3,
  },
  encounterHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  encounterTypeContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  encounterTypeIcon: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    marginRight: 12,
  },
  encounterTypeLabel: {
    fontSize: 12,
    fontWeight: "700",
    letterSpacing: 0.5,
  },
  encounterMetadata: {
    flex: 1,
  },
  encounterReason: {
    fontSize: 16,
    fontWeight: "600",
    color: "#14142B",
    marginBottom: 4,
  },
  encounterDateContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  encounterDate: {
    fontSize: 14,
    color: "#6B7280",
    marginLeft: 4,
    marginRight: 12,
  },
  encounterTime: {
    fontSize: 14,
    color: "#6B7280",
    marginLeft: 4,
  },
  programFullName: {
    fontSize: 12,
    color: "#9CA3AF",
    fontStyle: "italic",
    marginTop: 2,
  },
  durationBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#ECFDF5",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  durationText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#059669",
    marginLeft: 4,
  },
  encounterContent: {
    fontSize: 15,
    lineHeight: 22,
    color: "#374151",
    marginBottom: 16,
  },
  encounterFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: "#F3F4F6",
  },
  encounterMeta: {
    flex: 1,
  },
  addedBy: {
    fontSize: 12,
    color: "#9CA3AF",
    fontStyle: "italic",
  },
  encounterActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  actionButton: {
    padding: 8,
    marginLeft: 8,
    borderRadius: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 40,
  },
  emptyStateIcon: {
    marginBottom: 16,
  },
  emptyStateTitle: {
    fontSize: 22,
    fontWeight: "700",
    color: "#14142B",
    marginBottom: 8,
    textAlign: "center",
  },
  emptyStateSubtitle: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 32,
  },
  emptyStateButton: {
    backgroundColor: CoreColors.TurquoiseBlue,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
    shadowColor: CoreColors.TurquoiseBlue,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  emptyStateButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF",
  },
  fab: {
    position: "absolute",
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
  },
  fabButton: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 28,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  modalHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#E5E7EB",
  },
  modalCloseButton: {
    padding: 8,
    minWidth: 70,
  },
  modalCloseText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#6B7280",
  },
  modalTitleContainer: {
    flex: 1,
    alignItems: "center",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: "#14142B",
  },
  modalSubtitle: {
    fontSize: 14,
    color: "#6B7280",
    marginTop: 4,
  },
  modalSaveButton: {
    backgroundColor: CoreColors.TurquoiseBlue,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    minWidth: 70,
    alignItems: "center",
  },
  modalSaveButtonDisabled: {
    backgroundColor: "#D1D5DB",
  },
  modalSaveText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF",
  },
  modalSaveTextDisabled: {
    color: "#9CA3AF",
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  modalSection: {
    marginBottom: 24,
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#14142B",
    marginBottom: 12,
  },
  modalSectionSubtitle: {
    fontSize: 14,
    color: "#6B7280",
    fontStyle: "italic",
  },
  dateTimeButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#E0E4EF",
    minHeight: 56,
  },
  dateTimeText: {
    fontSize: 16,
    color: "#14142B",
    marginLeft: 12,
    fontWeight: "400",
  },
  reviewTypeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  reviewTypeButton: {
    flex: 1,
    padding: 12,
    marginHorizontal: 4,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#E5E7EB",
    alignItems: "center",
  },
  reviewTypeButtonText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#6B7280",
  },
  reviewTypeButtonTextSelected: {
    color: "#FFFFFF",
  },
  reasonContainer: {
    marginTop: 8,
  },
  reasonChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    backgroundColor: "#F3F4F6",
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  reasonChipSelected: {
    backgroundColor: CoreColors.TurquoiseBlue,
    borderColor: CoreColors.TurquoiseBlue,
  },
  reasonChipText: {
    fontSize: 14,
    color: "#6B7280",
    fontWeight: "500",
  },
  reasonChipTextSelected: {
    color: "#FFFFFF",
    fontWeight: "600",
  },
  durationInput: {
    padding: 16,
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#E0E4EF",
    fontSize: 16,
    color: "#14142B",
    minHeight: 56,
    fontWeight: "400",
  },
  textInputContainer: {
    borderColor: "#E0E4EF",
    borderWidth: 1,
    borderRadius: 12,
    backgroundColor: "#FFFFFF",
    padding: 16,
    minHeight: 140,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  textInput: {
    fontSize: 16,
    lineHeight: 24,
    color: "#14142B",
    textAlign: "left",
    textAlignVertical: "top",
    minHeight: 120,
    maxHeight: 200,
    paddingTop: 0,
    paddingBottom: 8,
    padding: 0,
  },
  characterCount: {
    fontSize: 14,
    color: "#6B7280",
  },
  partialResultsContainer: {
    marginTop: 8,
    padding: 8,
    backgroundColor: "rgba(83, 208, 236, 0.1)",
    borderRadius: 8,
  },
  partialResultsText: {
    fontSize: 14,
    color: "#4A5268",
    fontStyle: "italic",
  },
  textInputFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 12,
  },
  micButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    borderWidth: 1,
    borderColor: CoreColors.TurquoiseBlue,
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  micButtonActive: {
    backgroundColor: CoreColors.TurquoiseBlue,
    borderColor: CoreColors.TurquoiseBlue,
  },
  micButtonText: {
    fontSize: 14,
    fontWeight: "600",
    color: CoreColors.TurquoiseBlue,
    marginLeft: 6,
  },
  micButtonTextActive: {
    color: "#FFFFFF",
  },
  reasonScrollContainer: {
    paddingVertical: 4,
  },
  debugText: {
    fontSize: 12,
    color: "#9CA3AF",
    marginTop: 8,
    fontStyle: "italic",
  },
  dropdownButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#E0E4EF",
    minHeight: 56,
  },
  dropdownButtonText: {
    fontSize: 16,
    color: "#14142B",
    flex: 1,
    fontWeight: "400",
  },
  dropdownPlaceholderText: {
    color: "#6B7280",
    fontWeight: "400",
  },
  dropdownList: {
    marginTop: 4,
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#E0E4EF",
    maxHeight: 240,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
  },
  dropdownScrollView: {
    maxHeight: 240,
  },
  dropdownItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderBottomWidth: 0.5,
    borderBottomColor: "#F1F3F4",
  },
  dropdownItemSelected: {
    backgroundColor: "#F0F9FF",
  },
  dropdownItemText: {
    fontSize: 15,
    color: "#14142B",
    flex: 1,
    lineHeight: 20,
  },
  dropdownItemTextSelected: {
    color: CoreColors.TurquoiseBlue,
    fontWeight: "500",
  },
});
