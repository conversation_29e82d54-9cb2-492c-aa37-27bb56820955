import {
  FETCH_PATIENTS_LIST,
  FETCH_PATIENTS_LIST_FAILURE,
  FETCH_PATIENTS_LIST_SUCCESS,
  FETCH_MORE_PATIENTS_LIST,
  FETCH_MORE_PATIENTS_LIST_SUCCESS,
  FETCH_MORE_PATIENTS_LIST_FAILURE,
} from './type';

export const fetchPatientsList = (params: {
  careGiverId: string | number;
  orgId: string | number;
  pageNumber?: number;
  pageSize?: number;
  resetPagination?: boolean;
}) => {
  return {
    type: FETCH_PATIENTS_LIST,
    payload: {
      careGiverId: params.careGiverId,
      orgId: params.orgId,
      pageNumber: params.pageNumber || 0,
      pageSize: params.pageSize || 10,
      resetPagination: params.resetPagination || false,
    },
  };
};

export const fetchMorePatientsList = (params: {
  careGiverId: string | number;
  orgId: string | number;
  pageNumber: number;
  pageSize: number;
}) => {
  return {
    type: FETCH_MORE_PATIENTS_LIST,
    payload: {
      careGiverId: params.careGiverId,
      orgId: params.orgId,
      pageNumber: params.pageNumber,
      pageSize: params.pageSize,
    },
  };
};

export const fetchPatientsListSuccess = (params: {
  type: string;
  data: any[];
  orgId?: string | number;
  totalCount?: number;
  pageNumber?: number;
  pageSize?: number;
  isLoadingMore?: boolean;
}) => {
  return {
    type: FETCH_PATIENTS_LIST_SUCCESS,
    data: params.data,
    orgId: params.orgId,
    totalCount: params.totalCount,
    pageNumber: params.pageNumber,
    pageSize: params.pageSize,
    isLoadingMore: params.isLoadingMore || false,
  };
};

export const fetchMorePatientsListSuccess = (params: {
  data: any[];
  orgId?: string | number;
  totalCount?: number;
  pageNumber: number;
  pageSize: number;
}) => {
  return {
    type: FETCH_MORE_PATIENTS_LIST_SUCCESS,
    data: params.data,
    orgId: params.orgId,
    totalCount: params.totalCount,
    pageNumber: params.pageNumber,
    pageSize: params.pageSize,
  };
};

export const fetchPatientsListFailure = (error: string) => {
  return {
    type: FETCH_PATIENTS_LIST_FAILURE,
    msg: error,
  };
};

export const fetchMorePatientsListFailure = (error: string) => {
  return {
    type: FETCH_MORE_PATIENTS_LIST_FAILURE,
    msg: error,
  };
};
