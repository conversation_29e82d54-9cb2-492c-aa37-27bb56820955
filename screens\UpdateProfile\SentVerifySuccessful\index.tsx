import {useNavigation} from '@react-navigation/native';
import React, {memo, useCallback} from 'react';
import {Image, StyleSheet} from 'react-native';
import ButtonLinear from '@/components/Buttons/ButtonLinear';
import Container from '@/components/Layout/Container';
import Text from '@/components/Text';
import {Routes} from '@/constants';
import Theme from '@/constants/Theme';
import scale from '@/utils/scale';
interface SentVerifySuccessfulProps {}

const SentVerifySuccessful = memo((props: SentVerifySuccessfulProps) => {
  const {navigate} = useNavigation();

  const onGoToDashBoard = useCallback(() => {
    navigate(Routes.MainTab);
  }, [navigate]);

  return (
    <Container style={styles.container}>
      <Image
        source={require('images/img_sent_message.png')}
        style={{width: scale(160, true), height: scale(160, true)}}
      />
      <Text size={20} lineHeight={24} bold marginTop={scale(55)}>
        Request sent successful!
      </Text>
      <Text size={15} lineHeight={24} marginTop={16}>
        Our Medical Expert will check and confirm about your profile within 2-3
        business day.
      </Text>
      <ButtonLinear
        white
        title={'Go to Home Dashboard'}
        style={{paddingHorizontal: 32}}
        onPress={onGoToDashBoard}
      />
    </Container>
  );
});

export default SentVerifySuccessful;

const styles = StyleSheet.create({
  container: {
    ...Theme.container,
    ...Theme.center,
  },
});
