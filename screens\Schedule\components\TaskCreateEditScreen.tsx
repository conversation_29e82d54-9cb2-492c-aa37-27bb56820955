import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Platform,
  TextInput,
  FlatList,
  SafeAreaView,
  Alert,
  Keyboard,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import moment from 'moment';
import Text from '@/components/Text';
import { Task } from '../types';
import { fetchPatientsList } from '@/services/actions/fetchPatientListActions';
import { PatientItemProps } from '@/models';
import { useRouter } from 'expo-router';
import { useNavigation } from '@react-navigation/native';
import { getBottomTabSafePadding } from '@/utils/layoutHelper';

// Modern Design System following Material Design 3
const DESIGN_SYSTEM = {
  spacing: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },
  borderRadius: {
    xs: 4,
    sm: 8,
    md: 12,
    lg: 16,
    xl: 20,
    full: 999,
  },
  typography: {
    sizes: {
      caption: 12,
      body2: 14,
      body1: 16,
      subtitle2: 14,
      subtitle1: 16,
      h6: 18,
      h5: 20,
      h4: 24,
    },
    weights: {
      regular: '400' as const,
      medium: '500' as const,
      semibold: '600' as const,
      bold: '700' as const,
    },
    lineHeights: {
      tight: 1.2,
      normal: 1.4,
      relaxed: 1.6,
    }
  },
  elevation: {
    level1: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    level2: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.08,
      shadowRadius: 4,
      elevation: 2,
    },
    level3: {
      shadowColor: '#000000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.12,
      shadowRadius: 8,
      elevation: 4,
    },
  },
  colors: {
    // Surface colors
    surface: '#FFFFFF',
    surfaceVariant: '#F7F9FC',
    surfaceContainer: '#F1F5F9',
    
    // Text colors
    onSurface: '#0F172A',
    onSurfaceVariant: '#475569',
    onSurfaceSecondary: '#64748B',
    onSurfaceTertiary: '#94A3B8',
    
    // Brand colors
    primary: '#0EA5E9',
    primaryContainer: '#E0F2FE',
    onPrimary: '#FFFFFF',
    onPrimaryContainer: '#0C4A6E',
    
    // Action colors
    success: '#10B981',
    successContainer: '#ECFDF5',
    warning: '#F59E0B',
    warningContainer: '#FEF3C7',
    error: '#EF4444',
    errorContainer: '#FEF2F2',
    info: '#3B82F6',
    infoContainer: '#EFF6FF',
    purple: '#8B5CF6',
    purpleContainer: '#F3F4F6',
    
    // Border and divider
    outline: '#E2E8F0',
    outlineVariant: '#F1F5F9',
    
    // States
    pressed: 'rgba(0, 0, 0, 0.08)',
    hover: 'rgba(0, 0, 0, 0.04)',
    focused: 'rgba(0, 0, 0, 0.12)',
  }
};

interface TaskCreateEditScreenProps {
  selectedDate?: Date;
  selectedTask?: Task | null;
  isEditMode?: boolean;
  onSubmit: (task: Task) => Promise<void>;
  onDelete?: (taskId: string) => Promise<void>;
  onClose: () => void;
  isLoading?: boolean;
}

const TaskCreateEditScreen: React.FC<TaskCreateEditScreenProps> = ({
  selectedDate = new Date(),
  selectedTask,
  isEditMode = false,
  onSubmit,
  onDelete,
  onClose,
  isLoading = false,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const navigation = useNavigation();

  // Redux state
  const caregiverId = useSelector((state: any) => state?.loginReducer?.data?.userId);
  const orgId = useSelector((state: any) => state?.currentOrgIdReducer?.orgId);
  const patientListData = useSelector((state: any) => state?.patientsListReducer?.data);
  const isFetchingPatients = useSelector((state: any) => state?.patientsListReducer?.isFetching);

  // Form state
  const [title, setTitle] = useState(selectedTask?.title || '');
  const [description, setDescription] = useState(selectedTask?.description || '');
  const [priority, setPriority] = useState<'Low' | 'Medium' | 'High' | 'Critical'>(
    selectedTask?.priority || 'Low'
  );
  
  // Initialize dates with today's date/time for new tasks, defaulting to current time
  const initializeDateTime = () => {
    const now = new Date();
    
    // For existing tasks, use the task's date/time
    if (selectedTask) {
      // Parse dates from the API format if they are strings
      const parsedStart = typeof selectedTask.start === 'string' 
        ? moment(selectedTask.start, ['MM-DD-YYYY hh:mm A', 'YYYY-MM-DDTHH:mm:ss', moment.ISO_8601]).toDate()
        : selectedTask.start;
      
      const parsedEnd = typeof selectedTask.end === 'string'
        ? moment(selectedTask.end, ['MM-DD-YYYY hh:mm A', 'YYYY-MM-DDTHH:mm:ss', moment.ISO_8601]).toDate()
        : selectedTask.end;
      
      return { 
        start: parsedStart instanceof Date ? parsedStart.toISOString() : parsedStart,
        end: parsedEnd instanceof Date ? parsedEnd.toISOString() : parsedEnd
      };
    }
    
    // Always use today's date for new tasks, regardless of selectedDate
    // This ensures users can create tasks for today easily
    const todayDate = new Date();
    
    // If selectedDate is provided and is today, use it with current time
    // Otherwise, always default to today with current time
    const useSelectedDate = selectedDate && 
      new Date(selectedDate).toDateString() === todayDate.toDateString();
      
    const newDate = useSelectedDate ? new Date(selectedDate) : todayDate;
    
    // Set to current time
    newDate.setHours(now.getHours(), now.getMinutes(), 0, 0);
    
    // End time is 1 hour after start time
    const endTime = new Date(newDate.getTime() + 60 * 60 * 1000);
    
    return { start: newDate.toISOString(), end: endTime.toISOString() };
  };

  const defaultDateTime = initializeDateTime();

  const [startDate, setStartDate] = useState(() => {
    if (selectedTask) {
      // Parse date from the API format if it's a string
      return typeof selectedTask.start === 'string' 
        ? moment(selectedTask.start, ['MM-DD-YYYY hh:mm A', 'YYYY-MM-DDTHH:mm:ss', moment.ISO_8601]).toDate()
        : new Date(selectedTask.start);
    }
    return new Date(defaultDateTime.start);
  });
  
  const [startTime, setStartTime] = useState(() => {
    if (selectedTask) {
      // Parse time from the API format if it's a string
      return typeof selectedTask.start === 'string' 
        ? moment(selectedTask.start, ['MM-DD-YYYY hh:mm A', 'YYYY-MM-DDTHH:mm:ss', moment.ISO_8601]).toDate()
        : new Date(selectedTask.start);
    }
    return new Date(defaultDateTime.start);
  });
  
  const [endDate, setEndDate] = useState(() => {
    if (selectedTask) {
      // Parse date from the API format if it's a string
      return typeof selectedTask.end === 'string' 
        ? moment(selectedTask.end, ['MM-DD-YYYY hh:mm A', 'YYYY-MM-DDTHH:mm:ss', moment.ISO_8601]).toDate()
        : new Date(selectedTask.end);
    }
    return new Date(defaultDateTime.end);
  });
  
  const [endTime, setEndTime] = useState(() => {
    if (selectedTask) {
      // Parse time from the API format if it's a string
      return typeof selectedTask.end === 'string' 
        ? moment(selectedTask.end, ['MM-DD-YYYY hh:mm A', 'YYYY-MM-DDTHH:mm:ss', moment.ISO_8601]).toDate()
        : new Date(selectedTask.end);
    }
    return new Date(defaultDateTime.end);
  });
  
  const [selectedPatient, setSelectedPatient] = useState<PatientItemProps | null>(null);

  // UI state
  const [searchQuery, setSearchQuery] = useState('');
  const [showPatientSearch, setShowPatientSearch] = useState(false);
  const [showPriorityDropdown, setShowPriorityDropdown] = useState(false);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);

  // Priority options
  const priorityOptions: Array<'Low' | 'Medium' | 'High' | 'Critical'> = [
    'Low', 'Medium', 'High', 'Critical'
  ];

  // Priority colors
  const getPriorityColor = (priorityLevel: string) => {
    switch (priorityLevel) {
      case 'Low': return DESIGN_SYSTEM.colors.success;
      case 'Medium': return DESIGN_SYSTEM.colors.warning;
      case 'High': return DESIGN_SYSTEM.colors.error;
      case 'Critical': return DESIGN_SYSTEM.colors.purple;
      default: return DESIGN_SYSTEM.colors.primary;
    }
  };

  // Initialize form with selected task data
  useEffect(() => {
    if (selectedTask && isEditMode) {
      // Find the patient if we have patient data
      if (patientListData && selectedTask.patientId) {
        const patient = patientListData.find(
          (p: PatientItemProps) => p.patientId.toString() === selectedTask.patientId
        );
        if (patient) {
          setSelectedPatient(patient);
        }
      }
    }
  }, [selectedTask, isEditMode, patientListData]);

  // Debug state changes
  useEffect(() => {
    console.log('Picker States:', {
      showStartDatePicker,
      showStartTimePicker,
      showEndDatePicker,
      showEndTimePicker,
      platform: Platform.OS
    });
  }, [showStartDatePicker, showStartTimePicker, showEndDatePicker, showEndTimePicker]);

  // Fetch patients on mount
  useEffect(() => {
    if (caregiverId && orgId && !patientListData) {
      dispatch(fetchPatientsList({ careGiverId: caregiverId, orgId: orgId }));
    }
  }, [dispatch, caregiverId, orgId, patientListData]);

  // Filter patients based on search query
  const filteredPatients = React.useMemo(() => {
    if (!patientListData) return [];
    return patientListData.filter((patient: PatientItemProps) =>
      patient.patientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (patient.mrn && patient.mrn.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (patient.phone && patient.phone.includes(searchQuery))
    );
  }, [patientListData, searchQuery]);

  // Validate form
  const validateForm = (): string | null => {
    if (!title.trim()) return 'Please enter a task title';
    if (!selectedPatient) return 'Please select a patient';
    
    const startDateTime = new Date(startDate);
    startDateTime.setHours(startTime.getHours(), startTime.getMinutes(), 0, 0);
    
    const endDateTime = new Date(endDate);
    endDateTime.setHours(endTime.getHours(), endTime.getMinutes(), 0, 0);
    
    if (endDateTime <= startDateTime) return 'End time must be after start time';
    
    // Check if the start date is not in the past (except for edit mode of current/future tasks)
    if (!isEditMode) {
      const today = moment().startOf('day');
      if (moment(startDateTime).isBefore(today)) {
        return 'Cannot schedule tasks for previous dates';
      }
    } else {
      // For edit mode, check if the task is in the past and prevent editing past tasks
      const now = moment();
      if (selectedTask && moment(selectedTask.start).isBefore(now.subtract(1, 'hour'))) {
        return 'Cannot edit tasks that started more than 1 hour ago';
      }
    }
    
    return null;
  };

  // Handle form submission
  const handleSubmit = async () => {
    const validationError = validateForm();
    if (validationError) {
      Alert.alert('Validation Error', validationError);
      return;
    }

    try {
      const startDateTime = new Date(startDate);
      startDateTime.setHours(startTime.getHours(), startTime.getMinutes(), 0, 0);
      
      const endDateTime = new Date(endDate);
      endDateTime.setHours(endTime.getHours(), endTime.getMinutes(), 0, 0);

      // Format dates as MM-DD-YYYY HH:mm AM/PM
      const formatDateTime = (date: Date): string => {
        return moment(date).format('MM-DD-YYYY hh:mm A');
      };

      const task: Task = {
        id: selectedTask?.id || '',
        title: title.trim(),
        description: description.trim(),
        priority,
        start: formatDateTime(startDateTime),
        end: formatDateTime(endDateTime),
        patientId: selectedPatient!.patientId.toString(),
        patientName: selectedPatient!.patientName,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        userId: parseInt(caregiverId),
        className: '',
      };

      await onSubmit(task);
    } catch (error) {
      console.error('Error submitting task:', error);
      Alert.alert('Error', 'Failed to save task. Please try again.');
    }
  };

  // Handle delete
  const handleDelete = () => {
    if (!selectedTask?.id || !onDelete) return;

    Alert.alert(
      'Delete Task',
      'Are you sure you want to delete this task? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => onDelete(selectedTask.id),
        },
      ]
    );
  };

  // Handle date/time changes
  const onDateTimeChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      // Android: Always close picker after selection
      setShowStartDatePicker(false);
      setShowStartTimePicker(false);
      setShowEndDatePicker(false);
      setShowEndTimePicker(false);
    }

    if (selectedDate) {
      if (showStartDatePicker) {
        setStartDate(selectedDate);
      } else if (showStartTimePicker) {
        setStartTime(selectedDate);
      } else if (showEndDatePicker) {
        setEndDate(selectedDate);
      } else if (showEndTimePicker) {
        setEndTime(selectedDate);
      }
    }
  };

  // iOS picker handlers
  const handleIOSDateConfirm = (type: string) => {
    if (type === 'startDate') setShowStartDatePicker(false);
    else if (type === 'startTime') setShowStartTimePicker(false);
    else if (type === 'endDate') setShowEndDatePicker(false);
    else if (type === 'endTime') setShowEndTimePicker(false);
  };

  const handleIOSDateCancel = () => {
    setShowStartDatePicker(false);
    setShowStartTimePicker(false);
    setShowEndDatePicker(false);
    setShowEndTimePicker(false);
  };

  // Render patient search item
  const renderPatientItem = ({ item }: { item: PatientItemProps }) => (
    <TouchableOpacity
      style={styles.patientItem}
      onPress={() => {
        setSelectedPatient(item);
        setShowPatientSearch(false);
        setSearchQuery('');
        Keyboard.dismiss();
      }}
      activeOpacity={0.7}
    >
      <View style={styles.patientInfo}>
        <Text style={styles.patientName}>{item.patientName}</Text>
        <Text style={styles.patientDetails}>
          MRN: {item.mrn || 'N/A'} • {item.phone || 'No phone'}
        </Text>
        {item.address && (
          <Text style={styles.patientAddress} numberOfLines={1}>
            {item.address}
          </Text>
        )}
      </View>
      <Ionicons name="chevron-forward" size={20} color={DESIGN_SYSTEM.colors.onSurfaceVariant} />
    </TouchableOpacity>
  );

  // Render priority item
  const renderPriorityItem = (priorityLevel: 'Low' | 'Medium' | 'High' | 'Critical') => (
    <TouchableOpacity
      key={priorityLevel}
      style={styles.priorityItem}
      onPress={() => {
        setPriority(priorityLevel);
        setShowPriorityDropdown(false);
      }}
      activeOpacity={0.7}
    >
      <View style={[styles.priorityIndicator, { backgroundColor: getPriorityColor(priorityLevel) }]} />
      <Text style={[
        styles.priorityText,
        priority === priorityLevel && styles.priorityTextSelected
      ]}>
        {priorityLevel}
      </Text>
      {priority === priorityLevel && (
        <Ionicons name="checkmark" size={20} color={DESIGN_SYSTEM.colors.primary} />
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <SafeAreaView style={styles.headerSafeArea}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={onClose}
            activeOpacity={0.7}
          >
            <Ionicons name="chevron-back" size={24} color={DESIGN_SYSTEM.colors.onSurface} />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>
            {isEditMode ? 'Edit Task' : 'New Task'}
          </Text>

          {isEditMode && onDelete && (
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={handleDelete}
              activeOpacity={0.7}
            >
              <Ionicons name="trash-outline" size={20} color={DESIGN_SYSTEM.colors.error} />
            </TouchableOpacity>
          )}
        </View>
      </SafeAreaView>

      {/* Main Content */}
      <View style={styles.mainContent}>
        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={styles.scrollContent}
        >
          {/* Task Title */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Task Title *</Text>
            <TextInput
              style={[
                styles.textInput,
                isEditMode && styles.disabledInput
              ]}
              value={title}
              onChangeText={setTitle}
              placeholder="Enter task title"
              placeholderTextColor={DESIGN_SYSTEM.colors.onSurfaceVariant}
              maxLength={100}
              editable={!isEditMode}
            />
            {isEditMode && (
              <Text style={styles.disabledHelperText}>
                Task title cannot be changed when editing
              </Text>
            )}
          </View>

          {/* Task Description */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Description</Text>
            <TextInput
              style={[
                styles.textInput, 
                styles.textArea,
                isEditMode && styles.disabledInput
              ]}
              value={description}
              onChangeText={setDescription}
              placeholder="Enter task description (optional)"
              placeholderTextColor={DESIGN_SYSTEM.colors.onSurfaceVariant}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
              maxLength={500}
              editable={!isEditMode}
            />
            {isEditMode && (
              <Text style={styles.disabledHelperText}>
                Task description cannot be changed when editing
              </Text>
            )}
          </View>

          {/* Patient Selection */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Patient *</Text>
            
            {selectedPatient ? (
              <TouchableOpacity
                style={[
                  styles.selectedPatientContainer,
                  isEditMode && styles.disabledContainer
                ]}
                onPress={isEditMode ? undefined : () => setShowPatientSearch(true)}
                activeOpacity={isEditMode ? 1 : 0.7}
                disabled={isEditMode}
              >
                <View style={styles.selectedPatientInfo}>
                  <Text style={[
                    styles.selectedPatientName,
                    isEditMode && styles.disabledText
                  ]}>
                    {selectedPatient.patientName}
                  </Text>
                  <Text style={[
                    styles.selectedPatientDetails,
                    isEditMode && styles.disabledText
                  ]}>
                    MRN: {selectedPatient.mrn || 'N/A'}
                  </Text>
                </View>
                {!isEditMode && (
                  <Ionicons name="chevron-forward" size={20} color={DESIGN_SYSTEM.colors.onSurfaceVariant} />
                )}
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={[
                  styles.selectPatientButton,
                  isEditMode && styles.disabledContainer
                ]}
                onPress={isEditMode ? undefined : () => setShowPatientSearch(true)}
                activeOpacity={isEditMode ? 1 : 0.7}
                disabled={isEditMode}
              >
                <Ionicons 
                  name="person-add-outline" 
                  size={20} 
                  color={isEditMode ? DESIGN_SYSTEM.colors.onSurfaceVariant : DESIGN_SYSTEM.colors.primary} 
                />
                <Text style={[
                  styles.selectPatientText,
                  isEditMode && styles.disabledText
                ]}>
                  Select Patient
                </Text>
              </TouchableOpacity>
            )}
            
            {isEditMode && (
              <Text style={styles.disabledHelperText}>
                Patient assignment cannot be changed when editing
              </Text>
            )}
          </View>

          {/* Priority Selection */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Priority</Text>
            <TouchableOpacity
              style={styles.prioritySelector}
              onPress={() => setShowPriorityDropdown(!showPriorityDropdown)}
              activeOpacity={0.7}
            >
              <View style={styles.prioritySelectorContent}>
                <View style={[styles.priorityIndicator, { backgroundColor: getPriorityColor(priority) }]} />
                <Text style={styles.prioritySelectorText}>{priority}</Text>
              </View>
              <Ionicons
                name={showPriorityDropdown ? "chevron-up" : "chevron-down"}
                size={20}
                color={DESIGN_SYSTEM.colors.onSurfaceVariant}
              />
            </TouchableOpacity>
            
            {showPriorityDropdown && (
              <View style={styles.priorityDropdown}>
                {priorityOptions.map(renderPriorityItem)}
              </View>
            )}
          </View>

          {/* Date & Time Selection */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Schedule</Text>
            
            {/* Start Date & Time */}
            <View style={styles.dateTimeRow}>
              <Text style={styles.dateTimeLabel}>Start</Text>
              <View style={styles.dateTimeButtons}>
                <TouchableOpacity
                  style={styles.dateTimeButton}
                  onPress={() => {
                    console.log('Start date button pressed');
                    setShowStartDatePicker(true);
                  }}
                  activeOpacity={0.7}
                >
                  <Ionicons name="calendar-outline" size={18} color={DESIGN_SYSTEM.colors.primary} />
                  <Text style={styles.dateTimeButtonText}>
                    {moment(startDate).format('MMM D, YYYY')}
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={styles.dateTimeButton}
                  onPress={() => {
                    console.log('Start time button pressed');
                    setShowStartTimePicker(true);
                  }}
                  activeOpacity={0.7}
                >
                  <Ionicons name="time-outline" size={18} color={DESIGN_SYSTEM.colors.primary} />
                  <Text style={styles.dateTimeButtonText}>
                    {moment(startTime).format('h:mm A')}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* End Date & Time */}
            <View style={styles.dateTimeRow}>
              <Text style={styles.dateTimeLabel}>End</Text>
              <View style={styles.dateTimeButtons}>
                <TouchableOpacity
                  style={styles.dateTimeButton}
                  onPress={() => {
                    console.log('End date button pressed');
                    setShowEndDatePicker(true);
                  }}
                  activeOpacity={0.7}
                >
                  <Ionicons name="calendar-outline" size={18} color={DESIGN_SYSTEM.colors.primary} />
                  <Text style={styles.dateTimeButtonText}>
                    {moment(endDate).format('MMM D, YYYY')}
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={styles.dateTimeButton}
                  onPress={() => {
                    console.log('End time button pressed');
                    setShowEndTimePicker(true);
                  }}
                  activeOpacity={0.7}
                >
                  <Ionicons name="time-outline" size={18} color={DESIGN_SYSTEM.colors.primary} />
                  <Text style={styles.dateTimeButtonText}>
                    {moment(endTime).format('h:mm A')}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Bottom Actions - Moved inside ScrollView */}
          <View style={styles.bottomActions}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={onClose}
              disabled={isLoading}
              activeOpacity={0.7}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
              onPress={handleSubmit}
              disabled={isLoading}
              activeOpacity={0.7}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color={DESIGN_SYSTEM.colors.onPrimary} />
              ) : (
                <Text style={styles.saveButtonText}>
                  {isEditMode ? 'Update Task' : 'Create Task'}
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>

      {/* Patient Search Modal */}
      {showPatientSearch && (
        <View style={styles.modal}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Patient</Text>
              <TouchableOpacity
                onPress={() => {
                  setShowPatientSearch(false);
                  setSearchQuery('');
                }}
                activeOpacity={0.7}
              >
                <Ionicons name="close" size={24} color={DESIGN_SYSTEM.colors.onSurface} />
              </TouchableOpacity>
            </View>

            <View style={styles.searchContainer}>
              <Ionicons name="search" size={20} color={DESIGN_SYSTEM.colors.onSurfaceVariant} />
              <TextInput
                style={styles.searchInput}
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholder="Search patients by name, MRN, or phone..."
                placeholderTextColor={DESIGN_SYSTEM.colors.onSurfaceVariant}
                autoFocus
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setSearchQuery('')} activeOpacity={0.7}>
                  <Ionicons name="close-circle" size={20} color={DESIGN_SYSTEM.colors.onSurfaceVariant} />
                </TouchableOpacity>
              )}
            </View>

            {isFetchingPatients ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={DESIGN_SYSTEM.colors.primary} />
                <Text style={styles.loadingText}>Loading patients...</Text>
              </View>
            ) : (
              <FlatList
                data={filteredPatients}
                renderItem={renderPatientItem}
                keyExtractor={(item) => item.patientId.toString()}
                style={styles.patientsList}
                showsVerticalScrollIndicator={false}
                ItemSeparatorComponent={() => <View style={styles.separator} />}
                ListEmptyComponent={() => (
                  <View style={styles.emptyState}>
                    <Ionicons name="person-outline" size={48} color={DESIGN_SYSTEM.colors.onSurfaceVariant} />
                    <Text style={styles.emptyStateText}>
                      {searchQuery ? 'No patients found matching your search' : 'No patients available'}
                    </Text>
                  </View>
                )}
              />
            )}
          </View>
        </View>
      )}

      {/* Date/Time Pickers - iOS */}
      {Platform.OS === 'ios' ? (
        <>
          {showStartDatePicker && (
            <View style={styles.pickerModal}>
              <View style={styles.pickerContainer}>
                <View style={styles.pickerHeader}>
                  <TouchableOpacity onPress={handleIOSDateCancel}>
                    <Text style={styles.pickerButton}>Cancel</Text>
                  </TouchableOpacity>
                  <Text style={styles.pickerTitle}>Select Start Date</Text>
                  <TouchableOpacity onPress={() => handleIOSDateConfirm('startDate')}>
                    <Text style={[styles.pickerButton, styles.pickerButtonPrimary]}>Done</Text>
                  </TouchableOpacity>
                </View>
                <View style={styles.pickerContent}>
                  <DateTimePicker
                    value={startDate}
                    mode="date"
                    display="spinner"
                    onChange={(event, date) => {
                      console.log('iOS Start Date Changed:', date);
                      onDateTimeChange(event, date);
                    }}
                    minimumDate={isEditMode ? undefined : new Date()}
                    textColor="#1F2937"
                    accentColor="#0EA5E9"
                    themeVariant="light"
                  />
                </View>
              </View>
            </View>
          )}

          {showStartTimePicker && (
            <View style={styles.pickerModal}>
              <View style={styles.pickerContainer}>
                <View style={styles.pickerHeader}>
                  <TouchableOpacity onPress={handleIOSDateCancel}>
                    <Text style={styles.pickerButton}>Cancel</Text>
                  </TouchableOpacity>
                  <Text style={styles.pickerTitle}>Select Start Time</Text>
                  <TouchableOpacity onPress={() => handleIOSDateConfirm('startTime')}>
                    <Text style={[styles.pickerButton, styles.pickerButtonPrimary]}>Done</Text>
                  </TouchableOpacity>
                </View>
                <View style={styles.pickerContent}>
                  <DateTimePicker
                    value={startTime}
                    mode="time"
                    display="spinner"
                    onChange={(event, date) => {
                      console.log('iOS Start Time Changed:', date);
                      onDateTimeChange(event, date);
                    }}
                    minuteInterval={15}
                    textColor="#1F2937"
                    accentColor="#0EA5E9"
                    themeVariant="light"
                  />
                </View>
              </View>
            </View>
          )}

          {showEndDatePicker && (
            <View style={styles.pickerModal}>
              <View style={styles.pickerContainer}>
                <View style={styles.pickerHeader}>
                  <TouchableOpacity onPress={handleIOSDateCancel}>
                    <Text style={styles.pickerButton}>Cancel</Text>
                  </TouchableOpacity>
                  <Text style={styles.pickerTitle}>Select End Date</Text>
                  <TouchableOpacity onPress={() => handleIOSDateConfirm('endDate')}>
                    <Text style={[styles.pickerButton, styles.pickerButtonPrimary]}>Done</Text>
                  </TouchableOpacity>
                </View>
                <View style={styles.pickerContent}>
                  <DateTimePicker
                    value={endDate}
                    mode="date"
                    display="spinner"
                    onChange={(event, date) => {
                      console.log('iOS End Date Changed:', date);
                      onDateTimeChange(event, date);
                    }}
                    minimumDate={isEditMode ? undefined : new Date()}
                    textColor="#1F2937"
                    accentColor="#0EA5E9"
                    themeVariant="light"
                  />
                </View>
              </View>
            </View>
          )}

          {showEndTimePicker && (
            <View style={styles.pickerModal}>
              <View style={styles.pickerContainer}>
                <View style={styles.pickerHeader}>
                  <TouchableOpacity onPress={handleIOSDateCancel}>
                    <Text style={styles.pickerButton}>Cancel</Text>
                  </TouchableOpacity>
                  <Text style={styles.pickerTitle}>Select End Time</Text>
                  <TouchableOpacity onPress={() => handleIOSDateConfirm('endTime')}>
                    <Text style={[styles.pickerButton, styles.pickerButtonPrimary]}>Done</Text>
                  </TouchableOpacity>
                </View>
                <View style={styles.pickerContent}>
                  <DateTimePicker
                    value={endTime}
                    mode="time"
                    display="spinner"
                    onChange={(event, date) => {
                      console.log('iOS End Time Changed:', date);
                      onDateTimeChange(event, date);
                    }}
                    minuteInterval={15}
                    textColor="#1F2937"
                    accentColor="#0EA5E9"
                    themeVariant="light"
                  />
                </View>
              </View>
            </View>
          )}
        </>
      ) : (
        <>
          {showStartDatePicker && (
            <DateTimePicker
              value={startDate}
              mode="date"
              display="default"
              onChange={(event, date) => {
                console.log('Android Start Date Changed:', date);
                onDateTimeChange(event, date);
              }}
              minimumDate={isEditMode ? undefined : new Date()}
            />
          )}
          {showStartTimePicker && (
            <DateTimePicker
              value={startTime}
              mode="time"
              display="default"
              onChange={(event, date) => {
                console.log('Android Start Time Changed:', date);
                onDateTimeChange(event, date);
              }}
              minuteInterval={15}
            />
          )}
          {showEndDatePicker && (
            <DateTimePicker
              value={endDate}
              mode="date"
              display="default"
              onChange={(event, date) => {
                console.log('Android End Date Changed:', date);
                onDateTimeChange(event, date);
              }}
              minimumDate={isEditMode ? undefined : new Date()}
            />
          )}
          {showEndTimePicker && (
            <DateTimePicker
              value={endTime}
              mode="time"
              display="default"
              onChange={(event, date) => {
                console.log('Android End Time Changed:', date);
                onDateTimeChange(event, date);
              }}
              minuteInterval={15}
            />
          )}
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: DESIGN_SYSTEM.colors.surface,
  },
  headerSafeArea: {
    backgroundColor: DESIGN_SYSTEM.colors.surface,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: DESIGN_SYSTEM.spacing.lg,
    paddingVertical: DESIGN_SYSTEM.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: DESIGN_SYSTEM.colors.outline,
    backgroundColor: DESIGN_SYSTEM.colors.surface,
    ...DESIGN_SYSTEM.elevation.level1,
  },
  backButton: {
    padding: DESIGN_SYSTEM.spacing.sm,
    marginLeft: -DESIGN_SYSTEM.spacing.sm,
  },
  headerTitle: {
    fontSize: DESIGN_SYSTEM.typography.sizes.h5,
    fontWeight: DESIGN_SYSTEM.typography.weights.bold,
    color: DESIGN_SYSTEM.colors.onSurface,
    flex: 1,
    textAlign: 'center',
  },
  deleteButton: {
    padding: DESIGN_SYSTEM.spacing.sm,
  },
  mainContent: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: DESIGN_SYSTEM.spacing.lg,
  },
  scrollContent: {
    flexGrow: 1,
  },
  section: {
    marginTop: DESIGN_SYSTEM.spacing.xl,
  },
  sectionTitle: {
    fontSize: DESIGN_SYSTEM.typography.sizes.subtitle1,
    fontWeight: DESIGN_SYSTEM.typography.weights.semibold,
    color: DESIGN_SYSTEM.colors.onSurface,
    marginBottom: DESIGN_SYSTEM.spacing.md,
  },
  textInput: {
    borderWidth: 1,
    borderColor: DESIGN_SYSTEM.colors.outline,
    borderRadius: DESIGN_SYSTEM.borderRadius.md,
    padding: DESIGN_SYSTEM.spacing.lg,
    fontSize: DESIGN_SYSTEM.typography.sizes.body1,
    color: DESIGN_SYSTEM.colors.onSurface,
    backgroundColor: DESIGN_SYSTEM.colors.surface,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  selectedPatientContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: DESIGN_SYSTEM.colors.outline,
    borderRadius: DESIGN_SYSTEM.borderRadius.md,
    padding: DESIGN_SYSTEM.spacing.lg,
    backgroundColor: DESIGN_SYSTEM.colors.surface,
  },
  selectedPatientInfo: {
    flex: 1,
  },
  selectedPatientName: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body1,
    fontWeight: DESIGN_SYSTEM.typography.weights.semibold,
    color: DESIGN_SYSTEM.colors.onSurface,
    marginBottom: DESIGN_SYSTEM.spacing.xs,
  },
  selectedPatientDetails: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body2,
    color: DESIGN_SYSTEM.colors.onSurfaceVariant,
  },
  selectPatientButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: DESIGN_SYSTEM.colors.primary,
    borderStyle: 'dashed',
    borderRadius: DESIGN_SYSTEM.borderRadius.md,
    padding: DESIGN_SYSTEM.spacing.xl,
  },
  selectPatientText: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body1,
    fontWeight: DESIGN_SYSTEM.typography.weights.medium,
    color: DESIGN_SYSTEM.colors.primary,
    marginLeft: DESIGN_SYSTEM.spacing.sm,
  },
  prioritySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: DESIGN_SYSTEM.colors.outline,
    borderRadius: DESIGN_SYSTEM.borderRadius.md,
    padding: DESIGN_SYSTEM.spacing.lg,
    backgroundColor: DESIGN_SYSTEM.colors.surface,
  },
  prioritySelectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  prioritySelectorText: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body1,
    color: DESIGN_SYSTEM.colors.onSurface,
    marginLeft: DESIGN_SYSTEM.spacing.sm,
  },
  priorityDropdown: {
    borderWidth: 1,
    borderColor: DESIGN_SYSTEM.colors.outline,
    borderRadius: DESIGN_SYSTEM.borderRadius.md,
    backgroundColor: DESIGN_SYSTEM.colors.surface,
    marginTop: DESIGN_SYSTEM.spacing.sm,
    ...DESIGN_SYSTEM.elevation.level2,
  },
  priorityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: DESIGN_SYSTEM.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: DESIGN_SYSTEM.colors.outlineVariant,
  },
  priorityIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: DESIGN_SYSTEM.spacing.md,
  },
  priorityText: {
    flex: 1,
    fontSize: DESIGN_SYSTEM.typography.sizes.body1,
    color: DESIGN_SYSTEM.colors.onSurface,
  },
  priorityTextSelected: {
    fontWeight: DESIGN_SYSTEM.typography.weights.semibold,
    color: DESIGN_SYSTEM.colors.primary,
  },
  dateTimeRow: {
    marginBottom: DESIGN_SYSTEM.spacing.lg,
  },
  dateTimeLabel: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body2,
    fontWeight: DESIGN_SYSTEM.typography.weights.medium,
    color: DESIGN_SYSTEM.colors.onSurfaceVariant,
    marginBottom: DESIGN_SYSTEM.spacing.sm,
  },
  dateTimeButtons: {
    flexDirection: 'row',
    gap: DESIGN_SYSTEM.spacing.md,
  },
  dateTimeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: DESIGN_SYSTEM.colors.outline,
    borderRadius: DESIGN_SYSTEM.borderRadius.md,
    padding: DESIGN_SYSTEM.spacing.md,
    backgroundColor: DESIGN_SYSTEM.colors.surface,
  },
  dateTimeButtonText: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body2,
    color: DESIGN_SYSTEM.colors.onSurface,
    marginLeft: DESIGN_SYSTEM.spacing.sm,
    fontWeight: DESIGN_SYSTEM.typography.weights.medium,
  },
  bottomActions: {
    flexDirection: 'row',
    paddingHorizontal: DESIGN_SYSTEM.spacing.lg,
    paddingTop: DESIGN_SYSTEM.spacing.xl,
    paddingBottom: getBottomTabSafePadding() + 40, // Extra padding to ensure buttons are well above the tab bar and FAB
    gap: DESIGN_SYSTEM.spacing.md,
    marginTop: DESIGN_SYSTEM.spacing.lg,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: DESIGN_SYSTEM.spacing.lg,
    borderRadius: DESIGN_SYSTEM.borderRadius.md,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: DESIGN_SYSTEM.colors.outline,
  },
  cancelButtonText: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body1,
    fontWeight: DESIGN_SYSTEM.typography.weights.semibold,
    color: DESIGN_SYSTEM.colors.onSurface,
  },
  saveButton: {
    flex: 2,
    paddingVertical: DESIGN_SYSTEM.spacing.lg,
    borderRadius: DESIGN_SYSTEM.borderRadius.md,
    alignItems: 'center',
    backgroundColor: DESIGN_SYSTEM.colors.primary,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonText: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body1,
    fontWeight: DESIGN_SYSTEM.typography.weights.semibold,
    color: DESIGN_SYSTEM.colors.onPrimary,
  },
  modal: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: DESIGN_SYSTEM.colors.surface,
    borderRadius: DESIGN_SYSTEM.borderRadius.xl,
    width: '90%',
    maxHeight: '80%',
    ...DESIGN_SYSTEM.elevation.level3,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: DESIGN_SYSTEM.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: DESIGN_SYSTEM.colors.outline,
  },
  modalTitle: {
    fontSize: DESIGN_SYSTEM.typography.sizes.h6,
    fontWeight: DESIGN_SYSTEM.typography.weights.bold,
    color: DESIGN_SYSTEM.colors.onSurface,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: DESIGN_SYSTEM.spacing.lg,
    paddingHorizontal: DESIGN_SYSTEM.spacing.lg,
    paddingVertical: DESIGN_SYSTEM.spacing.md,
    borderWidth: 1,
    borderColor: DESIGN_SYSTEM.colors.outline,
    borderRadius: DESIGN_SYSTEM.borderRadius.md,
    backgroundColor: DESIGN_SYSTEM.colors.surfaceVariant,
  },
  searchInput: {
    flex: 1,
    fontSize: DESIGN_SYSTEM.typography.sizes.body1,
    color: DESIGN_SYSTEM.colors.onSurface,
    marginLeft: DESIGN_SYSTEM.spacing.sm,
  },
  patientsList: {
    maxHeight: 400,
  },
  patientItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: DESIGN_SYSTEM.spacing.lg,
  },
  patientInfo: {
    flex: 1,
  },
  patientName: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body1,
    fontWeight: DESIGN_SYSTEM.typography.weights.semibold,
    color: DESIGN_SYSTEM.colors.onSurface,
    marginBottom: DESIGN_SYSTEM.spacing.xs,
  },
  patientDetails: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body2,
    color: DESIGN_SYSTEM.colors.onSurfaceVariant,
    marginBottom: DESIGN_SYSTEM.spacing.xs,
  },
  patientAddress: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body2,
    color: DESIGN_SYSTEM.colors.onSurfaceSecondary,
  },
  separator: {
    height: 1,
    backgroundColor: DESIGN_SYSTEM.colors.outlineVariant,
    marginHorizontal: DESIGN_SYSTEM.spacing.lg,
  },
  loadingContainer: {
    padding: DESIGN_SYSTEM.spacing.xxxl,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body1,
    color: DESIGN_SYSTEM.colors.onSurfaceVariant,
    marginTop: DESIGN_SYSTEM.spacing.md,
  },
  emptyState: {
    padding: DESIGN_SYSTEM.spacing.xxxl,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body1,
    color: DESIGN_SYSTEM.colors.onSurfaceVariant,
    textAlign: 'center',
    marginTop: DESIGN_SYSTEM.spacing.md,
  },
  pickerModal: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'flex-end',
    zIndex: 9999,
  },
  pickerContainer: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: DESIGN_SYSTEM.borderRadius.xl,
    borderTopRightRadius: DESIGN_SYSTEM.borderRadius.xl,
    paddingBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  pickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: DESIGN_SYSTEM.spacing.lg,
    paddingVertical: DESIGN_SYSTEM.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    backgroundColor: '#F9FAFB',
    borderTopLeftRadius: DESIGN_SYSTEM.borderRadius.xl,
    borderTopRightRadius: DESIGN_SYSTEM.borderRadius.xl,
  },
  pickerButton: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body1,
    fontWeight: DESIGN_SYSTEM.typography.weights.semibold,
    color: '#6B7280',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  pickerButtonPrimary: {
    color: '#0EA5E9',
    fontWeight: DESIGN_SYSTEM.typography.weights.bold,
  },
  pickerTitle: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body1,
    fontWeight: DESIGN_SYSTEM.typography.weights.semibold,
    color: '#1F2937',
    textAlign: 'center',
  },
  pickerContent: {
    padding: DESIGN_SYSTEM.spacing.lg,
    backgroundColor: '#FFFFFF',
  },
  disabledInput: {
    backgroundColor: DESIGN_SYSTEM.colors.surfaceVariant,
  },
  disabledHelperText: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body2,
    color: DESIGN_SYSTEM.colors.onSurfaceSecondary,
    marginTop: DESIGN_SYSTEM.spacing.sm,
  },
  disabledContainer: {
    backgroundColor: DESIGN_SYSTEM.colors.surfaceVariant,
  },
  disabledText: {
    color: DESIGN_SYSTEM.colors.onSurfaceSecondary,
  },
});

export default TaskCreateEditScreen; 