import {
  FETCH_ALERTS_LIST,
  FETCH_ALERTS_LIST_FAILURE,
  FETCH_ALERTS_LIST_SUCCESS,
} from './type';

export const fetchAlertsList = (params: Object) => {
  return {
    type: FETCH_ALERTS_LIST,
    payload: params,
  };
};

export const fetchAlertsListSuccess = (data: Object) => {
  return {
    type: FETCH_ALERTS_LIST_SUCCESS,
    data,
  };
};

export const fetchAlertsListFailure = (msg: String) => {
  return {
    type: FETCH_ALERTS_LIST_FAILURE,
    msg,
  };
};
