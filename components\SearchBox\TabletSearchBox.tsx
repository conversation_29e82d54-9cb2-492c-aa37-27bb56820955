import React, { useState, useCallback, useRef, useMemo } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
  Modal,
  ScrollView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import Text from '@/components/Text';
import { useTabletTheme } from '@/hooks/useTabletTheme';
import { useOrientation } from '@/hooks/useOrientation';
import {
  isTablet,
  isLargeTablet,
  spacing,
  typography,
  iconSizes,
} from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';

export interface FilterOption {
  id: string;
  label: string;
  value: any;
  count?: number;
}

export interface FilterGroup {
  id: string;
  label: string;
  options: FilterOption[];
  multiSelect?: boolean;
  searchable?: boolean;
}

interface TabletSearchBoxProps {
  value?: string;
  onChangeText?: (text: string) => void;
  placeholder?: string;
  onSubmitEditing?: () => void;
  onFocus?: () => void;
  onBlur?: () => void;
  autoFocus?: boolean;
  style?: ViewStyle;
  
  // Enhanced filtering props
  showFilters?: boolean;
  filterGroups?: FilterGroup[];
  selectedFilters?: Record<string, any[]>;
  onFiltersChange?: (filters: Record<string, any[]>) => void;
  onClearFilters?: () => void;
  
  // Search suggestions
  suggestions?: string[];
  onSuggestionPress?: (suggestion: string) => void;
  showSuggestions?: boolean;
  
  // Advanced search
  showAdvancedSearch?: boolean;
  onAdvancedSearchToggle?: () => void;
  
  // Results info
  resultsCount?: number;
  totalCount?: number;
}

/**
 * Enhanced Search Box component optimized for tablet layouts
 * Includes advanced filtering, suggestions, and responsive design
 */
const TabletSearchBox: React.FC<TabletSearchBoxProps> = ({
  value = '',
  onChangeText,
  placeholder = 'Search patients...',
  onSubmitEditing,
  onFocus,
  onBlur,
  autoFocus = false,
  style,
  
  showFilters = false,
  filterGroups = [],
  selectedFilters = {},
  onFiltersChange,
  onClearFilters,
  
  suggestions = [],
  onSuggestionPress,
  showSuggestions = false,
  
  showAdvancedSearch = false,
  onAdvancedSearchToggle,
  
  resultsCount,
  totalCount,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [showSuggestionsModal, setShowSuggestionsModal] = useState(false);
  const inputRef = useRef<TextInput>(null);
  
  const { orientation } = useOrientation();
  const tabletTheme = useTabletTheme();

  // Handle input focus
  const handleFocus = useCallback(() => {
    setIsFocused(true);
    if (showSuggestions && suggestions.length > 0) {
      setShowSuggestionsModal(true);
    }
    onFocus?.();
  }, [onFocus, showSuggestions, suggestions.length]);

  // Handle input blur
  const handleBlur = useCallback(() => {
    setIsFocused(false);
    // Delay hiding suggestions to allow for suggestion selection
    setTimeout(() => {
      setShowSuggestionsModal(false);
    }, 200);
    onBlur?.();
  }, [onBlur]);

  // Handle text change
  const handleTextChange = useCallback((text: string) => {
    onChangeText?.(text);
    if (showSuggestions && text.length > 0) {
      setShowSuggestionsModal(true);
    } else {
      setShowSuggestionsModal(false);
    }
  }, [onChangeText, showSuggestions]);

  // Handle suggestion press
  const handleSuggestionPress = useCallback((suggestion: string) => {
    onSuggestionPress?.(suggestion);
    setShowSuggestionsModal(false);
    inputRef.current?.blur();
  }, [onSuggestionPress]);

  // Clear search
  const clearSearch = useCallback(() => {
    onChangeText?.('');
    inputRef.current?.focus();
  }, [onChangeText]);

  // Calculate active filters count
  const activeFiltersCount = useMemo(() => {
    return Object.values(selectedFilters).reduce((count, filters) => count + filters.length, 0);
  }, [selectedFilters]);

  // Dynamic styles based on tablet configuration
  const containerStyles = useMemo(() => ({
    ...styles.container,
    height: isTablet() ? tabletTheme.components.get('input').height : 48,
    paddingHorizontal: isTablet() ? spacing.lg : spacing.md,
    borderRadius: tabletTheme.borderRadius('medium'),
    ...tabletTheme.shadow('light'),
  }), [tabletTheme]);

  const inputStyles = useMemo(() => ({
    ...styles.input,
    fontSize: isTablet() ? tabletTheme.components.get('input').fontSize : typography.body.fontSize,
  }), [tabletTheme]);

  return (
    <View style={[styles.wrapper, style]}>
      {/* Main Search Container */}
      <View style={[containerStyles, isFocused && styles.containerFocused]}>
        {/* Search Icon */}
        <Ionicons
          name="search-outline"
          size={isTablet() ? iconSizes.lg : iconSizes.md}
          color={isFocused ? CoreColors.TurquoiseBlue : CoreColors.GrayBlue}
          style={styles.searchIcon}
        />

        {/* Text Input */}
        <TextInput
          ref={inputRef}
          value={value}
          onChangeText={handleTextChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onSubmitEditing={onSubmitEditing}
          placeholder={placeholder}
          placeholderTextColor={CoreColors.GrayBlue}
          style={inputStyles}
          returnKeyType="search"
          autoFocus={autoFocus}
          clearButtonMode="never"
        />

        {/* Clear Button */}
        {value.length > 0 && (
          <TouchableOpacity
            onPress={clearSearch}
            style={styles.clearButton}
            accessibilityLabel="Clear search"
          >
            <Ionicons
              name="close-circle"
              size={isTablet() ? iconSizes.md : iconSizes.sm}
              color={CoreColors.GrayBlue}
            />
          </TouchableOpacity>
        )}

        {/* Filter Button */}
        {showFilters && (
          <TouchableOpacity
            onPress={() => setShowFilterModal(true)}
            style={[
              styles.filterButton,
              activeFiltersCount > 0 && styles.filterButtonActive,
            ]}
            accessibilityLabel={`Filters ${activeFiltersCount > 0 ? `(${activeFiltersCount} active)` : ''}`}
          >
            <Ionicons
              name="filter"
              size={isTablet() ? iconSizes.md : iconSizes.sm}
              color={activeFiltersCount > 0 ? CoreColors.TurquoiseBlue : CoreColors.GrayBlue}
            />
            {activeFiltersCount > 0 && (
              <View style={styles.filterBadge}>
                <Text style={styles.filterBadgeText}>{activeFiltersCount}</Text>
              </View>
            )}
          </TouchableOpacity>
        )}

        {/* Advanced Search Toggle */}
        {showAdvancedSearch && (
          <TouchableOpacity
            onPress={onAdvancedSearchToggle}
            style={styles.advancedButton}
            accessibilityLabel="Advanced search"
          >
            <Ionicons
              name="options"
              size={isTablet() ? iconSizes.md : iconSizes.sm}
              color={CoreColors.GrayBlue}
            />
          </TouchableOpacity>
        )}
      </View>

      {/* Results Info */}
      {(resultsCount !== undefined || totalCount !== undefined) && (
        <View style={styles.resultsInfo}>
          <Text style={[
            styles.resultsText,
            { fontSize: tabletTheme.typography.get('caption').fontSize }
          ]}>
            {resultsCount !== undefined && totalCount !== undefined
              ? `Showing ${resultsCount} of ${totalCount} patients`
              : resultsCount !== undefined
              ? `${resultsCount} patients found`
              : `${totalCount} total patients`
            }
          </Text>
          
          {activeFiltersCount > 0 && (
            <TouchableOpacity
              onPress={onClearFilters}
              style={styles.clearFiltersButton}
            >
              <Text style={styles.clearFiltersText}>Clear filters</Text>
            </TouchableOpacity>
          )}
        </View>
      )}

      {/* Suggestions Modal */}
      <Modal
        visible={showSuggestionsModal && suggestions.length > 0}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowSuggestionsModal(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowSuggestionsModal(false)}
        >
          <View style={[
            styles.suggestionsContainer,
            {
              maxWidth: isTablet() ? 600 : '90%',
              borderRadius: tabletTheme.borderRadius('medium'),
            }
          ]}>
            <ScrollView style={styles.suggestionsList} keyboardShouldPersistTaps="handled">
              {suggestions.slice(0, 8).map((suggestion, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.suggestionItem}
                  onPress={() => handleSuggestionPress(suggestion)}
                >
                  <Ionicons
                    name="search"
                    size={iconSizes.sm}
                    color={CoreColors.GrayBlue}
                    style={styles.suggestionIcon}
                  />
                  <Text style={styles.suggestionText}>{suggestion}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Filter Modal */}
      <Modal
        visible={showFilterModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowFilterModal(false)}
      >
        <FilterModal
          filterGroups={filterGroups}
          selectedFilters={selectedFilters}
          onFiltersChange={onFiltersChange}
          onClose={() => setShowFilterModal(false)}
          onClearAll={onClearFilters}
        />
      </Modal>
    </View>
  );
};

/**
 * Filter Modal Component
 */
interface FilterModalProps {
  filterGroups: FilterGroup[];
  selectedFilters: Record<string, any[]>;
  onFiltersChange?: (filters: Record<string, any[]>) => void;
  onClose: () => void;
  onClearAll?: () => void;
}

const FilterModal: React.FC<FilterModalProps> = ({
  filterGroups,
  selectedFilters,
  onFiltersChange,
  onClose,
  onClearAll,
}) => {
  const tabletTheme = useTabletTheme();

  const handleFilterToggle = useCallback((groupId: string, optionValue: any, multiSelect: boolean = false) => {
    const currentFilters = { ...selectedFilters };
    const groupFilters = currentFilters[groupId] || [];

    if (multiSelect) {
      const index = groupFilters.indexOf(optionValue);
      if (index > -1) {
        groupFilters.splice(index, 1);
      } else {
        groupFilters.push(optionValue);
      }
    } else {
      currentFilters[groupId] = groupFilters.includes(optionValue) ? [] : [optionValue];
    }

    if (groupFilters.length === 0) {
      delete currentFilters[groupId];
    } else {
      currentFilters[groupId] = groupFilters;
    }

    onFiltersChange?.(currentFilters);
  }, [selectedFilters, onFiltersChange]);

  return (
    <View style={styles.filterModal}>
      {/* Header */}
      <View style={styles.filterHeader}>
        <Text style={[
          styles.filterTitle,
          { fontSize: tabletTheme.typography.get('h3').fontSize }
        ]}>
          Filters
        </Text>
        <View style={styles.filterHeaderActions}>
          {onClearAll && (
            <TouchableOpacity onPress={onClearAll} style={styles.clearAllButton}>
              <Text style={styles.clearAllText}>Clear All</Text>
            </TouchableOpacity>
          )}
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={iconSizes.lg} color={CoreColors.DarkJungleGreen} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Filter Groups */}
      <ScrollView style={styles.filterContent}>
        {filterGroups.map((group) => (
          <View key={group.id} style={styles.filterGroup}>
            <Text style={[
              styles.filterGroupTitle,
              { fontSize: tabletTheme.typography.get('h6').fontSize }
            ]}>
              {group.label}
            </Text>
            
            {group.options.map((option) => {
              const isSelected = selectedFilters[group.id]?.includes(option.value) || false;
              
              return (
                <TouchableOpacity
                  key={option.id}
                  style={[styles.filterOption, isSelected && styles.filterOptionSelected]}
                  onPress={() => handleFilterToggle(group.id, option.value, group.multiSelect)}
                >
                  <View style={styles.filterOptionContent}>
                    <Text style={[
                      styles.filterOptionText,
                      isSelected && styles.filterOptionTextSelected,
                    ]}>
                      {option.label}
                    </Text>
                    {option.count !== undefined && (
                      <Text style={styles.filterOptionCount}>({option.count})</Text>
                    )}
                  </View>
                  {isSelected && (
                    <Ionicons
                      name="checkmark"
                      size={iconSizes.sm}
                      color={CoreColors.TurquoiseBlue}
                    />
                  )}
                </TouchableOpacity>
              );
            })}
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    width: '100%',
  } as ViewStyle,

  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  } as ViewStyle,

  containerFocused: {
    borderColor: CoreColors.TurquoiseBlue,
    borderWidth: 2,
  } as ViewStyle,

  searchIcon: {
    marginRight: spacing.sm,
  },

  input: {
    flex: 1,
    color: CoreColors.DarkJungleGreen,
    paddingVertical: 0,
  } as TextStyle,

  clearButton: {
    padding: spacing.xs,
    marginLeft: spacing.xs,
  } as ViewStyle,

  filterButton: {
    padding: spacing.xs,
    marginLeft: spacing.sm,
    position: 'relative',
  } as ViewStyle,

  filterButtonActive: {
    backgroundColor: CoreColors.TurquoiseBlue + '15',
    borderRadius: 6,
  } as ViewStyle,

  filterBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: CoreColors.TurquoiseBlue,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  } as ViewStyle,

  filterBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  } as TextStyle,

  advancedButton: {
    padding: spacing.xs,
    marginLeft: spacing.sm,
  } as ViewStyle,

  resultsInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  } as ViewStyle,

  resultsText: {
    color: CoreColors.GrayBlue,
  } as TextStyle,

  clearFiltersButton: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  } as ViewStyle,

  clearFiltersText: {
    color: CoreColors.TurquoiseBlue,
    fontSize: typography.caption.fontSize,
    fontWeight: '600',
  } as TextStyle,

  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'flex-start',
    paddingTop: 100,
    paddingHorizontal: spacing.lg,
  } as ViewStyle,

  suggestionsContainer: {
    backgroundColor: '#FFFFFF',
    maxHeight: 300,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 12,
      },
      android: {
        elevation: 8,
      },
    }),
  } as ViewStyle,

  suggestionsList: {
    maxHeight: 300,
  } as ViewStyle,

  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F5F9',
  } as ViewStyle,

  suggestionIcon: {
    marginRight: spacing.sm,
  },

  suggestionText: {
    fontSize: typography.body.fontSize,
    color: CoreColors.DarkJungleGreen,
  } as TextStyle,

  // Filter modal styles
  filterModal: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  } as ViewStyle,

  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  } as ViewStyle,

  filterTitle: {
    fontWeight: '700',
    color: CoreColors.DarkJungleGreen,
  } as TextStyle,

  filterHeaderActions: {
    flexDirection: 'row',
    alignItems: 'center',
  } as ViewStyle,

  clearAllButton: {
    marginRight: spacing.lg,
  } as ViewStyle,

  clearAllText: {
    color: CoreColors.TurquoiseBlue,
    fontSize: typography.body.fontSize,
    fontWeight: '600',
  } as TextStyle,

  closeButton: {
    padding: spacing.xs,
  } as ViewStyle,

  filterContent: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  } as ViewStyle,

  filterGroup: {
    marginVertical: spacing.lg,
  } as ViewStyle,

  filterGroupTitle: {
    fontWeight: '600',
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.md,
  } as TextStyle,

  filterOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    borderRadius: 8,
    marginBottom: spacing.xs,
  } as ViewStyle,

  filterOptionSelected: {
    backgroundColor: CoreColors.TurquoiseBlue + '15',
  } as ViewStyle,

  filterOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  } as ViewStyle,

  filterOptionText: {
    fontSize: typography.body.fontSize,
    color: CoreColors.DarkJungleGreen,
    marginRight: spacing.sm,
  } as TextStyle,

  filterOptionTextSelected: {
    color: CoreColors.TurquoiseBlue,
    fontWeight: '600',
  } as TextStyle,

  filterOptionCount: {
    fontSize: typography.caption.fontSize,
    color: CoreColors.GrayBlue,
  } as TextStyle,
});

export default TabletSearchBox;