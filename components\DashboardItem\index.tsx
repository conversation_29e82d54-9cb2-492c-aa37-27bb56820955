import {useNavigation} from '@react-navigation/native';
import {useRouter} from 'expo-router';
import {useSelector} from 'react-redux';
import React, {memo, useCallback, useRef} from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
  Animated,
  Easing,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialCommunityIcons, FontAwesome5, Ionicons, FontAwesome, Feather, MaterialIcons, AntDesign } from "@expo/vector-icons";
import {Constants} from '@/constants';
import Theme from '@/constants/Theme';
import Text from '../Text';

interface ItemProps {
  id?: number;
  icon?: {
    family: string;
    name: string;
    size: number;
  };
  img?: any; // Keep for backward compatibility
  title: string;
  numberNew?: number;
  route?: string;
  style?: ViewStyle;
  color: string;
  patientId?: string; // Add patientId prop
}

export default memo((props: ItemProps) => {
  const {navigate} = useNavigation() as any;
  const router = useRouter();

  // Get the patient ID from Redux
  const patientId = useSelector((state: any) => state?.currentPatientIdReducer?.patientId);
  const patientName = useSelector((state: any) => state?.currentPatientNameReducer?.patientName);

  const onPress = useCallback(() => {
    if (props.route) {
      try {
        // Map old routes to new Expo Router paths - with proper routing
        const routeMap: Record<string, any> = {
          // Vitals Report
          'VitalRepors': { path: '/patient/vitals', useParams: false },
          'VitalReportItem': { path: '/patient/vitals/[id]', useTitle: true },

          // Medications
          'MedicationPage': { path: '/patient/medications', useParams: true },
          'AddMedication': { path: '/patient/medication/add', useParams: true },

          // GPS/Map
          'PatientMapView': { path: '/patient/map/[id]', generateId: true },

          // Alerts and Reminders
          'PatientAlerts': { path: '/patient/alerts', useParams: true },
          'CustomAlerts': { path: '/patient/custom-alerts', useParams: true },

          // CCM
          'CCMReports': { path: '/patient/ccm', useParams: true },
          'CarePlanDetails': { path: '/patient/care-plan/[id]', generateId: true },

          // Tasks
          'Consult': { path: '/patient/tasks', useParams: true },
          'EditTask': { path: '/patient/tasks/edit/[id]', generateId: true },

          // Communication
          'ChatDetail': { path: '/patient/chat/[id]', generateId: true },
          'PatientVideoCall': { path: '/patient/video-call/[id]', generateId: true },
          
          // New placeholder screens
          'patient-summary': { path: '/patient-summary', useParams: true },
          'patient-check-in': { path: '/patient-check-in', useParams: true },
          'patient-critical-alerts': { path: '/patient-critical-alerts', useParams: true },
        };

        // Get the patient ID from props first, then Redux
        let currentPatientId = props.patientId || patientId;

        // Don't navigate if we don't have a patient ID
        if (!currentPatientId) {
          return;
        }

        const routeInfo = routeMap[props.route];

        if (routeInfo) {
          // Declare finalPath outside try block for scope
          let finalPath = routeInfo.path;

          try {
            // Build params object for all routes
            const params: Record<string, string> = {
              patientId: currentPatientId as string
            };

            // For vitals, we need to pass the title as well
            if (routeInfo.useTitle) {
              // For vitals, use the item's ID as the route parameter
              finalPath = finalPath.replace('[id]', props.id?.toString() || '1');
              params.title = props.title;
            }
            // For other generateId routes, replace [id] with the patient ID
            else if (routeInfo.generateId) {
              finalPath = finalPath.replace('[id]', currentPatientId as string);
            }

            console.log('DashboardItem: Navigating to:', finalPath, 'Route:', props.route, 'Params:', params);

            if (routeInfo.useParams) {
              // Use router.push with params for routes that need query parameters
              router.push({
                pathname: finalPath as any,
                params: params
              });
            } else {
              // For vitals with title, pass the title as query param
              if (routeInfo.useTitle) {
                router.push({
                  pathname: finalPath as any,
                  params: {
                    title: props.title
                  }
                });
              } else {
                // For routes with ID in the path, just push the path
                router.push(finalPath as any);
              }
            }
          } catch (navigationError) {
            // Only use navigate as fallback if available
            if (navigate) {
              navigate(props?.route, {
                title: props.title,
                patientId: currentPatientId
              });
            }
          }
        } else {
          // Only use navigate as fallback if available
          if (navigate) {
            navigate(props?.route, {
              title: props.title,
              patientId: currentPatientId
            });
          }
        }
      } catch (error) {
        // Only use navigate as fallback if available
        if (navigate) {
          navigate(props?.route, {
            title: props.title,
            patientId: props.patientId || patientId
          });
        }
      }
    }
  }, [props.route, navigate, router, patientId, patientName, props.patientId]);

  // Animation values
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  // Animation configurations
  const handlePressIn = () => {
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 150,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic)
      }),
      Animated.timing(opacityAnim, {
        toValue: 0.9,
        duration: 150,
        useNativeDriver: true
      })
    ]).start();
  };

  const handlePressOut = () => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 5,
        tension: 40,
        useNativeDriver: true
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true
      })
    ]).start();
  };

  // Helper function to render the appropriate vector icon based on family
  const renderVectorIcon = (family: string, name: string, size: number) => {
    // Define a style for the icon container with proper TypeScript types
    const containerStyle: ViewStyle = {
      justifyContent: 'center' as 'center',
      alignItems: 'center' as 'center',
      width: '100%',
      height: '100%',
    };
    
    switch (family) {
      case 'MaterialCommunityIcons':
        return <MaterialCommunityIcons name={name as any} size={size} color="#FFFFFF" />;
      case 'FontAwesome5':
        return <FontAwesome5 name={name as any} size={size} color="#FFFFFF" />;
      case 'Ionicons':
        return <Ionicons name={name as any} size={size} color="#FFFFFF" />;
      case 'FontAwesome':
        return <FontAwesome name={name as any} size={size} color="#FFFFFF" />;
      case 'Feather':
        return <Feather name={name as any} size={size} color="#FFFFFF" />;
      case 'MaterialIcons':
        return <MaterialIcons name={name as any} size={size} color="#FFFFFF" />;
      case 'AntDesign':
        return <AntDesign name={name as any} size={size} color="#FFFFFF" />;
      default:
        return <Ionicons name="apps" size={size} color="#FFFFFF" />;
    }
  };

  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}>
      <Animated.View
        style={[
          styles.container,
          props.style,
          {
            transform: [{ scale: scaleAnim }],
            opacity: opacityAnim
          }
        ]}>
        <LinearGradient
          colors={[props.color, props.color]}
          style={styles.gradientContainer}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {/* Icon with shadow effect */}
          <View style={styles.iconContainer}>
            {props.icon ? (
              renderVectorIcon(props.icon.family, props.icon.name, props.icon.size || 32)
            ) : props.img ? (
              <Animated.Image
                source={props.img}
                style={[
                  styles.icon,
                  // Adjust icon size based on container width
                  props.style?.width && typeof props.style.width === 'number' && props.style.width < 150 ?
                    { width: 32, height: 32 } :
                    { width: 40, height: 40 }
                ]}
                resizeMode="contain"
              />
            ) : (
              <Ionicons name="apps" size={32} color="#FFFFFF" />
            )}
          </View>
          
          {/* Title with improved typography and alignment */}
          <Text
            marginTop={8}
            size={props.style?.width && typeof props.style.width === 'number' && props.style.width < 150 ? 15 : 18}
            lineHeight={20}
            center
            bold
            numberOfLines={2}
            style={{ 
              width: '100%', 
              color: '#FFFFFF', 
              textAlign: 'center', 
              fontWeight: '900',
              letterSpacing: -0.3
            }}
          >
            {props.title}
          </Text>
          
          {/* Badge for notifications */}
          {props.numberNew && props.numberNew > 0 ? (
            <View style={styles.badge}>
              <Text size={10} color="#FFFFFF" bold>
                {props.numberNew}
              </Text>
            </View>
          ) : null}
        </LinearGradient>
      </Animated.View>
    </TouchableOpacity>
  );
});

const styles = StyleSheet.create({
  container: {
    // Width and height will be controlled by parent component
    // for better responsiveness across different screen sizes
    borderRadius: 16,
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 5,
    overflow: 'hidden',
  },
  gradientContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
    ...Theme.center,
    paddingHorizontal: 12,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    width: 70,
    height: 70,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 2,
  },
  icon: {
    height: 40,
    width: 40,
    tintColor: '#FFFFFF',
  },
  vectorIcon: {
    alignSelf: 'center',
  },
  badge: {
    position: 'absolute',
    top: 8,
    right: 8,
    minWidth: 22,
    height: 22,
    borderRadius: 11,
    backgroundColor: '#FA4169',
    ...Theme.center,
    paddingHorizontal: 6,
    shadowColor: 'rgba(0, 0, 0, 0.3)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 3,
    borderWidth: 1.5,
    borderColor: 'rgba(255, 255, 255, 0.8)',
  },
});
