/**
 * Safely extracts a unique key from an item object for use in list rendering
 * Compatible with React Native's FlatList keyExtractor signature
 * 
 * @param item The item object that should contain an 'id' property
 * @param index The index of the item in the list (used as fallback)
 * @returns String representation of the key
 */
function keyExtractor(
  item: any,
  index: number
): string {
  // Handle null/undefined
  if (item === null || item === undefined) {
    return `item-${index}`;
  }

  // Check if item is an object
  if (typeof item !== 'object') {
    return `${item}-${index}`;
  }

  // Try to use 'id' property (most common case)
  if (item.id !== undefined && item.id !== null) {
    return String(item.id);
  }

  // Use _id if exists (common in MongoDB)
  if (item._id !== undefined) {
    return String(item._id);
  }
  
  // Use key if exists
  if (item.key !== undefined) {
    return String(item.key);
  }
  
  // Generate a key based on index as last resort
  return `item-${index}`;
}

export default keyExtractor;
