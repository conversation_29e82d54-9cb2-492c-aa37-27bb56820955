import React from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import PatientsScreen from '@/screens/Patients';

export default function PatientsRoute() {
  return (
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      <PatientsScreen />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFBFC',
    marginTop: 0,
    paddingTop: 0,
    paddingBottom: 0,
    position: 'relative', // Establish position context
    zIndex: 1, // Lower than header
  },
});