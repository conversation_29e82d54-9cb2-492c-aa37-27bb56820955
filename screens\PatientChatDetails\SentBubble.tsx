import Moment from 'moment';
import React, { memo, useEffect, useRef } from 'react';
import { StyleSheet, View, ViewStyle, TextStyle, Animated, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Text from '@/components/Text';
import { Colors } from '@/constants';

interface Props {
  question: string;
  timestamp: string;
  isLastMessage?: boolean;
  connectingDevice?: string;
}

const SentBubble = memo(({ question, timestamp, isLastMessage = false, connectingDevice = 'watch' }: Props) => {
  const slideAnim = useRef(new Animated.Value(50)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  
  const isMobileDevice = connectingDevice === 'mobile';

  useEffect(() => {
    Animated.parallel([
      Animated.spring(slideAnim, {
        toValue: 0,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const formatTimestamp = (timestamp: string) => {
    if (!timestamp || timestamp.trim() === '') {
      return '';
    }

    try {
      // Try multiple timestamp formats that might be used
      const formats = [
        'MM-DD-YYYY hh:mm:ss',
        'MM-DD-YYYY HH:mm:ss',
        'YYYY-MM-DD hh:mm:ss',
        'YYYY-MM-DD HH:mm:ss',
        'YYYY-MM-DD hh:mm A',
        'YYYY-MM-DD HH:mm A',
        'MM/DD/YYYY hh:mm:ss',
        'MM/DD/YYYY HH:mm:ss',
        'DD-MM-YYYY hh:mm:ss',
        'DD-MM-YYYY HH:mm:ss'
      ];

      let messageTime = Moment();
      let formatWorked = false;

      // Try each format until one works
      for (const format of formats) {
        const testTime = Moment(timestamp, format, true);
        if (testTime.isValid()) {
          messageTime = testTime;
          formatWorked = true;
          break;
        }
      }

      // If no specific format worked, try Moment's default parsing
      if (!formatWorked) {
        const testTime = Moment(timestamp);
        if (testTime.isValid()) {
          messageTime = testTime;
        } else {
          console.warn('Unable to parse timestamp:', timestamp);
          return 'Recently';
        }
      }

      const now = Moment();
      const today = now.clone().startOf('day');
      const yesterday = now.clone().subtract(1, 'day').startOf('day');

      if (messageTime.isSame(today, 'day')) {
        return messageTime.format('h:mm A');
      } else if (messageTime.isSame(yesterday, 'day')) {
        return `Yesterday ${messageTime.format('h:mm A')}`;
      } else if (messageTime.isAfter(now.clone().subtract(7, 'days'))) {
        return messageTime.format('ddd h:mm A');
      } else {
        return messageTime.format('MMM DD, h:mm A');
      }
    } catch (error) {
      console.warn('Error formatting timestamp:', timestamp, error);
      return 'Recently';
    }
  };

  return (
    <Animated.View 
      style={[
        styles.container,
        {
          transform: [
            { translateX: slideAnim },
            { scale: scaleAnim }
          ],
          opacity: opacityAnim,
        }
      ]}
    >
      <View style={styles.contentContainer}>
        <View style={[
          styles.bubbleContainer,
          Platform.OS === 'ios' && styles.bubbleContainerIOS,
          Platform.OS === 'android' && styles.bubbleContainerAndroid,
        ]}>
          <Text style={styles.messageText}>
            {question}
          </Text>
        </View>
        <Text style={styles.timestampText}>
          {formatTimestamp(timestamp)}
        </Text>
      </View>
    </Animated.View>
  );
});

export default SentBubble;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingLeft: 64,
    marginBottom: 16,
  },
  contentContainer: {
    maxWidth: '85%',
    alignItems: 'flex-end',
  },
  bubbleContainer: {
    backgroundColor: Colors.TealBlue,
    paddingHorizontal: 18,
    paddingVertical: 14,
    borderRadius: 22,
    borderBottomRightRadius: 8,
    minHeight: 46,
    justifyContent: 'center',
  },
  bubbleContainerIOS: {
    shadowColor: Colors.TealBlue,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
  },
  bubbleContainerAndroid: {
    elevation: 4,
  },
  messageText: {
    fontSize: 16,
    color: Colors.White,
    lineHeight: 22,
    fontWeight: '400',
  },
  timestampText: {
    fontSize: 11,
    color: Colors.GrayBlue,
    marginTop: 4,
    marginRight: 8,
    alignSelf: 'flex-end',
  },
  metadataContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    marginRight: 4,
  },
  statusContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusIcon: {
    opacity: 0.8,
  },
});
