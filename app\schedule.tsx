import React from 'react';
import { Stack } from 'expo-router';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import ScheduleScreen from '@/screens/Schedule';
import NavigationHeader from '@/components/NavigationHeader';

export default function ScheduleRoute() {
  return (
    <>
      <Stack.Screen 
        options={{ 
          title: 'Schedule',
          headerShown: false // Hide the default header since we're using our custom NavigationHeader
        }} 
      />
      <SafeAreaView style={styles.container} edges={['left', 'right']}>
        <NavigationHeader
          title="Schedule"
          showLogo={true}
          showLogout={true}
          showRightLogo={false}
        />
        <ScheduleScreen />
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(240, 240, 240, 1)',
    marginTop: 0,
    paddingTop: 0,
    paddingBottom: 0,
    position: 'relative',
    zIndex: 1,
  },
});
