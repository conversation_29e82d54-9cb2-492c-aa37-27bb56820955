import {
  StackActions,
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import React, { memo, useCallback, useState } from 'react';
import { 
  Alert, 
  Image, 
  ScrollView, 
  StyleSheet, 
  View,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useDispatch } from 'react-redux';
import { getStatusBarHeight } from 'react-native-iphone-x-helper';
import ButtonLinear from '@/components/Buttons/ButtonLinear';
import InputApp from '@/components/InputApp';
import Loader from '@/components/Loader/Loader';
import Text from '@/components/Text';
import { Colors, Routes } from '@/constants';
import { userLogOut } from '@/services/actions/logOutActions';
import { apiPost } from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import { resetCredentials } from '@/services/secure-storage';
import { isInternetAvailable } from '@/utils/internetCheck/connectivityCheck';
import passwordValidation from '@/utils/validation/PasswordValidation';

interface RecoveryPasswordProps {}

const PasswordExpire = memo((props: RecoveryPasswordProps) => {
  const [loader, setLoader] = useState(false);
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [visibleNewPassword, setVisibleNewPassword] = useState(false);
  const [visibleConfirmPassword, setVisibleConfirmPassword] = useState(false);
  const [newPasswordError, setNewPasswordError] = useState('');
  const [touched, setTouched] = useState(false);
  const [email, setEmail] = useState('');

  const navigation = useNavigation();
  const dispatch = useDispatch();
  const route = useRoute();

  const onShowHideNewPassword = useCallback(() => {
    setVisibleNewPassword(prev => !prev);
  }, []);

  const onShowHideConfirmPassword = useCallback(() => {
    setVisibleConfirmPassword(prev => !prev);
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      const obj = route?.params as any;
      setEmail(obj?.userName || '');
    }, [route?.params]),
  );

  const changepasswordApiCall = async () => {
    setLoader(true);

    let body = { username: email, password: newPassword };
    const response = await apiPost(
      URLS.authUrl + 'caregiver/expirePasswordReset', 
      body
    );
    
    if (response?.status == 200) {
      setLoader(false);
      if (response?.data?.responseMessage === 'Success') {
        resetCredentials();
        dispatch(userLogOut());
        navigation.dispatch(
          StackActions.replace(Routes.ChangePasswordSuccessful, {}),
        );
      } else {
        Alert.alert('Error', 'Change password failed, Please try again...', [
          { text: 'Dismiss' },
        ]);
      }
    } else {
      setLoader(false);
      const errorMessage = response?.response?.data?.responseMessage
        ? response?.response?.data?.responseMessage
        : response.message === 'Network Error'
          ? 'Network error. Please check your data connection.'
          : response.message;
      Alert.alert('Error', errorMessage, [{ text: 'Dismiss' }]);
    }
  };

  const isFormValid = () => {
    return (
      newPasswordError?.length === 0 &&
      newPassword?.length > 8 &&
      confirmPassword?.length > 0 &&
      confirmPassword === newPassword
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      <Loader modalVisible={loader} />
      
      {/* Background Gradient */}
      <LinearGradient
        colors={['#f8fffe', '#e8f8f7', '#ffffff']}
        style={styles.background}
      />

      <KeyboardAvoidingView 
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header Section */}
          <View style={styles.headerSection}>
            <View style={styles.iconContainer}>
              <View style={styles.lockIconWrapper}>
                <Image 
                  source={require('@/assets/images/ic_accept.png')} 
                  style={styles.lockIcon}
                  resizeMode="contain"
                />
              </View>
            </View>
            
            <View style={styles.titleContainer}>
              <Text style={styles.title}>Password Expired</Text>
              <Text style={styles.subtitle}>
                Your password has expired for security reasons. Please create a new secure password.
              </Text>
            </View>
          </View>

          {/* Form Section */}
          <View style={styles.formSection}>
            {/* Username Display */}
            <View style={styles.usernameSection}>
              <Text style={styles.usernameLabel}>Account</Text>
              <Text style={styles.usernameValue}>{email}</Text>
            </View>

            {/* New Password */}
            <InputApp
              title="New Password"
              value={newPassword}
              onChangeText={text => {
                setNewPassword(text);
                if (text) {
                  if (passwordValidation(text)) {
                    setNewPasswordError('');
                  } else {
                    setNewPasswordError(
                      'Password must be alphanumeric with min 8 - max 16 characters, 1 uppercase, 1 lowercase, 1 special character and no space.',
                    );
                  }
                } else {
                  setNewPasswordError('');
                }
              }}
              placeholder="Enter new password"
              marginTop={24}
              secureTextEntry={!visibleNewPassword}
              isShowIcon={true}
              borderColor={newPassword.length > 0 ? Colors.TiffanyBlue : Colors.InnearColor}
              icon={
                <Image
                  source={require('@/assets/images/ic_eye_on.png')}
                  style={styles.inputIcon}
                />
              }
              iconPress={onShowHideNewPassword}
              style={styles.input}
            />

            {newPasswordError !== '' && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{newPasswordError}</Text>
              </View>
            )}

            {/* Confirm Password */}
            <InputApp
              title="Confirm New Password"
              value={confirmPassword}
              onChangeText={text => {
                setConfirmPassword(text);
                setTouched(true);
              }}
              placeholder="Confirm new password"
              marginTop={24}
              secureTextEntry={!visibleConfirmPassword}
              isShowIcon={true}
              borderColor={confirmPassword.length > 0 ? Colors.TiffanyBlue : Colors.InnearColor}
              icon={
                <Image
                  source={require('@/assets/images/ic_eye_on.png')}
                  style={styles.inputIcon}
                />
              }
              iconPress={onShowHideConfirmPassword}
              style={styles.input}
            />

            {touched && confirmPassword !== newPassword && confirmPassword.length > 0 && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>Passwords do not match</Text>
              </View>
            )}

            {/* Submit Button */}
            <View style={styles.buttonContainer}>
              <ButtonLinear
                title="Update Password"
                onPress={async () => {
                  if (await isInternetAvailable()) {
                    changepasswordApiCall();
                  }
                }}
                disabled={!isFormValid()}
                style={styles.submitButton}
              />
            </View>

            {/* Security Note */}
            <View style={styles.securityNote}>
              <Text style={styles.securityText}>
                💡 Use a strong password with at least 8 characters including uppercase, lowercase, numbers, and special characters.
              </Text>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
});

export default PasswordExpire;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.White,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: getStatusBarHeight() + 20,
    paddingBottom: 40,
  },
  headerSection: {
    alignItems: 'center',
    marginBottom: 40,
  },
  iconContainer: {
    marginBottom: 30,
  },
  lockIconWrapper: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.PinkOrange + '20',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.PinkOrange,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  lockIcon: {
    width: 40,
    height: 40,
    tintColor: Colors.PinkOrange,
  },
  titleContainer: {
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.DarkJungleGreen,
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: Colors.GrayBlue,
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  formSection: {
    flex: 1,
  },
  usernameSection: {
    backgroundColor: Colors.Isabelline,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  usernameLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.GrayBlue,
    marginBottom: 4,
  },
  usernameValue: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.DarkJungleGreen,
  },
  input: {
    borderRadius: 12,
    backgroundColor: Colors.White,
    shadowColor: Colors.Black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  inputIcon: {
    width: 20,
    height: 20,
    tintColor: Colors.TiffanyBlue,
  },
  errorContainer: {
    marginTop: 8,
    paddingHorizontal: 12,
  },
  errorText: {
    fontSize: 12,
    color: Colors.RedNeonFuchsia,
    lineHeight: 16,
  },
  buttonContainer: {
    marginTop: 32,
  },
  submitButton: {
    height: 56,
    borderRadius: 16,
    shadowColor: Colors.TiffanyBlue,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  securityNote: {
    marginTop: 24,
    padding: 16,
    backgroundColor: Colors.TiffanyBlueOpacity,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: Colors.TiffanyBlue,
  },
  securityText: {
    fontSize: 14,
    color: Colors.DarkJungleGreen,
    lineHeight: 20,
  },
});
