import Layout from "@/components/Layout/Layout";
import Text from "@/components/Text";
import { Colors } from "@/constants";
import Theme from "@/constants/Theme";
import React, { memo, useCallback } from "react";
import {
  StyleSheet,
  TouchableOpacity,
  ViewProps,
  View,
  Animated,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface Props {
  tabs?: string[];
  style?: ViewProps;
  activeTintColor?: string;
  inactiveTintColor?: string;
  activeBackgroundColor?: string;
  inactiveBackgroundColor?: string;
  tabStyle?: ViewProps;
  onChangeTab?: (index: number) => void;
  initialTab?: number;
  value?: number; // <-- prop for controlled mode
  counts?: number[];
}

export default memo(
  ({
    tabs,
    onChangeTab,
    tabStyle,
    activeBackgroundColor = Colors.TealBlue,
    initialTab = 3,
    value,
    counts = [0, 0, 0, 0],
  }: Props) => {
    // Internal tab state (only used if not controlled)
    const [tabActive, setTabActive] = React.useState<number>(initialTab);

    // Use external value if provided (controlled component)
    const activeTab = value !== undefined ? value : tabActive;

    const _onChangeTab = useCallback(
      (index: number) => {
        if (value === undefined) {
          setTabActive(index); // Update internal state only if not controlled
        }
        onChangeTab?.(index);
      },
      [onChangeTab, value]
    );

    // Get icon for tab based on index
    const getTabIcon = (index: number) => {
      switch (index) {
        case 0: return "warning" as const; 
        case 1: return "notifications" as const;
        case 2: return "information-circle" as const;
        default: return "list" as const;
      }
    };

    // Get color for tab based on index
    const getTabColor = (index: number) => {
      switch (index) {
        case 0: return Colors.Red; // Changed Alarm tab to red to match alert color
        case 1: return Colors.pastelOrange; // Warning tab stays yellow/orange
        case 2: return Colors.Malachite;
        default: return activeBackgroundColor;
      }
    };

    return (
      <Layout style={styles.container}>
        <View style={styles.tabsWrapper}>
          {tabs?.map((item, index) => {
            const isActive = activeTab === index;
            const tabColor = getTabColor(index);
            const tabIcon = getTabIcon(index);
            const count = counts[index];

            // Determine styles based on active state
            const textColor = isActive ? tabColor : Colors.GrayBlue;
            
            return (
              <TouchableOpacity
                onPress={() => _onChangeTab(index)}
                key={index}
                style={[styles.tabStyle, tabStyle]}
                activeOpacity={0.7}
              >
                <View
                  style={[
                    styles.tabContent,
                    isActive && { borderBottomWidth: 3, borderBottomColor: tabColor }
                  ]}
                >
                  <Ionicons 
                    name={tabIcon} 
                    size={20} 
                    color={textColor}
                    style={styles.tabIcon}
                  />
                  <Text
                    color={textColor}
                    size={15}
                    bold
                  >
                    {item}
                  </Text>
                  
                  <View style={styles.badgeWrapper}>
                    {count > 0 && (
                      <View style={[styles.badgeContainer, { backgroundColor: tabColor }]}>
                        <Text size={10} color={Colors.White} bold>
                          {count > 99 ? '99+' : count}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
      </Layout>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    height: 56,
    ...Theme.flexDirection,
    justifyContent: "space-between",
    borderRadius: 12,
    marginHorizontal: 0,
    marginVertical: 0,
    backgroundColor: Colors.White,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    overflow: 'hidden',
  },
  tabsWrapper: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  tabStyle: {
    flex: 1,
    ...Theme.center,
  },
  tabContent: {
    flex: 1,
    width: "100%",
    ...Theme.center,
    flexDirection: 'row',
    paddingVertical: 16,
    position: 'relative',
    paddingHorizontal: 8,
  },
  tabIcon: {
    marginRight: 6,
  },
  badgeWrapper: {
    position: 'relative',
    marginLeft: 2,
  },
  badgeContainer: {
    position: 'absolute',
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    top: -23,
    right: -15,
    paddingHorizontal: 4,
  },
});
