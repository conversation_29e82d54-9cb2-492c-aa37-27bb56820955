import { Stack } from 'expo-router';

export default function AuthLayout() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="login" />
      <Stack.Screen name="signup" />
      <Stack.Screen name="forgot-password" />
      <Stack.Screen name="recovery-password" />
      <Stack.Screen name="change-password-successful" />
      <Stack.Screen name="verify-phone" />
      <Stack.Screen name="signup-successful" />
      <Stack.Screen name="password-expire" />
    </Stack>
  );
}