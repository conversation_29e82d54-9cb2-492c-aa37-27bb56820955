import React, { useState, useCallback, forwardRef } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
  KeyboardTypeOptions,
  Platform,
  TextInputProps,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import Text from '@/components/Text';
import { useTabletTheme } from '@/hooks/useTabletTheme';
import {
  isTablet,
  spacing,
  iconSizes,
} from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';

interface TabletTextInputProps extends Omit<TextInputProps, 'style'> {
  label?: string;
  value: string;
  onChangeText?: (text: string) => void;
  placeholder?: string;
  error?: string;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  
  // Icons
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onLeftIconPress?: () => void;
  onRightIconPress?: () => void;
  
  // Input types
  secureTextEntry?: boolean;
  keyboardType?: KeyboardTypeOptions;
  multiline?: boolean;
  numberOfLines?: number;
  
  // Styling
  style?: ViewStyle;
  inputStyle?: TextStyle;
  variant?: 'outlined' | 'filled';
  size?: 'small' | 'medium' | 'large';
  
  // Validation
  showValidation?: boolean;
  isValid?: boolean;
}

/**
 * Tablet-optimized TextInput component with enhanced features
 */
const TabletTextInput = forwardRef<TextInput, TabletTextInputProps>(({
  label,
  value,
  onChangeText,
  placeholder,
  error,
  helperText,
  required = false,
  disabled = false,
  
  leftIcon,
  rightIcon,
  onLeftIconPress,
  onRightIconPress,
  
  secureTextEntry = false,
  keyboardType = 'default',
  multiline = false,
  numberOfLines = 1,
  
  style,
  inputStyle,
  variant = 'outlined',
  size = 'medium',
  
  showValidation = true,
  isValid,
  
  ...textInputProps
}, ref) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(!secureTextEntry);
  const tabletTheme = useTabletTheme();

  // Handle focus events
  const handleFocus = useCallback((e: any) => {
    setIsFocused(true);
    textInputProps.onFocus?.(e);
  }, [textInputProps.onFocus]);

  const handleBlur = useCallback((e: any) => {
    setIsFocused(false);
    textInputProps.onBlur?.(e);
  }, [textInputProps.onBlur]);

  // Toggle password visibility
  const togglePasswordVisibility = useCallback(() => {
    setIsPasswordVisible(!isPasswordVisible);
  }, [isPasswordVisible]);

  // Get input dimensions based on size and device
  const getInputDimensions = useCallback(() => {
    const baseHeight = isTablet() ? 52 : 48;
    const sizeMultipliers = {
      small: 0.85,
      medium: 1,
      large: 1.2,
    };
    
    return {
      height: multiline ? Math.max(baseHeight * 2, baseHeight * numberOfLines) : baseHeight * sizeMultipliers[size],
      fontSize: isTablet() ? 
        (size === 'large' ? 18 : size === 'small' ? 14 : 16) :
        (size === 'large' ? 16 : size === 'small' ? 12 : 14),
      padding: isTablet() ? spacing.lg : spacing.md,
    };
  }, [size, multiline, numberOfLines]);

  const dimensions = getInputDimensions();

  // Determine validation state
  const hasError = error && error.length > 0;
  const validationState = showValidation ? (hasError ? 'error' : isValid === false ? 'error' : isValid === true ? 'success' : 'default') : 'default';

  // Get border color based on state
  const getBorderColor = useCallback(() => {
    if (disabled) return '#E5E7EB';
    if (hasError) return CoreColors.RedNeonFuchsia;
    if (isFocused) return CoreColors.TurquoiseBlue;
    if (isValid === true) return CoreColors.ForestGreen;
    return '#D1D5DB';
  }, [disabled, hasError, isFocused, isValid]);

  // Container styles
  const containerStyles: ViewStyle = {
    ...styles.container,
    ...style,
  };

  // Input container styles
  const inputContainerStyles: ViewStyle = {
    ...styles.inputContainer,
    height: dimensions.height,
    paddingHorizontal: dimensions.padding,
    borderColor: getBorderColor(),
    borderWidth: isFocused ? 2 : 1,
    backgroundColor: variant === 'filled' ? '#F9FAFB' : '#FFFFFF',
    borderRadius: tabletTheme.borderRadius('medium'),
    opacity: disabled ? 0.6 : 1,
    ...tabletTheme.shadow(isFocused ? 'medium' : 'light'),
  };

  // Input text styles
  const inputTextStyles: TextStyle = {
    ...styles.input,
    fontSize: dimensions.fontSize,
    color: disabled ? CoreColors.GrayBlue : CoreColors.DarkJungleGreen,
    lineHeight: multiline ? dimensions.fontSize * 1.4 : undefined,
    ...inputStyle,
  };

  return (
    <View style={containerStyles}>
      {/* Label */}
      {label && (
        <View style={styles.labelContainer}>
          <Text style={[
            styles.label,
            { fontSize: tabletTheme.typography.get('bodySmall').fontSize }
          ]}>
            {label}
            {required && <Text style={styles.required}> *</Text>}
          </Text>
        </View>
      )}

      {/* Input Container */}
      <View style={inputContainerStyles}>
        {/* Left Icon */}
        {leftIcon && (
          <TouchableOpacity
            style={styles.leftIconContainer}
            onPress={onLeftIconPress}
            disabled={!onLeftIconPress || disabled}
          >
            <Ionicons
              name={leftIcon}
              size={isTablet() ? iconSizes.md : iconSizes.sm}
              color={isFocused ? CoreColors.TurquoiseBlue : CoreColors.GrayBlue}
            />
          </TouchableOpacity>
        )}

        {/* Text Input */}
        <TextInput
          ref={ref}
          value={value}
          onChangeText={onChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          placeholderTextColor={CoreColors.GrayBlue}
          style={inputTextStyles}
          secureTextEntry={secureTextEntry && !isPasswordVisible}
          keyboardType={keyboardType}
          multiline={multiline}
          numberOfLines={multiline ? numberOfLines : 1}
          editable={!disabled}
          textAlignVertical={multiline ? 'top' : 'center'}
          {...textInputProps}
        />

        {/* Right Icon / Password Toggle */}
        {(rightIcon || secureTextEntry) && (
          <TouchableOpacity
            style={styles.rightIconContainer}
            onPress={secureTextEntry ? togglePasswordVisibility : onRightIconPress}
            disabled={(!onRightIconPress && !secureTextEntry) || disabled}
          >
            <Ionicons
              name={
                secureTextEntry 
                  ? (isPasswordVisible ? 'eye-off' : 'eye')
                  : rightIcon || 'chevron-down'
              }
              size={isTablet() ? iconSizes.md : iconSizes.sm}
              color={isFocused ? CoreColors.TurquoiseBlue : CoreColors.GrayBlue}
            />
          </TouchableOpacity>
        )}

        {/* Validation Icon */}
        {showValidation && validationState !== 'default' && (
          <View style={styles.validationIconContainer}>
            <Ionicons
              name={validationState === 'error' ? 'close-circle' : 'checkmark-circle'}
              size={iconSizes.sm}
              color={validationState === 'error' ? CoreColors.RedNeonFuchsia : CoreColors.ForestGreen}
            />
          </View>
        )}
      </View>

      {/* Helper Text / Error */}
      {(helperText || error) && (
        <View style={styles.helperContainer}>
          <Text style={[
            styles.helperText,
            hasError && styles.errorText,
            { fontSize: tabletTheme.typography.get('caption').fontSize }
          ]}>
            {error || helperText}
          </Text>
        </View>
      )}
    </View>
  );
});

TabletTextInput.displayName = 'TabletTextInput';

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.md,
  } as ViewStyle,

  labelContainer: {
    marginBottom: spacing.xs,
  } as ViewStyle,

  label: {
    fontWeight: '600',
    color: CoreColors.DarkJungleGreen,
  } as TextStyle,

  required: {
    color: CoreColors.RedNeonFuchsia,
    fontWeight: '600',
  } as TextStyle,

  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  } as ViewStyle,

  input: {
    flex: 1,
    paddingVertical: 0,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'Roboto',
    fontWeight: '400',
  } as TextStyle,

  leftIconContainer: {
    marginRight: spacing.sm,
    padding: spacing.xs,
  } as ViewStyle,

  rightIconContainer: {
    marginLeft: spacing.sm,
    padding: spacing.xs,
  } as ViewStyle,

  validationIconContainer: {
    marginLeft: spacing.xs,
  } as ViewStyle,

  helperContainer: {
    marginTop: spacing.xs,
    paddingHorizontal: spacing.xs,
  } as ViewStyle,

  helperText: {
    color: CoreColors.GrayBlue,
  } as TextStyle,

  errorText: {
    color: CoreColors.RedNeonFuchsia,
  } as TextStyle,
});

export default TabletTextInput;