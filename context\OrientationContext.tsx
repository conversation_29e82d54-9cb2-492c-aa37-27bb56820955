import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { Dimensions } from 'react-native';
import { getOrientation, getDeviceType } from '@/utils/responsive';

export type Orientation = 'portrait' | 'landscape';
export type DeviceType = 'phone' | 'tablet' | 'large-tablet' | 'extra-large-tablet';

interface OrientationState {
  orientation: Orientation;
  deviceType: DeviceType;
  screenWidth: number;
  screenHeight: number;
  isTransitioning: boolean;
  previousOrientation?: Orientation;
}

interface OrientationContextType extends OrientationState {
  setTransitioning: (transitioning: boolean) => void;
  preserveState: <T>(key: string, state: T) => void;
  getPreservedState: <T>(key: string) => T | undefined;
  clearPreservedState: (key: string) => void;
}

type OrientationAction = 
  | { type: 'ORIENTATION_CHANGE'; payload: { orientation: Orientation; screenWidth: number; screenHeight: number; deviceType: DeviceType } }
  | { type: 'SET_TRANSITIONING'; payload: boolean }
  | { type: 'PRESERVE_STATE'; payload: { key: string; state: any } }
  | { type: 'CLEAR_PRESERVED_STATE'; payload: string };

const initialState: OrientationState = {
  orientation: getOrientation(),
  deviceType: getDeviceType(),
  screenWidth: Dimensions.get('window').width,
  screenHeight: Dimensions.get('window').height,
  isTransitioning: false,
};

const orientationReducer = (state: OrientationState & { preservedStates?: Record<string, any> }, action: OrientationAction) => {
  switch (action.type) {
    case 'ORIENTATION_CHANGE':
      return {
        ...state,
        previousOrientation: state.orientation,
        orientation: action.payload.orientation,
        deviceType: action.payload.deviceType,
        screenWidth: action.payload.screenWidth,
        screenHeight: action.payload.screenHeight,
        isTransitioning: true,
      };
    
    case 'SET_TRANSITIONING':
      return {
        ...state,
        isTransitioning: action.payload,
      };
    
    case 'PRESERVE_STATE':
      return {
        ...state,
        preservedStates: {
          ...state.preservedStates,
          [action.payload.key]: action.payload.state,
        },
      };
    
    case 'CLEAR_PRESERVED_STATE':
      const newPreservedStates = { ...state.preservedStates };
      delete newPreservedStates[action.payload];
      return {
        ...state,
        preservedStates: newPreservedStates,
      };
    
    default:
      return state;
  }
};

const OrientationContext = createContext<OrientationContextType | undefined>(undefined);

interface OrientationProviderProps {
  children: ReactNode;
  transitionDuration?: number;
}

export const OrientationProvider: React.FC<OrientationProviderProps> = ({ 
  children, 
  transitionDuration = 300 
}) => {
  const [state, dispatch] = useReducer(orientationReducer, { 
    ...initialState, 
    preservedStates: {} 
  });

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      const newOrientation = window.width > window.height ? 'landscape' : 'portrait';
      const newDeviceType = getDeviceType();
      
      if (newOrientation !== state.orientation) {
        dispatch({
          type: 'ORIENTATION_CHANGE',
          payload: {
            orientation: newOrientation,
            screenWidth: window.width,
            screenHeight: window.height,
            deviceType: newDeviceType,
          },
        });

        // Auto-clear transitioning state after transition duration
        setTimeout(() => {
          dispatch({ type: 'SET_TRANSITIONING', payload: false });
        }, transitionDuration);
      }
    });

    return () => subscription?.remove?.();
  }, [state.orientation, transitionDuration]);

  const contextValue: OrientationContextType = {
    ...state,
    setTransitioning: (transitioning: boolean) => {
      dispatch({ type: 'SET_TRANSITIONING', payload: transitioning });
    },
    preserveState: <T>(key: string, stateToPreserve: T) => {
      dispatch({ type: 'PRESERVE_STATE', payload: { key, state: stateToPreserve } });
    },
    getPreservedState: <T>(key: string): T | undefined => {
      return (state as any).preservedStates?.[key];
    },
    clearPreservedState: (key: string) => {
      dispatch({ type: 'CLEAR_PRESERVED_STATE', payload: key });
    },
  };

  return (
    <OrientationContext.Provider value={contextValue}>
      {children}
    </OrientationContext.Provider>
  );
};

export const useOrientationContext = (): OrientationContextType => {
  const context = useContext(OrientationContext);
  if (!context) {
    throw new Error('useOrientationContext must be used within an OrientationProvider');
  }
  return context;
};

/**
 * Hook for preserving component state during orientation changes
 */
export const usePreservedState = <T>(key: string, initialValue: T): [T, (value: T) => void] => {
  const { preserveState, getPreservedState, clearPreservedState } = useOrientationContext();
  
  const preservedValue = getPreservedState<T>(key);
  const [state, setState] = React.useState<T>(preservedValue ?? initialValue);

  const setStateAndPreserve = (newValue: T) => {
    setState(newValue);
    preserveState(key, newValue);
  };

  // Clean up preserved state when component unmounts
  useEffect(() => {
    return () => {
      clearPreservedState(key);
    };
  }, [key, clearPreservedState]);

  return [state, setStateAndPreserve];
};

export default OrientationContext;