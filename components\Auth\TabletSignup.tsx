import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Image,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import { TabletAwareContainer, AdaptiveLayout } from '@/components/Layout';
import { AccessibleText, AccessibleButton } from '@/components/Accessibility';
import { TabletTextInput } from '@/components/Forms';
import { isTablet, isLargeTablet, getOrientation, spacing, typography } from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';
import { ONBOARDING_IMAGES } from '@/assets/images/OnBoardingImages';

interface SignupFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  organization: string;
  role: string;
  licenseNumber?: string;
}

interface TabletSignupProps {
  onSignup?: (data: SignupFormData) => void;
  onLogin?: () => void;
  loading?: boolean;
}

/**
 * Tablet-optimized Signup screen component
 */
const TabletSignup: React.FC<TabletSignupProps> = ({
  onSignup,
  onLogin,
  loading = false,
}) => {
  const [formData, setFormData] = useState<SignupFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    organization: '',
    role: '',
    licenseNumber: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const orientation = getOrientation();

  // Onboarding content for the image carousel
  const onboardingContent = [
    {
      id: 1,
      image: ONBOARDING_IMAGES.img1,
      title: 'Quality Care',
      description: 'Provide value-based care, improve patient outcomes and increase quality of care!',
    },
    {
      id: 2,
      image: ONBOARDING_IMAGES.img2,
      title: 'Virtual Care',
      description: 'With virtual Care and care team coordination, we reduce hospital admission!',
    },
    {
      id: 3,
      image: ONBOARDING_IMAGES.img3,
      title: 'Patient Engagement',
      description: 'Increase patient engagement with voice, video and text!',
    },
    {
      id: 4,
      image: ONBOARDING_IMAGES.img4,
      title: 'Independent Living',
      description: 'We help seniors stay healthy, live independently with dignity!',
    },
  ];

  React.useEffect(() => {
    // Auto-rotate images every 5 seconds
    const imageRotationInterval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        (prevIndex + 1) % onboardingContent.length
      );
    }, 5000);

    return () => clearInterval(imageRotationInterval);
  }, []);

  const updateFormData = (field: keyof SignupFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const isStep1Valid = () => {
    return formData.firstName && formData.lastName && formData.email && formData.phone;
  };

  const isStep2Valid = () => {
    return formData.password && formData.confirmPassword && formData.password === formData.confirmPassword;
  };

  const isStep3Valid = () => {
    return formData.organization && formData.role;
  };

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSignup = () => {
    if (isStep1Valid() && isStep2Valid() && isStep3Valid()) {
      onSignup?.(formData);
    }
  };

  const renderStepIndicator = () => (
    <View style={styles.stepIndicator}>
      {[1, 2, 3].map((step) => (
        <View key={step} style={styles.stepContainer}>
          <View
            style={[
              styles.stepCircle,
              currentStep >= step && styles.activeStepCircle,
            ]}
          >
            <AccessibleText
              variant="bodySmall"
              style={[
                styles.stepNumber,
                currentStep >= step && styles.activeStepNumber,
              ]}
            >
              {step}
            </AccessibleText>
          </View>
          {step < 3 && (
            <View
              style={[
                styles.stepLine,
                currentStep > step && styles.activeStepLine,
              ]}
            />
          )}
        </View>
      ))}
    </View>
  );

  const renderStep1 = () => (
    <View style={styles.stepContent}>
      <AccessibleText variant="h2" style={styles.stepTitle}>
        Personal Information
      </AccessibleText>
      <AccessibleText variant="body" style={styles.stepDescription}>
        Let's start with your basic information
      </AccessibleText>

      <View style={styles.inputRow}>
        <View style={styles.inputHalf}>
          <TabletTextInput
            label="First Name"
            value={formData.firstName}
            onChangeText={(value) => updateFormData('firstName', value)}
            placeholder="Enter first name"
            autoCapitalize="words"
            accessibilityLabel="First name input"
          />
        </View>
        <View style={styles.inputHalf}>
          <TabletTextInput
            label="Last Name"
            value={formData.lastName}
            onChangeText={(value) => updateFormData('lastName', value)}
            placeholder="Enter last name"
            autoCapitalize="words"
            accessibilityLabel="Last name input"
          />
        </View>
      </View>

      <TabletTextInput
        label="Email Address"
        value={formData.email}
        onChangeText={(value) => updateFormData('email', value)}
        placeholder="Enter your email"
        keyboardType="email-address"
        autoCapitalize="none"
        autoComplete="email"
        accessibilityLabel="Email address input"
        style={styles.input}
      />

      <TabletTextInput
        label="Phone Number"
        value={formData.phone}
        onChangeText={(value) => updateFormData('phone', value)}
        placeholder="Enter your phone number"
        keyboardType="phone-pad"
        autoComplete="tel"
        accessibilityLabel="Phone number input"
        style={styles.input}
      />
    </View>
  );

  const renderStep2 = () => (
    <View style={styles.stepContent}>
      <AccessibleText variant="h2" style={styles.stepTitle}>
        Create Password
      </AccessibleText>
      <AccessibleText variant="body" style={styles.stepDescription}>
        Choose a strong password for your account
      </AccessibleText>

      <TabletTextInput
        label="Password"
        value={formData.password}
        onChangeText={(value) => updateFormData('password', value)}
        placeholder="Enter your password"
        secureTextEntry={!showPassword}
        autoComplete="new-password"
        accessibilityLabel="Password input"
        style={styles.input}
        rightIcon="eye"
        onRightIconPress={() => setShowPassword(!showPassword)}
      />

      <TabletTextInput
        label="Confirm Password"
        value={formData.confirmPassword}
        onChangeText={(value) => updateFormData('confirmPassword', value)}
        placeholder="Confirm your password"
        secureTextEntry={!showConfirmPassword}
        autoComplete="new-password"
        accessibilityLabel="Confirm password input"
        style={styles.input}
        rightIcon="eye"
        onRightIconPress={() => setShowConfirmPassword(!showConfirmPassword)}
      />

      {formData.password && formData.confirmPassword && formData.password !== formData.confirmPassword && (
        <AccessibleText variant="bodySmall" style={styles.errorText}>
          Passwords do not match
        </AccessibleText>
      )}
    </View>
  );

  const renderStep3 = () => (
    <View style={styles.stepContent}>
      <AccessibleText variant="h2" style={styles.stepTitle}>
        Professional Information
      </AccessibleText>
      <AccessibleText variant="body" style={styles.stepDescription}>
        Tell us about your professional background
      </AccessibleText>

      <TabletTextInput
        label="Organization"
        value={formData.organization}
        onChangeText={(value) => updateFormData('organization', value)}
        placeholder="Enter your organization"
        autoCapitalize="words"
        accessibilityLabel="Organization input"
        style={styles.input}
      />

      <TabletTextInput
        label="Role/Title"
        value={formData.role}
        onChangeText={(value) => updateFormData('role', value)}
        placeholder="Enter your role or title"
        autoCapitalize="words"
        accessibilityLabel="Role input"
        style={styles.input}
      />

      <TabletTextInput
        label="License Number (Optional)"
        value={formData.licenseNumber}
        onChangeText={(value) => updateFormData('licenseNumber', value)}
        placeholder="Enter license number if applicable"
        accessibilityLabel="License number input"
        style={styles.input}
      />
    </View>
  );

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return renderStep1();
      case 2:
        return renderStep2();
      case 3:
        return renderStep3();
      default:
        return renderStep1();
    }
  };

  const getNextButtonTitle = () => {
    switch (currentStep) {
      case 1:
        return 'Continue';
      case 2:
        return 'Continue';
      case 3:
        return 'Create Account';
      default:
        return 'Continue';
    }
  };

  const isNextButtonDisabled = () => {
    switch (currentStep) {
      case 1:
        return !isStep1Valid();
      case 2:
        return !isStep2Valid();
      case 3:
        return !isStep3Valid();
      default:
        return true;
    }
  };

  // Desktop-like landscape layout for tablets
  const renderLandscapeLayout = () => (
    <View style={styles.landscapeContainer}>
      {/* Left Panel - Images and Content */}
      <View style={styles.leftPanel}>
        <View style={styles.imageSection}>
          <Image
            source={onboardingContent[currentImageIndex].image}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <View style={styles.imageOverlay}>
            <AccessibleText variant="h1" style={styles.heroTitle}>
              {onboardingContent[currentImageIndex].title}
            </AccessibleText>
            <AccessibleText variant="body" style={styles.heroDescription}>
              {onboardingContent[currentImageIndex].description}
            </AccessibleText>
          </View>
        </View>

        {/* Image indicators */}
        <View style={styles.indicators}>
          {onboardingContent.map((_, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.indicator,
                index === currentImageIndex && styles.activeIndicator,
              ]}
              onPress={() => setCurrentImageIndex(index)}
            />
          ))}
        </View>
      </View>

      {/* Right Panel - Signup Form */}
      <View style={styles.rightPanel}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardAvoid}
        >
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            <View style={styles.authSection}>
              <View style={styles.header}>
                <Image
                  source={require("@/assets/images/img_logo.png")}
                  style={styles.logo}
                  resizeMode="contain"
                />
                <AccessibleText variant="h1" style={styles.title}>
                  Create Account
                </AccessibleText>
                <AccessibleText variant="body" style={styles.subtitle}>
                  Join WatchRx Care Team
                </AccessibleText>
              </View>

              {renderStepIndicator()}

              <View style={styles.formContainer}>
                {renderStepContent()}

                <View style={styles.buttonContainer}>
                  {currentStep > 1 && (
                    <AccessibleButton
                      title="Back"
                      onPress={handleBack}
                      variant="outline"
                      style={styles.backButton}
                    />
                  )}
                  <AccessibleButton
                    title={getNextButtonTitle()}
                    onPress={currentStep === 3 ? handleSignup : handleNext}
                    loading={loading && currentStep === 3}
                    disabled={isNextButtonDisabled() || loading}
                    style={styles.nextButton}
                  />
                </View>

                <View style={styles.loginContainer}>
                  <AccessibleText variant="body" style={styles.loginText}>
                    Already have an account?{' '}
                  </AccessibleText>
                  <AccessibleButton
                    title="Sign In"
                    onPress={onLogin}
                    variant="text"
                    size="small"
                  />
                </View>
              </View>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </View>
    </View>
  );

  // Portrait layout (similar to original)
  const renderPortraitLayout = () => (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.keyboardAvoid}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.portraitContainer}>
          <View style={styles.header}>
            <AccessibleText variant="h1" style={styles.title}>
              Create Account
            </AccessibleText>
            <AccessibleText variant="body" style={styles.subtitle}>
              Join WatchRx Care Team
            </AccessibleText>
          </View>

          {renderStepIndicator()}

          <View style={styles.formContainer}>
            {renderStepContent()}

            <View style={styles.buttonContainer}>
              {currentStep > 1 && (
                <AccessibleButton
                  title="Back"
                  onPress={handleBack}
                  variant="outline"
                  style={styles.backButton}
                />
              )}
              <AccessibleButton
                title={getNextButtonTitle()}
                onPress={currentStep === 3 ? handleSignup : handleNext}
                loading={loading && currentStep === 3}
                disabled={isNextButtonDisabled() || loading}
                style={styles.nextButton}
              />
            </View>

            <View style={styles.loginContainer}>
              <AccessibleText variant="body" style={styles.loginText}>
                Already have an account?{' '}
              </AccessibleText>
              <AccessibleButton
                title="Sign In"
                onPress={onLogin}
                variant="text"
                size="small"
              />
            </View>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );

  return (
    <TabletAwareContainer style={styles.container}>
      <AdaptiveLayout
        phoneLayout={renderPortraitLayout}
        tabletPortraitLayout={renderPortraitLayout}
        tabletLandscapeLayout={renderLandscapeLayout}
        largeTabletLayout={renderLandscapeLayout}
      />
    </TabletAwareContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    minHeight: '100%',
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing.xl,
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  title: {
    color: CoreColors.DarkJungleGreen,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  subtitle: {
    color: CoreColors.SlateGray,
    textAlign: 'center',
  },
  stepIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.xl,
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeStepCircle: {
    backgroundColor: CoreColors.TurquoiseBlue,
  },
  stepNumber: {
    color: CoreColors.SlateGray,
    fontWeight: '600',
  },
  activeStepNumber: {
    color: '#FFFFFF',
  },
  stepLine: {
    width: 40,
    height: 2,
    backgroundColor: '#E5E7EB',
    marginHorizontal: spacing.sm,
  },
  activeStepLine: {
    backgroundColor: CoreColors.TurquoiseBlue,
  },
  formContainer: {
    width: '100%',
    maxWidth: isTablet() ? 600 : 400,
    backgroundColor: '#FFFFFF',
    borderRadius: isTablet() ? 16 : 12,
    padding: spacing.xl,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  stepContent: {
    marginBottom: spacing.xl,
  },
  stepTitle: {
    color: CoreColors.DarkJungleGreen,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  stepDescription: {
    color: CoreColors.SlateGray,
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  inputRow: {
    flexDirection: isTablet() ? 'row' : 'column',
    gap: spacing.md,
    marginBottom: spacing.lg,
  },
  inputHalf: {
    flex: 1,
  },
  input: {
    marginBottom: spacing.lg,
    backgroundColor: '#F9FAFB',
  },
  errorText: {
    color: CoreColors.RedOrange,
    textAlign: 'center',
    marginTop: spacing.sm,
  },
  buttonContainer: {
    flexDirection: isTablet() ? 'row' : 'column',
    gap: spacing.md,
    marginBottom: spacing.lg,
  },
  backButton: {
    flex: isTablet() ? 1 : 0,
  },
  nextButton: {
    flex: isTablet() ? 2 : 0,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  loginText: {
    color: CoreColors.SlateGray,
  },
});

export default TabletSignup;