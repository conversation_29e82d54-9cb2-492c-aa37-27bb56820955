import React, { useEffect } from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams } from 'expo-router';
import { Stack } from 'expo-router';
import { useSelector } from 'react-redux';

// Import the component using require to avoid default export issues
const CCMReportsScreen = require('@/screens/PatientDetails/CCMReports').default;

export default function CCMReportsRoute() {
  // Get the patient ID from the URL params
  const params = useLocalSearchParams();

  // Extract patientId from params and convert to string if needed
  const paramsObj = Object.fromEntries(
    Object.entries(params).map(([key, value]) => [key, value?.toString()])
  );

  const urlPatientId = paramsObj.patientId || '';

  // Get patient ID from Redux as fallback
  const reduxPatientId = useSelector(
    (state: any) => state?.currentPatientIdReducer?.patientId
  );

  // Use URL param ID first, then Redux ID
  const effectivePatientId = urlPatientId || reduxPatientId;

  // Log all sources of patient ID for debugging
  useEffect(() => {
    console.log('CCM Reports Route - Patient ID sources:', {
      urlPatientId,
      reduxPatientId,
      effectivePatientId,
      allParams: paramsObj
    });
  }, [urlPatientId, reduxPatientId, effectivePatientId, paramsObj]);

  return (
    <>
      <Stack.Screen options={{ title: 'CCM Reports' }} />
      <SafeAreaView style={styles.container} edges={['left', 'right']}>
        <CCMReportsScreen
          patientId={effectivePatientId}
          directPatientId={effectivePatientId} // Additional prop to ensure it gets through
        />
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});