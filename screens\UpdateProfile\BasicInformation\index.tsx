import {useNavigation} from '@react-navigation/native';
import React, {memo, useCallback, useState} from 'react';
import {Alert, Image, ScrollView, StyleSheet, View} from 'react-native';
import ButtonLinear from '@/components/Buttons/ButtonLinear';
import HeaderButton from '@/components/HeaderButton';
import InputApp from '@/components/InputApp';
import Container from '@/components/Layout/Container';
import Text from '@/components/Text';
import AvatarProfile from '@/components/UpdateProfile/BasicInformation/AvatarProfile';
import GenderItem from '@/components/UpdateProfile/BasicInformation/GenderItem';
import {Routes} from '@/constants';
import Theme from '@/constants/Theme';
import scale from '@/utils/scale';
interface BasicInformationProps {
  navigation?: any;
}

interface Gender {
  id: number;
  title: string;
  icon: any;
}

const genders: Gender[] = [
  {
    id: 1,
    title: 'Male',
    icon: require('@/assets/images/ic_male.png'),
  },
  {
    id: 0,
    title: 'Female',
    icon: require('@/assets/images/ic_female.png'),
  },
];

const BasicInformation = memo((props: BasicInformationProps) => {
  const [firstName, setFirstName] = useState('Martin');
  const [lastName, setLastName] = useState('Wallace');
  const [nationId, setNationId] = useState('**********');
  const [homeAddress, setHomeAddress] = useState('934 Miller Turnpike');
  const [birthday, setBirthday] = useState('02/12/1956');
  const [gender, setGender] = useState<Gender | {id: null; title: null; icon: null}>({
    id: null, 
    title: null, 
    icon: null
  });

  const navigation = useNavigation<any>();

  const onGotoWorkProfile = useCallback(() => {
    navigation.navigate(Routes.WorkProfile);
  }, [navigation]);
  const onUploadAvatar = useCallback(() => {
    // TODO: Implement avatar upload functionality
    Alert.alert('Upload Avatar', 'Avatar upload functionality will be implemented here');
  }, []);

  const onChangeBirthday = useCallback((text: string) => {
    setBirthday(text);
  }, []);
  const onGoToChangeAddress = useCallback(() => {
    navigation.navigate(Routes.SelectAddress, {
      onChangeAddress: (address: string) => setHomeAddress(address)
    });
  }, [navigation]);

  return (
    <Container style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={Theme.updateProfileContentScrollStyle}>
        <HeaderButton style={{position: 'relative'}} />

        <Text size={13} lineHeight={16} bold marginTop={32}>
          Step 1 of 3
        </Text>
        <Text size={24} lineHeight={28} bold marginTop={16}>
          Basic Information
        </Text>
        <Text size={13} lineHeight={22} marginTop={16}>
          Basic Your information will be share with our Medical Expert team who
          will verify your identity.
        </Text>
        <AvatarProfile onPress={onUploadAvatar} />
        <InputApp
          title={'First Name'}
          marginTop={38}
          value={firstName}
          onChangeText={setFirstName}
        />
        <InputApp
          title={'Last Name'}
          marginTop={24}
          value={lastName}
          onChangeText={setLastName}
        />
        <InputApp
          title={'National ID'}
          marginTop={24}
          value={nationId}
          onChangeText={setNationId}
        />
        <Text size={13} lineHeight={16} medium marginTop={24}>
          Gender
        </Text>
        <View style={styles.genders}>
          {genders.map((i, index) => {
            const onPress = () => {
              setGender(i);
            };
            return (
              <GenderItem
                {...i}
                isChoose={i.id == gender.id}
                isLastItem={index === genders.length - 1}
                onPress={onPress}
                key={i.id.toString()}
              />
            );
          })}
        </View>
        <InputApp
          title={'Home Address'}
          marginTop={38}
          value={homeAddress}
          iconLeft={
            <Image
              source={require('@/assets/images/ic_pin_map.png')}
              style={Theme.icons}
            />
          }
          isShowIconLeft
          editable={false}
          onPress={onGoToChangeAddress}
        />
        <InputApp
          title={'Birthday'}
          marginTop={24}
          value={birthday}
          onChangeText={setBirthday}
          iconLeft={
            <Image
              source={require('@/assets/images/ic_calendar.png')}
              style={Theme.icons}
            />
          }
          isShowIconLeft
        />
        <ButtonLinear
          white
          title={'Continue'}
          children={
            <Image
              source={require('@/assets/images/ic_next.png')}
              style={styles.buttonChildren}
            />
          }
          disabled={
            !firstName?.trim() ||
            !lastName?.trim() ||
            !nationId?.trim() ||
            !homeAddress?.trim() ||
            !birthday?.trim() ||
            gender.id === null
          }
          onPress={onGotoWorkProfile}
          style={styles.buttonLinear}
        />
      </ScrollView>
    </Container>
  );
});

export default BasicInformation;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  buttonChildren: {
    ...Theme.icons,
    marginLeft: 8,
  },
  inputApp: {
    marginTop: 24,
  },
  firstName: {
    marginTop: scale(38),
  },
  homeAddress: {
    marginTop: 32,
  },
  buttonLinear: {
    marginTop: 52,
  },
  genders: {
    marginTop: 24,
    ...Theme.flexRow,
    ...Theme.center,
  },
});
