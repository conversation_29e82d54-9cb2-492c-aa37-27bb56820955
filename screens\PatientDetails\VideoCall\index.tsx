import React, { useState, useEffect } from 'react';
import { Text, View, StyleSheet, Platform, Button, Linking, ActivityIndicator } from 'react-native';
import Constants from 'expo-constants';
import { useLocalSearchParams } from 'expo-router';
import { getZoomAccessToken, ZoomTokenResponse, ApiResponse } from '@/services/apis/apiManager';
import ZoomProviderWrapper from '@/components/ZoomProviderWrapper';
import { useSelector } from 'react-redux';

// Define the props interface for ZoomVideoCall component
interface ZoomVideoCallProps {
  patientId?: number;
  patientName?: string;
  accessToken?: string;
  meetingNumber?: string;
  password?: string;
  userName?: string;
  onCallEnd?: () => void;
  program?: string;
  onProgramChange?: (program: string) => void;
  programOptions?: Array<{programName: string, programId: string, mins: number, programActivated: boolean}>;
}

// Dynamically import ZoomVideoCall to avoid errors in Expo Go
// This prevents the native module error when running in Expo Go
let ZoomVideoCall: React.ComponentType<ZoomVideoCallProps> | null = null;

// Only try to import in a development build
if (Constants.executionEnvironment !== 'storeClient') {
  try {
    // Use require instead of import to make it conditional
    ZoomVideoCall = require('./ZoomVideoCall').default;
    console.log('Successfully imported Zoom Video components');
  } catch (error) {
    console.error('Failed to import Zoom Video components:', error);
  }
}

/**
 * VideoCall component that uses the ZoomVideoCall implementation
 * This implementation uses @zoom/react-native-videosdk for reliable video calls
 * It requires a development build to work properly
 */
const VideoCall = () => {
  console.log('VideoCall: Rendering VideoCall component');
  const { id } = useLocalSearchParams();
  console.log('VideoCall: Received patient ID from params:', id);
  
  // Convert id to a proper format (string or number)
  const patientId = Array.isArray(id) ? id[0] : id;
  
  // Get patient data from Redux store
  const patientsList = useSelector((state: any) => state?.patientsListReducer?.data);
  const [patientData, setPatientData] = useState<any>(null);
  const [loading, setLoading] = useState(true); // Initialize as true while we fetch data

  // Effect to find patient data from the patients list
  useEffect(() => {
    console.log('VideoCall: Searching for patient in list:', {
      patientsList: patientsList?.length,
      searchingForId: patientId,
      searchingForIdType: typeof patientId
    });
    
    if (patientsList && patientsList.length > 0 && patientId) {
      // Convert both IDs to strings for comparison
      const searchId = String(patientId);
      const patient = patientsList.find((p: any) => String(p.patientId) === searchId);
      
      if (patient) {
        console.log('VideoCall: Found patient data:', patient);
        setPatientData(patient);
      } else {
        console.log('VideoCall: No patient found with ID:', searchId);
        console.log('VideoCall: Available patient IDs:', patientsList.map((p: any) => ({
          id: p.patientId,
          type: typeof p.patientId
        })));
      }
    } else {
      console.log('VideoCall: Cannot search for patient:', {
        hasPatientsList: Boolean(patientsList),
        patientsListLength: patientsList?.length,
        hasPatientId: Boolean(patientId)
      });
    }
    setLoading(false);
  }, [patientsList, patientId]);
  const [error, setError] = useState<string | null>(null);
  const [zoomData, setZoomData] = useState<ZoomTokenResponse | null>(null);
  const [selectedProgram, setSelectedProgram] = useState<string>('');
  const [programOptions, setProgramOptions] = useState<Array<{programName: string, programId: string, mins: number, programActivated: boolean}>>([]);

  console.log('VideoCall: Initial state:', {
    patientId,
    patientData,
    loading,
    error,
    zoomData,
    selectedProgram,
    programOptions
  });
  // Extract program data from patient details
  useEffect(() => {
    console.log('VideoCall: patientData updated:', patientData);
    if (patientData && patientData.programs) {
      console.log('VideoCall: Found patient programs:', patientData.programs);
      // Debug log to check program structure
      if (patientData.programs.selectedPrograms) {
        console.log('VideoCall: Selected programs:', patientData.programs.selectedPrograms);
      }
      if (patientData.programs.availablePrograms) {
        console.log('VideoCall: Available programs:', patientData.programs.availablePrograms);
      }
      
      // If patient has selected programs, show only those
      if (patientData.programs.selectedPrograms?.length > 0) {
        console.log('VideoCall: Using selected programs:', patientData.programs.selectedPrograms);
        setProgramOptions(patientData.programs.selectedPrograms);
      }
      // Otherwise show all available programs
      else if (patientData.programs.availablePrograms?.length > 0) {
        console.log('VideoCall: No selected programs, using available programs:', patientData.programs.availablePrograms);
        setProgramOptions(patientData.programs.availablePrograms);
      }
      // If no programs at all, show empty array
      else {
        console.log('VideoCall: No programs found');
        setProgramOptions([]);
      }
    } else {
      console.log('VideoCall: No patient data or programs found:', { patientData });
      setProgramOptions([]);
    }
  }, [patientData]);

  // Function to fetch Zoom meeting details
  const fetchZoomData = async (program: string) => {
    if (!patientId) {
      setError('No patient ID provided');
      return;
    }

    try {
      console.log('VideoCall: Fetching Zoom access token for patient ID:', patientId, 'with program:', program);
      const response: ApiResponse<ZoomTokenResponse> = await getZoomAccessToken(patientId, 2, program);
      
      if (response.error || !response.data) {
        const errorMsg = response.error?.message || 'Failed to get Zoom meeting details';
        console.error('VideoCall: Error fetching Zoom data:', errorMsg);
        setError(errorMsg);
        return;
      }
      
      // Validate the response data
      const { accessToken, meetingNumber, password, userName } = response.data;
      
      if (!accessToken) {
        console.error('VideoCall: Missing access token in response');
        setError('Missing access token for video call');
        return;
      }
      
      if (!meetingNumber) {
        console.error('VideoCall: Missing meeting number in response');
        setError('Missing meeting number for video call');
        return;
      }
      
      console.log('VideoCall: Successfully retrieved Zoom meeting details');
      setZoomData(response.data);
      // We don't need to do anything else here - the ZoomVideoCall will start the call automatically
      // when it receives the new props
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('VideoCall: Exception fetching Zoom data:', errorMsg);
      setError(errorMsg);
    }
  };

  // Check if ZoomVideoCall was successfully imported
  if (!ZoomVideoCall) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>
          Zoom Video module could not be loaded. Please ensure you're running in a development build.
        </Text>
        <View style={styles.buttonContainer}>
          <Button
            title="Learn more"
            onPress={() => Linking.openURL('https://docs.expo.dev/development/introduction/')}
          />
        </View>
      </View>
    );
  }

  // Only show loading state while fetching initial patient data
  if (loading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.statusText}>Loading patient data...</Text>
      </View>
    );
  }

  
  // Show error state if there was an error fetching Zoom data
  if (error) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>
          {error || 'Failed to get video call details. Please try again.'}
        </Text>
        <View style={styles.buttonContainer}>
          <Button
            title="Try Again"
            onPress={() => {
              setError(null);
              setLoading(false);
              setZoomData(null);
            }}
          />
        </View>
      </View>
    );
  }



  try {
    // Render the ZoomVideoCall component
    return (
      <ZoomProviderWrapper>
        <ZoomVideoCall 
          patientId={patientId ? Number(patientId) : 0}
          patientName={patientData?.patientName || `Patient ${patientId}`}
          accessToken={zoomData?.accessToken || ""}
          meetingNumber={zoomData?.meetingNumber || ""}
          password={zoomData?.password || ""}
          userName={zoomData?.userName || "Care Manager"}
          program={selectedProgram}
          programOptions={programOptions}
          onProgramChange={(program: string) => {
            console.log('VideoCall: Call button clicked with program:', program);
            setSelectedProgram(program);
            // Now make the API call since the call button was clicked
            fetchZoomData(program);
          }}
          onCallEnd={() => {
            console.log('VideoCall: Call ended');
            // Reset state when call ends
            setZoomData(null);
            setSelectedProgram('');
          }}
        />
      </ZoomProviderWrapper>
    );
  } catch (error) {
    console.error('VideoCall: Error rendering component:', error);
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>
          Error loading video call component. Please try again.
        </Text>
      </View>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: 'white',
  },
  warningText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
    textAlign: 'center',
    marginBottom: 10,
  },
  warningSubtext: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
  },
  buttonContainer: {
    marginVertical: 15,
    width: '100%',
  },
  commandContainer: {
    backgroundColor: '#f0f0f0',
    padding: 15,
    borderRadius: 8,
    marginTop: 20,
    width: '100%',
  },
  commandText: {
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    fontSize: 14,
    color: '#333',
    marginVertical: 5,
  },
  errorText: {
    fontSize: 16,
    color: 'red',
    textAlign: 'center',
    marginBottom: 20,
  },
  statusText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 15,
  },
});

export default VideoCall;
