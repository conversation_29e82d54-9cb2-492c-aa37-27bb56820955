/**
 * Validates an email address using a regular expression that follows RFC 5322 standards
 * 
 * @param email The email address to validate
 * @returns boolean - true if the email is valid, false otherwise
 */
function validateEmail(email: string | null | undefined): boolean {
  // Handle null, undefined, or empty strings
  if (!email) {
    return false;
  }

  // Trim whitespace
  const trimmedEmail = email.trim();
  if (trimmedEmail.length === 0) {
    return false;
  }

  // RFC 5322 compliant regex for email validation
  const emailRegex = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  
  return emailRegex.test(trimmedEmail);
}

export default validateEmail;
