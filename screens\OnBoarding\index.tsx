import { useRouter } from 'expo-router';
import React, {memo, useCallback} from 'react';
import {StyleSheet, View, Platform, StatusBar, Dimensions} from 'react-native';
import Animated, { useSharedValue, useAnimatedScrollHandler } from 'react-native-reanimated';
import ButtonText from '@/components/Buttons/ButtonText';
import LinearColors from '@/components/LinearColors';
import DotProgress from '@/components/OnBoarding/DotProgress';
import OnboardingPage from '@/components/OnBoarding/OnBoardingPage';
import {Colors, Constants} from '@/constants';
import Theme from '@/constants/Theme';
import { ONBOARDING_IMAGES } from '@/assets/images/OnBoardingImages';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

// Replacement for react-native-iphone-x-helper's getBottomSpace
const getBottomSpace = () => {
  return Platform.OS === 'ios' ? 34 : 20;
};

interface OnBoardingProps {}

const onboardingPages = [
  {
    id: 1,
    image: ONBOARDING_IMAGES.img1,
    title: 'Quality Care',
    desc: 'Provide value-based care, improve patient outcomes and increase quality of care!',
  },
  {
    id: 2,
    image: ONBOARDING_IMAGES.img2,
    title: 'Virtual Care',
    desc: 'With virtual Care and care team coordination, we reduce hospital admission!',
  },
  {
    id: 3,
    image: ONBOARDING_IMAGES.img3,
    title: 'Patient Engagement',
    desc: 'Increase patient engagement with voice, video and text!',
  },
  {
    id: 4,
    image: ONBOARDING_IMAGES.img4,
    title: 'Independent Living',
    desc: 'We help seniors stay healthy, live independently with dignity!',
  },
];

const OnBoarding = memo((_props: OnBoardingProps) => {
  const scrollX = useSharedValue(0);
  const router = useRouter();

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollX.value = event.contentOffset.x;
    },
  });

  const onLogin = useCallback(() => {
    router.replace('/auth/login');
  }, [router]);

  const onSignUp = useCallback(() => {
    router.replace('/auth/signup');
  }, [router]);
  
  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.TealBlue} />
      <LinearColors
        style={StyleSheet.absoluteFillObject}
        colors={[Colors.TealBlue, Colors.TurquoiseBlue]}>
        
        <View style={styles.content}>
          {/* Large Professional Image Section at Top */}
          <View style={styles.imageSection}>
            <Animated.ScrollView
              horizontal
              pagingEnabled
              snapToInterval={Constants.width}
              decelerationRate="fast"
              showsHorizontalScrollIndicator={false}
              bounces={false}
              scrollEventThrottle={16}
              onScroll={scrollHandler}
              style={styles.scrollView}>
              {onboardingPages.map((i, index) => (
                <OnboardingPage
                  {...i}
                  key={i.id.toString()}
                  isFirstItem={index === 0}
                  isLastItem={index === onboardingPages.length - 1}
                />
              ))}
            </Animated.ScrollView>
          </View>
          
          {/* Dot Progress Indicator */}
          <View style={styles.progressSection}>
            <DotProgress numberOfDots={onboardingPages.length} scrollX={scrollX} />
          </View>
          
          {/* Login/Signup Buttons at Bottom */}
          <View style={styles.buttonSection}>
            <View style={styles.buttonContainer}>
              <ButtonText
                title={'Log in'}
                style={styles.loginButton}
                titleColor={Colors.TealBlue}
                textProps={{
                  style: {
                    fontFamily: 'DMSans-Bold',
                    fontSize: 16,
                    letterSpacing: -0.2,
                  }
                }}
                onPress={onLogin}
              />
              <ButtonText
                title={'Sign Up'}
                style={styles.signUpButton}
                titleColor={Colors.White}
                textProps={{
                  style: {
                    fontFamily: 'DMSans-Bold',
                    fontSize: 16,
                    letterSpacing: -0.2,
                  }
                }}
                onPress={onSignUp}
                borderColor="rgba(255, 255, 255, 0.5)"
              />
            </View>
          </View>
        </View>
      </LinearColors>
    </View>
  );
});

export default OnBoarding;

const styles = StyleSheet.create({
  container: {
    ...Theme.container,
  },
  content: {
    flex: 1,
  },
  imageSection: {
    height: SCREEN_HEIGHT * 0.7,
  },
  scrollView: {
    flex: 1,
  },
  progressSection: {
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 32,
    marginBottom: 32,
  },
  buttonSection: {
    paddingHorizontal: 24,
    paddingBottom: getBottomSpace() + 24,
    flex: 1,
    justifyContent: 'flex-end',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 16,
  },
  loginButton: {
    flex: 1,
    height: 56,
    backgroundColor: Colors.White,
    borderRadius: 28,
    borderWidth: 0,
    shadowColor: Colors.Black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  signUpButton: {
    flex: 1,
    height: 56,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 28,
    borderWidth: 1.5,
    borderColor: 'rgba(255, 255, 255, 0.5)',
    overflow: 'hidden',
  },
});
