/**
 * Utility function to get the organization ID from Redux or use a default value
 * This helps ensure consistent organization ID usage across the app
 */

import { store } from '@/services/store';
import { setCurrentOrgId } from '@/services/actions/organizationActions';

/**
 * Gets the organization ID from Redux or returns a default value if not set
 * @param defaultOrgId The default organization ID to use if not set in Redux
 * @returns The organization ID from Redux or the default value
 */
export function getOrganizationId(defaultOrgId: string = '1'): string {
  // Get the current Redux state
  const state = store.getState();

  // Get the organization ID from the Redux state
  const orgId = state?.currentOrgIdReducer?.orgId;

  // Return the organization ID from Redux or the default value
  return orgId || defaultOrgId;
}

/**
 * Gets the organization details from Redux including name
 * @returns The organization details from Redux or null if not set
 */
export function getOrganizationDetails(): { id: string; name: string } | null {
  // Get the current Redux state
  const state = store.getState();

  // Get the organization details from the Redux state
  const orgDetails = state?.currentOrgIdReducer?.orgDetails;

  return orgDetails || null;
}

/**
 * Gets the organization name from Redux
 * @param fallbackName The fallback name to use if not set in Redux
 * @returns The organization name from Redux or the fallback name
 */
export function getOrganizationName(fallbackName: string = 'WatchRx'): string {
  const orgDetails = getOrganizationDetails();
  return orgDetails?.name || fallbackName;
}

/**
 * Sets a default organization ID in Redux if one isn't already set
 * @param defaultOrgId The default organization ID to set
 */
export function ensureOrganizationId(defaultOrgId: string = '1'): void {
  // Get the current Redux state
  const state = store.getState();

  // Get the organization ID from the Redux state
  const orgId = state?.currentOrgIdReducer?.orgId;

  // If the organization ID isn't set in Redux, dispatch an action to set it
  if (!orgId) {
    // Import the action creator and dispatch the action
    store.dispatch(setCurrentOrgId(defaultOrgId));
  }
}
