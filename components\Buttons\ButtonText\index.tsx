import React, {memo} from 'react';
import {
  ColorValue,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import {Colors} from '@/constants';
import Theme from '@/constants/Theme';
import Text, {TextProps} from '../../Text';

interface ButtonTextProps {
  title?: string;
  borderColor?: ColorValue | string;
  style?: ViewStyle;
  titleColor?: string;
  textProps?: TextProps;
  onPress?: () => void;
  bold?: boolean;
  white?: boolean;
  blueLight?: boolean;
  marginTop?: number;
  marginRight?: number;
  darkJungGreen?: boolean;
  hilight?: boolean;
}

const ButtonText = memo(
  ({
    title,
    style,
    titleColor = Colors.TealBlue,
    onPress,
    white,
    borderColor,
    darkJungGreen,
    marginRight,
    hilight,
    blueLight,
    marginTop,
    bold,
    ...textProps
  }: ButtonTextProps) => {
    return (
      <TouchableOpacity
        style={[
          styles.container,
          style,
          {
            borderColor: borderColor,
            marginTop: marginTop,
            marginRight: marginRight,
          },
        ]}
        onPress={onPress}
        activeOpacity={0.54}>
        <Text
          hilight={hilight}
          darkJungGreen={darkJungGreen}
          white={white}
          blueLight={blueLight}
          type="H5"
          color={titleColor}
          {...textProps}
          bold>
          {title}
        </Text>
      </TouchableOpacity>
    );
  },
);

export default ButtonText;

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderRadius: 12,
    borderColor: Colors.White,
    ...Theme.center,
    ...Theme.flexRow,
  },
});
