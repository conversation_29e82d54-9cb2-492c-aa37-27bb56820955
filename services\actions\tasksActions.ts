import {
  CURRENT_TASK,
  <PERSON>ET<PERSON>_DTASKS_LIST,
  FETCH_DTASKS_LIST_FAILURE,
  FETCH_DTASKS_LIST_SUCCESS,
  FETCH_FTASKS_LIST,
  FETCH_FTASKS_LIST_FAILURE,
  FETCH_FTASKS_LIST_SUCCESS,
} from './type';

// Due Tasks
export const fetchDueTasksList = (payload: any) => {
  return {
    type: FETCH_DTASKS_LIST,
    payload,
  };
};

export const fetchDueTasksListSuccess = (data: any) => {
  return {
    type: FETCH_DTASKS_LIST_SUCCESS,
    data,
  };
};

export const fetchDueTasksListFailure = (error: any) => {
  return {
    type: FETCH_DTASKS_LIST_FAILURE,
    error,
  };
};

// Future Tasks
export const fetchFutureTasksList = (payload: any) => {
  return {
    type: FETCH_FTASKS_LIST,
    payload,
  };
};

export const fetchFutureTasksListSuccess = (data: any) => {
  return {
    type: FETCH_FTASKS_LIST_SUCCESS,
    data,
  };
};

export const fetchFutureTasksListFailure = (error: any) => {
  return {
    type: FETCH_FTASKS_LIST_FAILURE,
    error,
  };
};

// Current Task
export const setCurrentTask = (task: any) => {
  return {
    type: CURRENT_TASK,
    task,
  };
};