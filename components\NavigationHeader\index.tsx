import { createShadow } from "@/utils/shadowStyles";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Animated,
  Dimensions,
  Easing,
  Image,
  Modal,
  Platform,
  StatusBar,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { getStatusBarHeight } from "react-native-iphone-x-helper";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useDispatch, useSelector } from "react-redux";

import { SOURCE_ICON } from "@/assets/images";
import Text from "@/components/Text";
import { Colors } from "@/constants";
import Constants from "@/constants/Const";

import GenericModal from "@/components/SuccessFailModal/GenericModal";
import { userLogOut } from "@/services/actions/logOutActions";
import { resetCredentials } from "@/services/secure-storage";

interface NavigationHeaderProps {
  title?: string;
  showBackButton?: boolean;
  showLogo?: boolean;
  showLogout?: boolean;
  showRightLogo?: boolean;
  centerContent?: boolean;
  rightComponent?: React.ReactNode;
  leftComponent?: React.ReactNode;
  onBackPress?: () => void;
}

/**
 * A modern, unified NavigationHeader component that can be used across all screens
 * Supports back button, custom title, logo, and logout functionality
 */
const NavigationHeader: React.FC<NavigationHeaderProps> = ({
  title,
  showBackButton = false,
  showLogo = true,
  showLogout = false,
  showRightLogo = false,
  centerContent = true,
  rightComponent,
  leftComponent,
  onBackPress,
}) => {
  const [visibleModal, setVisibleModal] = useState<boolean>(false);
  const [animatedElevation] = useState(new Animated.Value(1));
  const [animatedOpacity] = useState(new Animated.Value(0));
  const [animatedTranslateY] = useState(new Animated.Value(-10));
  const dispatch = useDispatch();
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const screenWidth = Dimensions.get("window").width;

  // Get user profile data from Redux
  const userProfileDetails = useSelector(
    (state: any) => state?.userProfileReducer?.data
  );

  // Display title from props or from user profile
  const displayTitle = title || userProfileDetails?.name || "Welcome";

  // Animate header on mount
  useEffect(() => {
    Animated.parallel([
      Animated.timing(animatedOpacity, {
        toValue: 1,
        duration: 350,
        useNativeDriver: true,
        easing: Easing.out(Easing.ease),
      }),
      Animated.timing(animatedTranslateY, {
        toValue: 0,
        duration: 350,
        useNativeDriver: true,
        easing: Easing.out(Easing.ease),
      }),
    ]).start();
  }, []);

  // Handle logout button press
  const handleLogout = () => {
    setVisibleModal(true);
  };

  const closeModal = () => {
    setVisibleModal(false);
  };

  const confirmLogout = () => {
    setVisibleModal(false);
    setTimeout(() => {
      dispatch(userLogOut());
      resetCredentials();
      router.replace("/auth/login");
    }, 500);
  };

  // Handle back button press
  const handleBackPress = () => {
    // Add subtle animation for feedback
    Animated.sequence([
      Animated.timing(animatedElevation, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(animatedElevation, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();

    // Execute the navigation after a slight delay for better UX
    setTimeout(() => {
      if (onBackPress) {
        onBackPress();
      } else {
        router.back();
      }
    }, 50);
  };

  // Generate dynamic shadow style based on Platform
  const shadowStyle = createShadow(
    4, // Increased shadow elevation
    0.2, // Slightly increased shadow opacity for more depth
    4, // Increased shadow radius
    "#000",
    { width: 0, height: 3 }, // Increased shadow offset for more depth
    true // Preserve background color
  );

  // Determine the left component to display
  const renderLeftComponent = () => {
    if (leftComponent) {
      return leftComponent;
    }

    if (showBackButton) {
      return (
        <Animated.View style={{ transform: [{ scale: animatedElevation }] }}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBackPress}
            activeOpacity={0.7}
          >
            <Ionicons name="chevron-back" size={24} color={Colors.White} />
          </TouchableOpacity>
        </Animated.View>
      );
    }

    if (showLogo) {
      return (
        <View style={styles.logoContainer}>
          <Image
            style={styles.logo}
            source={SOURCE_ICON.logoHeader}
            resizeMode="contain"
          />
        </View>
      );
    }

    return null;
  };

  // Determine the right component to display
  const renderRightComponent = () => {
    if (rightComponent) {
      return rightComponent;
    }

    // Prioritize showing logo on right side
    if (showRightLogo) {
      return (
        <View style={styles.logoContainer}>
          <Image
            style={styles.logo}
            source={SOURCE_ICON.logoHeader}
            resizeMode="contain"
          />
        </View>
      );
    }

    if (showLogout) {
      return (
        <TouchableOpacity
          style={styles.logoutButton}
          onPress={handleLogout}
          activeOpacity={0.7}
        >
          <Ionicons name="log-out-outline" size={22} color={Colors.White} />
        </TouchableOpacity>
      );
    }

    if (showLogo && !leftComponent && !showBackButton) {
      // If logo is on the left, we might want to balance the header
      return <View style={styles.placeholderRight} />;
    }

    return null;
  };

  return (
    <>
      <StatusBar
        barStyle="light-content"
        backgroundColor={Colors.TurquoiseBlue}
        translucent={Platform.OS === 'ios'}
      />
      <Animated.View
        style={[
          styles.headerContainer,
          shadowStyle,
          {
            opacity: animatedOpacity,
            paddingTop: Platform.OS === 'android' 
              ? (insets.top > 0 ? insets.top : getStatusBarHeight()) + 8
              : (insets.top > 0 ? insets.top : getStatusBarHeight()),
            transform: [
              { translateY: animatedTranslateY },
              {
                translateY: animatedElevation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [-0.5, 0],
                }),
              },
            ],
          },
        ]}
      >
        <View style={styles.headerContent}>
          {/* Left side */}
          <View
            style={[
              styles.headerLeft,
              centerContent &&
                !renderLeftComponent() &&
                styles.invisiblePlaceholder,
            ]}
          >
            {renderLeftComponent()}
          </View>

          {/* Center title */}
          <Animated.View
            style={[
              styles.headerCenter,
              { opacity: animatedOpacity },
              // Adjust width based on whether left/right components exist
              {
                maxWidth:
                  screenWidth -
                  (renderLeftComponent() ? 90 : 40) -
                  (renderRightComponent() ? 90 : 40),
              },
            ]}
          >
            <Text
              style={styles.headerTitle}
              center
              numberOfLines={1}
              {...{ ellipsizeMode: "tail" }}
            >
              {displayTitle}
            </Text>
          </Animated.View>

          {/* Right side */}
          <View
            style={[
              styles.headerRight,
              centerContent &&
                !renderRightComponent() &&
                styles.invisiblePlaceholder,
            ]}
          >
            {renderRightComponent()}
          </View>
        </View>
      </Animated.View>

      {/* Logout confirmation modal */}
      <Modal
        visible={visibleModal}
        onRequestClose={closeModal}
        transparent
        animationType="fade"
      >
        <GenericModal
          close={closeModal}
          open={confirmLogout}
          title="Confirm Logout"
          confirmText="Logout"
          cancelText="Cancel"
          message="Are you sure you want to logout?"
          icon={
            <Ionicons
              name="log-out-outline"
              size={60}
              color={Colors.TealBlue}
              style={{ marginBottom: 10 }}
            />
          }
        />
      </Modal>
    </>
  );
};

export default NavigationHeader;

const styles = StyleSheet.create({
  headerContainer: {
    height: "auto",
    backgroundColor: Colors.TurquoiseBlue,
    paddingBottom: 12,
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    borderBottomLeftRadius: Platform.OS === 'ios' ? 0 : 16,
    borderBottomRightRadius: Platform.OS === 'ios' ? 0 : 16,
    ...Platform.select({
      android: {
        elevation: 10,
      },
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
    }),
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: Constants.widthScale(16),
    paddingVertical: 12,
    minHeight: 60,
  },
  headerLeft: {
    width: 70,
    justifyContent: "center",
    alignItems: "flex-start",
  },
  headerCenter: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 8,
  },
  headerRight: {
    width: 70,
    justifyContent: "center",
    alignItems: "flex-end",
  },
  headerTitle: {
    fontSize: Constants.fontScale(17),
    color: Colors.Black,
    fontWeight: "bold",
    fontFamily: 'DMSans-Bold',
    letterSpacing: 0.3,
    textShadowColor: 'rgba(0, 0, 0, 0.05)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  backButton: {
    width: 42,
    height: 42,
    borderRadius: 21,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
    ...Platform.select({
      android: {
        overflow: "hidden",
      },
    }),
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  logoContainer: {
    padding: 4,
    borderRadius: 8,
    backgroundColor: "rgba(255, 255, 255, 0.15)",
  },
  logo: {
    width: 34,
    height: 34,
    resizeMode: "contain",
  },
  logoutButton: {
    width: 42,
    height: 42,
    borderRadius: 21,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    ...Platform.select({
      android: {
        overflow: "hidden",
      },
    }),
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  placeholderRight: {
    width: 42,
    height: 42,
  },
  invisiblePlaceholder: {
    opacity: 0,
  },
});