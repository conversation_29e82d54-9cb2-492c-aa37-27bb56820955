import AlertsTabBar from "@/components/AlertsTabBar";
import AlertsDatePicker from "@/components/DatePickerButtons/alerts-date-picker";
import Container from "@/components/Layout/Container";
import Loader from "@/components/Loader/Loader";
import NavigationHeader from "@/components/NavigationHeader";
import Text from "@/components/Text";
import { Colors } from "@/constants";
import { AlertsProps } from "@/models";
import { apiPostWithToken } from "@/services/apis/apiManager";
import URLS from "@/services/config/config";
import { useIsFocused } from "@react-navigation/native";
import Moment from "moment";
import React, { useEffect, useRef, useState } from "react";
import { Alert, Animated, StyleSheet, View, RefreshControl, StatusBar } from "react-native";
import { useSelector } from "react-redux";
import AlertsPage from "./AlertsPage";

import { useLocalSearchParams } from "expo-router";
import { Ionicons } from "@expo/vector-icons";

const PatientAlerts = () => {
  const isFocused = useIsFocused();
  const [alertsList, setAlertsList] = useState<AlertsProps[]>();
  const [tabActive, setTabActive] = useState<number>(3);
  const [loader, setLoader] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const orgId = useSelector((state: any) => state?.currentOrgIdReducer?.orgId);

  const caregiverId = useSelector(
    (state: any) => state?.loginReducer?.data?.userId
  );

  const isFetching = useSelector(
    (state: any) => state?.alertsListReducer?.isFetching
  );

  const alertsData = useSelector(
    (state: any) => state?.alertsListReducer?.data
  );

  const patientId = useSelector(
    (state: any) => state?.currentPatientIdReducer?.patientId
  );

  const [fromDate, setFromDate] = useState<Date>();
  const [toDate, setToDate] = useState<Date>();

  const { index } = useLocalSearchParams();

  // Initialize tab index from params
  useEffect(() => {
    if (index !== undefined) {
      const parsedIndex = Number(index);
      if (!isNaN(parsedIndex)) {
        setTabActive(parsedIndex);
      }
    }
  }, [index]);

  // Initialize date range on component mount
  useEffect(() => {
    const today = new Date();
    setToDate(today);
    setFromDate(Moment(today).add(-7, "days").toDate());
  }, []);

  // Handle tab change with animation
  const handleTabChange = (index: number) => {
    // Change tab immediately
    setTabActive(index);

    // Fade in new content
    fadeAnim.setValue(0.5);
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 250,
      useNativeDriver: true,
    }).start();
  };

  // Load alerts when component mounts or when date range changes
  useEffect(() => {
    // Show loader immediately when component mounts or date changes
    if (fromDate && toDate) {
      setLoader(true);
      const tDate = Moment(toDate).format("YYYY-MM-DD");
      const fDate = Moment(fromDate).format("YYYY-MM-DD");
      
      // Add a small delay to ensure loader is visible
      const timer = setTimeout(() => {
        getAlerts(fDate, tDate);
      }, 50);
      
      return () => clearTimeout(timer);
    }
  }, [fromDate, toDate]);
  
  // Also load alerts when screen comes into focus (but only if we have dates)
  useEffect(() => {
    if (isFocused && fromDate && toDate) {
      const tDate = Moment(toDate).format("YYYY-MM-DD");
      const fDate = Moment(fromDate).format("YYYY-MM-DD");
      getAlerts(fDate, tDate);
    }
  }, [isFocused]);

  const getAlerts = async (startDate: string, endDate: string) => {
    try {
      // Loader is already set in the useEffect
      console.log(`Fetching alerts from ${startDate} to ${endDate}`);
      
      const response = await apiPostWithToken(
        {
          careGiverId: caregiverId,
          startDate: startDate,
          endDate: endDate,
          orgId: orgId,
        },
        URLS.caregiverUrl + "getAlertbynurseid"
      );
      
      if (response?.status == 200) {
        const alerts = response?.data?.data || [];
        console.log(`Received ${alerts.length} alerts`);
        
        // Process alerts in a more efficient way
        if (alerts.length) {   
          // Exclude Critical alerts since they have their own dedicated screen
          const nonCriticalAlerts = alerts.filter((alert: AlertsProps) => alert.alertType !== 'Critical');
          // Apply patient filter if needed
          const filteredAlerts = patientId > 0 
            ? nonCriticalAlerts.filter((alert: AlertsProps) => alert.patientId.toString() === patientId.toString())
            : nonCriticalAlerts;
            
          // Set the alerts list
          setAlertsList(filteredAlerts);
        } else {
          setAlertsList([]);
        }
      } else {
        const errorMessage = response?.response?.data?.responseMessage
          ? response?.response?.data?.responseMessage
          : response.message === "Network Error"
          ? "Network error. Please check your data connection."
          : response.message;
        Alert.alert("Error", errorMessage, [{ text: "Dismiss" }]);
        setAlertsList([]);
      }
    } catch (error) {
      console.error('Error fetching alerts:', error);
      Alert.alert("Error", "An unexpected error occurred. Please try again.", [{ text: "Dismiss" }]);
      setAlertsList([]);
    } finally {
      // Always hide loader and refreshing indicator when done
      setLoader(false);
      setRefreshing(false);
    }
  };

  // Update alerts list when redux data changes
  useEffect(() => {
    if (alertsData?.length) {
      console.log('Redux alertsData received:', alertsData.length, 'alerts');
      // Exclude Critical alerts since they have their own dedicated screen
      const nonCriticalAlerts = alertsData.filter((alert: any) => alert.alertType !== 'Critical');
      console.log('Redux alerts after filtering Critical:', nonCriticalAlerts.length);
      
      if (patientId > 0) {
        setAlertsList(
          nonCriticalAlerts.filter((alert: any) => {
            return alert.patientId.toString() === patientId.toString();
          })
        );
      } else {
        setAlertsList(nonCriticalAlerts);
      }
    } else {
      setAlertsList([]);
    }
  }, [alertsData?.length]);

  const hasName = () => {
    return patientId ? false : true;
  };
  
  const handleRefresh = () => {
    setRefreshing(true);
    const tDate = Moment(toDate).format("YYYY-MM-DD");
    const fDate = Moment(fromDate).format("YYYY-MM-DD");
    getAlerts(fDate, tDate);
  };

  // Calculate alert counts based on pre-filtered data
  const alertCounts = React.useMemo(() => {
    if (!alertsList || alertsList.length === 0) return [0, 0, 0, 0];
    
    // Prepare filtered alerts for each tab (pre-filtered)
    const alertTypes = ["Alarm", "Warning", "Info"];
    const filtered = alertTypes.map(type => 
      alertsList.filter(item => item.alertType === type)
    );
    
    const warning = filtered[0].length;
    const alarm = filtered[1].length;
    const info = filtered[2].length;
    
    return [warning, alarm, info, warning + alarm + info];
  }, [alertsList]);
  

  // Prepare filtered alerts for each tab (pre-filtered)
  const allFilteredAlerts = React.useMemo(() => {
    if (!alertsList) return [[], [], [], []]; // Empty arrays for each tab if no data
    
    const alertTypes = ["Alarm", "Warning", "Info"];
    const filtered = alertTypes.map(type => 
      alertsList.filter(item => item.alertType === type)
    );
    
    // Add the "All" tab data
    filtered.push(alertsList);
    
    return filtered;
  }, [alertsList]);
  
  // Get the currently active filtered alerts
  const filteredAlerts = allFilteredAlerts[tabActive];
      
  const tabTitles = ["Alarm", "Warning", "Info", "All"];

  return (
    <Container style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.TurquoiseBlue} />
      <NavigationHeader 
        title="Alerts"
        showBackButton={true}
        showLogo={false}
        showRightLogo={true}
      />
      <Loader modalVisible={loader} />
      <View style={styles.contentContainer}>
        <View style={styles.datePickersContainer}>
          <View style={styles.datePickerWrapper}>
            <AlertsDatePicker
              date={fromDate}
              onDateChange={(date) => setFromDate(date)}
              label="Start Date"
            />
          </View>
          <View style={styles.dateSeparator}>
            <Ionicons name="arrow-forward" size={18} color={Colors.GrayBlue} />
          </View>
          <View style={styles.datePickerWrapper}>
            <AlertsDatePicker
              date={toDate}
              onDateChange={(date) => setToDate(date)}
              label="End Date"
            />
          </View>
        </View>

        <View style={styles.tabBarContainer}>
          <AlertsTabBar
            onChangeTab={handleTabChange}
            tabs={tabTitles}
            activeBackgroundColor={Colors.TealBlue}
            initialTab={tabActive}
            value={tabActive}
            counts={alertCounts}
          />
        </View>

        <Animated.View style={[styles.alertsContainer, { opacity: fadeAnim }]}>
          <AlertsPage
            data={filteredAlerts}
            title={tabTitles[tabActive]}
            nameDisplay={hasName()}
            onRefresh={handleRefresh}
            refreshing={refreshing}
          />
        </Animated.View>
      </View>
    </Container>
  );
};

export default PatientAlerts;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    height: "100%",
    backgroundColor: "#f8f9fa",
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    fontFamily: 'DMSans-Bold',
    color: Colors.DarkJungleGreen,
    marginLeft: 8,
  },
  datePickersContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  datePickerWrapper: {
    width: "46%",
  },
  dateSeparator: {
    width: "8%",
    alignItems: "center",
    justifyContent: "center",
    paddingTop: 20,
  },
  tabBarContainer: {
    marginVertical: 16,
  },
  alertsContainer: {
    flex: 1,
  },
});
