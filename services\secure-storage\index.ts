import AsyncStorage from "@react-native-async-storage/async-storage";
import * as SecureStore from "expo-secure-store";

/**
 * Storage keys for secure data
 */
enum StorageKeys {
  CREDENTIALS = "Credentials",
  AUTH_TOKEN = "AuthToken",
  FCM_TOKEN = "FCMToken",
  USER_ROLE = "UserRole",
}

/**
 * Credentials interface for user login information
 */
export interface Credentials {
  username: string;
  password: string;
  userRole?: 'caregiver' | 'physician'; // Store selected role
}

/**
 * User role information interface
 */
export interface StoredUserRole {
  userType: 'caregiver' | 'physician';
  roleId: number;
}

/**
 * Clear all stored credentials and auth data
 * @returns Promise that resolves when all data is cleared
 */
export async function resetCredentials(): Promise<void> {
  try {
    await Promise.all([
      SecureStore.deleteItemAsync(StorageKeys.CREDENTIALS),
      SecureStore.deleteItemAsync(StorageKeys.AUTH_TOKEN),
      SecureStore.deleteItemAsync(StorageKeys.USER_ROLE),
    ]);
  } catch (error) {
    console.error("Error clearing credentials:", error);
  }
}

/**
 * Stores user credentials securely with role information
 * @param username User's username or email
 * @param password User's password
 * @param userRole Selected user role
 * @returns Promise that resolves when credentials are stored
 */
export async function setCredentialsSInfo(
  username: string,
  password: string,
  userRole?: 'caregiver' | 'physician'
): Promise<void> {
  if (!username || !password) {
    throw new Error("Username and password are required");
  }

  try {
    const details: Credentials = { username, password, userRole };
    await SecureStore.setItemAsync(
      StorageKeys.CREDENTIALS,
      JSON.stringify(details)
    );
    if (userRole) {
      const roleInfo: StoredUserRole = {
        userType: userRole,
        roleId: userRole === 'physician' ? 3 : 5
      };
      await SecureStore.setItemAsync(
        StorageKeys.USER_ROLE,
        JSON.stringify(roleInfo)
      );
    }
  } catch (error) {
    console.error("Error storing credentials:", error);
    throw new Error("Failed to store credentials securely");
  }
}

/**
 * Retrieves stored credentials
 * @returns Promise that resolves with the user's credentials or null if not found
 */
export async function fetchCredentialsSInfo(): Promise<Credentials | null> {
  try {
    const data = await SecureStore.getItemAsync(StorageKeys.CREDENTIALS);
    if (!data) return null;

    return JSON.parse(data) as Credentials;
  } catch (error) {
    console.error("Error fetching credentials:", error);
    return null;
  }
}

/**
 * Retrieves stored user role information
 * @returns Promise that resolves with the user's role info or null if not found
 */
export async function getStoredUserRole(): Promise<StoredUserRole | null> {
  try {
    const data = await SecureStore.getItemAsync(StorageKeys.USER_ROLE);
    if (!data) return null;

    return JSON.parse(data) as StoredUserRole;
  } catch (error) {
    console.error("Error fetching user role:", error);
    return null;
  }
}

/**
 * Stores authentication token securely
 * @param token The authentication token to store
 * @returns Promise that resolves when token is stored
 */
export async function setAuthToken(token: string): Promise<void> {
  if (!token) {
    throw new Error("Token is required");
  }

  try {
    console.log(StorageKeys.AUTH_TOKEN, token);
    await SecureStore.setItemAsync(StorageKeys.AUTH_TOKEN, token);
  } catch (error) {
    console.error("Error storing auth token:", error);
    throw new Error("Failed to store authentication token");
  }
}

/**
 * Retrieves stored authentication token
 * @returns Promise that resolves with the token or null if not found
 */
export async function getAuthToken(): Promise<string | null> {
  try {
    return await SecureStore.getItemAsync(StorageKeys.AUTH_TOKEN);
  } catch (error) {
    console.error("Error getting auth token:", error);
    return null;
  }
}

/**
 * Store FCM token securely
 * @param fcmToken The FCM token to store
 * @returns Promise that resolves when token is stored
 */
export async function setFCMToken(fcmToken: string): Promise<void> {
  if (!fcmToken) {
    throw new Error("FCM token is required");
  }

  try {
    await SecureStore.setItemAsync(StorageKeys.FCM_TOKEN, fcmToken);
  } catch (error) {
    console.error("Error storing FCM token:", error);
    throw new Error("Failed to store FCM token");
  }
}

/**
 * Get stored FCM token
 * @returns Promise that resolves with the FCM token or null if not found
 */
export async function getFCMToken(): Promise<string | null> {
  try {
    return await SecureStore.getItemAsync(StorageKeys.FCM_TOKEN);
  } catch (error) {
    console.error("Error getting FCM token:", error);
    return null;
  }
}

export default {
  setCredentialsSInfo,
  fetchCredentialsSInfo,
  setAuthToken,
  getAuthToken,
  resetCredentials,
  setFCMToken,
  getFCMToken,
  getStoredUserRole,
};
