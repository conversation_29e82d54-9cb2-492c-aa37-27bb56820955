import React, {Dispatch, SetStateAction} from 'react';
import {
  ColorValue,
  KeyboardTypeOptions,
  Platform,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import Text from '@/components/Text';
import {Colors} from '@/constants';
import {useTheme} from '@/constants/Theme';
import Theme from '@/constants/Theme';

interface Props {
  value: string;
  onChangeText?: (text: string) => void | Dispatch<SetStateAction<string>>;
  placeholder?: string;
  isShowIcon?: boolean;
  icon?: any;
  secureTextEntry?: boolean;
  style?: ViewStyle;
  borderColor?: ColorValue | string;
  iconPress?: () => void;
  autoFocus?: boolean;
  backgroundColor?: string;
  iconLeft?: any;
  isShowIconLeft?: boolean;
  iconPressLeft?: () => void;
  multiline?: boolean;
  editable?: boolean;
  keyboardType?: KeyboardTypeOptions;
}

export default ({
  value,
  placeholder,
  onChangeText,
  isShowIcon,
  icon,
  secureTextEntry,
  style,
  borderColor,
  backgroundColor,
  iconPress,
  isShowIconLeft,
  iconLeft,
  iconPressLeft,
  ...props
}: Props) => {
  const {theme} = useTheme();

  let lineHeight;
  if (props.multiline) {
    lineHeight = 24;
  }
  let height;

  return (
    <View
      style={[
        styles.container,
        style,
        {
          borderColor: borderColor || theme.innearColor,
          borderWidth: 1,
          backgroundColor: theme.searchBox,
        },
        props.multiline && styles.muli,
      ]}>
      {isShowIconLeft && !!iconLeft && (
        <TouchableOpacity
          style={styles.iconLeftView}
          onPress={iconPressLeft}
          disabled={!iconPressLeft}>
          {iconLeft}
        </TouchableOpacity>
      )}
      {props.editable ? (
        <TextInput
          placeholder={placeholder}
          value={value}
          onChangeText={onChangeText}
          style={{
            flex: 1,
            fontSize: 15,
            lineHeight: lineHeight,
            fontFamily: 'OpenSans-Bold',
            height: '100%',
            color: theme.text,
          }}
          secureTextEntry={secureTextEntry}
          editable={props.editable}
          placeholderTextColor={Colors.GrayBlue}
          keyboardType={props.keyboardType ? props.keyboardType : 'default'}
          {...props}
        />
      ) : (
        <Text size={15} lineHeight={24} semibold style={{flex: 1}}>
          {value}
        </Text>
      )}
      {isShowIcon && !!icon && (
        <TouchableOpacity
          style={styles.iconView}
          onPress={iconPress}
          disabled={!iconPress}>
          {icon}
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    minHeight: 48,
    justifyContent: 'center',
    paddingHorizontal: 16,
    borderRadius: 8,
    ...Theme.flexRow,
  },
  iconView: {
    width: 24,
    height: 24,
    position: 'absolute',
    right: 12,
    ...Theme.center,
  },
  iconLeftView: {
    width: 24,
    height: 24,
    ...Theme.center,
    marginRight: 16,
  },
  muli: {
    ...Platform.select({
      ios: {
        paddingBottom: 11,
      },
    }),
  },
});
