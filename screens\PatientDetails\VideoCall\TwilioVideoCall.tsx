// import {useFocusEffect, useRoute} from '@react-navigation/native';
// import React, {useEffect, useRef, useState, useCallback} from 'react';
// import {
//   Alert,
//   PermissionsAndroid,
//   Platform,
//   StyleSheet,
//   View,
//   Dimensions,
//   TouchableOpacity,
//   StatusBar,
//   Linking,
// } from 'react-native';
// import { Camera } from 'expo-camera';
// import { Audio } from 'expo-av';

// import {
//   TwilioVideo,
//   TwilioVideoLocalView,
//   TwilioVideoParticipantView,
// } from 'react-native-twilio-video-webrtc';
// import Loader from '@/components/Loader/Loader';
// import Text from '@/components/Text';
// import {Colors} from '@/constants';
// import {ackCallEnded, getAccessToken} from '@/services/apis/apiManager';

// // Import icons from @expo/vector-icons
// import { MaterialIcons, Ionicons, FontAwesome5 } from '@expo/vector-icons';

// // Define types for Twilio video tracks and participants
// interface TrackIdentifier {
//   participantSid: string;
//   videoTrackSid: string;
// }

// interface TwilioVideoCallProps {
//   patientId?: number;
// }

// interface TwilioParticipant {
//   sid: string;
//   identity?: string;
// }

// interface TwilioTrack {
//   trackSid: string;
//   enabled?: boolean;
// }

// interface RoomConnectParams {
//   participants: TwilioParticipant[];
//   localParticipant: TwilioParticipant;
//   roomSid: string;
// }

// interface TrackEventParams {
//   participant: TwilioParticipant;
//   track: TwilioTrack;
// }

// interface NetworkQualityParams {
//   participant: TwilioParticipant;
//   isLocalUser: boolean;
//   quality: number;
// }

// interface DominantSpeakerParams {
//   roomName: string;
//   roomSid: string;
//   participant: TwilioParticipant;
// }

// interface ErrorParams {
//   error: Error | string;
// }

// const Example = ({ patientId: propPatientId }: TwilioVideoCallProps) => {
//   const [isAudioEnabled, setIsAudioEnabled] = useState<boolean>(true);
//   const [isVideoEnabled, setIsVideoEnabled] = useState<boolean>(true);
//   const [status, setStatus] = useState<string>('disconnected');
//   const [isConnecting, setIsConnecting] = useState<boolean>(false);
//   const [videoTracks, setVideoTracks] = useState<Map<string, TrackIdentifier>>(new Map());

//   // Add ref to track if component is mounted
//   const isMountedRef = useRef<boolean>(true);
  
//   // Add ref to track rendering issues on iOS
//   const renderAttempts = useRef<number>(0);

//   const twilioVideo = useRef<TwilioVideo | null>(null);
//   const [loader, setLoader] = useState<boolean>(false);
//   const [patientId, setPatientId] = useState<number>(propPatientId || 0);
//   const [roomSid, setRoomSid] = useState<string | null>(null);
//   const route = useRoute();
//   const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

//   // Debug function to log track state
//   const logTrackState = () => {
//     console.log(`=== TRACK STATE ===`);
//     console.log(`Total tracks: ${videoTracks.size}`);
//     Array.from(videoTracks.entries()).forEach(([trackId, info]) => {
//       console.log(`Track ${trackId} -> Participant: ${info.participantSid}`);
//     });
//     console.log(`=== END TRACK STATE ===`);
//   };

//   useFocusEffect(
//     useCallback(() => {
//       // First try to use the prop patientId (from Expo Router)
//       if (propPatientId) {
//         console.log('Using prop patientId:', propPatientId);
//         setPatientId(propPatientId);
//         return;
//       }

//       // Fall back to route params (from React Navigation)
//       const obj = route?.params as {patientId?: number};
//       if (obj?.patientId) {
//         console.log('Using route params patientId:', obj.patientId);
//         setPatientId(obj.patientId);
//       } else {
//         console.log('No patientId found in props or route params');
//       }
//     }, [propPatientId]),
//   );

//   useEffect(() => {
//     requestPermissions();
    
//     // Set component as mounted
//     isMountedRef.current = true;
    
//     // Cleanup effect for component unmount
//     return () => {
//       // Mark component as unmounted first
//       isMountedRef.current = false;
      
//       console.log('Component is unmounting - cleaning up resources');

//       // Store roomSid for API call before cleanup
//       const currentRoomSid = roomSid;

//       // Ensure we properly clean up all resources
//       if (twilioVideo?.current) {
//         // Disable audio and video first
//         try {
//           twilioVideo.current.setLocalAudioEnabled(false);
//           twilioVideo.current.setLocalVideoEnabled(false);
//         } catch (err) {
//           console.warn('Error disabling media on unmount:', err);
//         }

//         // Then disconnect from the room
//         if (status === 'connected' || status === 'connecting') {
//           try {
//             twilioVideo.current.disconnect();
//           } catch (err) {
//             console.warn('Error disconnecting on unmount:', err);
//           }
//         }
//       }

//       // Call API with the stored room SID if available
//       if (currentRoomSid) {
//         callEndedAPI(currentRoomSid);
//       }
//     };
//   }, []); // Empty dependency array ensures this only runs on mount/unmount

//   // Force re-render of video tracks on iOS periodically if needed
//   useEffect(() => {
//     let interval: NodeJS.Timeout | null = null;
    
//     if (Platform.OS === 'ios' && status === 'connected' && videoTracks.size > 0) {
//       // Only start the interval if we're connected and have tracks
//       interval = setInterval(() => {
//         if (isMountedRef.current) {
//           renderAttempts.current += 1;
//           console.log(`Forcing video refresh attempt #${renderAttempts.current}`);
          
//           // Force a re-render by creating a new reference to the Map
//           setVideoTracks(prevTracks => new Map(prevTracks));
//         }
//       }, 5000); // Check every 5 seconds
      
//       // Stop after 5 attempts (25 seconds)
//       if (renderAttempts.current >= 5) {
//         if (interval) {
//           clearInterval(interval);
//         }
//       }
//     }
    
//     // Clean up interval
//     return () => {
//       if (interval) {
//         clearInterval(interval);
//       }
//     };
//   }, [status, videoTracks.size]);

//   const requestPermissions = async () => {
//     try {
//       if (Platform.OS === 'android') {
//         const userResponse = await PermissionsAndroid.requestMultiple([
//           PermissionsAndroid.PERMISSIONS.CAMERA,
//           PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
//           PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
//         ]);
//         return userResponse;
//       } else if (Platform.OS === 'ios') {
//         // Request camera permission
//         const { status: cameraStatus } = await Camera.requestCameraPermissionsAsync();
//         console.log('Camera permission:', cameraStatus);

//         // Request microphone permission
//         const { status: audioStatus } = await Audio.requestPermissionsAsync();
//         console.log('Audio permission:', audioStatus);

//         // Check if any permission was denied
//         if (cameraStatus !== 'granted' || audioStatus !== 'granted') {
//           Alert.alert(
//             'Permissions Required',
//             'Camera and microphone access are required for video calls. Please enable them in your device settings.',
//             [
//               { text: 'Cancel', style: 'cancel' },
//               { text: 'Open Settings', onPress: () => Linking.openSettings() }
//             ]
//           );
//         }

//         return {
//           camera: cameraStatus === 'granted',
//           audio: audioStatus === 'granted'
//         };
//       }
//     } catch (err) {
//       console.log('Error requesting permissions:', err);
//       Alert.alert(
//         'Permission Error',
//         'There was an error requesting camera and microphone permissions. Please try again.'
//       );
//     }
//     return null;
//   };

//   const getAccessTokenById = async (patientId: number) => {
//     if (patientId > 0) {
//       setLoader(true);
//       try {
//         const response = await getAccessToken(patientId);
//         if (response?.status == 200) {
//           setLoader(false);
//           console.log('Response:', response?.data);
//           return response?.data; // Return the access token instead of connecting immediately
//         } else {
//           setLoader(false);
//           const errorMessage = response?.response?.data?.responseMessage
//             ? response?.response?.data?.responseMessage
//             : response.message === 'Network Error'
//             ? 'Network error. Please check your data connection.'
//             : response.message;
//           Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
//           return null;
//         }
//       } catch (error) {
//         setLoader(false);
//         Alert.alert('Error', 'Failed to get access token. Please try again.', [{text: 'Dismiss'}]);
//         console.error('Error getting access token:', error);
//         return null;
//       }
//     }
//     return null;
//   };

//   const initiateConnection = (accessToken: string) => {
//     try {
//       if (!isMountedRef.current) {
//         console.log('Component unmounted, skipping connection');
//         return;
//       }

//       console.log('Initiating connection with token:', accessToken.substring(0, 20) + '...');
//       console.log('Room name:', patientId);

//       // IMPORTANT: Don't clear tracks here as it might interfere with rendering
//       // We'll let the room events handle track management
      
//       // Set status to connecting before the actual connect call
//       setStatus('connecting');

//       // Ensure the twilioVideo ref exists
//       if (!twilioVideo?.current) {
//         console.error('Twilio video ref is not available');
//         setStatus('disconnected');
//         setIsConnecting(false);
//         return;
//       }

//       // Platform-specific connection handling
//       if (Platform.OS === 'ios') {
//         // On iOS, ensure we're using the correct configuration
//         console.log('iOS: Setting up connection with enhanced options');

//         // For iOS, make sure to pass primitive values, not objects
//         // This is critical for iOS to properly handle the connection
//         const connectOptions = {
//           roomName: '' + patientId,
//           accessToken: accessToken,
//           enableNetworkQualityReporting: true,
//           dominantSpeakerEnabled: true,
//           enableVideo: true,
//           enableAudio: true,
//           encodingParameters: {
//             enableH264Codec: true,
//             // Add more specific video encoding parameters for iOS
//             videoBitrate: 1500, // Higher bitrate for better quality
//             videoHeight: 720,   // HD resolution
//             videoWidth: 1280,
//           },
//           cameraType: 'front' as 'front' | 'back',
//         };

//         // Prepare the video component before connecting
//         if (twilioVideo?.current) {
//           // Ensure camera is initialized
//           twilioVideo.current.setLocalVideoEnabled(true)
//             .then((enabled) => {
//               console.log('Local video enabled before connection:', enabled);

//               // Connect with a small delay to ensure UI and camera are ready
//               setTimeout(() => {
//                 if (isMountedRef.current && twilioVideo?.current) {
//                   console.log('iOS: Connecting with options:', JSON.stringify(connectOptions));
//                   twilioVideo.current.connect(connectOptions);
//                 }
//               }, 500);
//             })
//             .catch(err => {
//               console.error('Error enabling local video before connection:', err);
//               // Still try to connect even if enabling video fails
//               setTimeout(() => {
//                 if (isMountedRef.current && twilioVideo?.current) {
//                   twilioVideo.current.connect(connectOptions);
//                 }
//               }, 500);
//             });
//         }
//       } else {
//         // Android connection
//         console.log('Android: Setting up connection');
//         twilioVideo.current.connect({
//           roomName: '' + patientId,
//           accessToken: accessToken,
//           enableNetworkQualityReporting: true,
//           dominantSpeakerEnabled: true,
//           enableVideo: true,
//           enableAudio: true,
//           encodingParameters: {
//             enableH264Codec: true,
//           },
//           cameraType: 'front' as 'front' | 'back', // Type assertion to match expected enum
//         });
//       }
//     } catch (error) {
//       console.error('Error initiating connection:', error);
//       if (isMountedRef.current) {
//         Alert.alert('Connection Error', 'Failed to connect to the video call. Please try again.');
//         setStatus('disconnected');
//         setIsConnecting(false);
//       }
//     }
//   };

//   // Track whether we've already called the API for a specific room
//   const [calledEndAPI, setCalledEndAPI] = useState<Set<string>>(new Set());

//   const callEndedAPI = async (sid: string | null) => {
//     if (!patientId || patientId <= 0) {
//       console.log('Invalid patient ID for call ended API');
//       return;
//     }

//     if (!sid) {
//       console.log('No room SID available for call ended API');
//       return;
//     }

//     // Prevent duplicate API calls for the same room SID
//     if (calledEndAPI.has(sid)) {
//       console.log(`Already called end API for room ${sid}, skipping`);
//       return;
//     }

//     try {
//       // Mark this room as processed to prevent duplicate calls
//       setCalledEndAPI(prev => new Set(prev).add(sid));

//       console.log(`Acknowledging call end: Room SID ${sid}, Patient ID ${patientId}`);
//       const response = await ackCallEnded(sid, patientId);

//       if (response?.status == 200) {
//         console.log('Call end acknowledgment successful:', response?.data);
//       } else {
//         // Don't show alerts for 404 errors as they're expected in some cases
//         const isNotFoundError = response?.error?.status === 404;
//         const errorMessage = response?.response?.data?.responseMessage
//           ? response?.response?.data?.responseMessage
//           : response.message === 'Network Error'
//           ? 'Network error. Please check your data connection.'
//           : response.message;

//         if (isNotFoundError) {
//           console.log('Call end API returned 404 - this is expected if the call was very short');
//         } else {
//           console.warn('Call end acknowledgment failed:', errorMessage);
//         }
//       }
//     } catch (error) {
//       console.error('Error in callEndedAPI:', error);
//     } finally {
//       setLoader(false);
//     }
//   };

//   const _onEndButtonPress = () => {
//     // Store roomSid before clearing it for the API call
//     const currentRoomSid = roomSid;

//     // Disconnect from the room
//     if (twilioVideo?.current) {
//       console.log('Manually disconnecting from room');
//       try {
//         twilioVideo.current.disconnect();
//       } catch (err) {
//         console.warn('Error disconnecting from room:', err);
//       }
//     }

//     // Call API with the stored room SID
//     if (currentRoomSid) {
//       callEndedAPI(currentRoomSid);
//     }

//     // Reset state
//     setRoomSid(null);
//     setStatus('disconnected');
//     setVideoTracks(new Map());
//   };

//   const _connectCall = async () => {
//     try {
//       // Set connecting state to prevent multiple connection attempts
//       setIsConnecting(true);

//       // Get permissions before connecting
//       const permissions = await requestPermissions();

//       // For iOS, verify permissions were granted
//       if (Platform.OS === 'ios' && permissions) {
//         const {camera, audio} = permissions as {camera: boolean, audio: boolean};
//         if (!camera || !audio) {
//           console.log('Missing required permissions:', {camera, audio});
//           setIsConnecting(false);
//           return; // Don't proceed if permissions not granted
//         }
//       }

//       console.log('Requesting access token for patient ID:', patientId);
//       const accessToken = await getAccessTokenById(patientId);

//       if (accessToken) {
//         console.log('Access token received, initiating connection');
//         initiateConnection(accessToken);
//       } else {
//         console.warn('Failed to get access token');
//         setIsConnecting(false);
//       }
//     } catch (error) {
//       console.error('Error in _connectCall:', error);
//       setIsConnecting(false);
//       Alert.alert('Connection Error', 'Failed to connect to the video call. Please try again.');
//     }
//   };

//   const _onMuteButtonPress = () => {
//     if (!twilioVideo?.current) return;
    
//     twilioVideo.current
//       .setLocalAudioEnabled(!isAudioEnabled)
//       .then((isEnabled: boolean) =>
//         setIsAudioEnabled(isEnabled),
//       )
//       .catch((err) => {
//         console.error('Error toggling audio:', err);
//       });
//   };

//   const _onFlipButtonPress = () => {
//     if (!twilioVideo?.current) return;
    
//     try {
//       twilioVideo.current.flipCamera();
//     } catch (err) {
//       console.error('Error flipping camera:', err);
//     }
//   };

//   const _videoOff = () => {
//     if (!twilioVideo?.current) return;
    
//     twilioVideo.current
//       .setLocalVideoEnabled(!isVideoEnabled)
//       .then((isEnabled: boolean) =>
//         setIsVideoEnabled(isEnabled),
//       )
//       .catch((err) => {
//         console.error('Error toggling video:', err);
//       });
//   };

//   const _onRoomDidConnect = ({roomSid, participants, localParticipant}: RoomConnectParams) => {
//     console.log('Room connected! Room SID:', roomSid);
//     console.log('participants:', JSON.stringify(participants));
//     console.log('Local localParticipant:', JSON.stringify(localParticipant));

//     // Skip updates if component is unmounted
//     if (!isMountedRef.current) return;

//     // IMPORTANT: Don't reset video tracks here to preserve any tracks that might have been added
//     // during the connection process

//     // Set connected status and room SID
//     setStatus('connected');
//     setRoomSid(roomSid);
//     setIsConnecting(false);

//     // Reset render attempts counter
//     renderAttempts.current = 0;

//     // Ensure video and audio are enabled after connection
//     if (Platform.OS === 'ios') {
//       // Use a small delay to ensure the connection is stable before publishing
//       setTimeout(() => {
//         if (twilioVideo?.current && isMountedRef.current) {
//           console.log('iOS: Publishing video and audio tracks');

//           // First ensure local video is enabled
//           twilioVideo.current.setLocalVideoEnabled(true)
//             .then(enabled => {
//               console.log('Local video enabled status:', enabled);

//               // Then publish the tracks
//               twilioVideo.current?.publishLocalVideo();
//               twilioVideo.current?.publishLocalAudio();

//               // Force a re-render of the local video
//               setIsVideoEnabled(true);

//               // Additional iOS-specific setup
//               console.log('iOS: Setting up additional track options');

//               // Log tracks after connection is established
//               logTrackState();

//               // If there are participants, log them again after publishing
//               if (participants && participants.length > 0) {
//                 console.log('Participants after publishing:', JSON.stringify(participants));
//               }
//             })
//             .catch(err => {
//               console.error('Error enabling local video after connection:', err);
//               // Still try to publish even if enabling fails
//               try {
//                 twilioVideo.current?.publishLocalVideo();
//                 twilioVideo.current?.publishLocalAudio();
//               } catch (pubErr) {
//                 console.error('Error publishing local tracks:', pubErr);
//               }
//             });
//         }
//       }, 1000);
//     } else {
//       // For Android, also check track state
//       setTimeout(() => {
//         logTrackState();
//       }, 2000);
//     }
//   };

//   const onStatsReceived = (data: Record<string, unknown>) => {
//     console.log('onStatsReceived', data);
//   };

//   const _onRoomDidDisconnect = ({error}: ErrorParams) => {
//     console.log('Room disconnected with error: ', error);

//     // Skip updates if component is unmounted
//     if (!isMountedRef.current) return;

//     // Store roomSid before clearing it for the API call
//     const currentRoomSid = roomSid;

//     // Clean up state
//     setStatus('disconnected');
//     setVideoTracks(new Map());

//     // Call the API to acknowledge the call has ended
//     if (currentRoomSid) {
//       callEndedAPI(currentRoomSid);
//     }

//     // Clear the roomSid after API call is initiated
//     setRoomSid(null);

//     // Make sure video and audio are stopped
//     if (twilioVideo?.current) {
//       try {
//         twilioVideo.current.setLocalAudioEnabled(false);
//         twilioVideo.current.setLocalVideoEnabled(false);
//       } catch (err) {
//         console.warn('Error disabling media after disconnect:', err);
//       }
//     }

//     // Show error to user if appropriate
//     if (error && typeof error === 'string' && error !== 'Disconnected' && isMountedRef.current) {
//       Alert.alert('Call Disconnected', 'The video call was disconnected. ' + error);
//     }
//   };

//   const _onRoomDidFailToConnect = ({error}: ErrorParams) => {
//     console.log('Room failed to connect with error: ', error);
    
//     // Skip updates if component is unmounted
//     if (!isMountedRef.current) return;
    
//     setStatus('disconnected');
//     setRoomSid(null);
//     setIsConnecting(false);

//     // Show error to user
//     if (isMountedRef.current) {
//       Alert.alert(
//         'Connection Failed',
//         'Failed to connect to the video call. Please try again.',
//         [{ text: 'OK' }]
//       );
//     }
//   };

//   const _onParticipantAddedVideoTrack = ({
//     participant,
//     track,
//   }: TrackEventParams) => {
//     // Skip updates if component is unmounted
//     if (!isMountedRef.current) return;
    
//     console.log(`Participant ${participant.identity || participant.sid} added video track ${track.trackSid}`);
//     console.log('Track details:', JSON.stringify(track));
//     console.log('Participant details:', JSON.stringify(participant));

//     // Update video tracks with the new participant's track - FIXED VERSION
//     setVideoTracks(prevTracks => {
//       // Create a new Map from the previous tracks
//       const newTracks = new Map(prevTracks);

//       // Add the new track if it doesn't exist
//       if (!newTracks.has(track.trackSid)) {
//         const trackIdentifier = {
//           participantSid: participant.sid,
//           videoTrackSid: track.trackSid
//         };

//         console.log('Adding track with identifier:', JSON.stringify(trackIdentifier));
//         newTracks.set(track.trackSid, trackIdentifier);
//         console.log('Added new track to video tracks map');
//       } else {
//         console.log('Track ID already exists in video tracks map');
//       }

//       // Log the current state of tracks
//       console.log(`Current video tracks count: ${newTracks.size}`);
      
//       return newTracks;
//     });

//     // Log track state after update
//     setTimeout(() => {
//       logTrackState();
//     }, 500);

//     // Force a re-render after a short delay to ensure the track is properly displayed
//     // This helps with iOS rendering issues
//     if (Platform.OS === 'ios') {
//       setTimeout(() => {
//         if (isMountedRef.current) {
//           console.log('Forcing re-render of video tracks on iOS');
//           setVideoTracks(prevTracks => new Map(prevTracks));
//         }
//       }, 1000);
//     }
//   };

//   const _onParticipantRemovedVideoTrack = ({
//     participant,
//     track,
//   }: TrackEventParams) => {
//     // Skip updates if component is unmounted
//     if (!isMountedRef.current) return;
    
//     console.log('onParticipantRemovedVideoTrack: ', participant, track);

//     setVideoTracks(prevTracks => {
//       // Create a new Map from the previous tracks
//       const newTracks = new Map(prevTracks);

//       // Remove the track if it exists
//       if (newTracks.has(track.trackSid)) {
//         newTracks.delete(track.trackSid);
//         console.log('Removed track', track.trackSid);
//       }

//       // Log the updated tracks
//       console.log(`Updated video tracks count: ${newTracks.size}`);

//       return newTracks;
//     });

//     // Log track state after update
//     setTimeout(() => {
//       logTrackState();
//     }, 500);
//   };

//   const _onNetworkLevelChanged = ({
//     participant,
//     isLocalUser,
//     quality,
//   }: NetworkQualityParams) => {
//     console.log(
//       'Participant',
//       participant,
//       'isLocalUser',
//       isLocalUser,
//       'quality',
//       quality,
//     );
//   };

//   const _onDominantSpeakerDidChange = ({
//     roomName,
//     roomSid,
//     participant,
//   }: DominantSpeakerParams) => {
//     console.log(
//       'onDominantSpeakerDidChange',
//       `roomName: ${roomName}`,
//       `roomSid: ${roomSid}`,
//       'participant:',
//       participant,
//     );
//   };

//   return (
//     <View style={styles.container}>
//       <StatusBar barStyle="dark-content" backgroundColor="white" />
//       <Loader modalVisible={loader} />

//       {/* Status bar with connection status */}
//       <View style={styles.statusBar}>
//         <Text style={styles.welcome}>
//           {status === 'connected' ? 'Connected' :
//            status === 'connecting' ? 'Connecting...' :
//            'Ready to Connect'}
//         </Text>
//         {status === 'connected' && (
//           <View style={styles.connectionIndicator}>
//             <View style={styles.connectionDot} />
//           </View>
//         )}
//       </View>

//       <View style={styles.callContainer}>
//         {status === 'connected' && (
//           <View style={styles.remoteGrid}>
//             {videoTracks.size > 0 ? (
//               Array.from(videoTracks, ([trackSid, trackIdentifier]) => {
//                 console.log('Rendering track:', trackSid, JSON.stringify(trackIdentifier));
//                 return (
//                   <View
//                     key={trackSid}
//                     style={styles.remoteVideoContainer}>
//                     <TwilioVideoParticipantView
//                       style={styles.remoteVideo}
//                       trackIdentifier={trackIdentifier}
//                       scaleType={'fill'}
//                       applyZOrder={Platform.OS === 'android'} // Only apply zOrder on Android
//                     />
//                     {/* Subtle overlay for better UI */}
//                     <View style={styles.videoOverlay} />
//                   </View>
//                 );
//               })
//             ) : (
//               <View style={styles.waitingContainer}>
//                 <MaterialIcons name="person-outline" size={60} color={Colors.InnearColor} />
//                 <Text style={styles.waitingText}>Waiting for patient to join...</Text>
//                 <Text style={styles.waitingSubtext}>The call is connected and ready</Text>
//               </View>
//             )}
//           </View>
//         )}

//         {status === 'connecting' && (
//           <View style={styles.waitingContainer}>
//             <MaterialIcons name="connect-without-contact" size={60} color={Colors.InnearColor} />
//             <Text style={styles.waitingText}>Connecting to call...</Text>
//             <Text style={styles.waitingSubtext}>Please wait while we establish the connection</Text>
//           </View>
//         )}

//         {status === 'disconnected' && (
//           <View style={styles.waitingContainer}>
//             <MaterialIcons name="videocam" size={60} color={Colors.InnearColor} />
//             <Text style={styles.waitingText}>Ready to start video call</Text>
//             <Text style={styles.waitingSubtext}>Press the green button below to connect</Text>

//             {/* Call button in the center of the screen */}
//             <TouchableOpacity
//               style={styles.centerCallButton}
//               onPress={_connectCall}
//               disabled={isConnecting}
//             >
//               <MaterialIcons name="call" size={36} color="white" />
//             </TouchableOpacity>
//           </View>
//         )}

//         {/* Local video view with frame - only show when connected or connecting */}
//         {(status === 'connected' || status === 'connecting') && (
//           <View style={styles.localVideoContainer}>
//             <View style={styles.localVideoFrame}>
//               <TwilioVideoLocalView
//                 enabled={isVideoEnabled}
//                 style={styles.localVideo}
//                 applyZOrder={true}
//               />
//             </View>
//           </View>
//         )}
//       </View>

//       {/* Controls container */}
//       <View style={styles.optionsContainer}>
//         {/* Mute/Unmute button */}
//         <TouchableOpacity
//           style={[styles.controlButton, isAudioEnabled ? styles.activeButton : styles.inactiveButton]}
//           onPress={_onMuteButtonPress}
//           disabled={status !== 'connected'}
//         >
//           <MaterialIcons
//             name={isAudioEnabled ? "mic" : "mic-off"}
//             size={28}
//             color={isAudioEnabled ? "white" : Colors.InnearColor}
//           />
//         </TouchableOpacity>

//         {/* Flip camera button */}
//         <TouchableOpacity
//           style={[styles.controlButton, styles.activeButton]}
//           onPress={_onFlipButtonPress}
//           disabled={status !== 'connected'}
//         >
//           <Ionicons name="camera-reverse-outline" size={28} color="white" />
//         </TouchableOpacity>

//         {/* Video on/off button */}
//         <TouchableOpacity
//           style={[styles.controlButton, isVideoEnabled ? styles.activeButton : styles.inactiveButton]}
//           onPress={_videoOff}
//           disabled={status !== 'connected'}
//         >
//           <FontAwesome5
//             name={isVideoEnabled ? "video" : "video-slash"}
//             size={24}
//             color={isVideoEnabled ? "white" : Colors.InnearColor}
//           />
//         </TouchableOpacity>

//         {/* Connect/Disconnect call button */}
//         <TouchableOpacity
//           style={[styles.controlButton,
//             status === 'connected' || status === 'connecting'
//               ? styles.endCallButton
//               : styles.startCallButton]}
//           onPress={
//             status === 'connected' || status === 'connecting'
//               ? _onEndButtonPress
//               : _connectCall
//           }
//           disabled={isConnecting && status === 'disconnected'} // Disable while connecting
//         >
//           <MaterialIcons
//             name={status === 'connected' || status === 'connecting' ? "call-end" : "videocam"}
//             size={28}
//             color="white"
//           />
//         </TouchableOpacity>
//       </View>

//       <TwilioVideo
//         ref={twilioVideo}
//         autoInitializeCamera={true}
//         onRoomDidConnect={_onRoomDidConnect}
//         onRoomDidDisconnect={_onRoomDidDisconnect}
//         onRoomDidFailToConnect={_onRoomDidFailToConnect}
//         onParticipantAddedVideoTrack={_onParticipantAddedVideoTrack}
//         onParticipantRemovedVideoTrack={_onParticipantRemovedVideoTrack}
//         onNetworkQualityLevelsChanged={_onNetworkLevelChanged}
//         onDominantSpeakerDidChange={_onDominantSpeakerDidChange}
//         onStatsReceived={onStatsReceived}
//         onCameraDidStart={() => console.log('Camera started')}
//         onCameraDidStopRunning={() => console.log('Camera stopped running')}
//         onCameraWasInterrupted={() => console.log('Camera was interrupted')}
//       />
//     </View>
//   );
// };

// export default Example;

// // Get screen dimensions for layout calculations
// const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: 'white',
//   },
//   callContainer: {
//     flex: 1,
//     marginTop: 40, // Leave space for status bar
//   },
//   statusBar: {
//     height: 40,
//     backgroundColor: Colors.InnearColor,
//     flexDirection: 'row',
//     alignItems: 'center',
//     justifyContent: 'center',
//     paddingHorizontal: 15,
//   },
//   welcome: {
//     fontSize: 16,
//     color: 'white',
//     fontWeight: '600',
//     textAlign: 'center',
//   },
//   connectionIndicator: {
//     marginLeft: 8,
//     justifyContent: 'center',
//   },
//   connectionDot: {
//     width: 8,
//     height: 8,
//     borderRadius: 4,
//     backgroundColor: '#4CAF50', // Green dot for connected status
//   },
//   waitingContainer: {
//     flex: 1,
//     justifyContent: 'center',
//     alignItems: 'center',
//     padding: 20,
//   },
//   waitingText: {
//     fontSize: 20,
//     fontWeight: '600',
//     color: Colors.InnearColor,
//     marginTop: 20,
//     textAlign: 'center',
//   },
//   waitingSubtext: {
//     fontSize: 16,
//     color: '#666',
//     marginTop: 10,
//     marginBottom: 30,
//     textAlign: 'center',
//   },
//   centerCallButton: {
//     width: 80,
//     height: 80,
//     borderRadius: 40,
//     backgroundColor: '#4CAF50',
//     justifyContent: 'center',
//     alignItems: 'center',
//     marginTop: 20,
//     // Add shadow for iOS
//     shadowColor: '#000',
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.3,
//     shadowRadius: 4,
//     // Add elevation for Android
//     elevation: 5,
//   },
//   localVideoContainer: {
//     position: 'absolute',
//     right: 16,
//     bottom: 100,
//     width: 120,
//     height: 160,
//     borderRadius: 12,
//     overflow: 'hidden',
//     // Add shadow for iOS
//     shadowColor: '#000',
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.3,
//     shadowRadius: 4,
//     // Add elevation for Android
//     elevation: 5,
//     zIndex: 10,
//   },
//   localVideoFrame: {
//     flex: 1,
//     borderWidth: 2,
//     borderColor: Colors.InnearColor,
//     borderRadius: 12,
//     overflow: 'hidden',
//   },
//   localVideo: {
//     width: '100%',
//     height: '100%',
//   },
//   remoteGrid: {
//     flex: 1,
//     flexDirection: 'row',
//   },
//   remoteVideoContainer: {
//     flex: 1,
//     justifyContent: 'center',
//     alignItems: 'center',
//     position: 'relative',
//     backgroundColor: '#f5f5f5',
//   },
//   remoteVideo: {
//     width: screenWidth,
//     height: screenHeight * 0.75,
//     borderRadius: 0,
//     backgroundColor: '#f5f5f5', // Add background color to help with rendering
//     ...(Platform.OS === 'ios' ? {
//       // iOS-specific styles to help with rendering
//       overflow: 'hidden',
//       // Force hardware acceleration
//       backfaceVisibility: 'hidden',
//       // Use opacity to force composition layer
//       opacity: 0.99
//     } : {}),
//   },
//   videoOverlay: {
//     position: 'absolute',
//     bottom: 0,
//     left: 0,
//     right: 0,
//     height: 80,
//     backgroundColor: 'rgba(0,0,0,0.1)',
//   },
//   optionsContainer: {
//     position: 'absolute',
//     bottom: 0,
//     height: 80,
//     backgroundColor: Colors.InnearColor,
//     flexDirection: 'row',
//     alignItems: 'center',
//     width: '100%',
//     justifyContent: 'space-evenly',
//     paddingHorizontal: 20,
//   },
//   controlButton: {
//     width: 56,
//     height: 56,
//     borderRadius: 28,
//     justifyContent: 'center',
//     alignItems: 'center',
//   },
//   activeButton: {
//     backgroundColor: Colors.InnearColor,
//   },
//   inactiveButton: {
//     backgroundColor: 'white',
//     borderWidth: 1,
//     borderColor: Colors.InnearColor,
//   },
//   startCallButton: {
//     backgroundColor: '#4CAF50', // Green for start call
//   },
//   endCallButton: {
//     backgroundColor: '#F44336', // Red for end call
//   },
// });