export interface MonthlyStatsData {
    success: boolean;
    patientStats: {
      totalPatients: number;
      totalActivePatients: number;
      totalInActivePatients: number;
      newPatients: number;
      ccmPatients: number;
      rpmPatients: number;
      daysReadingCaptured: number;
      pcmPatients: number;
      growth: number;
    };
    minsStats: {
      totalPatients: number;
      rpm20Minless: number;
      rpm40Minless: number;
      rpm60Minless: number;
      rpm60Plus: number;
      ccm20Minless: number;
      ccm40Minless: number;
      ccm60Minless: number;
      ccm60Plus: number;
      pcm30Minless: number;
      pcm60Minless: number;
      pcm60Plus: number;
      daysMeasured: number;
      exceeded16Days: number;
    };
  }
  
export interface PatientSummaryData {
    patientId: string;
    patientCreatedDate: string;
    clinician: {
      phoneNumber: string;
      available: boolean;
      firstName: string;
      lastName: string;
      email: string;
      clinicianId: string;
    };
    mrn: string;
    vitalVO: {
      weight: string;
      weightDate: string;
      temperature: string;
      temperatureDate: string;
      stepCount: string;
      stepCountDate: string;
      heartRate: string;
      heartRateDate: string;
    };
    latestVitals: {
      [key: string]: {
        min: number;
        max: number;
        values: Array<{
          datetime: string;
          value: number;
        }>;
      };
    };
    firstName: string;
    lastName: string;
    status: string;
    physicianName: string;
    physicianEmail: string;
    physicianPhone: string;
    chronicConditions: Array<{
      chonicId: string;
      chronicConditionName: string;
      icdCode: string;
    }>;
    email: string;
    rpmMins: number;
    ccmMins: number;
    pcmMins: number;
    dateOfBirth: string;
    daysMeasured: number;
    groupName: string;
    programs: {
      selectedPrograms: Array<{
        mins: number;
        programName: string;
        programActivated: boolean;
        patientProgramId: string;
        programId: string;
      }>;
    };
    gender: string;
    patientAddress: string;
    assignedDevices: Array<{
      assignedWatch: string;
      imei: string;
      deviceType: string;
    }>;
  }
  