import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useDispatch } from 'react-redux';

import PatientChatDetailsScreen from '@/screens/PatientChatDetails/index';
import { setCurrentPatientId } from '@/services/actions/patientActions';
import { fetchPatientsList } from '@/services/actions/patientActions';
import { useSelector } from 'react-redux';
import Loader from '@/components/Loader/Loader';
import Text from '@/components/Text';
import { Colors } from '@/constants';

export default function PatientChatRoute() {
  const { id } = useLocalSearchParams();
  const dispatch = useDispatch();
  const router = useRouter();
  
  const [isValidating, setIsValidating] = useState(true);
  const [validDeviceType, setValidDeviceType] = useState<string | null>(null);

  // Get patients list from Redux (this is where the patient data is stored)
  const patientsList = useSelector(
    (state: any) => state?.patientsListReducer?.data
  );

  // Get current patient ID from Redux
  const currentPatientId = useSelector(
    (state: any) => state?.currentPatientIdReducer?.patientId
  );

  // Get caregiver and org info for fetching patients list
  const caregiverId = useSelector(
    (state: any) => state?.loginReducer?.data?.userId
  );
  const orgId = useSelector(
    (state: any) => state?.currentOrgIdReducer?.orgId
  );

  // Get loading state
  const isLoading = useSelector(
    (state: any) => state?.patientsListReducer?.isFetching
  );

  useEffect(() => {
    if (id) {
      console.log('Chat Screen: Setting current patient ID:', id);
      dispatch(setCurrentPatientId(id as string));
    }
  }, [id, dispatch]);

  // Fetch patients list if not available and we have required data
  useEffect(() => {
    if (caregiverId && orgId && (!patientsList || patientsList.length === 0)) {
      console.log('Chat Screen: Fetching patients list with caregiverId:', caregiverId, 'orgId:', orgId);
      dispatch(fetchPatientsList({ careGiverId: caregiverId, orgId: orgId }));
    }
  }, [caregiverId, orgId, patientsList, dispatch]);

  // Device type validation - only run when we have patient data
  useEffect(() => {
    if (!id || isLoading) {
      return;
    }

    console.log('Chat Screen: Starting device validation for patient:', id);
    console.log('Chat Screen: Patients list length:', patientsList?.length || 0);
    
    if (!patientsList || patientsList.length === 0) {
      console.log('Chat Screen: No patients list available, still loading...');
      return;
    }

    // Find the current patient in the patients list
    const currentPatient = patientsList.find((p: any) => 
      p.patientId === Number(id) || p.patientId === id || p.patientId?.toString() === id
    );

    console.log('Chat Screen: Found patient:', currentPatient?.patientName || 'Not found');
    console.log('Chat Screen: Patient connecting device:', currentPatient?.connectingDevice);

    let connectingDevice = currentPatient?.connectingDevice;

    // Validate device type
    if (connectingDevice) {
      const deviceType = connectingDevice.toLowerCase().trim();
      if (deviceType === 'watch' || deviceType === 'mobile' || 
          deviceType.includes('watch') || deviceType.includes('mobile')) {
        const normalizedType = deviceType.includes('watch') ? 'watch' : 'mobile';
        console.log('Chat Screen: Device validated successfully:', normalizedType);
        setValidDeviceType(normalizedType);
        setIsValidating(false);
        return;
      }
    }

    // If we reach here, validation failed
    console.error('Chat Screen: Invalid or missing device type:', connectingDevice);
    setIsValidating(false);
    
    Alert.alert(
      'Invalid Device Configuration',
      'This patient does not have a valid device type configured for chat. Please contact support.',
      [
        {
          text: 'Go Back',
          onPress: () => router.back(),
          style: 'default'
        }
      ],
      { cancelable: false }
    );
  }, [id, patientsList, isLoading, router]);

  // Timeout handling - if data doesn't load within 10 seconds
  useEffect(() => {
    if (!id) return;

    const timeout = setTimeout(() => {
      if (isValidating && (!patientsList || patientsList.length === 0)) {
        console.error('Chat Screen: Timeout waiting for patient data');
        setIsValidating(false);
        
        Alert.alert(
          'Unable to Load Patient Data',
          'Patient information could not be loaded. Please try again.',
          [
            {
              text: 'Go Back',
              onPress: () => router.back(),
              style: 'default'
            }
          ],
          { cancelable: false }
        );
      }
    }, 10000); // 10 second timeout

    return () => clearTimeout(timeout);
  }, [id, isValidating, patientsList, router]);

  // Show loading while validating
  if (isValidating) {
    return (
      <SafeAreaView style={styles.container} edges={['left', 'right']}>
        <Loader modalVisible={true} />
      </SafeAreaView>
    );
  }

  // Show error if no valid device type
  if (!validDeviceType) {
    return (
      <SafeAreaView style={styles.container} edges={['left', 'right']}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Invalid Device Configuration</Text>
          <Text style={styles.errorMessage}>
            This patient does not have a valid device type configured for chat.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Render chat screen with validated device type
  return (
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      <PatientChatDetailsScreen validatedDeviceType={validDeviceType} />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.DarkJungleGreen,
    textAlign: 'center',
    marginBottom: 16,
  },
  errorMessage: {
    fontSize: 14,
    color: Colors.GrayBlue,
    textAlign: 'center',
    lineHeight: 20,
  },
});
