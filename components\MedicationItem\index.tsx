// Navigation not needed for this component
import React, {memo, useCallback, useState} from 'react';
import {
  Alert,
  Image,
  Modal,
  StyleSheet,
  TouchableOpacity,
  View,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {Colors} from '@/constants';
import {useTheme} from '@/constants/Theme';
import {MedicationsProps} from '@/models';
import {apiPostWithToken} from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import Theme from '@/constants/Theme';
import GenericModal from '../SuccessFailModal/GenericModal';
import Text from '../Text';

interface Props extends MedicationsProps {
  onDelete(): void;
  onEdit?: () => void;
}

const MedicationItem = memo((props: Props) => {
  useTheme(); // Keep the hook call to avoid breaking dependencies
  const [visibleModal, setVisibleModal] = useState(false);
  const [scaleAnim] = useState(new Animated.Value(1));

  // Animation for touch feedback
  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.98,
      friction: 8,
      tension: 100,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 8,
      tension: 100,
      useNativeDriver: true,
    }).start();
  };

  const onPress = useCallback(() => {
    console.log('Clicked Medication');
    // Just show details, don't show delete modal immediately
  }, []);

  const onDeletePress = useCallback(() => {
    setVisibleModal(true);
  }, []);

  const open = () => {
    console.log('Delete medication. API call', props.medicineId);
    deletMedicationById();
    setVisibleModal(false);
  };

  const close = () => {
    setVisibleModal(false);
    console.log('Delete medication... Cancel');
  };

  const deletMedicationById = async () => {
    const response = await apiPostWithToken(
      {itemId: props.medicineId},
      URLS.caregiverUrl + 'deleteMedication',
    );
    if (response?.status == 200) {
      console.log('Deleted medication.');
      props.onDelete();
    } else {
      const errorMessage = response?.response?.data?.responseMessage
        ? response?.response?.data?.responseMessage
        : response.message === 'Network Error'
        ? 'Network error. Please check your data connection.'
        : response.message;
      Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
    }
  };

  // Generate medication type icon based on medicineForm
  const getMedicationTypeIcon = () => {
    switch (props.medicineForm) {
      case '1': return 'medical-outline';
      case '2': return 'medical-outline';
      case '3': return 'fitness-outline';
      case '4': return 'water-outline';
      case '5': return 'flask-outline';
      default: return 'medical-outline';
    }
  };

  // Format days display
  const formatDays = (daysString: string) => {
    if (!daysString) return 'No schedule';
    
    const days = daysString.split('|');
    if (days.length === 7) {
      return 'Every day';
    }
    
    const dayMap: { [key: string]: string } = {
      'Su': 'Sun', 'Mo': 'Mon', 'Tu': 'Tue', 'We': 'Wed',
      'Th': 'Thu', 'Fr': 'Fri', 'Sa': 'Sat'
    };
    
    return days.map(day => dayMap[day] || day).join(', ');
  };

  // Format time slots display with quantities
  const formatTimeSlots = (timeSlotsString: string) => {
    if (!timeSlotsString) return 'No schedule';
    
    const slots = timeSlotsString.split('|');
    const slotMap: { [key: string]: string } = {
      'EarlyMorning': 'Early morning',
      'Breakfast': 'Breakfast',
      'Lunch': 'Lunch',
      'AfternoonSnack': 'Afternoon',
      'Dinner': 'Dinner',
      'Bed': 'Bedtime'
    };
    
    return slots.map(slot => slotMap[slot] || slot).join(', ');
  };

  // Format time slots with quantities
  const formatTimeSlotsWithQuantities = (timeSlotsString: string, quantitiesString: string) => {
    if (!timeSlotsString) return 'No schedule';
    
    const slots = timeSlotsString.split('|');
    const quantities = quantitiesString ? quantitiesString.split('|') : [];
    
    const slotMap: { [key: string]: string } = {
      'EarlyMorning': 'Early morning',
      'Breakfast': 'Breakfast',
      'Lunch': 'Lunch',
      'AfternoonSnack': 'Afternoon',
      'Dinner': 'Dinner',
      'Bed': 'Bedtime'
    };
    
    return slots.map((slot, index) => {
      const slotName = slotMap[slot] || slot;
      const quantity = quantities[index] || '1';
      return `${slotName} (${quantity})`;
    }).join(', ');
  };

  // Calculate total times per day
  const getTotalTimesPerDay = (timeSlotsString: string) => {
    if (!timeSlotsString) return 0;
    return timeSlotsString.split('|').length;
  };

  // Calculate number of days
  const getNumberOfDays = (daysString: string) => {
    if (!daysString) return 0;
    const days = daysString.split('|');
    return days.length;
  };

  // Calculate total doses information
  const getDosageInfo = () => {
    const numDays = getNumberOfDays(props.daysOfWeek);
    const timesPerDay = getTotalTimesPerDay(props.timeSlots);
    
    if (numDays === 7) {
      return `Every day • ${timesPerDay} times daily`;
    } else if (numDays > 0) {
      return `${numDays} days • ${timesPerDay} times daily`;
    }
    return 'No schedule';
  };

  return (
    <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
      <TouchableOpacity
        activeOpacity={0.9}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onPress={onPress}
        style={styles.medicationCard}>
        
        {/* Left side - Medication photo */}
        <View style={styles.photoSection}>
          {props.image ? (
            <Image
              style={styles.medicationImage}
              source={{uri: props.image}}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.placeholderImage}>
              <Ionicons name={getMedicationTypeIcon() as any} size={24} color={Colors.GrayBlue} />
            </View>
          )}
        </View>

        {/* Center - Medication information */}
        <View style={styles.infoSection}>
          {/* Medicine name with type indicator */}
          <View style={styles.nameRow}>
            <Text size={16} lineHeight={20} bold color={Colors.DarkJungleGreen} numberOfLines={1}>
              {props.medicineName}
            </Text>
            <Ionicons 
              name={getMedicationTypeIcon() as any} 
              size={16} 
              color={Colors.TealBlue} 
              style={styles.typeIndicator}
            />
          </View>

          {/* Dosage and color */}
          <Text size={13} lineHeight={17} color={Colors.GrayBlue} marginTop={2} numberOfLines={1}>
            {props.strength} • {props.color || 'No color specified'}
          </Text>

          {/* Frequency info - Days and total times */}
          <View style={styles.scheduleRow}>
            <Ionicons name="repeat-outline" size={14} color={Colors.TealBlue} />
            <Text size={12} lineHeight={16} color={Colors.DarkJungleGreen} marginLeft={4} numberOfLines={1}>
              {getDosageInfo()}
            </Text>
          </View>

          {/* Days schedule - Only show for specific days, not "Every day" */}
          {getNumberOfDays(props.daysOfWeek) !== 7 && (
            <View style={styles.scheduleRow}>
              <Ionicons name="calendar-outline" size={14} color={Colors.TealBlue} />
              <Text size={12} lineHeight={16} color={Colors.DarkJungleGreen} marginLeft={4} numberOfLines={1}>
                {formatDays(props.daysOfWeek)}
              </Text>
            </View>
          )}

          {/* Time schedule */}
          <View style={styles.scheduleRow}>
            <Ionicons name="time-outline" size={14} color={Colors.TealBlue} />
            <Text size={12} lineHeight={16} color={Colors.DarkJungleGreen} marginLeft={4} numberOfLines={1}>
              {formatTimeSlotsWithQuantities(props.timeSlots, props.quantities)}
            </Text>
          </View>
        </View>

        {/* Right side - Edit and Delete buttons */}
        <View style={styles.actionSection}>
          {props.onEdit && (
            <TouchableOpacity 
              style={styles.editButton}
              onPress={props.onEdit}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons name="create-outline" size={18} color={Colors.TealBlue} />
            </TouchableOpacity>
          )}
          <TouchableOpacity 
            style={styles.deleteButton}
            onPress={onDeletePress}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="trash-outline" size={18} color={Colors.Red} />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>

      <Modal
        visible={visibleModal}
        onRequestClose={close}
        transparent
        animationType={'slide'}>
        <GenericModal
          close={close}
          open={open}
          title="Delete Medication"
          confirmText="Delete"
          cancelText="Cancel"
          message={'Are you sure you want to delete ' + props.medicineName + '?'}
          icon={<Ionicons name="trash-outline" size={60} color={Colors.Red} style={{marginBottom: 10}} />}
        />
      </Modal>
    </Animated.View>
  );
});

export default MedicationItem;

const styles = StyleSheet.create({
  medicationCard: {
    backgroundColor: Colors.White,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    ...Theme.shadow,
    borderWidth: 1,
    borderColor: Colors.Snow,
  },
  photoSection: {
    marginRight: 16,
  },
  medicationImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: Colors.Snow,
  },
  placeholderImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: Colors.Snow,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.Platinum,
    borderStyle: 'dashed',
  },

  infoSection: {
    flex: 1,
    paddingRight: 12,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  typeIndicator: {
    marginLeft: 8,
  },
  scheduleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
  },
  actionSection: {
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  editButton: {
    width: 36,
    height: 36,
    borderRadius: 8,
    backgroundColor: Colors.Snow,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.Platinum,
  },
  deleteButton: {
    width: 36,
    height: 36,
    borderRadius: 8,
    backgroundColor: Colors.Snow,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.Platinum,
  },
});

