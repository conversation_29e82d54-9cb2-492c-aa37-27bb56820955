import Text from "@/components/Text";
import { Colors } from "@/constants";
import { apiPostWithToken } from "@/services/apis/apiManager";
import URLS from "@/services/config/config";
import { MaterialIcons, Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import { useIsFocused, useNavigation } from "@react-navigation/native";
import { useRouter } from "expo-router";
import Moment from "moment";
import React, { useEffect, useState, createContext, useContext } from "react";
import { Alert, StyleSheet, TouchableOpacity, View, Animated } from "react-native";
import { useSelector } from "react-redux";
import { LinearGradient } from "expo-linear-gradient";

// Create context for alerts data
interface AlertsContextType {
  hasCritical: boolean;
  critical: number;
  hasAlarm: boolean;
  alarm: number;
  hasWarning: boolean;
  warning: number;
  hasInfo: boolean;
  info: number;
}

const AlertsContext = createContext<AlertsContextType>({
  hasCritical: false,
  critical: 0,
  hasAlarm: false,
  alarm: 0,
  hasWarning: false,
  warning: 0,
  hasInfo: false,
  info: 0,
});

export const useAlertsData = () => useContext(AlertsContext);

export const AlertsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [hasCritical, setHasCritical] = useState(false);
  const [hasAlarm, setHasAlarm] = useState(false);
  const [hasWarning, setHasWarning] = useState(false);
  const [hasInfo, setHasInfo] = useState(false);

  const [critical, setCritical] = useState(0);
  const [alarm, setAlarm] = useState(0);
  const [warning, setWarning] = useState(0);
  const [info, setInfo] = useState(0);

  const caregiverId = useSelector(
    (state: any) => state?.loginReducer?.data?.userId
  );

  // Use the same date range as in the Critical Alerts screen (last 7 days)
  const today = new Date();
  const startDate = Moment(today).add(-7, "days").format("YYYY-MM-DD");
  const currentDate = Moment(today).format("YYYY-MM-DD");
  const isFocused = useIsFocused();
  const navigation = useNavigation();
  const orgId = useSelector((state: any) => state?.currentOrgIdReducer?.orgId);
  
  // Listen to current patient ID changes to detect navigation to/from patient profiles
  const currentPatientId = useSelector(
    (state: any) => state?.currentPatientIdReducer?.patientId
  );

  // Refresh alerts when screen is focused
  useEffect(() => {
    if (isFocused) getAlertsCount();
  }, [isFocused]);

  // Also refresh alerts when navigation state changes (user returns from patient profile)
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      getAlertsCount();
    });

    return unsubscribe;
  }, [navigation]);

  // Refresh alerts when patient context changes (navigation to/from patient profiles)
  useEffect(() => {
    // Only refresh when returning to home (currentPatientId becomes 0 or undefined)
    if (isFocused && (!currentPatientId || currentPatientId === 0)) {
      getAlertsCount();
    }
  }, [currentPatientId, isFocused]);

  // Set up a periodic refresh to ensure alerts are always current (every 30 seconds when focused)
  useEffect(() => {
    if (!isFocused) return;

    const interval = setInterval(() => {
      getAlertsCount();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [isFocused]);

  const getAlertsCount = async () => {
    try {
      const params = {
        careGiverId: caregiverId,
        startDate: startDate,
        endDate: currentDate,
        orgId: orgId,
      };

      const response = await apiPostWithToken(
        params,
        URLS.caregiverUrl + "getAlertsCount"
      );

      if (response?.status === 200) {
        const alerts = response?.data?.data || [];
        
        console.log(`Total alerts received: ${alerts.length}`);
        
        // Reset all states first
        setHasCritical(false);
        setHasAlarm(false);
        setHasWarning(false);
        setHasInfo(false);
        setCritical(0);
        setAlarm(0);
        setWarning(0);
        setInfo(0);
        
        if (alerts.length > 0) {
          // Count alerts by type - exactly as done in the Critical Alerts screen
          const criticalAlerts = alerts.filter((alert: any) => alert.alertType === 'Critical');
          const alarmAlerts = alerts.filter((alert: any) => alert.alertType === 'Alarm');
          const warningAlerts = alerts.filter((alert: any) => alert.alertType === 'Warning');
          const infoAlerts = alerts.filter((alert: any) => alert.alertType === 'Info');
          
          console.log(`Filtered counts: Critical=${criticalAlerts.length}, Alarm=${alarmAlerts.length}, Warning=${warningAlerts.length}, Info=${infoAlerts.length}`);
          
          // Update state based on filtered counts
          if (criticalAlerts.length > 0) {
            setHasCritical(true);
            setCritical(criticalAlerts.length);
          }
          
          if (alarmAlerts.length > 0) {
            setHasAlarm(true);
            setAlarm(alarmAlerts.length);
          }
          
          if (warningAlerts.length > 0) {
            setHasWarning(true);
            setWarning(warningAlerts.length);
          }
          
          if (infoAlerts.length > 0) {
            setHasInfo(true);
            setInfo(infoAlerts.length);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching alerts count:", error);
      Alert.alert("Error", "Failed to load alerts data");
    }
  };

  const value = {
    hasCritical,
    critical,
    hasAlarm,
    alarm,
    hasWarning,
    warning,
    hasInfo,
    info,
  };

  return (
    <AlertsContext.Provider value={value}>
      {children}
    </AlertsContext.Provider>
  );
};

const WatchRxStatics = () => {
  // This component now just provides the context, doesn't render anything
  return null;
};

export default WatchRxStatics;

const styles = StyleSheet.create({
  // Keep styles for potential future use, but component currently renders nothing
  container: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 8,
  },
});
