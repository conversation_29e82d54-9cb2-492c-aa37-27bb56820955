import Moment from 'moment';
import React, { useState } from 'react';
import {Platform, StyleSheet, TouchableOpacity, View, Dimensions, Text as RNText} from 'react-native';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import {Ionicons} from '@expo/vector-icons';
import {Colors} from '@/constants';
import { ThemedText as Text } from '../ThemedText';

type Props = {
  date?: Date;
  onDateChange: (date: Date) => void;
  maxDate?: Date;
  label?: string;
};

const AlertsDatePicker = ({ date, onDateChange, maxDate, label }: Props) => {
  // Get screen width for responsive sizing
  const screenWidth = Dimensions.get('window').width;
  const [isVisible, setIsVisible] = useState(false);

  const showDatePicker = () => {
    setIsVisible(true);
  };

  const hideDatePicker = () => {
    setIsVisible(false);
  };

  const handleConfirm = (date: Date) => {
    setIsVisible(false);
    onDateChange(date);
  };

  // Format the date for display - adjust format based on screen size
  const getDateFormat = () => {
    if (screenWidth < 320) return 'MM/DD/YY';
    if (screenWidth < 375) return 'MM/DD/YYYY';
    return 'MMM DD, YYYY';
  };
  
  const formattedDate = date ? Moment(date).format(getDateFormat()) : 'Select date';

  return (
    <View style={styles.container}>
      {label && (
        <Text style={styles.label}>{label}</Text>
      )}
      <TouchableOpacity
        style={styles.pickerButton}
        onPress={showDatePicker}
        activeOpacity={0.7}
      >
        <View style={styles.dateTextContainer}>
          <RNText 
            style={styles.dateText}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {formattedDate}
          </RNText>
        </View>
        <View style={styles.iconContainer}>
          <Ionicons name="calendar-outline" size={20} color={Colors.TealBlue} />
        </View>
        <DateTimePickerModal
          isVisible={isVisible}
          mode="date"
          date={date}
          onConfirm={handleConfirm}
          onCancel={hideDatePicker}
          maximumDate={maxDate || new Date()}
          display={Platform.OS === 'android' ? 'default' : 'inline'}
          themeVariant="light"
          isDarkModeEnabled={false}
          textColor="#000000"
          accentColor={Colors.TealBlue}
          buttonTextColorIOS={Colors.TealBlue}
          confirmTextIOS="Confirm"
          cancelTextIOS="Cancel"
          pickerContainerStyleIOS={{
            alignItems: 'center', 
            paddingHorizontal: 0,
            backgroundColor: '#FFFFFF'
          }}
        />
      </TouchableOpacity>
    </View>
  );
};

export default AlertsDatePicker;

const styles = StyleSheet.create({
  container: {
    width: '100%',
    flexShrink: 1,
  },
  dateTextContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.DarkJungleGreen,
    marginBottom: 6,
  },
  pickerButton: {
    height: 50,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    backgroundColor: Colors.White,
    borderRadius: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  dateText: {
    fontSize: 15,
    fontWeight: '500',
    color: Colors.DarkJungleGreen,
    flex: 1,
  },
  iconContainer: {
    padding: 4,
  },
});
