import React, {memo, useCallback} from 'react';
import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import Text from '@/components/Text';
import {Colors} from '@/constants';
import {useTheme} from '@/constants/Theme';
import Theme from '@/constants/Theme';
import {TcodeArea} from '@/types/codeArea';

interface ButtonChangeCodeProps {
  codeArea: TcodeArea;
  onPress: () => void;
}

const ButtonChangeCode = memo((props: ButtonChangeCodeProps) => {
  const {theme} = useTheme();
  const {codeArea} = props;
  const onShowModal = useCallback(() => {
    props.onPress && props.onPress();
  }, [props.onPress]);
  return (
    <TouchableOpacity
      style={[
        styles.phoneAreaCode,
        {backgroundColor: theme.searchBox, borderColor: theme.innearColor},
      ]}
      onPress={onShowModal}>
      <Image source={codeArea.img} style={styles.flag} />
      <Text
        size={15}
        lineHeight={24}
        marginLeft={8}
        color={Colors.DarkJungleGreen}>
        {codeArea.code}
      </Text>
      <View style={styles.changePhoneCode}>
        <Image source={require('images/down.png')} />
      </View>
    </TouchableOpacity>
  );
});

export default ButtonChangeCode;

const styles = StyleSheet.create({
  container: {},
  phoneAreaCode: {
    width: 144,
    height: 48,
    borderRadius: 8,
    borderWidth: 1,
    ...Theme.flexRow,
    paddingHorizontal: 16,
  },
  phoneView: {
    ...Theme.flexRow,
    marginTop: 4,
  },
  changePhoneCode: {
    position: 'absolute',
    right: 16,
    alignSelf: 'center',
  },
  flag: {
    width: 32,
    height: 20,
  },
});
