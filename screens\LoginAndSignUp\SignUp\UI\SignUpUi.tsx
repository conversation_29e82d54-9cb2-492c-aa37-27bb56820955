import React, { Dispatch, memo, SetStateAction, useState } from 'react';
import {
  Image,
  StyleSheet,
  View,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Dimensions,
} from 'react-native';
import { getBottomSpace, getStatusBarHeight } from 'react-native-iphone-x-helper';
import { LinearGradient } from 'expo-linear-gradient';
import ButtonChangeCategory from '@/components/ButtonChangeCategory';
import ButtonLinear from '@/components/Buttons/ButtonLinear';
import HeaderButton from '@/components/HeaderButton';
import InputApp from '@/components/InputApp';
import Text from '@/components/Text';
import { Colors } from '@/constants';
import { SOURCE_ICON } from '@assets/images';
import { categoryList } from '@/types/category';
import scale from '@/utils/scale';
import passwordValidation from '@/utils/validation/PasswordValidation';
import CustomSwitch from '../CustomSwitch.js';

const { width } = Dimensions.get('window');

interface SignUpUiProps {
  firstName: string;
  setFirstName: Dispatch<SetStateAction<string>>;
  lastName: string;
  setLastName: Dispatch<SetStateAction<string>>;
  organizationName: string;
  setOrganizationName: Dispatch<SetStateAction<string>>;
  email: string;
  setEmail: Dispatch<SetStateAction<string>>;
  isValidEmail: boolean;
  password: string;
  setPassword: Dispatch<SetStateAction<string>>;
  visiblePassword: boolean;
  onShowHidePassword: () => void;
  onSignUp: () => void;
  onTermOfUse: () => void;
  onPrivacyPolicy: () => void;
  onGoToLogin: () => void;
  category: categoryList;
  openCategory: () => void;
  changeConsumerType: (index: any) => void;
  consumerType: string;
}

const SignUpUi = memo(
  ({
    firstName,
    setFirstName,
    lastName,
    setLastName,
    organizationName,
    setOrganizationName,
    email,
    setEmail,
    isValidEmail,
    password,
    setPassword,
    visiblePassword,
    onShowHidePassword,
    onSignUp,
    onTermOfUse,
    onPrivacyPolicy,
    onGoToLogin,
    category,
    openCategory,
    changeConsumerType,
    consumerType,
  }: SignUpUiProps) => {
    const [passwordError, setPasswordError] = useState('');

    return (
      <KeyboardAvoidingView 
        style={styles.container} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Background Gradient */}
        <LinearGradient
          colors={['#f8fffe', '#e8f8f7', '#ffffff']}
          style={styles.background}
        />

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header */}
          <View style={styles.headerContainer}>
            <HeaderButton style={styles.headerButton} />
            
            <View style={styles.titleContainer}>
              <Text style={styles.title}>Create Account</Text>
              <Text style={styles.subtitle}>
                Join us and start your healthcare journey
              </Text>
            </View>
          </View>

          {/* Form Section */}
          <View style={styles.formContainer}>
            {/* Account Type Switch */}
            <View style={styles.switchContainer}>
              <Text style={styles.switchLabel}>Account Type</Text>
              <View style={styles.customSwitchWrapper}>
                <CustomSwitch
                  selectionMode={1}
                  roundCorner={true}
                  option1={'Individual'}
                  option2={'Organization'}
                  onSelectSwitch={changeConsumerType}
                  selectionColor={Colors.TiffanyBlue}
                  textColor={Colors.GrayBlue}
                />
              </View>
            </View>

            {/* Personal Information */}
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Personal Information</Text>
              
              <View style={styles.nameRow}>
                <View style={styles.nameInputContainer}>
                  <InputApp
                    value={firstName}
                    onChangeText={setFirstName}
                    title="First Name"
                    placeholder="Enter first name"
                    isShowIcon={false}
                    style={styles.input}
                  />
                </View>
                
                <View style={styles.nameInputContainer}>
                  <InputApp
                    value={lastName}
                    onChangeText={setLastName}
                    title="Last Name"
                    placeholder="Enter last name"
                    isShowIcon={false}
                    style={styles.input}
                  />
                </View>
              </View>

              {consumerType === 'Organization' && (
                <InputApp
                  value={organizationName}
                  onChangeText={setOrganizationName}
                  title="Organization Name"
                  placeholder="Enter organization name"
                  marginTop={20}
                  isShowIcon={false}
                  style={styles.input}
                />
              )}
            </View>

            {/* Account Details */}
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Account Details</Text>
              
              <InputApp
                value={email}
                onChangeText={setEmail}
                title="Email Address"
                placeholder="Enter your email"
                marginTop={20}
                isShowIcon={true}
                keyboardType="email-address"
                borderColor={isValidEmail ? Colors.TiffanyBlue : Colors.InnearColor}
                icon={
                  isValidEmail ? (
                    <Image
                      source={require('@/assets/images/ic_accept.png')}
                      style={styles.inputIcon}
                    />
                  ) : (
                    <Image source={SOURCE_ICON['reset-search']} style={styles.inputIcon} />
                  )
                }
                style={styles.input}
              />
              
              <InputApp
                title="Password"
                value={password}
                onChangeText={text => {
                  setPassword(text);
                  if (text) {
                    if (passwordValidation(text)) {
                      setPasswordError('');
                    } else {
                      setPasswordError(
                        'Password must be alphanumeric with min 8 - max 16 characters, 1 uppercase, 1 lowercase, 1 special character and no space.',
                      );
                    }
                  } else {
                    setPasswordError('');
                  }
                }}
                placeholder="Create a strong password"
                marginTop={20}
                secureTextEntry={!visiblePassword}
                isShowIcon={true}
                borderColor={password.length > 0 ? Colors.TiffanyBlue : Colors.InnearColor}
                icon={
                  <Image
                    source={require('@/assets/images/ic_eye_on.png')}
                    style={styles.inputIcon}
                  />
                }
                iconPress={onShowHidePassword}
                style={styles.input}
              />

              {passwordError !== '' && (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>{passwordError}</Text>
                </View>
              )}
            </View>

            {/* User Role */}
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Professional Role</Text>
              <View style={styles.categoryContainer}>
                <ButtonChangeCategory 
                  category={category} 
                  onPress={openCategory}
                />
              </View>
            </View>

            {/* Sign Up Button */}
            <View style={styles.buttonContainer}>
              <ButtonLinear
                title="Create Account"
                onPress={onSignUp}
                disabled={
                  email.length === 0 ||
                  password.length === 0 ||
                  !isValidEmail ||
                  firstName?.length === 0 ||
                  lastName?.length === 0 ||
                  passwordError !== ''
                }
                style={styles.signUpButton}
              />
            </View>

            {/* Terms and Privacy */}
            <View style={styles.termsContainer}>
              <Text style={styles.termsText}>
                By creating an account, you agree to our{'\n'}
                <Text style={styles.termsLink} onPress={onTermOfUse}>
                  Terms of Service
                </Text>
                {' '}and{' '}
                <Text style={styles.termsLink} onPress={onPrivacyPolicy}>
                  Privacy Policy
                </Text>
              </Text>
            </View>

            {/* Login Link */}
            <View style={styles.loginContainer}>
              <Text style={styles.loginText}>
                Already have an account?{' '}
                <Text style={styles.loginLink} onPress={onGoToLogin}>
                  Sign In
                </Text>
              </Text>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    );
  },
);

export default SignUpUi;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.White,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 24,
    paddingTop: getStatusBarHeight(),
    paddingBottom: getBottomSpace() + 20,
  },
  headerContainer: {
    marginBottom: 32,
  },
  headerButton: {
    marginTop: 0,
    marginBottom: 20,
  },
  titleContainer: {
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.DarkJungleGreen,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: Colors.GrayBlue,
    textAlign: 'center',
    lineHeight: 22,
  },
  formContainer: {
    flex: 1,
  },
  switchContainer: {
    marginBottom: 32,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.DarkJungleGreen,
    marginBottom: 12,
  },
  customSwitchWrapper: {
    alignItems: 'center',
  },
  sectionContainer: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.DarkJungleGreen,
    marginBottom: 16,
  },
  nameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  nameInputContainer: {
    flex: 1,
    marginRight: 8,
  },
  input: {
    borderRadius: 12,
    backgroundColor: Colors.White,
    shadowColor: Colors.Black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  inputIcon: {
    width: 20,
    height: 20,
    tintColor: Colors.TiffanyBlue,
  },
  errorContainer: {
    marginTop: 8,
    paddingHorizontal: 12,
  },
  errorText: {
    fontSize: 12,
    color: Colors.RedNeonFuchsia,
    lineHeight: 16,
  },
  categoryContainer: {
    marginTop: 12,
  },
  categoryButton: {
    borderRadius: 12,
    shadowColor: Colors.Black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  buttonContainer: {
    marginTop: 16,
    marginBottom: 24,
  },
  signUpButton: {
    height: 56,
    borderRadius: 16,
    shadowColor: Colors.TiffanyBlue,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  termsContainer: {
    alignItems: 'center',
    marginBottom: 32,
    paddingHorizontal: 16,
  },
  termsText: {
    fontSize: 14,
    color: Colors.GrayBlue,
    textAlign: 'center',
    lineHeight: 20,
  },
  termsLink: {
    color: Colors.TiffanyBlue,
    fontWeight: '600',
  },
  loginContainer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  loginText: {
    fontSize: 16,
    color: Colors.GrayBlue,
    textAlign: 'center',
  },
  loginLink: {
    color: Colors.TiffanyBlue,
    fontWeight: '600',
  },
});
