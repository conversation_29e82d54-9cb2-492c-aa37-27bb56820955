import {useNavigation} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {Alert, Image, ScrollView, StyleSheet, TouchableOpacity, View} from 'react-native';
// Remove PhoneInput import to fix defaultProps warning
// import PhoneInput from 'react-native-phone-number-input';
import {useSelector} from 'react-redux';
import ButtonLinear from '@/components/Buttons/ButtonLinear';
import InputApp from '@/components/InputApp';
import Container from '@/components/Layout/Container';
import {useTheme} from '@/constants/Theme';
import Theme from '@/constants/Theme';
import scale from '@/utils/scale';
import {Colors} from '@/constants';
import NavigationHeader from '@/components/NavigationHeader';
import Text from '@/components/Text';
import TextWrapper from '@/components/TextWrapper';
import {MaterialIcons, Feather} from '@expo/vector-icons';
import Loader from '@/components/Loader/Loader';

// Custom Phone Input Component to replace react-native-phone-number-input
const CustomPhoneInput = ({ value, onChangeText, placeholder }: { 
  value: string; 
  onChangeText: (text: string) => void; 
  placeholder?: string; 
}) => {
  const [countryCode, setCountryCode] = useState('+1');
  const [phoneNumber, setPhoneNumber] = useState('');

  useEffect(() => {
    // Parse existing phone value
    if (value) {
      // Remove any non-digit characters except +
      const cleanValue = value.replace(/[^\d+]/g, '');
      if (cleanValue.startsWith('+1')) {
        setCountryCode('+1');
        setPhoneNumber(cleanValue.substring(2));
      } else if (cleanValue.startsWith('+91')) {
        setCountryCode('+91');
        setPhoneNumber(cleanValue.substring(3));
      } else if (cleanValue.startsWith('+')) {
        // Handle other country codes
        const match = cleanValue.match(/^(\+\d{1,3})(\d+)$/);
        if (match) {
          setCountryCode(match[1]);
          setPhoneNumber(match[2]);
        }
      } else {
        setPhoneNumber(cleanValue);
      }
    }
  }, [value]);

  const handlePhoneChange = (text: string) => {
    // Only allow digits
    const cleanText = text.replace(/[^\d]/g, '');
    setPhoneNumber(cleanText);
    onChangeText(countryCode + cleanText);
  };

  const handleCountryCodeChange = (code: string) => {
    setCountryCode(code);
    onChangeText(code + phoneNumber);
  };

  return (
    <View style={styles.customPhoneContainer}>
      <TouchableOpacity 
        style={styles.countryCodeButton}
        onPress={() => {
          // Simple toggle between common country codes
          const newCode = countryCode === '+1' ? '+91' : '+1';
          handleCountryCodeChange(newCode);
        }}
      >
        <Text style={styles.countryCodeText}>{countryCode}</Text>
        <MaterialIcons name="arrow-drop-down" size={20} color={Colors.GrayBlue} />
      </TouchableOpacity>
      
      <View style={styles.phoneNumberInput}>
        <InputApp
          title=""
          value={phoneNumber}
          onChangeText={handlePhoneChange}
          placeholder={placeholder || "Enter phone number"}
          keyboardType="phone-pad"
          style={styles.phoneInputStyle}
          styleView={styles.phoneInputView}
        />
      </View>
    </View>
  );
};

const UpdateProfile = () => {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [address1, setAddress1] = useState('');
  const [address2, setAddress2] = useState('');
  const [city, setCity] = useState('');
  const [state, setSate] = useState('');
  const [zip, setZip] = useState('');
  const [coutry, setCoutry] = useState('');
  const [phone, setPhone] = useState('');
  const [loader, setLoader] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);
  const {navigate} = useNavigation() as any;
  const {theme} = useTheme();

  // const phoneInput = useRef(null);

  const userProfileDetails = useSelector(
    (state: any) => state?.userProfileReducer?.data,
  );

  useEffect(() => {
    setFirstName(userProfileDetails?.firstName);
    setLastName(userProfileDetails?.lastName);
    setEmail(userProfileDetails?.userName);
    setPhone(userProfileDetails?.phoneNumber?.replace('+', ''));

    setAddress1(userProfileDetails?.address?.address1);
    setAddress2(userProfileDetails?.address?.address2);
    setSate(userProfileDetails?.address?.state);
    setCity(userProfileDetails?.address?.city);
    setZip(userProfileDetails?.address?.zip),
    setCoutry(userProfileDetails?.address?.country);
  }, [userProfileDetails]);
  
  // Validate form fields
  useEffect(() => {
    const isValid = 
      firstName.trim().length > 0 &&
      lastName.trim().length > 0 &&
      email.trim().length > 0 &&
      phone.length > 0 &&
      address1.trim().length > 0 &&
      city.trim().length > 0 &&
      state.trim().length > 0 &&
      zip.trim().length > 0 &&
      coutry.trim().length > 0;
      
    setIsFormValid(isValid);
  }, [firstName, lastName, email, phone, address1, city, state, zip, coutry]);
  
  const handleUpdateProfile = () => {
    // Mock implementation - replace with actual API call
    setLoader(true);
    
    // Simulate API call
    setTimeout(() => {
      setLoader(false);
      Alert.alert('Success', 'Profile updated successfully');
    }, 1500);
  };

  return (
    <Container style={styles.container}>
      <NavigationHeader 
        title="Update Profile"
        showBackButton={true}
        showLogo={false}
        showRightLogo={true}
      />
      <Loader modalVisible={loader} />
      
      <TextWrapper style={styles.contentContainer}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}>
          
          {/* Personal Information Section */}
          <TextWrapper style={styles.sectionCard}>
            <TextWrapper style={styles.sectionHeader}>
              <MaterialIcons name="person" size={24} color={Colors.TealBlue} />
              <TextWrapper style={styles.headerTextContainer}>
                <Text size={18} bold color={Colors.DarkJungleGreen}>
                  Personal Information
                </Text>
              </TextWrapper>
            </TextWrapper>
            
            <TextWrapper style={styles.inputGroup}>
              <InputApp
                title={'First Name*'}
                marginTop={0}
                value={firstName}
                onChangeText={setFirstName}
                placeholder="Enter your first name"
              />
              
              <InputApp
                title={'Last Name*'}
                marginTop={16}
                value={lastName}
                onChangeText={setLastName}
                placeholder="Enter your last name"
              />
              
              <InputApp
                title={'Email ID*'}
                marginTop={16}
                value={email}
                onChangeText={setEmail}
                editable={false}
                icon={<Feather name="lock" size={20} color={Colors.GrayBlue} />}
              />
              
              <TextWrapper style={styles.phoneInputLabel}>
                <Text size={14} color={Colors.DarkJungleGreen} bold>
                  Phone Number*
                </Text>
              </TextWrapper>
              
              <View style={styles.phoneInputContainer}>
                <CustomPhoneInput
                  value={phone}
                  onChangeText={setPhone}
                  placeholder="Enter your phone number"
                />
              </View>
            </TextWrapper>
          </TextWrapper>
          
          {/* Address Information Section */}
          <TextWrapper style={styles.sectionCard}>
            <TextWrapper style={styles.sectionHeader}>
              <MaterialIcons name="location-on" size={24} color={Colors.TealBlue} />
              <TextWrapper style={styles.headerTextContainer}>
                <Text size={18} bold color={Colors.DarkJungleGreen}>
                  Address Information
                </Text>
              </TextWrapper>
            </TextWrapper>
            
            <TextWrapper style={styles.inputGroup}>
              <InputApp
                title={'Address Line 1*'}
                marginTop={0}
                value={address1}
                onChangeText={setAddress1}
                placeholder="Enter street address"
              />
              
              <InputApp
                title={'Address Line 2'}
                marginTop={16}
                value={address2}
                onChangeText={setAddress2}
                placeholder="Apartment, suite, unit, etc. (optional)"
              />
              
              <TextWrapper style={styles.addressRow}>
                <TextWrapper style={styles.addressCol}>
                  <InputApp
                    title={'City*'}
                    marginTop={16}
                    value={city}
                    onChangeText={setCity}
                    placeholder="City"
                  />
                </TextWrapper>
                
                <TextWrapper style={styles.addressCol}>
                  <InputApp
                    title={'State*'}
                    marginTop={16}
                    value={state}
                    onChangeText={setSate}
                    placeholder="State"
                  />
                </TextWrapper>
              </TextWrapper>
              
              <TextWrapper style={styles.addressRow}>
                <TextWrapper style={styles.addressCol}>
                  <InputApp
                    title={'Zip*'}
                    marginTop={16}
                    value={zip}
                    onChangeText={setZip}
                    placeholder="Zip code"
                  />
                </TextWrapper>
                
                <TextWrapper style={styles.addressCol}>
                  <InputApp
                    title={'Country*'}
                    marginTop={16}
                    value={coutry}
                    onChangeText={setCoutry}
                    placeholder="Country"
                  />
                </TextWrapper>
              </TextWrapper>
            </TextWrapper>
          </TextWrapper>
          
          <ButtonLinear
            white
            title={'Update Profile'}
            children={
              <MaterialIcons name="check" size={24} color={Colors.White} style={styles.buttonIcon} />
            }
            disabled={!isFormValid}
            onPress={handleUpdateProfile}
            style={styles.updateButton}
          />
        </ScrollView>
      </TextWrapper>
    </Container>
  );
};

export default UpdateProfile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8fafc",
  },
  contentContainer: {
    flex: 1,
    marginHorizontal: 20,
    marginTop: 16,
    marginBottom: 20,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  sectionCard: {
    backgroundColor: Colors.White,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    paddingBottom: 12,
  },
  headerTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  inputGroup: {
    marginBottom: 8,
  },
  phoneInputLabel: {
    marginBottom: 6,
    marginTop: 16,
  },
  phoneInputContainer: {
    marginTop: 6,
  },
  addressRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  addressCol: {
    width: '48%',
  },
  updateButton: {
    marginTop: 32,
    height: 56,
    borderRadius: 12,
  },
  buttonIcon: {
    marginLeft: 8,
  },
  customPhoneContainer: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 10,
    backgroundColor: Colors.White,
    alignItems: 'center',
    minHeight: 48,
  },
  countryCodeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 14,
    borderRightWidth: 1,
    borderRightColor: '#E2E8F0',
    minWidth: 80,
  },
  countryCodeText: {
    fontSize: 16,
    color: Colors.DarkJungleGreen,
    marginRight: 4,
  },
  phoneNumberInput: {
    flex: 1,
    paddingHorizontal: 12,
  },
  phoneInputStyle: {
    borderWidth: 0,
    backgroundColor: 'transparent',
    marginTop: 0,
  },
  phoneInputView: {
    marginTop: 0,
  },
});
