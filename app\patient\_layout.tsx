import { Stack, usePathname } from 'expo-router';
import { View, Dimensions } from 'react-native';
import NavigationHeader from '@/components/NavigationHeader';
import { useCCMContentContext } from '@/context/CCMContentContext';

export default function PatientLayout() {
  const pathname = usePathname();
  const screenHeight = Dimensions.get('window').height;
  const { ccmContentTitle, vitalsTitle, dynamicTitle } = useCCMContentContext();

  // Don't show header for chat screens
  const shouldShowHeader = !pathname.includes('chat/');

  // Determine the title based on the pathname
  const getTitle = () => {
    // If there's a dynamic title set, use it (highest priority)
    if (dynamicTitle) {
      return dynamicTitle;
    }
    
    // Check for specific paths with dynamic titles
    if (pathname.includes('medication/add')) {
      return 'Add Medication';
    } else if (pathname.includes('medications/add')) {
      return 'Add Medication';
    } else if (pathname.includes('vitals')) {
      // Use dynamic vitals title if available, otherwise default
      return vitalsTitle || 'Vitals';
    } else if (pathname.includes('medications')) {
      return 'Medications';
    } else if (pathname.includes('medication')) {
      return 'Medications';
    } else if (pathname.includes('map')) {
      return 'Location';
    } else if (pathname.includes('custom-alerts')) {
      return 'Custom Alerts';
    } else if (pathname.includes('alerts')) {
      return 'Alerts';
    } else if (pathname.includes('ccm-content')) {
      // For CCM Content, use the dynamic title from the context
      return ccmContentTitle || 'Report Details';
    } else if (pathname.includes('ccm')) {
      // Use dynamic CCM title if available, otherwise default
      return ccmContentTitle || 'Care Team Notes';
    } else if (pathname.includes('care-plan')) {
      return 'Care Plan';
    } else if (pathname.includes('video-call')) {
      return 'Video Call';
    } else if (pathname.includes('tasks')) {
      return 'Tasks';
    } else {
      return 'Patient Details';
    }
  };

  return (
    <View style={{ flex: 1, height: screenHeight }}>
      {shouldShowHeader && (
        <NavigationHeader
          title={getTitle()}
          showBackButton={true}
          showLogout={false}
          showRightLogo={true}
        />
      )}
      <Stack
        screenOptions={{
          headerShown: false,
        }}
      >
        {/* Main patient details screen */}
        <Stack.Screen name="[id]" options={{ title: 'Patient Details' }} />

        {/* Communication routes */}
        <Stack.Screen name="chat/[id]" options={{ title: 'Chat Details' }} />
        <Stack.Screen name="video-call/[id]" options={{ title: 'Video Call' }} />

        {/* Dashboard item routes - these are used in the PatientDetails screen */}
        <Stack.Screen name="vitals/[id]" options={{ title: 'Vitals Report' }} />
        <Stack.Screen name="vitals/index" options={{ title: 'Vitals Reports' }} />
        <Stack.Screen name="medications/[id]" options={{ title: 'Medications' }} />
        <Stack.Screen name="medications/add" options={{ title: 'Add Medication' }} />
        <Stack.Screen name="medication/index" options={{ title: 'Medications' }} />
        <Stack.Screen name="medication/add" options={{ title: 'Add Medication' }} />
        <Stack.Screen name="map/[id]" options={{ title: 'Map' }} />
        <Stack.Screen name="alerts" options={{ title: 'Alerts' }} />
        <Stack.Screen name="custom-alerts" options={{ title: 'Custom Alerts' }} />
        <Stack.Screen name="ccm-reports/[id]" options={{ title: 'Care Team Notes' }} />
        <Stack.Screen name="ccm/[id]" options={{ title: 'CCM Details' }} />
        <Stack.Screen name="ccm/index" options={{ title: 'Care Team Notes' }} />
        <Stack.Screen name="care-plan/[id]" options={{ title: 'Care Plan Details' }} />

        {/* Task management routes */}
        <Stack.Screen name="tasks" options={{ title: 'Tasks' }} />
        <Stack.Screen name="tasks/edit/[id]" options={{ title: 'Edit Task' }} />
      </Stack>
    </View>
  );
}
