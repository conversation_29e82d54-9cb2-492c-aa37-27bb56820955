import React from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams } from 'expo-router';
import { Stack } from 'expo-router';

import CCMPlaceholderScreen from '@/screens/PatientDetails/CCMReports/CCMScreen';

// Define the tile titles array to match the ones defined in CCMReports
const tileTitles = [
  'RPM',
  'CCM',
  'PCM', 
  'RTM',
  'Home Health',
  'Hospice'
];

export default function CCMContentRoute() {
  const { id, tileIndex } = useLocalSearchParams();
  
  // Get the title based on the tileIndex
  const index = parseInt(tileIndex as string || '0');
  const title = tileTitles[index] || 'CCM Content';

  return (
    <>
      <Stack.Screen options={{ title }} />
      <SafeAreaView style={styles.container} edges={['left', 'right']}>
        <CCMPlaceholderScreen />
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
