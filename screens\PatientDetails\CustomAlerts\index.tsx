import React, {useEffect, useState, useRef, useCallback} from 'react';
import {
  Alert,
  Image,
  ScrollView,
  StyleSheet,
  View,
  TouchableOpacity,
  Platform,
  Animated,
  RefreshControl,
  Dimensions,
  Modal,
  TextInput,
  ActionSheetIOS,
  FlatList,
  KeyboardAvoidingView,
} from 'react-native';
import {useSelector} from 'react-redux';
import Container from '@/components/Layout/Container';
import Loader from '@/components/Loader/Loader';
import Text from '@/components/Text';
import {Colors} from '@/constants';
import {CustomAlertsProps} from '@/models';
import {apiPostWithToken} from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import scale from '@/utils/scale';
import {
  Ionicons,
  MaterialIcons,
  Feather,
} from '@expo/vector-icons';
import Moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { BlurView } from 'expo-blur';
import { ChevronDownIcon, CloseIcon, SearchIcon, CheckmarkIcon } from '@/components/Icons';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

interface CustomAlertsScreenProps {
  patientId?: string | number;
  navigation?: any;
}

interface NewCustomAlert {
  alertType: string;
  alertTime: string;
  detail: string;
  startDate: string;
  endDate: string;
  type: 'daily' | 'schedule';
}

interface CustomAlertFormData extends NewCustomAlert {
  alertId?: number;
}

const ALERT_TYPES = [
  {label: 'Medication Alert', value: 'Medication Alert', icon: 'medical-outline'},
  {label: 'Walk Alert', value: 'Walk Alert', icon: 'walk-outline'},
  {label: 'Water Alert', value: 'Water Alert', icon: 'water-outline'},
  {label: 'Exercise Alert', value: 'Exercise Alert', icon: 'fitness-outline'},
  {label: 'Doctor Visit', value: 'Doctor Visit', icon: 'medical-outline'},
  {label: 'Blood Pressure Check', value: 'Blood Pressure Check', icon: 'heart-outline'},
  {label: 'Blood Sugar Check', value: 'Blood Sugar Check', icon: 'water-outline'},
  {label: 'Custom Reminder', value: 'Custom Reminder', icon: 'notifications-outline'},
];

const REMINDER_TYPES = [
  {label: 'Daily', value: 'daily', icon: 'repeat'},
  {label: 'Scheduled', value: 'schedule', icon: 'calendar-outline'},
];

/**
 * Enhanced CustomAlerts screen with clean white UI theme
 * Integrates save, edit, and delete APIs with consistent design
 */
const CustomAlerts = (props: CustomAlertsScreenProps) => {
  const [alertsList, setAlertsList] = useState<CustomAlertsProps[]>([]);
  const [loader, setLoader] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingAlert, setEditingAlert] = useState<CustomAlertsProps | null>(null);

  // Form state
  const [formData, setFormData] = useState<CustomAlertFormData>({
    alertType: 'Medication Alert',
    alertTime: '09:00',
    detail: '',
    startDate: '',
    endDate: '',
    type: 'daily',
  });

  // Date/Time picker states
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [showTypeSelector, setShowTypeSelector] = useState(false);
  const [showAlertTypeSelector, setShowAlertTypeSelector] = useState(false);

  // Search states for custom selectors
  const [alertTypeSearchQuery, setAlertTypeSearchQuery] = useState('');
  const [typeSearchQuery, setTypeSearchQuery] = useState('');

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  // Modal animation functions
  const alertTypeModalAnim = useRef(new Animated.Value(screenHeight)).current;
  const reminderTypeModalAnim = useRef(new Animated.Value(screenHeight)).current;

  // Redux selectors
  const patientName = useSelector(
    (state: {currentPatientNameReducer?: {patientName?: string}}) =>
      state?.currentPatientNameReducer?.patientName || 'Patient',
  );

  const reduxPatientId = useSelector(
    (state: {currentPatientIdReducer?: {patientId?: string}}) =>
      state?.currentPatientIdReducer?.patientId,
  );

  const userId = useSelector(
    (state: any) => state?.loginReducer?.data?.userId,
  );

  const orgId = useSelector(
    (state: any) => state?.currentOrgIdReducer?.orgId,
  );

  // Get effective patient ID
  let effectivePatientId = props.patientId || reduxPatientId;
  if (effectivePatientId === 'index') {
    effectivePatientId = reduxPatientId;
  }

  // Load animations
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Fetch custom alerts
  const getCustomAlertsByPatientId = useCallback(
    async (patientId: number | string) => {
      setLoader(true);
      try {
        const response = await apiPostWithToken(
          {patientId: patientId},
          URLS.caregiverUrl + 'getPatientCustomAlerts',
        );
        
        console.log('Full API Response:', JSON.stringify(response, null, 2));
        
        if (response?.status === 200) {
          console.log('Response data:', response.data);
          console.log('Response data items:', response.data?.items);
          
          const alertsList = response?.data?.items as CustomAlertsProps[];
          console.log('Parsed alerts list:', alertsList);
          console.log('Alerts list type:', typeof alertsList);
          console.log('Is array:', Array.isArray(alertsList));
          
          if (Array.isArray(alertsList)) {
            setAlertsList(alertsList);
            console.log('Successfully set alerts list with', alertsList.length, 'items');
          } else {
            console.log('Response items is not an array, setting empty array');
            setAlertsList([]);
          }
        } else {
          console.log('API call failed with status:', response?.status);
          const errorMessage =
            response?.response?.data?.responseMessage ||
            (response.message === 'Network Error'
              ? 'Network error. Please check your data connection.'
              : response.message);
          Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
        }
      } catch (error) {
        console.error('Error fetching custom alerts:', error);
        Alert.alert('Error', 'Failed to load custom alerts', [{text: 'Dismiss'}]);
      } finally {
        setLoader(false);
        setRefreshing(false);
      }
    },
    [],
  );

  // Save new custom alert
  const saveCustomAlert = async (alertData: NewCustomAlert) => {
    if (!effectivePatientId) {
      Alert.alert('Error', 'Patient ID is required');
      return;
    }

    setLoader(true);
    try {
      const payload = {
        patientId: String(effectivePatientId),
        customAlerts: [
          {
            alertType: alertData.alertType,
            alertTime: alertData.alertTime,
            detail: alertData.detail,
            startDate: alertData.startDate,
            endDate: alertData.endDate,
            type: alertData.type,
          },
        ],
      };

      const response = await apiPostWithToken(
        payload,
        URLS.caregiverUrl + 'saveNewCustomAlerts',
      );

      if (response?.status === 200) {
        Alert.alert('Success', 'Custom alert saved successfully!', [
          {
            text: 'OK',
            onPress: () => {
              setShowCreateModal(false);
              resetForm();
              getCustomAlertsByPatientId(effectivePatientId);
            },
          },
        ]);
      } else {
        const errorMessage =
          response?.response?.data?.responseMessage ||
          'Failed to save custom alert';
        Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
      }
    } catch (error) {
      console.error('Error saving custom alert:', error);
      Alert.alert('Error', 'Failed to save custom alert', [{text: 'Dismiss'}]);
    } finally {
      setLoader(false);
    }
  };

  // Edit existing custom alert
  const editCustomAlert = async (alertData: CustomAlertFormData) => {
    if (!effectivePatientId || !alertData.alertId) {
      Alert.alert('Error', 'Patient ID and Alert ID are required');
      return;
    }

    setLoader(true);
    try {
      const payload = {
        patientId: String(effectivePatientId),
        alertId: alertData.alertId,
        alertTime: alertData.alertTime,
        detail: alertData.detail,
        startDate: alertData.startDate,
        endDate: alertData.endDate,
        type: alertData.type,
      };

      const response = await apiPostWithToken(
        payload,
        URLS.caregiverUrl + 'saveEditedCustomAlert',
      );

      if (response?.status === 200) {
        Alert.alert('Success', 'Custom alert updated successfully!', [
          {
            text: 'OK',
            onPress: () => {
              setShowCreateModal(false);
              setEditingAlert(null);
              resetForm();
              getCustomAlertsByPatientId(effectivePatientId);
            },
          },
        ]);
      } else {
        const errorMessage =
          response?.response?.data?.responseMessage ||
          'Failed to update custom alert';
        Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
      }
    } catch (error) {
      console.error('Error updating custom alert:', error);
      Alert.alert('Error', 'Failed to update custom alert', [{text: 'Dismiss'}]);
    } finally {
      setLoader(false);
    }
  };

  // Delete custom alert
  const deleteCustomAlert = async (alertId: string) => {
    if (!effectivePatientId) {
      Alert.alert('Error', 'Patient ID is required');
      return;
    }

    Alert.alert(
      'Confirm Delete',
      'Are you sure you want to delete this custom alert?',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            setLoader(true);
            try {
              const payload = {
                patientId: String(effectivePatientId),
                itemId: Number(alertId),
                itemName: 'customAlert',
              };

              const response = await apiPostWithToken(
                payload,
                URLS.caregiverUrl + 'deleteAnItem',
              );

              if (response?.status === 200) {
                Alert.alert('Success', 'Custom alert deleted successfully!', [
                  {
                    text: 'OK',
                    onPress: () => getCustomAlertsByPatientId(effectivePatientId),
                  },
                ]);
              } else {
                const errorMessage =
                  response?.response?.data?.responseMessage ||
                  'Failed to delete custom alert';
                Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
              }
            } catch (error) {
              console.error('Error deleting custom alert:', error);
              Alert.alert('Error', 'Failed to delete custom alert', [
                {text: 'Dismiss'},
              ]);
            } finally {
              setLoader(false);
            }
          },
        },
      ],
    );
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      alertType: 'Medication Alert',
      alertTime: '09:00',
      detail: '',
      startDate: '',
      endDate: '',
      type: 'daily',
    });
    setAlertTypeSearchQuery('');
    setTypeSearchQuery('');
  };

  // Handle refresh
  const handleRefresh = () => {
    setRefreshing(true);
    if (effectivePatientId) {
      getCustomAlertsByPatientId(effectivePatientId);
    }
  };

  // Open create modal
  const openCreateModal = () => {
    resetForm();
    setEditingAlert(null);
    setShowCreateModal(true);
  };

  // Open edit modal - Fixed to preserve alert type and disable editing
  const openEditModal = (alert: CustomAlertsProps) => {
    // Ensure we preserve ALL data exactly as it is, especially alertType
    const preservedData = {
      alertId: Number(alert.alertId),
      alertType: alert.alertType, // NEVER change this in edit mode
      alertTime: alert.alertTime || '09:00',
      detail: alert.detail || '',
      startDate: alert.startDate || '',
      endDate: alert.endDate || '',
      type: (alert.type === 'schedule' ? 'schedule' : 'daily') as 'daily' | 'schedule',
    };
    
    console.log('Edit mode - preserving original data:', preservedData);
    setFormData(preservedData);
    setEditingAlert(alert);
    setShowCreateModal(true);
  };

  // Handle form submit
  const handleSubmit = () => {
    if (!formData.detail.trim()) {
      Alert.alert('Error', 'Please enter alert details');
      return;
    }

    if (formData.type === 'schedule' && (!formData.startDate || !formData.endDate)) {
      Alert.alert('Error', 'Please select start and end dates for scheduled alerts');
      return;
    }

    if (editingAlert) {
      editCustomAlert(formData);
    } else {
      saveCustomAlert(formData);
    }
  };

  // Modal animation functions
  const openAlertTypeModal = () => {
    setShowAlertTypeSelector(true);
    Animated.timing(alertTypeModalAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const closeAlertTypeModal = () => {
    Animated.timing(alertTypeModalAnim, {
      toValue: screenHeight,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setShowAlertTypeSelector(false);
      setAlertTypeSearchQuery('');
    });
  };

  const openReminderTypeModal = () => {
    setShowTypeSelector(true);
    Animated.timing(reminderTypeModalAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const closeReminderTypeModal = () => {
    Animated.timing(reminderTypeModalAnim, {
      toValue: screenHeight,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setShowTypeSelector(false);
      setTypeSearchQuery('');
    });
  };

  // Show alert type picker - now opens modern modal
  const showAlertTypePicker = () => {
    if (editingAlert) return; // Disabled in edit mode
    openAlertTypeModal();
  };

  // Show reminder type picker - now opens modern modal
  const showTypePicker = () => {
    openReminderTypeModal();
  };

  // Get alert type icon
  const getAlertTypeIcon = (alertType: string) => {
    const type = ALERT_TYPES.find(t => t.value === alertType);
    return type?.icon || 'notifications-outline';
  };

  // Filter functions for search
  const filteredAlertTypes = ALERT_TYPES.filter(type =>
    type.label.toLowerCase().includes(alertTypeSearchQuery.toLowerCase())
  );

  const filteredReminderTypes = REMINDER_TYPES.filter(type =>
    type.label.toLowerCase().includes(typeSearchQuery.toLowerCase())
  );

  // Load initial data
  useEffect(() => {
    if (effectivePatientId) {
      getCustomAlertsByPatientId(effectivePatientId);
    }
  }, [effectivePatientId, getCustomAlertsByPatientId]);

  // Render alert item with clean white design
  const renderAlertItem = (item: CustomAlertsProps, index: number) => {
    console.log('Rendering alert item:', item); // Debug log
    
    if (!item || !item.alertId) {
      console.log('Invalid alert item:', item);
      return null;
    }

    const icon = getAlertTypeIcon(item.alertType);

    return (
      <View
        key={`alert-${item.alertId}-${index}`}
        style={styles.alertCard}>
        {/* Alert Header */}
        <View style={styles.alertHeader}>
          <View style={styles.alertIconContainer}>
            <Ionicons
              name={icon as any}
              size={24}
              color={Colors.TealBlue}
            />
          </View>
          <View style={styles.alertHeaderInfo}>
            <Text style={styles.alertType} numberOfLines={1}>
              {item.alertType || 'Alert'}
            </Text>
            <Text style={styles.alertTime}>
              {item.alertTime ? Moment(item.alertTime, 'HH:mm').format('h:mm A') : 'No time set'}
            </Text>
          </View>
          <View style={styles.alertActions}>
            <TouchableOpacity
              onPress={() => openEditModal(item)}
              style={styles.actionButton}>
              <Feather name="edit-2" size={18} color={Colors.TealBlue} />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => deleteCustomAlert(item.alertId)}
              style={[styles.actionButton, styles.deleteButton]}>
              <Feather name="trash-2" size={18} color={Colors.RedNeonFuchsia} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Alert Details */}
        <View style={styles.alertBody}>
          <Text style={styles.alertDetail}>{item.detail || 'No details provided'}</Text>
          
          <View style={styles.alertMeta}>
            <View style={styles.metaItem}>
              <Ionicons
                name="repeat"
                size={14}
                color={Colors.GrayBlue}
              />
              <Text style={styles.metaText}>
                {item.type === 'daily' ? 'Daily' : item.type === 'schedule' ? 'Scheduled' : 'Daily'}
              </Text>
            </View>
            
            {item.type === 'schedule' && item.startDate && item.endDate && (
              <View style={styles.metaItem}>
                <Ionicons
                  name="calendar"
                  size={14}
                  color={Colors.GrayBlue}
                />
                <Text style={styles.metaText}>
                  {Moment(item.startDate, 'MM-DD-YYYY').format('MMM DD')} -{' '}
                  {Moment(item.endDate, 'MM-DD-YYYY').format('MMM DD')}
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>
    );
  };

  // Render alert type item for custom list
  const renderAlertTypeItem = ({item}: {item: typeof ALERT_TYPES[0]}) => {
    const isSelected = formData.alertType === item.value;
    return (
      <TouchableOpacity
        style={[
          styles.customListItem,
          isSelected && styles.customListItemSelected
        ]}
        onPress={() => {
          setFormData(prev => ({...prev, alertType: item.value}));
          closeAlertTypeModal();
        }}
        disabled={editingAlert !== null} // Disable in edit mode
        activeOpacity={0.7}
      >
        <View style={[
          styles.customListIconContainer,
          isSelected && { backgroundColor: Colors.TealBlue + "30" }
        ]}>
          <Ionicons
            name={item.icon as any}
            size={18}
            color={isSelected ? Colors.TealBlue : Colors.GrayBlue}
          />
        </View>
        <Text style={[
          styles.customListItemText,
          isSelected && styles.customListItemTextSelected
        ]}>
          {item.label}
        </Text>
        {isSelected && (
          <CheckmarkIcon color={Colors.TealBlue} size={18} />
        )}
      </TouchableOpacity>
    );
  };

  // Render reminder type item for custom list
  const renderReminderTypeItem = ({item}: {item: typeof REMINDER_TYPES[0]}) => {
    const isSelected = formData.type === item.value;
    return (
      <TouchableOpacity
        style={[
          styles.customListItem,
          isSelected && styles.customListItemSelected
        ]}
        onPress={() => {
          setFormData(prev => ({...prev, type: item.value as 'daily' | 'schedule'}));
          closeReminderTypeModal();
        }}
        activeOpacity={0.7}
      >
        <View style={[
          styles.customListIconContainer,
          isSelected && { backgroundColor: Colors.TealBlue + "30" }
        ]}>
          <Ionicons
            name={item.icon as any}
            size={18}
            color={isSelected ? Colors.TealBlue : Colors.GrayBlue}
          />
        </View>
        <Text style={[
          styles.customListItemText,
          isSelected && styles.customListItemTextSelected
        ]}>
          {item.label}
        </Text>
        {isSelected && (
          <CheckmarkIcon color={Colors.TealBlue} size={18} />
        )}
      </TouchableOpacity>
    );
  };

  // Render create/edit modal with KeyboardAvoidingView
  const renderFormModal = () => (
    <Modal
      visible={showCreateModal}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowCreateModal(false)}>
      <KeyboardAvoidingView 
        style={styles.modalOverlay}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
      >
        <View style={styles.modalContainer}>
          <ScrollView 
            style={styles.modalContent} 
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.modalScrollContent}
            keyboardShouldPersistTaps="handled"
          >
            {/* Modal Header */}
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {editingAlert ? 'Edit Alert' : 'Create New Alert'}
              </Text>
              <TouchableOpacity
                onPress={() => setShowCreateModal(false)}
                style={styles.closeButton}>
                <Ionicons name="close" size={24} color={Colors.DarkJungleGreen} />
              </TouchableOpacity>
            </View>

            {/* Alert Type Selection - Disabled in edit mode */}
            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>
                Alert Type {editingAlert && <Text style={styles.disabledLabel}>(Cannot be changed)</Text>}
              </Text>
              <TouchableOpacity
                style={[
                  styles.modernSelectorButton,
                  editingAlert && styles.selectorButtonDisabled
                ]}
                onPress={showAlertTypePicker}
                disabled={editingAlert !== null}
                activeOpacity={0.7}
              >
                <Ionicons
                  name={getAlertTypeIcon(formData.alertType) as any}
                  size={20}
                  color={editingAlert ? Colors.GrayBlue : Colors.TealBlue}
                  style={styles.selectorIcon}
                />
                <Text style={[
                  styles.modernSelectorText,
                  editingAlert && styles.selectorTextDisabled
                ]} numberOfLines={1}>
                  {formData.alertType}
                </Text>
                {!editingAlert && (
                  <ChevronDownIcon color={Colors.GrayBlue} size={12} />
                )}
              </TouchableOpacity>
            </View>

            {/* Time Selection */}
            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Alert Time</Text>
              <TouchableOpacity
                style={styles.pickerButton}
                onPress={() => setShowTimePicker(true)}>
                <Ionicons name="time-outline" size={20} color={Colors.TealBlue} />
                <Text style={styles.pickerText}>
                  {Moment(formData.alertTime, 'HH:mm').format('h:mm A')}
                </Text>
                <Ionicons name="chevron-down" size={20} color={Colors.GrayBlue} />
              </TouchableOpacity>
            </View>

            {/* Alert Details */}
            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Alert Details</Text>
              <TextInput
                style={styles.textInput}
                value={formData.detail}
                onChangeText={text => setFormData(prev => ({...prev, detail: text}))}
                placeholder="Enter alert description..."
                placeholderTextColor={Colors.GrayBlue}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
                returnKeyType="done"
                blurOnSubmit={true}
              />
            </View>

            {/* Reminder Type */}
            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Reminder Type</Text>
              <TouchableOpacity
                style={styles.modernSelectorButton}
                onPress={showTypePicker}
                activeOpacity={0.7}
              >
                <Ionicons
                  name={formData.type === 'daily' ? 'repeat' : 'calendar-outline'}
                  size={20}
                  color={Colors.TealBlue}
                  style={styles.selectorIcon}
                />
                <Text style={styles.modernSelectorText} numberOfLines={1}>
                  {formData.type === 'daily' ? 'Daily' : 'Scheduled'}
                </Text>
                <ChevronDownIcon color={Colors.GrayBlue} size={12} />
              </TouchableOpacity>
            </View>

            {/* Date Range (for scheduled alerts) */}
            {formData.type === 'schedule' && (
              <>
                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Start Date</Text>
                  <TouchableOpacity
                    style={styles.pickerButton}
                    onPress={() => setShowStartDatePicker(true)}>
                    <Ionicons name="calendar-outline" size={20} color={Colors.TealBlue} />
                    <Text style={styles.pickerText}>
                      {formData.startDate
                        ? Moment(formData.startDate, 'MM-DD-YYYY').format(
                            'MMM DD, YYYY',
                          )
                        : 'Select start date'}
                    </Text>
                    <Ionicons name="chevron-down" size={20} color={Colors.GrayBlue} />
                  </TouchableOpacity>
                </View>

                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>End Date</Text>
                  <TouchableOpacity
                    style={styles.pickerButton}
                    onPress={() => setShowEndDatePicker(true)}>
                    <Ionicons name="calendar-outline" size={20} color={Colors.TealBlue} />
                    <Text style={styles.pickerText}>
                      {formData.endDate
                        ? Moment(formData.endDate, 'MM-DD-YYYY').format(
                            'MMM DD, YYYY',
                          )
                        : 'Select end date'}
                    </Text>
                    <Ionicons name="chevron-down" size={20} color={Colors.GrayBlue} />
                  </TouchableOpacity>
                </View>
              </>
            )}

            {/* Action Buttons */}
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowCreateModal(false)}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.submitButton]}
                onPress={handleSubmit}>
                <Text style={styles.submitButtonText}>
                  {editingAlert ? 'Update' : 'Create'}
                </Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </KeyboardAvoidingView>

      {/* Date/Time Pickers with white theme */}
      <DateTimePickerModal
        isVisible={showTimePicker}
        mode="time"
        display={Platform.OS === 'ios' ? 'spinner' : 'default'}
        themeVariant="light"
        isDarkModeEnabled={false}
        textColor="#000000"
        accentColor={Colors.TealBlue}
        onConfirm={time => {
          setShowTimePicker(false);
          setFormData(prev => ({
            ...prev,
            alertTime: Moment(time).format('HH:mm'),
          }));
        }}
        onCancel={() => setShowTimePicker(false)}
      />

      <DateTimePickerModal
        isVisible={showStartDatePicker}
        mode="date"
        display={Platform.OS === 'ios' ? 'spinner' : 'default'}
        themeVariant="light"
        isDarkModeEnabled={false}
        textColor="#000000"
        accentColor={Colors.TealBlue}
        onConfirm={date => {
          setShowStartDatePicker(false);
          setFormData(prev => ({
            ...prev,
            startDate: Moment(date).format('MM-DD-YYYY'),
          }));
        }}
        onCancel={() => setShowStartDatePicker(false)}
        minimumDate={new Date()}
      />

      <DateTimePickerModal
        isVisible={showEndDatePicker}
        mode="date"
        display={Platform.OS === 'ios' ? 'spinner' : 'default'}
        themeVariant="light"
        isDarkModeEnabled={false}
        textColor="#000000"
        accentColor={Colors.TealBlue}
        onConfirm={date => {
          setShowEndDatePicker(false);
          setFormData(prev => ({
            ...prev,
            endDate: Moment(date).format('MM-DD-YYYY'),
          }));
        }}
        onCancel={() => setShowEndDatePicker(false)}
        minimumDate={
          formData.startDate
            ? Moment(formData.startDate, 'MM-DD-YYYY').toDate()
            : new Date()
        }
      />

      {/* Custom Alert Type Selector Modal */}
      {renderAlertTypeModal()}

      {/* Custom Reminder Type Selector Modal */}
      {renderReminderTypeModal()}
    </Modal>
  );

  // Render modern alert type selector modal
  const renderAlertTypeModal = () => {
    const filteredAlertTypes = ALERT_TYPES.filter(item =>
      item.label.toLowerCase().includes(alertTypeSearchQuery.toLowerCase())
    );

    return (
      <Modal
        visible={showAlertTypeSelector}
        transparent
        animationType="none"
        onRequestClose={closeAlertTypeModal}
      >
        <View style={styles.customModalOverlay}>
          {/* Backdrop with blur effect */}
          {Platform.OS === "ios" && (
            <BlurView
              intensity={20}
              style={StyleSheet.absoluteFill}
              tint="light"
            />
          )}

          {/* Close backdrop */}
          <TouchableOpacity
            style={StyleSheet.absoluteFill}
            onPress={closeAlertTypeModal}
            activeOpacity={1}
          />

          {/* Modal content */}
          <Animated.View
            style={[
              styles.customModal,
              {
                transform: [{ translateY: alertTypeModalAnim }],
              },
            ]}
          >
            {/* Handle bar */}
            <View style={styles.handleBar} />

            {/* Header */}
            <View style={styles.customModalHeader}>
              <Text style={styles.customModalTitle}>Select Alert Type</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={closeAlertTypeModal}
              >
                <CloseIcon color={Colors.DarkJungleGreen} size={16} />
              </TouchableOpacity>
            </View>

            {/* Search input */}
            <View style={styles.searchContainer}>
              <View style={styles.searchIcon}>
                <SearchIcon color={Colors.GrayBlue} size={16} />
              </View>
              <TextInput
                style={styles.searchInput}
                placeholder="Search alert types..."
                placeholderTextColor={Colors.GrayBlue}
                value={alertTypeSearchQuery}
                onChangeText={setAlertTypeSearchQuery}
                autoCapitalize="none"
                autoCorrect={false}
              />
              {alertTypeSearchQuery.length > 0 && (
                <TouchableOpacity
                  style={styles.clearSearchButton}
                  onPress={() => setAlertTypeSearchQuery("")}
                >
                  <CloseIcon color={Colors.DarkJungleGreen} size={12} />
                </TouchableOpacity>
              )}
            </View>

            {/* Alert types list */}
            <FlatList
              data={filteredAlertTypes}
              renderItem={renderAlertTypeItem}
              keyExtractor={(item) => item.value}
              style={styles.customList}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={
                <View style={styles.emptySearchContainer}>
                  <Text style={styles.emptySearchText}>
                    {alertTypeSearchQuery.length > 0
                      ? "No alert types found"
                      : "No alert types available"}
                  </Text>
                </View>
              }
            />
          </Animated.View>
        </View>
      </Modal>
    );
  };

  // Render modern reminder type selector modal
  const renderReminderTypeModal = () => {
    const filteredReminderTypes = REMINDER_TYPES.filter(item =>
      item.label.toLowerCase().includes(typeSearchQuery.toLowerCase())
    );

    return (
      <Modal
        visible={showTypeSelector}
        transparent
        animationType="none"
        onRequestClose={closeReminderTypeModal}
      >
        <View style={styles.customModalOverlay}>
          {/* Backdrop with blur effect */}
          {Platform.OS === "ios" && (
            <BlurView
              intensity={20}
              style={StyleSheet.absoluteFill}
              tint="light"
            />
          )}

          {/* Close backdrop */}
          <TouchableOpacity
            style={StyleSheet.absoluteFill}
            onPress={closeReminderTypeModal}
            activeOpacity={1}
          />

          {/* Modal content */}
          <Animated.View
            style={[
              styles.customModal,
              {
                transform: [{ translateY: reminderTypeModalAnim }],
              },
            ]}
          >
            {/* Handle bar */}
            <View style={styles.handleBar} />

            {/* Header */}
            <View style={styles.customModalHeader}>
              <Text style={styles.customModalTitle}>Select Reminder Type</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={closeReminderTypeModal}
              >
                <CloseIcon color={Colors.DarkJungleGreen} size={16} />
              </TouchableOpacity>
            </View>

            {/* Search input */}
            <View style={styles.searchContainer}>
              <View style={styles.searchIcon}>
                <SearchIcon color={Colors.GrayBlue} size={16} />
              </View>
              <TextInput
                style={styles.searchInput}
                placeholder="Search reminder types..."
                placeholderTextColor={Colors.GrayBlue}
                value={typeSearchQuery}
                onChangeText={setTypeSearchQuery}
                autoCapitalize="none"
                autoCorrect={false}
              />
              {typeSearchQuery.length > 0 && (
                <TouchableOpacity
                  style={styles.clearSearchButton}
                  onPress={() => setTypeSearchQuery("")}
                >
                  <CloseIcon color={Colors.DarkJungleGreen} size={12} />
                </TouchableOpacity>
              )}
            </View>

            {/* Reminder types list */}
            <FlatList
              data={filteredReminderTypes}
              renderItem={renderReminderTypeItem}
              keyExtractor={(item) => item.value}
              style={styles.customList}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={
                <View style={styles.emptySearchContainer}>
                  <Text style={styles.emptySearchText}>
                    {typeSearchQuery.length > 0
                      ? "No reminder types found"
                      : "No reminder types available"}
                  </Text>
                </View>
              }
            />
          </Animated.View>
        </View>
      </Modal>
    );
  };

  // Render empty state with clean design
  const renderEmptyState = () => (
    <Animated.View
      style={[
        styles.emptyContainer,
        {
          opacity: fadeAnim,
          transform: [{translateY: slideAnim}],
        },
      ]}>
      <View style={styles.emptyIconContainer}>
        <Ionicons
          name="notifications-outline"
          size={80}
          color={Colors.GrayBlue}
        />
      </View>
      <Text style={styles.emptyTitle}>No Custom Alerts Set</Text>
      <Text style={styles.emptySubtitle}>
        Create personalized reminders to help {patientName} stay on track with
        their care routine.
      </Text>
      <TouchableOpacity
        style={styles.emptyActionButton}
        onPress={openCreateModal}>
        <Ionicons name="add" size={20} color={Colors.White} />
        <Text style={styles.emptyActionText}>Create First Alert</Text>
      </TouchableOpacity>
    </Animated.View>
  );

  // Render floating action button with clean design
  const renderFloatingActionButton = () => (
    <TouchableOpacity
      style={styles.fab}
      onPress={openCreateModal}
      activeOpacity={0.8}>
      <Ionicons name="add" size={28} color={Colors.White} />
    </TouchableOpacity>
  );

  return (
    <Container style={styles.container}>
      <Loader modalVisible={loader} />

      {(() => {
        console.log('Current alerts list:', alertsList, 'Length:', alertsList.length);
        return null;
      })()}

      {alertsList.length === 0 ? (
        renderEmptyState()
      ) : (
        <>
          <ScrollView
            style={styles.scrollContainer}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                tintColor={Colors.TealBlue}
                colors={[Colors.TealBlue]}
              />
            }>
            {/* Header */}
            <View style={styles.header}>
              
            </View>

            {/* Alerts List */}
            <View style={styles.alertsList}>
              {alertsList.map((item, index) => {
                console.log(`Mapping alert ${index}:`, item);
                return renderAlertItem(item, index);
              })}
            </View>
          </ScrollView>

          {renderFloatingActionButton()}
        </>
      )}

      {renderFormModal()}

      {/* Modern dropdown modals */}
      {renderAlertTypeModal()}
      {renderReminderTypeModal()}
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.DarkJungleGreen,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: Colors.GrayBlue,
  },
  alertsList: {
    paddingHorizontal: 20,
  },
  alertCard: {
    marginBottom: 16,
    borderRadius: 12,
    backgroundColor: Colors.White,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  alertHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  alertIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f9ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  alertHeaderInfo: {
    flex: 1,
  },
  alertType: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.DarkJungleGreen,
    marginBottom: 2,
  },
  alertTime: {
    fontSize: 14,
    color: Colors.GrayBlue,
  },
  alertActions: {
    flexDirection: 'row',
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f8fafc',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  deleteButton: {
    backgroundColor: '#fef2f2',
    borderColor: '#fecaca',
  },
  alertBody: {
    paddingLeft: 52,
  },
  alertDetail: {
    fontSize: 14,
    color: Colors.DarkJungleGreen,
    lineHeight: 20,
    marginBottom: 8,
  },
  alertMeta: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 4,
  },
  metaText: {
    fontSize: 12,
    color: Colors.GrayBlue,
    marginLeft: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    margin: 20,
    backgroundColor: Colors.White,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  emptyIconContainer: {
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.DarkJungleGreen,
    marginBottom: 12,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    color: Colors.GrayBlue,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  emptyActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.TealBlue,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyActionText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.White,
    marginLeft: 8,
  },
  fab: {
    position: 'absolute',
    bottom: 30,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.TealBlue,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: Colors.White,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: screenHeight * 0.85, // Reduced height to prevent keyboard issues
  },
  modalContent: {
    maxHeight: screenHeight * 0.8,
  },
  modalScrollContent: {
    padding: 24,
    paddingBottom: 40, // Extra padding for keyboard
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.DarkJungleGreen,
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f8fafc',
    justifyContent: 'center',
    alignItems: 'center',
  },
  formGroup: {
    marginBottom: 20,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.DarkJungleGreen,
    marginBottom: 8,
  },
  disabledLabel: {
    fontSize: 12,
    fontWeight: 'normal',
    color: Colors.GrayBlue,
    fontStyle: 'italic',
  },
  pickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.White,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    padding: 16,
    minHeight: 50,
  },
  pickerButtonDisabled: {
    backgroundColor: '#f8fafc',
    opacity: 0.7,
  },
  pickerText: {
    flex: 1,
    fontSize: 16,
    color: Colors.DarkJungleGreen,
    marginLeft: 12,
  },
  pickerTextDisabled: {
    color: Colors.GrayBlue,
  },
  textInput: {
    backgroundColor: Colors.White,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    color: Colors.DarkJungleGreen,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  modalActions: {
    flexDirection: 'row',
    marginTop: 24,
    gap: 12,
  },
  modalButton: {
    flex: 1,
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.GrayBlue,
  },
  submitButton: {
    backgroundColor: Colors.TealBlue,
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.White,
  },
  // Custom modal styles - like PatientSelector
  customModalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  customModalBackdrop: {
    flex: 1,
  },
  customModal: {
    backgroundColor: Colors.White,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    maxHeight: screenHeight * 0.7,
    paddingBottom: Platform.OS === 'ios' ? 34 : 24,
  },
  handleBar: {
    width: 48,
    height: 6,
    backgroundColor: '#e2e8f0',
    borderRadius: 3,
    alignSelf: 'center',
    marginTop: 12,
    marginBottom: 8,
  },
  customModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  customModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.DarkJungleGreen,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 44,
    borderRadius: 8,
    backgroundColor: '#f8fafc',
    marginHorizontal: 24,
    marginVertical: 16,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: 15,
    color: Colors.DarkJungleGreen,
  },
  clearSearchButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#e2e8f0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  customList: {
    paddingHorizontal: 24,
    maxHeight: screenHeight * 0.4,
  },
  customListItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  customListItemSelected: {
    backgroundColor: '#f0f9ff',
  },
  customListIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f8fafc',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  customListItemText: {
    fontSize: 16,
    color: Colors.DarkJungleGreen,
    flex: 1,
  },
  customListItemTextSelected: {
    fontWeight: '600',
    color: Colors.TealBlue,
  },
  emptySearchContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32,
  },
  emptySearchText: {
    fontSize: 16,
    color: Colors.GrayBlue,
  },
  debugSection: {
    padding: 20,
    backgroundColor: Colors.White,
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
  },
  debugText: {
    fontSize: 16,
    color: Colors.GrayBlue,
    marginBottom: 8,
  },
  debugButton: {
    backgroundColor: Colors.TealBlue,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  debugButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.White,
  },
  debugButtonSecondary: {
    backgroundColor: '#fef2f2',
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  modernSelectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.White,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    padding: 16,
    minHeight: 50,
  },
  selectorButtonDisabled: {
    backgroundColor: '#f8fafc',
    opacity: 0.7,
  },
  selectorIcon: {
    marginRight: 12,
  },
  modernSelectorText: {
    flex: 1,
    fontSize: 16,
    color: Colors.DarkJungleGreen,
  },
  selectorTextDisabled: {
    color: Colors.GrayBlue,
  },
});

export default CustomAlerts;
