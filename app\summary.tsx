import Container from "@/components/Layout/Container";
import NavigationHeader from "@/components/NavigationHeader";
import { Colors } from "@/constants";
import { Stack } from "expo-router";
import React from "react";
import { StatusBar } from "react-native";

import ValidationScreen from "@/screens/Validation";

export default function SummaryRoute() {
  return (
    <>
      <Stack.Screen
        options={{
          title: "Summary",
          headerShown: false, // Hide the default header since we're using our custom NavigationHeader
        }}
      />
      <Container style={{ flex: 1, backgroundColor: "#f8fafc" }}>
        <StatusBar
          barStyle="dark-content"
          backgroundColor={Colors.TurquoiseBlue}
        />
        <NavigationHeader
          title="Summary"
          showBackButton={true}
          showLogo={false}
          showRightLogo={true}
        />
        <ValidationScreen />
      </Container>
    </>
  );
}
