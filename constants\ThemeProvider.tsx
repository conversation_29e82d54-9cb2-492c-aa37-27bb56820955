import React from 'react';
import { ThemeContext, themes, TMode } from './Theme';

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Always use light mode
  const mode: TMode = 'light';

  // Dummy function that does nothing since we're removing dark mode
  const toggleTheme = React.useCallback(() => {
    // No-op function - we only support light mode
  }, []);

  const value = React.useMemo(() => ({
    theme: themes[mode],
    toggleTheme,
    colorScheme: mode,
  }), []);

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeProvider;