/**
 * Tablet Screen Integration
 *
 * This file provides utilities to integrate tablet-optimized components
 * with the existing app screens and routing system
 */

import React from "react";
import { isTablet } from "./responsive";

// Import tablet-optimized components
import { TabletHome } from "@/components/Home";
import { TabletSchedule } from "@/components/Schedule";
import { TabletTasks } from "@/components/Tasks";
import { TabletProfile } from "@/components/Profile";
import { PatientListTablet } from "@/components/Patients";
import { PatientDetailTablet } from "@/components/PatientDetails";
import { TabletChatInterface } from "@/components/Chat";
import { TabletVideoCallInterface } from "@/components/VideoCall";

/**
 * Higher-order component that conditionally renders tablet or mobile version
 */
export function withTabletOptimization<P extends object>(
  MobileComponent: React.ComponentType<P>,
  TabletComponent?: React.ComponentType<P>
) {
  return function TabletOptimizedComponent(props: P) {
    if (isTablet() && TabletComponent) {
      return <TabletComponent {...props} />;
    }
    return <MobileComponent {...props} />;
  };
}

/**
 * Screen component mappings for tablet optimization
 */
export const TabletScreenMappings = {
  // Main tab screens
  HomeScreen: {
    mobile: "screens/Home/index.tsx",
    tablet: TabletHome,
  },
  PatientsScreen: {
    mobile: "screens/Patients/index.tsx",
    tablet: PatientListTablet,
  },
  ScheduleScreen: {
    mobile: "screens/Schedule/index.tsx",
    tablet: TabletSchedule,
  },
  TasksScreen: {
    mobile: "screens/Tasks/index.tsx",
    tablet: TabletTasks,
  },
  ProfileScreen: {
    mobile: "screens/UserProfile/index.tsx",
    tablet: TabletProfile,
  },

  // Patient detail screens
  PatientDetailScreen: {
    mobile: "screens/PatientDetails/index.tsx",
    tablet: PatientDetailTablet,
  },

  // Communication screens
  ChatScreen: {
    mobile: "screens/PatientChatDetails/index.tsx",
    tablet: TabletChatInterface,
  },
  VideoCallScreen: {
    mobile: "screens/PatientDetails/VideoCall",
    tablet: TabletVideoCallInterface,
  },
};

/**
 * Get the appropriate screen component based on device type
 */
export function getTabletOptimizedScreen(
  screenName: keyof typeof TabletScreenMappings
) {
  const mapping = TabletScreenMappings[screenName];
  if (!mapping) {
    console.warn(`No tablet mapping found for screen: ${screenName}`);
    return null;
  }

  return isTablet() ? mapping.tablet : mapping.mobile;
}

/**
 * Create a tablet-optimized version of a screen component
 */
export function createTabletScreen<P extends object>(
  screenName: keyof typeof TabletScreenMappings,
  MobileComponent: React.ComponentType<P>
) {
  const TabletComponent = TabletScreenMappings[screenName]?.tablet;

  return function OptimizedScreen(props: P) {
    if (isTablet() && TabletComponent) {
      return <TabletComponent {...props} />;
    }
    return <MobileComponent {...props} />;
  };
}

/**
 * Hook to determine if current screen has tablet optimization
 */
export function useTabletOptimization(screenName: string) {
  return {
    hasTabletVersion: isTablet() && screenName in TabletScreenMappings,
    isTabletDevice: isTablet(),
    shouldUseTabletLayout: isTablet() && screenName in TabletScreenMappings,
  };
}

/**
 * Utility to wrap existing screen components with tablet optimization
 */
export const TabletOptimizedScreens = {
  Home: withTabletOptimization(null, TabletHome),
  Patients: withTabletOptimization(null, PatientListTablet),
  Schedule: withTabletOptimization(null, TabletSchedule),
  Tasks: withTabletOptimization(null, TabletTasks),
  Profile: withTabletOptimization(null, TabletProfile),
  PatientDetail: withTabletOptimization(null, PatientDetailTablet),
  Chat: withTabletOptimization(null, TabletChatInterface),
  VideoCall: withTabletOptimization(null, TabletVideoCallInterface),
};

export default {
  withTabletOptimization,
  TabletScreenMappings,
  getTabletOptimizedScreen,
  createTabletScreen,
  useTabletOptimization,
  TabletOptimizedScreens,
};
