import { useEffect } from 'react';
import { Redirect, useRouter } from 'expo-router';
import { useSelector } from 'react-redux';

export default function IndexRoute() {
  const router = useRouter();
  const isLoggedIn = useSelector((state: any) => state.loginReducer?.isLoggedIn);
  const hasCompletedOnboarding = useSelector((state: any) => state.onboardingReducer?.hasCompleted);

  useEffect(() => {
    // This allows the splash screen to be visible briefly
    const timeout = setTimeout(() => {
      if (isLoggedIn) {
        // Try all possible organization screens
        console.log('Navigating to organization screen from index');
        try {
          // First try the standard organization route
          router.replace('/organization');
        } catch (error) {
          console.error('Error navigating to /organization:', error);
          try {
            // Then try the org route
            router.replace('/org');
          } catch (error2) {
            console.error('Error navigating to /org:', error2);
            try {
              // Then try the simple-org route
              router.replace('/simple-org');
            } catch (error3) {
              console.error('Error navigating to /simple-org:', error3);
              // Last resort - go to tabs
              router.replace('/(tabs)');
            }
          }
        }
      } else if (hasCompletedOnboarding) {
        router.push('/auth/login');
      } else {
        router.push('/onboarding');
      }
    }, 1500);

    return () => clearTimeout(timeout);
  }, [isLoggedIn, hasCompletedOnboarding, router]);

  // Initially redirect to splash screen
  return <Redirect href="/splash" />;
}