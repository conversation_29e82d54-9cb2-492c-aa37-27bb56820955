import React, { useState, useCallback, useEffect, useMemo } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ViewStyle,
  StatusBar,
  Platform,
} from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import Text from '@/components/Text';
import { useOrientation } from '@/hooks/useOrientation';
import { useTabletTheme } from '@/hooks/useTabletTheme';
import {
  isTablet,
  isLargeTablet,
  spacing,
  typography,
  iconSizes,
  tabletLayout,
} from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';

interface VideoCallParticipant {
  id: string;
  name: string;
  isLocal?: boolean;
  isMuted?: boolean;
  isVideoEnabled?: boolean;
  connectionQuality?: 'excellent' | 'good' | 'poor';
}

interface TabletVideoCallInterfaceProps {
  // Participants
  localParticipant: VideoCallParticipant;
  remoteParticipants: VideoCallParticipant[];
  
  // Call state
  isConnected: boolean;
  callDuration?: string;
  connectionQuality?: 'excellent' | 'good' | 'poor';
  
  // Controls
  onToggleMute?: () => void;
  onToggleVideo?: () => void;
  onToggleSpeaker?: () => void;
  onSwitchCamera?: () => void;
  onEndCall?: () => void;
  onToggleChat?: () => void;
  onToggleScreenShare?: () => void;
  
  // Settings
  isMuted?: boolean;
  isVideoEnabled?: boolean;
  isSpeakerEnabled?: boolean;
  isScreenSharing?: boolean;
  showChat?: boolean;
  
  // Video components (to be provided by video SDK)
  LocalVideoComponent?: React.ComponentType<any>;
  RemoteVideoComponent?: React.ComponentType<any>;
  
  // Styling
  style?: ViewStyle;
}

/**
 * Video Call Control Button
 */
const VideoCallButton: React.FC<{
  icon: keyof typeof Ionicons.glyphMap;
  label: string;
  onPress: () => void;
  isActive?: boolean;
  variant?: 'primary' | 'secondary' | 'danger';
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
}> = ({
  icon,
  label,
  onPress,
  isActive = false,
  variant = 'secondary',
  disabled = false,
  size = 'medium',
}) => {
  const tabletTheme = useTabletTheme();

  const getButtonStyles = useCallback(() => {
    const baseSize = isTablet() ? 
      (size === 'large' ? 64 : size === 'small' ? 48 : 56) :
      (size === 'large' ? 56 : size === 'small' ? 44 : 48);

    const colors = {
      primary: {
        background: isActive ? CoreColors.TurquoiseBlue : 'rgba(255, 255, 255, 0.2)',
        icon: '#FFFFFF',
      },
      secondary: {
        background: isActive ? 'rgba(255, 255, 255, 0.3)' : 'rgba(255, 255, 255, 0.1)',
        icon: '#FFFFFF',
      },
      danger: {
        background: CoreColors.Red,
        icon: '#FFFFFF',
      },
    };

    return {
      width: baseSize,
      height: baseSize,
      borderRadius: baseSize / 2,
      backgroundColor: colors[variant].background,
      iconColor: colors[variant].icon,
      iconSize: isTablet() ? 
        (size === 'large' ? iconSizes.xl : size === 'small' ? iconSizes.md : iconSizes.lg) :
        (size === 'large' ? iconSizes.lg : size === 'small' ? iconSizes.sm : iconSizes.md),
    };
  }, [variant, isActive, size]);

  const buttonStyles = getButtonStyles();

  return (
    <View style={styles.controlButtonContainer}>
      <TouchableOpacity
        style={[
          styles.controlButton,
          {
            width: buttonStyles.width,
            height: buttonStyles.height,
            borderRadius: buttonStyles.borderRadius,
            backgroundColor: buttonStyles.background,
            opacity: disabled ? 0.5 : 1,
          }
        ]}
        onPress={onPress}
        disabled={disabled}
        activeOpacity={0.8}
      >
        <Ionicons
          name={icon}
          size={buttonStyles.iconSize}
          color={buttonStyles.iconColor}
        />
      </TouchableOpacity>
      
      {isTablet() && (
        <Text style={[
          styles.controlButtonLabel,
          { fontSize: tabletTheme.typography.get('caption').fontSize }
        ]}>
          {label}
        </Text>
      )}
    </View>
  );
};

/**
 * Video Call Status Bar
 */
const VideoCallStatusBar: React.FC<{
  callDuration?: string;
  connectionQuality?: 'excellent' | 'good' | 'poor';
  participantCount: number;
}> = ({ callDuration, connectionQuality, participantCount }) => {
  const tabletTheme = useTabletTheme();

  const getConnectionIcon = useCallback(() => {
    switch (connectionQuality) {
      case 'excellent':
        return { name: 'wifi' as const, color: CoreColors.Green };
      case 'good':
        return { name: 'wifi' as const, color: CoreColors.Orange };
      case 'poor':
        return { name: 'wifi' as const, color: CoreColors.Red };
      default:
        return { name: 'wifi-off' as const, color: CoreColors.GrayBlue };
    }
  }, [connectionQuality]);

  const connectionIcon = getConnectionIcon();

  return (
    <View style={styles.statusBar}>
      <View style={styles.statusLeft}>
        <View style={styles.statusItem}>
          <Ionicons
            name="people"
            size={iconSizes.sm}
            color="#FFFFFF"
            style={styles.statusIcon}
          />
          <Text style={[
            styles.statusText,
            { fontSize: tabletTheme.typography.get('caption').fontSize }
          ]}>
            {participantCount}
          </Text>
        </View>
        
        {callDuration && (
          <View style={styles.statusItem}>
            <Ionicons
              name="time"
              size={iconSizes.sm}
              color="#FFFFFF"
              style={styles.statusIcon}
            />
            <Text style={[
              styles.statusText,
              { fontSize: tabletTheme.typography.get('caption').fontSize }
            ]}>
              {callDuration}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.statusRight}>
        <View style={styles.statusItem}>
          <Ionicons
            name={connectionIcon.name}
            size={iconSizes.sm}
            color={connectionIcon.color}
          />
        </View>
      </View>
    </View>
  );
};

/**
 * Main Tablet Video Call Interface
 */
const TabletVideoCallInterface: React.FC<TabletVideoCallInterfaceProps> = ({
  localParticipant,
  remoteParticipants,
  isConnected,
  callDuration,
  connectionQuality,
  
  onToggleMute,
  onToggleVideo,
  onToggleSpeaker,
  onSwitchCamera,
  onEndCall,
  onToggleChat,
  onToggleScreenShare,
  
  isMuted = false,
  isVideoEnabled = true,
  isSpeakerEnabled = false,
  isScreenSharing = false,
  showChat = false,
  
  LocalVideoComponent,
  RemoteVideoComponent,
  
  style,
}) => {
  const [controlsVisible, setControlsVisible] = useState(true);
  const [controlsTimeout, setControlsTimeout] = useState<NodeJS.Timeout | null>(null);
  const { orientation } = useOrientation();
  const insets = useSafeAreaInsets();
  const tabletTheme = useTabletTheme();

  // Auto-hide controls after inactivity
  const resetControlsTimeout = useCallback(() => {
    if (controlsTimeout) {
      clearTimeout(controlsTimeout);
    }
    
    setControlsVisible(true);
    const timeout = setTimeout(() => {
      setControlsVisible(false);
    }, 5000);
    
    setControlsTimeout(timeout);
  }, [controlsTimeout]);

  // Handle screen tap to show/hide controls
  const handleScreenTap = useCallback(() => {
    if (controlsVisible) {
      setControlsVisible(false);
      if (controlsTimeout) {
        clearTimeout(controlsTimeout);
      }
    } else {
      resetControlsTimeout();
    }
  }, [controlsVisible, controlsTimeout, resetControlsTimeout]);

  // Initialize controls timeout
  useEffect(() => {
    resetControlsTimeout();
    return () => {
      if (controlsTimeout) {
        clearTimeout(controlsTimeout);
      }
    };
  }, []);

  // Calculate layout dimensions
  const layoutDimensions = useMemo(() => {
    const { width, height } = Dimensions.get('window');
    const isLandscape = orientation === 'landscape';
    
    if (isLandscape && isTablet()) {
      // Landscape tablet layout
      const chatWidth = showChat ? (isLargeTablet() ? 400 : 320) : 0;
      const videoWidth = width - chatWidth;
      
      return {
        videoArea: { width: videoWidth, height },
        chatArea: { width: chatWidth, height },
        localVideoSize: { width: 200, height: 150 },
        isLandscape: true,
      };
    } else {
      // Portrait or phone layout
      return {
        videoArea: { width, height },
        chatArea: { width: 0, height: 0 },
        localVideoSize: { width: 120, height: 90 },
        isLandscape: false,
      };
    }
  }, [orientation, showChat]);

  // Main control buttons
  const mainControls = useMemo(() => [
    {
      icon: isMuted ? 'mic-off' : 'mic',
      label: isMuted ? 'Unmute' : 'Mute',
      onPress: onToggleMute,
      isActive: !isMuted,
      variant: 'primary' as const,
    },
    {
      icon: isVideoEnabled ? 'videocam' : 'videocam-off',
      label: isVideoEnabled ? 'Stop Video' : 'Start Video',
      onPress: onToggleVideo,
      isActive: isVideoEnabled,
      variant: 'primary' as const,
    },
    {
      icon: 'call',
      label: 'End Call',
      onPress: onEndCall,
      variant: 'danger' as const,
      size: 'large' as const,
    },
  ], [isMuted, isVideoEnabled, onToggleMute, onToggleVideo, onEndCall]);

  // Secondary control buttons
  const secondaryControls = useMemo(() => [
    {
      icon: isSpeakerEnabled ? 'volume-high' : 'volume-medium',
      label: 'Speaker',
      onPress: onToggleSpeaker,
      isActive: isSpeakerEnabled,
    },
    {
      icon: 'camera-reverse',
      label: 'Switch Camera',
      onPress: onSwitchCamera,
    },
    {
      icon: isScreenSharing ? 'stop-circle' : 'desktop',
      label: isScreenSharing ? 'Stop Share' : 'Share Screen',
      onPress: onToggleScreenShare,
      isActive: isScreenSharing,
    },
    {
      icon: 'chatbubbles',
      label: 'Chat',
      onPress: onToggleChat,
      isActive: showChat,
    },
  ], [isSpeakerEnabled, isScreenSharing, showChat, onToggleSpeaker, onSwitchCamera, onToggleScreenShare, onToggleChat]);

  return (
    <View style={[styles.container, style]}>
      <StatusBar hidden={true} />
      
      {/* Main Video Area */}
      <TouchableOpacity
        style={[
          styles.videoArea,
          {
            width: layoutDimensions.videoArea.width,
            height: layoutDimensions.videoArea.height,
          }
        ]}
        onPress={handleScreenTap}
        activeOpacity={1}
      >
        {/* Remote Video */}
        <View style={styles.remoteVideoContainer}>
          {RemoteVideoComponent ? (
            <RemoteVideoComponent participants={remoteParticipants} />
          ) : (
            <View style={styles.placeholderVideo}>
              <Ionicons
                name="person"
                size={isTablet() ? iconSizes.xl * 2 : iconSizes.xl}
                color="rgba(255, 255, 255, 0.5)"
              />
              <Text style={[
                styles.placeholderText,
                { fontSize: tabletTheme.typography.get('h3').fontSize }
              ]}>
                {remoteParticipants[0]?.name || 'Connecting...'}
              </Text>
            </View>
          )}
        </View>

        {/* Local Video (Picture-in-Picture) */}
        <View style={[
          styles.localVideoContainer,
          {
            width: layoutDimensions.localVideoSize.width,
            height: layoutDimensions.localVideoSize.height,
            top: insets.top + spacing.lg,
            right: spacing.lg,
          }
        ]}>
          {LocalVideoComponent ? (
            <LocalVideoComponent participant={localParticipant} />
          ) : (
            <View style={styles.localVideoPlaceholder}>
              <Ionicons
                name="person"
                size={iconSizes.lg}
                color="rgba(255, 255, 255, 0.8)"
              />
            </View>
          )}
        </View>

        {/* Status Bar */}
        {controlsVisible && (
          <View style={[
            styles.statusBarContainer,
            { top: insets.top + spacing.sm }
          ]}>
            <VideoCallStatusBar
              callDuration={callDuration}
              connectionQuality={connectionQuality}
              participantCount={remoteParticipants.length + 1}
            />
          </View>
        )}

        {/* Controls Overlay */}
        {controlsVisible && (
          <View style={[
            styles.controlsOverlay,
            { paddingBottom: insets.bottom + spacing.lg }
          ]}>
            {/* Secondary Controls */}
            {isTablet() && (
              <View style={styles.secondaryControls}>
                {secondaryControls.map((control, index) => (
                  <VideoCallButton
                    key={index}
                    icon={control.icon as any}
                    label={control.label}
                    onPress={control.onPress || (() => {})}
                    isActive={control.isActive}
                    size="small"
                  />
                ))}
              </View>
            )}

            {/* Main Controls */}
            <View style={styles.mainControls}>
              {mainControls.map((control, index) => (
                <VideoCallButton
                  key={index}
                  icon={control.icon as any}
                  label={control.label}
                  onPress={control.onPress || (() => {})}
                  isActive={control.isActive}
                  variant={control.variant}
                  size={control.size}
                />
              ))}
            </View>
          </View>
        )}
      </TouchableOpacity>

      {/* Chat Panel (Tablet Landscape Only) */}
      {showChat && layoutDimensions.chatArea.width > 0 && (
        <View style={[
          styles.chatPanel,
          {
            width: layoutDimensions.chatArea.width,
            height: layoutDimensions.chatArea.height,
          }
        ]}>
          <View style={styles.chatHeader}>
            <Text style={[
              styles.chatTitle,
              { fontSize: tabletTheme.typography.get('h6').fontSize }
            ]}>
              Chat
            </Text>
            <TouchableOpacity
              onPress={onToggleChat}
              style={styles.chatCloseButton}
            >
              <Ionicons name="close" size={iconSizes.md} color={CoreColors.DarkJungleGreen} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.chatContent}>
            <Text style={styles.chatPlaceholder}>Chat functionality would be implemented here</Text>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#000000',
  } as ViewStyle,

  // Video Area
  videoArea: {
    flex: 1,
    position: 'relative',
  } as ViewStyle,

  remoteVideoContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  } as ViewStyle,

  placeholderVideo: {
    justifyContent: 'center',
    alignItems: 'center',
  } as ViewStyle,

  placeholderText: {
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: spacing.md,
    fontWeight: '600',
  } as ViewStyle,

  localVideoContainer: {
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  } as ViewStyle,

  localVideoPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  } as ViewStyle,

  // Status Bar
  statusBarContainer: {
    position: 'absolute',
    left: spacing.lg,
    right: spacing.lg,
  } as ViewStyle,

  statusBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
  } as ViewStyle,

  statusLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  } as ViewStyle,

  statusRight: {
    flexDirection: 'row',
    alignItems: 'center',
  } as ViewStyle,

  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: spacing.md,
  } as ViewStyle,

  statusIcon: {
    marginRight: spacing.xs,
  },

  statusText: {
    color: '#FFFFFF',
    fontWeight: '500',
  } as ViewStyle,

  // Controls
  controlsOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    alignItems: 'center',
  } as ViewStyle,

  secondaryControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: spacing.lg,
    paddingHorizontal: spacing.lg,
  } as ViewStyle,

  mainControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  } as ViewStyle,

  controlButtonContainer: {
    alignItems: 'center',
    marginHorizontal: spacing.sm,
  } as ViewStyle,

  controlButton: {
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  } as ViewStyle,

  controlButtonLabel: {
    color: '#FFFFFF',
    marginTop: spacing.xs,
    textAlign: 'center',
    fontWeight: '500',
  } as ViewStyle,

  // Chat Panel
  chatPanel: {
    backgroundColor: '#FFFFFF',
    borderLeftWidth: 1,
    borderLeftColor: '#E5E7EB',
  } as ViewStyle,

  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  } as ViewStyle,

  chatTitle: {
    fontWeight: '600',
    color: CoreColors.DarkJungleGreen,
  } as ViewStyle,

  chatCloseButton: {
    padding: spacing.xs,
  } as ViewStyle,

  chatContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
  } as ViewStyle,

  chatPlaceholder: {
    color: CoreColors.GrayBlue,
    textAlign: 'center',
  } as ViewStyle,
});

export default TabletVideoCallInterface;