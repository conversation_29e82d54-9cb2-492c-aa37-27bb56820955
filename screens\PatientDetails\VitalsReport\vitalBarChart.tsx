import React from 'react';
import {Dimensions, Text, View} from 'react-native';
import {BarChart} from 'react-native-chart-kit';
import Theme from '@/constants/Theme';

interface GraphProps {
  measuredDates: any[];
  measuredValues: any[];
  title: string;
  min: number;
  max: number;
  cMin: number;
  cMax: number;
}

const VitalBarChart = (props: GraphProps) => {
  console.log('measuredValues::::::', props.measuredValues);

  const chartConfig = {
    backgroundColor: 'transparent',
    backgroundGradientTo: 'white',
    backgroundGradientFromOpacity: 0,
    backgroundGradientFrom: 'white',
    backgroundGradientToOpacity: 0,
    color: (opacity = 1) => '#000000',
    labelColor: (opacity = 1) => `rgba(0,0,0,${opacity})`, // Fixed format - no space after comma
    barPercentage: 0.8,
    barRadius: 5,
    decimalPlaces: 0,
    propsForLabels: {
      fontFamily: 'OpenSans-Regular',
      fontSize: 16,
    },
    propsForBackgroundLines: {
      strokeDasharray: '', // solid background lines with no dashes
    },
  };

  return (
    <View>
      <View
        style={{
          marginVertical: 20,
          marginHorizontal: 20,
          borderRadius: 10,
          ...Theme.flexRowSpace,
        }}>
        <Text
          style={{
            color: 'rgb(0,0,255)',
            fontFamily: 'OpenSans-Medium',
            fontSize: 16,
          }}>
          {'\u2B24'} {props.title}
        </Text>
      </View>
      <BarChart
        style={{
          marginVertical: 8,
          borderRadius: 16,
        }}
        data={{
          labels: props.measuredDates,
          datasets: [
            {
              data: props?.measuredValues,
              colors: [
                (opacity = 1) => `rgba(0,0,255,${opacity})`, // Fixed format - no space after comma
                (opacity = 1) => `rgba(0,0,255,${opacity})`,
                (opacity = 1) => `rgba(0,0,255,${opacity})`,
                (opacity = 1) => `rgba(0,0,255,${opacity})`,
                (opacity = 1) => `rgba(0,0,255,${opacity})`,
                (opacity = 1) => `rgba(0,0,255,${opacity})`,
                (opacity = 1) => `rgba(0,0,255,${opacity})`,
              ],
            },
          ],
        }}
        width={Dimensions.get('window').width * 0.9}
        height={220}
        yAxisLabel=""
        yAxisSuffix=""
        chartConfig={chartConfig}
        verticalLabelRotation={0}
        withInnerLines={false}
        showBarTops={true}
        showValuesOnTopOfBars={true}
        withHorizontalLabels={true}
        fromZero={true}
        flatColor={true}
        withCustomBarColorFromData={true}
      />
    </View>
  );
};

export default VitalBarChart;
