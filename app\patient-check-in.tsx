import React from 'react';
import { Stack, useLocalSearchParams } from 'expo-router';
import { Colors } from '@/constants';
import CheckInScreen from '@/screens/PatientDetails/CheckInScreen';

/**
 * Patient Check In Screen Route
 * This screen allows care managers to log check-ins with patients
 */
export default function PatientCheckInRoute() {
  const params = useLocalSearchParams();
  const patientName = params.patientName as string || 'Patient';
  const patientId = params.patientId as string;
  
  return (
    <>
      <Stack.Screen 
        options={{
          headerShown: false // Hide the Stack.Screen header
        }} 
      />
      <CheckInScreen 
        patientId={patientId} 
        patientName={patientName} 
      />
    </>
  );
}
