/**
 * Re-export the improved fillNumberLength function from the .ts file
 * This file exists for backward compatibility with existing imports
 */
import fillNumberLength from './fillNumberLength';

export default function(input: string, inputLength: number): string {
  // Call the improved function with stripNonDigits=true to match the original behavior
  return fillNumberLength(input, inputLength, '#', true);
}
