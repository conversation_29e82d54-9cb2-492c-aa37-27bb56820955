/**
 * UI Components index file
 * 
 * This file exports all UI components migrated from the original React Native project
 * to make them easier to import in other files.
 */

// Text and Typography
export { default as Text } from './Text';
export { ThemedText } from './ThemedText';

// Buttons
export { default as ButtonText } from './Buttons/ButtonText';
export { default as ButtonBorder } from './Buttons/ButtonBorder';
export { default as ButtonLinear } from './Buttons/ButtonLinear';
export { default as ButtonIcon } from './Buttons/ButtonIcon';
export { default as ButtonAdd } from './Buttons/ButtonAdd';

// Input components
export { default as TextInput } from './TextInput';

// Form components
export { TabletTextInput, TabletForm } from './Forms';

// Layout components
export { default as Layout } from './Layout/Layout';
export { default as Container } from './Layout/Container';
export { default as TabletAwareContainer } from './Layout/TabletAwareContainer';
export { default as AdaptiveLayout } from './Layout/AdaptiveLayout';
export { default as ResponsiveGrid } from './Layout/ResponsiveGrid';
export { default as Content } from './Layout/Content';
export { default as Line } from './Layout/Line';
export { default as LayoutItem } from './Layout/LayoutItem';
export { ThemedView } from './ThemedView';

// Modal components
export { default as ModalSlideBottom } from './ModalSlideBottom';

// Navigation components
export { MasterDetailNavigation, SidebarNavigation } from './Navigation';

// Other UI components
export { default as CheckBox } from './CheckBox';
export { default as FocusAwareStatusBar } from './StatusBar/FocusAwareStatusBar';
export { HapticTab } from './HapticTab';
export { Collapsible } from './Collapsible';
export { ExternalLink } from './ExternalLink';

// Accessibility components
export { withAccessibility, AccessibleButton, AccessibleText } from './Accessibility';

// Tablet-optimized screen components
export { TabletHome } from './Home';
export { TabletSchedule } from './Schedule';
export { TabletTasks } from './Tasks';
export { TabletProfile } from './Profile';
export { TabletLogin, TabletSignup } from './Auth';
export { TabletSplash } from './Splash';
export { TabletAlerts } from './Alerts';
export { TabletOnBoarding } from './OnBoarding';
export { TabletOrganization } from './Organization';
export { TabletMedications } from './Medications';