import Text from "@/components/Text";
import { Colors } from "@/constants";
import { Ionicons, MaterialIcons, MaterialCommunityIcons, FontAwesome5 } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import Moment from "moment";
import React from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";

interface VitalCardProps {
  title: string;
  value: number | string;
  value2?: number | string;
  value3?: number | string;
  date: string;
  unit: string;
  min: number;
  max: number;
  cMin: number;
  cMax: number;
  onPress?: () => void;
  isPlaceholder?: boolean;
}

const VitalCard = (props: VitalCardProps) => {
  const {
    title,
    value,
    value2,
    date,
    unit,
    min,
    max,
    cMin,
    cMax,
    onPress,
    isPlaceholder = false,
  } = props;

  // Enhanced vital icon configuration with vector icons
  const getVitalIcon = () => {
    switch (title) {
      case "Heart Rate":
        return {
          iconFamily: "MaterialCommunityIcons",
          iconName: "heart-pulse",
          color: "#FF6B6B",
          bgGradient: ["#FF6B6B", "#EE5A52"],
          iconColor: "#FFFFFF",
          statusColor: "#FF6B6B",
        };
      case "Blood Pressure":
        return {
          iconFamily: "MaterialIcons",
          iconName: "monitor-heart",
          color: "#F25F5C",
          bgGradient: ["#F25F5C", "#EF4444"],
          iconColor: "#FFFFFF",
          statusColor: "#F25F5C",
        };
      case "Blood Sugar":
        return {
          iconFamily: "MaterialCommunityIcons",
          iconName: "water",
          color: "#4ECDC4",
          bgGradient: ["#4ECDC4", "#44A08D"],
          iconColor: "#FFFFFF",
          statusColor: "#4ECDC4",
        };
      case "Oxygen Saturation":
        return {
          iconFamily: "FontAwesome5",
          iconName: "lungs",
          color: "#36B5E0",
          bgGradient: ["#36B5E0", "#3490DC"],
          iconColor: "#FFFFFF",
          statusColor: "#36B5E0",
        };
      case "Temperature":
        return {
          iconFamily: "FontAwesome5",
          iconName: "thermometer-half",
          color: "#FF9F1C",
          bgGradient: ["#FF9F1C", "#F59E0B"],
          iconColor: "#FFFFFF",
          statusColor: "#FF9F1C",
        };
      case "Weight":
        return {
          iconFamily: "FontAwesome5",
          iconName: "weight",
          color: "#7A77FF",
          bgGradient: ["#7A77FF", "#6366F1"],
          iconColor: "#FFFFFF",
          statusColor: "#7A77FF",
        };
      case "Pedometer":
        return {
          iconFamily: "FontAwesome5",
          iconName: "walking",
          color: "#5FB49C",
          bgGradient: ["#5FB49C", "#10B981"],
          iconColor: "#FFFFFF",
          statusColor: "#5FB49C",
        };
      case "Sleep Monitor":
        return {
          iconFamily: "FontAwesome5",
          iconName: "bed",
          color: "#9381FF",
          bgGradient: ["#9381FF", "#8B5CF6"],
          iconColor: "#FFFFFF",
          statusColor: "#9381FF",
        };
      default:
        return {
          iconFamily: "MaterialCommunityIcons",
          iconName: "heart-pulse",
          color: "#FF6B6B",
          bgGradient: ["#FF6B6B", "#EE5A52"],
          iconColor: "#FFFFFF",
          statusColor: "#FF6B6B",
        };
    }
  };

  const vitalIcon = getVitalIcon();
  
  // Enhanced date formatting with relative time
  const formatDate = (dateString: string) => {
    if (!dateString) return "Unknown Date";
    
    const momentDate = Moment(dateString);
    const now = Moment();
    
    if (momentDate.isSame(now, 'day')) {
      return `Today, ${momentDate.format("HH:mm")}`;
    } else if (momentDate.isSame(now.clone().subtract(1, 'day'), 'day')) {
      return `Yesterday, ${momentDate.format("HH:mm")}`;
    } else if (momentDate.isAfter(now.clone().subtract(7, 'days'))) {
      return momentDate.format("dddd, HH:mm");
    } else {
      return momentDate.format("MMM DD, HH:mm");
    }
  };

  // Check if value is within normal range
  const isNormal = (val: number) => {
    if (isNaN(val) || min === undefined || max === undefined) return true;
    return val >= min && val <= max;
  };

  // Check if value is in critical range
  const isCritical = (val: number) => {
    if (isNaN(val) || cMin === undefined || cMax === undefined) return false;
    return val < cMin || val > cMax;
  };

  // Enhanced status color system
  const getStatusColor = (val: number) => {
    if (isNaN(val)) return "#94A3B8";
    if (isCritical(val)) return "#EF4444";
    if (!isNormal(val)) return "#F59E0B";
    return "#10B981";
  };

  // Enhanced status configuration with icons
  const getStatusConfig = (val: number) => {
    if (isNaN(val)) {
      return {
        text: "Unknown",
        color: "#94A3B8",
        icon: "help-circle",
        bgColor: "rgba(148, 163, 184, 0.1)"
      };
    }
    if (isCritical(val)) {
      return {
        text: "Critical",
        color: "#EF4444",
        icon: "warning",
        bgColor: "rgba(239, 68, 68, 0.1)"
      };
    }
    if (!isNormal(val)) {
      return {
        text: "Warning",
        color: "#F59E0B",
        icon: "alert-triangle",
        bgColor: "rgba(245, 158, 11, 0.1)"
      };
    }
    return {
      text: "Normal",
      color: "#10B981",
      icon: "check-circle",
      bgColor: "rgba(16, 185, 129, 0.1)"
    };
  };

  // Enhanced value display with better typography
  const renderValueDisplay = () => {
    if (isPlaceholder) {
      return (
        <View style={styles.placeholderContainer}>
          <View style={styles.placeholderShimmer}>
            <Text size={16} color="#94A3B8" medium>
              Data will be available soon
            </Text>
            <Text size={14} color="#94A3B8" style={styles.placeholderSubtext}>
              Integration in progress
            </Text>
          </View>
        </View>
      );
    }

    // Handle different vital types with enhanced formatting
    if (title === "Blood Pressure" && value2) {
      const statusConfig = getStatusConfig(Number(value));
      return (
        <View style={styles.enhancedValueContainer}>
          <View style={styles.primaryValueRow}>
            <Text size={24} lineHeight={28} bold color={statusConfig.color}>
              {value}/{value2}
            </Text>
            <Text size={14} color="#64748B" medium style={styles.unitText}>
              {unit}
            </Text>
          </View>
          <View style={styles.metricLabels}>
            <Text size={12} color="#94A3B8" medium>
              Systolic / Diastolic
            </Text>
          </View>
        </View>
      );
    } else if (title === "Blood Sugar") {
      const fastingStatus = getStatusConfig(Number(value));
      const randomStatus = getStatusConfig(Number(value2));
      return (
        <View style={styles.enhancedValueContainer}>
          <View style={styles.dualValueRow}>
                         <View style={styles.valueSectionItem}>
               <Text size={14} color="#64748B" medium>Fasting</Text>
               <View style={styles.valueWithUnit}>
                 <Text size={18} bold color={fastingStatus.color}>
                   {value}
                 </Text>
                 <Text size={12} color="#94A3B8" medium>{unit}</Text>
               </View>
             </View>
             <View style={styles.valueDivider} />
             <View style={styles.valueSectionItem}>
               <Text size={14} color="#64748B" medium>Random</Text>
               <View style={styles.valueWithUnit}>
                 <Text size={18} bold color={randomStatus.color}>
                   {value2}
                 </Text>
                 <Text size={12} color="#94A3B8" medium>{unit}</Text>
               </View>
             </View>
          </View>
        </View>
      );
    } else if (title === "Sleep Monitor" && value2) {
      return (
        <View style={styles.enhancedValueContainer}>
          <View style={styles.dualValueRow}>
                         <View style={styles.valueSectionItem}>
               <Text size={14} color="#64748B" medium>Light Sleep</Text>
               <View style={styles.valueWithUnit}>
                 <Text size={18} bold color="#4ECDC4">
                   {value}
                 </Text>
                 <Text size={12} color="#94A3B8" medium>{unit}</Text>
               </View>
             </View>
             <View style={styles.valueDivider} />
             <View style={styles.valueSectionItem}>
               <Text size={14} color="#64748B" medium>Deep Sleep</Text>
               <View style={styles.valueWithUnit}>
                 <Text size={18} bold color="#9381FF">
                   {value2}
                 </Text>
                 <Text size={12} color="#94A3B8" medium>{unit}</Text>
               </View>
             </View>
          </View>
        </View>
      );
    } else {
      const statusConfig = getStatusConfig(Number(value));
      return (
        <View style={styles.enhancedValueContainer}>
          <View style={styles.primaryValueRow}>
            <Text size={26} lineHeight={30} bold color={statusConfig.color}>
              {value}
            </Text>
            <Text size={14} color="#64748B" medium style={styles.unitText}>
              {unit}
            </Text>
          </View>
        </View>
      );
    }
  };

  const primaryStatusConfig = getStatusConfig(Number(value));

  return (
    <TouchableOpacity style={styles.container} onPress={onPress} activeOpacity={0.7}>
      {/* Gradient background overlay */}
      <LinearGradient
        colors={['rgba(255, 255, 255, 0.95)', 'rgba(255, 255, 255, 0.98)'] as const}
        style={styles.gradientOverlay}
      />
      
      {/* Main content */}
      <View style={styles.cardContent}>
        {/* Header section */}
        <View style={styles.headerSection}>
          <View style={styles.iconSection}>
            <LinearGradient
              colors={vitalIcon.bgGradient as any}
              style={styles.iconWrapper}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              {vitalIcon.iconFamily === "MaterialCommunityIcons" && (
                <MaterialCommunityIcons 
                  name={vitalIcon.iconName as any} 
                  size={20} 
                  color={vitalIcon.iconColor} 
                />
              )}
              {vitalIcon.iconFamily === "MaterialIcons" && (
                <MaterialIcons 
                  name={vitalIcon.iconName as any} 
                  size={20} 
                  color={vitalIcon.iconColor} 
                />
              )}
              {vitalIcon.iconFamily === "FontAwesome5" && (
                <FontAwesome5 
                  name={vitalIcon.iconName as any} 
                  size={20} 
                  color={vitalIcon.iconColor} 
                />
              )}
              {vitalIcon.iconFamily === "Ionicons" && (
                <Ionicons 
                  name={vitalIcon.iconName as any} 
                  size={20} 
                  color={vitalIcon.iconColor} 
                />
              )}
            </LinearGradient>
            <View style={styles.titleContainer}>
              <Text size={16} bold color="#1E293B">
                {title}
              </Text>
              <Text size={12} color="#64748B" medium>
                Latest reading
              </Text>
            </View>
          </View>
          
          {/* Enhanced status badge */}
          {title !== "Sleep Monitor" && (
            <View style={[styles.statusBadge, { backgroundColor: primaryStatusConfig.bgColor }]}>
              <MaterialIcons 
                name={primaryStatusConfig.icon as any} 
                size={14} 
                color={primaryStatusConfig.color} 
              />
              <Text size={12} bold color={primaryStatusConfig.color} style={styles.statusText}>
                {primaryStatusConfig.text}
              </Text>
            </View>
          )}
        </View>

        {/* Value display section */}
        <View style={styles.valueSection}>
          {renderValueDisplay()}
        </View>

        {/* Footer section with timestamp */}
        <View style={styles.footerSection}>
          <View style={styles.timestampContainer}>
            <Ionicons name="time-outline" size={14} color="#94A3B8" />
            <Text size={12} color="#94A3B8" medium style={styles.timestampText}>
              {formatDate(date)}
            </Text>
          </View>
          
          {/* Trend indicator placeholder - can be enhanced later */}
          <View style={styles.trendContainer}>
            <MaterialIcons name="trending-up" size={16} color="#10B981" />
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default VitalCard;

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: "#FFFFFF",
    borderWidth: 1,
    borderColor: "#E2E8F0",
    shadowColor: "#1E293B",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
    overflow: "hidden",
    position: "relative",
  },
  gradientOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  cardContent: {
    padding: 16,
    position: "relative",
    zIndex: 1,
  },
  headerSection: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  iconSection: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  iconWrapper: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  titleContainer: {
    flex: 1,
  },
  statusBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 20,
    marginLeft: 8,
  },
  statusText: {
    marginLeft: 4,
  },
  valueSection: {
    marginBottom: 12,
  },
  enhancedValueContainer: {
    marginVertical: 4,
  },
  primaryValueRow: {
    flexDirection: "row",
    alignItems: "baseline",
    marginBottom: 4,
  },
  unitText: {
    marginLeft: 6,
  },
  metricLabels: {
    marginTop: 2,
  },
     dualValueRow: {
     flexDirection: "row",
     alignItems: "center",
     justifyContent: "space-between",
   },
   valueSectionItem: {
     flex: 1,
     alignItems: "center",
   },
  valueDivider: {
    width: 1,
    height: 32,
    backgroundColor: "#E2E8F0",
    marginHorizontal: 12,
  },
  valueWithUnit: {
    flexDirection: "row",
    alignItems: "baseline",
    marginTop: 4,
  },
  footerSection: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: "#F1F5F9",
  },
  timestampContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  timestampText: {
    marginLeft: 6,
  },
  trendContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  placeholderContainer: {
    paddingVertical: 8,
  },
  placeholderShimmer: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: "#F8FAFC",
    borderWidth: 1,
    borderColor: "#E2E8F0",
    alignItems: "center",
  },
  placeholderSubtext: {
    marginTop: 4,
    fontStyle: "italic",
  },
});
