import { Colors } from "@/constants";
import { AlertsProps } from "@/models";
import { Ionicons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import Moment from "moment";
import React, { useCallback } from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";
import Text from "../Text";
import { useRouter } from "expo-router";
import { useDispatch } from "react-redux";
import { setCurentPatientId } from "@/services/actions/currentPatientId";
import { setCurrentPatientName } from "@/services/actions/patientActions";

interface Props extends AlertsProps {
  nameDisplay: boolean;
}

const AlertsItem = (props: Props) => {
  const { navigate } = useNavigation() as any;
  const router = useRouter();
  const dispatch = useDispatch();

  const onPress = useCallback(() => {
    // Set the current patient in Redux
    dispatch(setCurentPatientId(props.patientId));
    dispatch(setCurrentPatientName(props.patientName || ""));

    // Navigate to patient profile screen
    try {
      const params = {
        id: props.patientId.toString(),
        patientName: props.patientName || "",
        mrn: props.mrn || props.patientId.toString(),
      };

      router.push({
        pathname: `/patient/${props.patientId}` as any,
        params: params,
      });
    } catch (error) {
      console.error("Error navigating to patient profile from alert:", error);
      // Fallback navigation if router fails
      navigate("PatientDetails", {
        patientId: props.patientId,
        patientName: props.patientName,
        mrn: props.mrn,
      });
    }
  }, [router, navigate, dispatch, props]);

  // Get alert type icon and color
  const getAlertTypeIcon = () => {
    switch (props?.alertType) {
      case "Critical":
        return {
          name: "warning-outline" as const,
          color: Colors.RedNeonFuchsia,
          bgColor: "rgba(250, 65, 105, 0.1)",
        };
      case "Alarm":
        return {
          name: "notifications-outline" as const,
          color: Colors.Red,
          bgColor: "rgba(250, 65, 105, 0.1)",
        };
      case "Warning":
        return {
          name: "alert-circle-outline" as const,
          color: Colors.pastelOrange,
          bgColor: "rgba(255, 181, 46, 0.1)",
        };
      case "Info":
        return {
          name: "information-circle-outline" as const,
          color: Colors.Malachite,
          bgColor: "rgba(0, 214, 91, 0.1)",
        };
      default:
        return {
          name: "information-circle-outline" as const,
          color: Colors.Malachite,
          bgColor: "rgba(0, 214, 91, 0.1)",
        };
    }
  };

  const alertIcon = getAlertTypeIcon();
  const formattedDate = Moment(props?.createdDate?.replace("Z", "")).format(
    "MMM DD, YYYY HH:mm"
  );

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View
        style={[styles.iconWrapper, { backgroundColor: alertIcon.bgColor }]}
      >
        <Ionicons name={alertIcon.name} size={24} color={alertIcon.color} />
      </View>
      <View style={styles.contentContainer}>
        <View style={styles.headerRow}>
          {props.nameDisplay && (
            <Text size={16} bold color={Colors.Black} marginBottom={4}>
              {props.patientName}
            </Text>
          )}
          <View style={[styles.badge, { backgroundColor: alertIcon.color }]}>
            <Text size={12} bold color={Colors.White}>
              {props.alertType}
            </Text>
          </View>
        </View>

        <Text
          size={15}
          semibold
          color={Colors.DarkJungleGreen}
          lineHeight={20}
          style={styles.descriptionText}
        >
          {props.alertDescription}
        </Text>

        <View style={styles.footerRow}>
          <Ionicons name="time-outline" size={16} color={Colors.GrayBlue} />
          <Text
            size={13}
            lineHeight={16}
            color={Colors.GrayBlue}
            style={styles.dateText}
          >
            {formattedDate}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default AlertsItem;

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 12,
    width: "100%",
    backgroundColor: Colors.White,
    flexDirection: "row",
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
    overflow: "hidden",
  },
  iconWrapper: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 14,
  },
  contentContainer: {
    flex: 1,
  },
  headerRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 6,
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  descriptionText: {
    marginBottom: 8,
  },
  footerRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  dateText: {
    marginLeft: 4,
  },
});
