import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import TabletAwareContainer from '@/components/Layout/TabletAwareContainer';
import AdaptiveLayout from '@/components/Layout/AdaptiveLayout';
import AccessibleText from '@/components/Accessibility/AccessibleText';
import AccessibleButton from '@/components/Accessibility/AccessibleButton';
import { isTablet, isLargeTablet, spacing } from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: string;
  department: string;
  avatar?: string;
  organization: string;
  licenseNumber?: string;
  specialization?: string;
  joinDate: string;
}

interface TabletProfileProps {
  profile?: UserProfile;
  onEditProfile?: () => void;
  onChangePassword?: () => void;
  onLogout?: () => void;
  onSettingsPress?: (setting: string) => void;
}

/**
 * Tablet-optimized Profile screen component
 */
const TabletProfile: React.FC<TabletProfileProps> = ({
  profile,
  onEditProfile,
  onChangePassword,
  onLogout,
  onSettingsPress,
}) => {
  const [selectedSection, setSelectedSection] = useState<'profile' | 'settings' | 'about'>('profile');

  // Sample profile data
  const sampleProfile: UserProfile = {
    id: '1',
    name: 'Dr. Sarah Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    role: 'Care Provider',
    department: 'Cardiology',
    organization: 'WatchRx Health',
    licenseNumber: 'MD123456',
    specialization: 'Cardiovascular Medicine',
    joinDate: '2023-01-15',
  };

  const userProfile = profile || sampleProfile;

  const settingsOptions = [
    {
      id: 'notifications',
      title: 'Notifications',
      description: 'Manage notification preferences',
      icon: '🔔',
    },
    {
      id: 'privacy',
      title: 'Privacy & Security',
      description: 'Privacy settings and security options',
      icon: '🔒',
    },
    {
      id: 'accessibility',
      title: 'Accessibility',
      description: 'Accessibility and display options',
      icon: '♿',
    },
    {
      id: 'language',
      title: 'Language & Region',
      description: 'Language and regional settings',
      icon: '🌐',
    },
    {
      id: 'data',
      title: 'Data & Storage',
      description: 'Manage app data and storage',
      icon: '💾',
    },
    {
      id: 'help',
      title: 'Help & Support',
      description: 'Get help and contact support',
      icon: '❓',
    },
  ];

  const renderProfileHeader = () => (
    <View style={styles.profileHeader}>
      <View style={styles.avatarContainer}>
        {userProfile.avatar ? (
          <Image source={{ uri: userProfile.avatar }} style={styles.avatar} />
        ) : (
          <View style={styles.avatarPlaceholder}>
            <AccessibleText variant=\"h1\" style={styles.avatarText}>
              {userProfile.name.split(' ').map(n => n[0]).join('')}
            </AccessibleText>
          </View>
        )}
        <TouchableOpacity
          style={styles.editAvatarButton}
          onPress={() => {}}
          accessible={true}
          accessibilityLabel=\"Edit profile picture\"
          accessibilityRole=\"button\"
        >
          <AccessibleText variant=\"caption\" style={styles.editAvatarText}>
            ✏️
          </AccessibleText>
        </TouchableOpacity>
      </View>
      
      <View style={styles.profileInfo}>
        <AccessibleText variant=\"h2\" style={styles.profileName}>
          {userProfile.name}
        </AccessibleText>
        <AccessibleText variant=\"body\" style={styles.profileRole}>
          {userProfile.role} • {userProfile.department}
        </AccessibleText>
        <AccessibleText variant=\"bodySmall\" style={styles.profileOrganization}>
          {userProfile.organization}
        </AccessibleText>
      </View>
    </View>
  );

  const renderProfileDetails = () => (
    <View style={styles.profileDetails}>
      <AccessibleText variant=\"h3\" style={styles.sectionTitle}>
        Contact Information
      </AccessibleText>
      
      <View style={styles.detailRow}>
        <AccessibleText variant=\"bodySmall\" style={styles.detailLabel}>
          Email
        </AccessibleText>
        <AccessibleText variant=\"body\" style={styles.detailValue}>
          {userProfile.email}
        </AccessibleText>
      </View>
      
      <View style={styles.detailRow}>
        <AccessibleText variant=\"bodySmall\" style={styles.detailLabel}>
          Phone
        </AccessibleText>
        <AccessibleText variant=\"body\" style={styles.detailValue}>
          {userProfile.phone}
        </AccessibleText>
      </View>
      
      {userProfile.licenseNumber && (
        <View style={styles.detailRow}>
          <AccessibleText variant=\"bodySmall\" style={styles.detailLabel}>
            License Number
          </AccessibleText>
          <AccessibleText variant=\"body\" style={styles.detailValue}>
            {userProfile.licenseNumber}
          </AccessibleText>
        </View>
      )}
      
      {userProfile.specialization && (
        <View style={styles.detailRow}>
          <AccessibleText variant=\"bodySmall\" style={styles.detailLabel}>
            Specialization
          </AccessibleText>
          <AccessibleText variant=\"body\" style={styles.detailValue}>
            {userProfile.specialization}
          </AccessibleText>
        </View>
      )}
      
      <View style={styles.detailRow}>
        <AccessibleText variant=\"bodySmall\" style={styles.detailLabel}>
          Member Since
        </AccessibleText>
        <AccessibleText variant=\"body\" style={styles.detailValue}>
          {new Date(userProfile.joinDate).toLocaleDateString()}
        </AccessibleText>
      </View>
      
      <View style={styles.profileActions}>
        <AccessibleButton
          title=\"Edit Profile\"
          onPress={onEditProfile}
          variant=\"primary\"
          style={styles.actionButton}
        />
        <AccessibleButton
          title=\"Change Password\"
          onPress={onChangePassword}
          variant=\"outline\"
          style={styles.actionButton}
        />
      </View>
    </View>
  );

  const renderSettingsOption = (option: typeof settingsOptions[0]) => (
    <TouchableOpacity
      key={option.id}
      style={styles.settingsOption}
      onPress={() => onSettingsPress?.(option.id)}
      accessible={true}
      accessibilityLabel={option.title}
      accessibilityHint={option.description}
      accessibilityRole=\"button\"
    >
      <View style={styles.settingsOptionIcon}>
        <AccessibleText variant=\"body\" style={styles.settingsIcon}>
          {option.icon}
        </AccessibleText>
      </View>
      <View style={styles.settingsOptionContent}>
        <AccessibleText variant=\"body\" style={styles.settingsOptionTitle}>
          {option.title}
        </AccessibleText>
        <AccessibleText variant=\"bodySmall\" style={styles.settingsOptionDescription}>
          {option.description}
        </AccessibleText>
      </View>
      <AccessibleText variant=\"body\" style={styles.settingsChevron}>
        ›
      </AccessibleText>
    </TouchableOpacity>
  );

  const renderSettings = () => (
    <View style={styles.settingsContainer}>
      <AccessibleText variant=\"h3\" style={styles.sectionTitle}>
        App Settings
      </AccessibleText>
      
      {settingsOptions.map(renderSettingsOption)}
      
      <View style={styles.dangerZone}>
        <AccessibleText variant=\"h3\" style={styles.dangerZoneTitle}>
          Account Actions
        </AccessibleText>
        <AccessibleButton
          title=\"Sign Out\"
          onPress={onLogout}
          variant=\"text\"
          style={[styles.actionButton, styles.dangerButton]}
        />
      </View>
    </View>
  );

  const renderAbout = () => (
    <View style={styles.aboutContainer}>
      <AccessibleText variant=\"h3\" style={styles.sectionTitle}>
        About WatchRx Care Team
      </AccessibleText>
      
      <View style={styles.aboutSection}>
        <AccessibleText variant=\"body\" style={styles.aboutText}>
          WatchRx Care Team is a comprehensive healthcare management platform designed to streamline patient care and improve health outcomes.
        </AccessibleText>
      </View>
      
      <View style={styles.aboutSection}>
        <AccessibleText variant=\"bodySmall\" style={styles.aboutLabel}>
          Version
        </AccessibleText>
        <AccessibleText variant=\"body\" style={styles.aboutValue}>
          1.0.0 (Build 123)
        </AccessibleText>
      </View>
      
      <View style={styles.aboutSection}>
        <AccessibleText variant=\"bodySmall\" style={styles.aboutLabel}>
          Last Updated
        </AccessibleText>
        <AccessibleText variant=\"body\" style={styles.aboutValue}>
          January 15, 2024
        </AccessibleText>
      </View>
      
      <View style={styles.aboutLinks}>
        <TouchableOpacity style={styles.aboutLink}>
          <AccessibleText variant=\"body\" style={styles.aboutLinkText}>
            Privacy Policy
          </AccessibleText>
        </TouchableOpacity>
        <TouchableOpacity style={styles.aboutLink}>
          <AccessibleText variant=\"body\" style={styles.aboutLinkText}>
            Terms of Service
          </AccessibleText>
        </TouchableOpacity>
        <TouchableOpacity style={styles.aboutLink}>
          <AccessibleText variant=\"body\" style={styles.aboutLinkText}>
            Contact Support
          </AccessibleText>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderSectionTabs = () => (
    <View style={styles.sectionTabs}>
      {(['profile', 'settings', 'about'] as const).map((section) => (
        <TouchableOpacity
          key={section}
          style={[
            styles.sectionTab,
            selectedSection === section && styles.activeSectionTab,
          ]}
          onPress={() => setSelectedSection(section)}
          accessible={true}
          accessibilityLabel={`${section} section`}
          accessibilityRole=\"button\"
        >
          <AccessibleText
            variant=\"body\"
            style={[
              styles.sectionTabText,
              selectedSection === section && styles.activeSectionTabText,
            ]}
          >
            {section.charAt(0).toUpperCase() + section.slice(1)}
          </AccessibleText>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderSectionContent = () => {
    switch (selectedSection) {
      case 'settings':
        return renderSettings();
      case 'about':
        return renderAbout();
      case 'profile':
      default:
        return renderProfileDetails();
    }
  };

  return (
    <TabletAwareContainer style={styles.container}>
      <AdaptiveLayout
        mobileLayout={() => (
          <ScrollView style={styles.mobileLayout}>
            {renderProfileHeader()}
            {renderSectionTabs()}
            {renderSectionContent()}
          </ScrollView>
        )}
        tabletLayout={() => (
          <View style={styles.tabletLayout}>
            <View style={styles.leftPanel}>
              {renderProfileHeader()}
              {renderSectionTabs()}
            </View>
            <View style={styles.rightPanel}>
              <ScrollView showsVerticalScrollIndicator={false}>
                {renderSectionContent()}
              </ScrollView>
            </View>
          </View>
        )}
      />
    </TabletAwareContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  mobileLayout: {
    flex: 1,
  },
  tabletLayout: {
    flex: 1,
    flexDirection: 'row',
    padding: spacing.md,
  },
  leftPanel: {
    width: isLargeTablet() ? 350 : 300,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: spacing.lg,
    marginRight: spacing.md,
  },
  rightPanel: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: spacing.lg,
  },
  profileHeader: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    marginBottom: spacing.lg,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: spacing.md,
  },
  avatar: {
    width: isTablet() ? 100 : 80,
    height: isTablet() ? 100 : 80,
    borderRadius: isTablet() ? 50 : 40,
  },
  avatarPlaceholder: {
    width: isTablet() ? 100 : 80,
    height: isTablet() ? 100 : 80,
    borderRadius: isTablet() ? 50 : 40,
    backgroundColor: CoreColors.TurquoiseBlue,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  editAvatarText: {
    fontSize: 16,
  },
  profileInfo: {
    alignItems: 'center',
  },
  profileName: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.xs,
    textAlign: 'center',
  },
  profileRole: {
    color: CoreColors.SlateGray,
    marginBottom: spacing.xs,
    textAlign: 'center',
  },
  profileOrganization: {
    color: CoreColors.TurquoiseBlue,
    textAlign: 'center',
  },
  sectionTabs: {
    flexDirection: 'row',
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 4,
    marginBottom: spacing.lg,
  },
  sectionTab: {
    flex: 1,
    paddingVertical: spacing.sm,
    borderRadius: 6,
    alignItems: 'center',
  },
  activeSectionTab: {
    backgroundColor: '#FFFFFF',
  },
  sectionTabText: {
    color: CoreColors.SlateGray,
  },
  activeSectionTabText: {
    color: CoreColors.DarkJungleGreen,
    fontWeight: '600',
  },
  profileDetails: {
    flex: 1,
  },
  sectionTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.md,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  detailLabel: {
    color: CoreColors.SlateGray,
    flex: 1,
  },
  detailValue: {
    color: CoreColors.DarkJungleGreen,
    flex: 2,
    textAlign: 'right',
  },
  profileActions: {
    marginTop: spacing.xl,
  },
  actionButton: {
    marginBottom: spacing.md,
  },
  settingsContainer: {
    flex: 1,
  },
  settingsOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  settingsOptionIcon: {
    width: 40,
    alignItems: 'center',
    marginRight: spacing.md,
  },
  settingsIcon: {
    fontSize: 20,
  },
  settingsOptionContent: {
    flex: 1,
  },
  settingsOptionTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.xs,
  },
  settingsOptionDescription: {
    color: CoreColors.SlateGray,
  },
  settingsChevron: {
    color: CoreColors.SlateGray,
    fontSize: 20,
  },
  dangerZone: {
    marginTop: spacing.xl,
    paddingTop: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  dangerZoneTitle: {
    color: CoreColors.RedOrange,
    marginBottom: spacing.md,
  },
  dangerButton: {
    borderColor: CoreColors.RedOrange,
  },
  aboutContainer: {
    flex: 1,
  },
  aboutSection: {
    marginBottom: spacing.lg,
  },
  aboutText: {
    color: CoreColors.SlateGray,
    lineHeight: 24,
  },
  aboutLabel: {
    color: CoreColors.SlateGray,
    marginBottom: spacing.xs,
  },
  aboutValue: {
    color: CoreColors.DarkJungleGreen,
  },
  aboutLinks: {
    marginTop: spacing.xl,
  },
  aboutLink: {
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  aboutLinkText: {
    color: CoreColors.TurquoiseBlue,
  },
});

export default TabletProfile;