import { Dimensions, PixelRatio } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Screen size breakpoints
export const SCREEN_BREAKPOINTS = {
  xs: 320,  // Very small phones
  sm: 375,  // Small phones (iPhone SE)
  md: 414,  // Medium phones (iPhone 11)
  lg: 768,  // Tablets (iPad Mini, small Android tablets)
  xl: 1024, // Large tablets (iPad Pro, large Android tablets)
  xxl: 1366, // Extra large tablets (iPad Pro 12.9")
};

// Screen height breakpoints
export const HEIGHT_BREAKPOINTS = {
  short: 600,   // Very short screens
  medium: 700,  // Medium screens
  tall: 800,    // Tall screens
  extraTall: 900, // Extra tall screens
};

// Get current screen size category
export const getScreenSize = () => {
  if (screenWidth <= SCREEN_BREAKPOINTS.xs) return 'xs';
  if (screenWidth <= SCREEN_BREAKPOINTS.sm) return 'sm';
  if (screenWidth <= SCREEN_BREAKPOINTS.md) return 'md';
  if (screenWidth <= SCREEN_BREAKPOINTS.lg) return 'lg';
  if (screenWidth <= SCREEN_BREAKPOINTS.xl) return 'xl';
  return 'xxl';
};

// Tablet-specific detection functions
export const isTablet = () => screenWidth >= SCREEN_BREAKPOINTS.lg;
export const isLargeTablet = () => screenWidth >= SCREEN_BREAKPOINTS.xl;
export const isExtraLargeTablet = () => screenWidth >= SCREEN_BREAKPOINTS.xxl;

// Device type detection
export const getDeviceType = () => {
  if (screenWidth < SCREEN_BREAKPOINTS.lg) return 'phone';
  if (screenWidth < SCREEN_BREAKPOINTS.xl) return 'tablet';
  if (screenWidth < SCREEN_BREAKPOINTS.xxl) return 'large-tablet';
  return 'extra-large-tablet';
};

// Orientation detection
export const getOrientation = () => {
  return screenWidth > screenHeight ? 'landscape' : 'portrait';
};

export const isLandscape = () => screenWidth > screenHeight;
export const isPortrait = () => screenHeight >= screenWidth;

// Get current screen height category
export const getScreenHeight = () => {
  if (screenHeight <= HEIGHT_BREAKPOINTS.short) return 'short';
  if (screenHeight <= HEIGHT_BREAKPOINTS.medium) return 'medium';
  if (screenHeight <= HEIGHT_BREAKPOINTS.tall) return 'tall';
  return 'extraTall';
};

// Responsive spacing system
export const spacing = {
  xs: screenWidth < SCREEN_BREAKPOINTS.sm ? 4 : screenWidth < SCREEN_BREAKPOINTS.lg ? 6 : 8,
  sm: screenWidth < SCREEN_BREAKPOINTS.sm ? 8 : screenWidth < SCREEN_BREAKPOINTS.lg ? 12 : 16,
  md: screenWidth < SCREEN_BREAKPOINTS.sm ? 12 : screenWidth < SCREEN_BREAKPOINTS.lg ? 16 : 20,
  lg: screenWidth < SCREEN_BREAKPOINTS.sm ? 16 : screenWidth < SCREEN_BREAKPOINTS.lg ? 20 : 24,
  xl: screenWidth < SCREEN_BREAKPOINTS.sm ? 20 : screenWidth < SCREEN_BREAKPOINTS.lg ? 24 : 32,
  xxl: screenWidth < SCREEN_BREAKPOINTS.sm ? 24 : screenWidth < SCREEN_BREAKPOINTS.lg ? 32 : 40,
};

// Responsive typography system
export const typography = {
  h1: {
    fontSize: screenHeight < HEIGHT_BREAKPOINTS.short ? 24 : screenHeight < HEIGHT_BREAKPOINTS.medium ? 28 : isTablet() ? 36 : 32,
    lineHeight: screenHeight < HEIGHT_BREAKPOINTS.short ? 28 : screenHeight < HEIGHT_BREAKPOINTS.medium ? 32 : isTablet() ? 42 : 36,
    fontWeight: '700' as const,
  },
  h2: {
    fontSize: screenHeight < HEIGHT_BREAKPOINTS.short ? 20 : screenHeight < HEIGHT_BREAKPOINTS.medium ? 22 : isTablet() ? 28 : 24,
    lineHeight: screenHeight < HEIGHT_BREAKPOINTS.short ? 24 : screenHeight < HEIGHT_BREAKPOINTS.medium ? 26 : isTablet() ? 34 : 28,
    fontWeight: '600' as const,
  },
  h3: {
    fontSize: screenHeight < HEIGHT_BREAKPOINTS.short ? 16 : screenHeight < HEIGHT_BREAKPOINTS.medium ? 18 : isTablet() ? 24 : 20,
    lineHeight: screenHeight < HEIGHT_BREAKPOINTS.short ? 20 : screenHeight < HEIGHT_BREAKPOINTS.medium ? 22 : isTablet() ? 30 : 24,
    fontWeight: '600' as const,
  },
  body: {
    fontSize: screenHeight < HEIGHT_BREAKPOINTS.short ? 14 : isTablet() ? 18 : 16,
    lineHeight: screenHeight < HEIGHT_BREAKPOINTS.short ? 18 : isTablet() ? 24 : 20,
    fontWeight: '400' as const,
  },
  bodySmall: {
    fontSize: screenHeight < HEIGHT_BREAKPOINTS.short ? 12 : isTablet() ? 16 : 14,
    lineHeight: screenHeight < HEIGHT_BREAKPOINTS.short ? 16 : isTablet() ? 22 : 18,
    fontWeight: '400' as const,
  },
  caption: {
    fontSize: screenHeight < HEIGHT_BREAKPOINTS.short ? 10 : isTablet() ? 14 : 12,
    lineHeight: screenHeight < HEIGHT_BREAKPOINTS.short ? 14 : isTablet() ? 18 : 16,
    fontWeight: '400' as const,
  },
};

// Responsive card dimensions
export const cardDimensions = {
  minHeight: screenHeight < HEIGHT_BREAKPOINTS.short ? 80 : screenHeight < HEIGHT_BREAKPOINTS.medium ? 100 : 120,
  padding: screenWidth < SCREEN_BREAKPOINTS.sm ? 12 : isTablet() ? 20 : 16,
  borderRadius: isTablet() ? 16 : 12,
  margin: screenWidth < SCREEN_BREAKPOINTS.sm ? 8 : isTablet() ? 16 : 12,
};

// Grid system for responsive layouts
export const grid = {
  container: {
    paddingHorizontal: screenWidth < SCREEN_BREAKPOINTS.sm ? 16 : isTablet() ? 32 : 20,
    paddingVertical: screenHeight < HEIGHT_BREAKPOINTS.short ? 12 : isTablet() ? 24 : 16,
  },
  row: {
    marginBottom: screenHeight < HEIGHT_BREAKPOINTS.short ? 12 : isTablet() ? 20 : 16,
  },
  col: {
    marginHorizontal: screenWidth < SCREEN_BREAKPOINTS.sm ? 4 : isTablet() ? 8 : 6,
  },
};

// Responsive icon sizes
export const iconSizes = {
  xs: screenHeight < HEIGHT_BREAKPOINTS.short ? 12 : isTablet() ? 16 : 14,
  sm: screenHeight < HEIGHT_BREAKPOINTS.short ? 16 : isTablet() ? 20 : 18,
  md: screenHeight < HEIGHT_BREAKPOINTS.short ? 20 : isTablet() ? 28 : 24,
  lg: screenHeight < HEIGHT_BREAKPOINTS.short ? 24 : isTablet() ? 32 : 28,
  xl: screenHeight < HEIGHT_BREAKPOINTS.short ? 28 : isTablet() ? 36 : 32,
};

// Responsive button dimensions
export const buttonDimensions = {
  height: screenHeight < HEIGHT_BREAKPOINTS.short ? 44 : isTablet() ? 52 : 48,
  padding: {
    horizontal: screenWidth < SCREEN_BREAKPOINTS.sm ? 16 : isTablet() ? 24 : 20,
    vertical: screenHeight < HEIGHT_BREAKPOINTS.short ? 10 : isTablet() ? 14 : 12,
  },
  borderRadius: isTablet() ? 12 : 8,
};

// Helper functions for responsive design
export const wp = (percentage: number) => {
  return (screenWidth * percentage) / 100;
};

export const hp = (percentage: number) => {
  return (screenHeight * percentage) / 100;
};

// Scale function for responsive sizing
export const scale = (size: number) => {
  const baseWidth = 375; // iPhone 8 width as base
  return (screenWidth / baseWidth) * size;
};

// Responsive font scaling
export const fontScale = (size: number) => {
  const baseWidth = 375;
  const scaledSize = (screenWidth / baseWidth) * size;
  return Math.max(scaledSize, size * 0.8); // Minimum 80% of original size
};

// Patient growth responsive sizing (making it much smaller as requested)
export const patientGrowthCard = {
  width: screenWidth < SCREEN_BREAKPOINTS.sm ? wp(28) : wp(22), // Even smaller width
  height: screenHeight < HEIGHT_BREAKPOINTS.short ? 35 : screenHeight < HEIGHT_BREAKPOINTS.medium ? 40 : 45, // Even smaller height
  fontSize: screenHeight < HEIGHT_BREAKPOINTS.short ? 9 : screenHeight < HEIGHT_BREAKPOINTS.medium ? 11 : 13, // Even smaller font
  iconSize: screenHeight < HEIGHT_BREAKPOINTS.short ? 10 : screenHeight < HEIGHT_BREAKPOINTS.medium ? 12 : 14, // Even smaller icon
  padding: screenHeight < HEIGHT_BREAKPOINTS.short ? 4 : screenHeight < HEIGHT_BREAKPOINTS.medium ? 6 : 8, // Even smaller padding
  marginBottom: screenHeight < HEIGHT_BREAKPOINTS.short ? 6 : screenHeight < HEIGHT_BREAKPOINTS.medium ? 8 : 10, // Even smaller margin
  // Make it more compact by reducing internal spacing
  compactMode: true,
};

// Responsive layout helpers
export const layout = {
  isSmallScreen: screenWidth < SCREEN_BREAKPOINTS.sm,
  isShortScreen: screenHeight < HEIGHT_BREAKPOINTS.short,
  isMediumScreen: screenWidth >= SCREEN_BREAKPOINTS.sm && screenWidth < SCREEN_BREAKPOINTS.lg,
  isLargeScreen: screenWidth >= SCREEN_BREAKPOINTS.lg,
  
  // Content area calculations
  contentWidth: screenWidth - (grid.container.paddingHorizontal * 2),
  safeContentHeight: screenHeight * 0.85, // 85% of screen height for safe content area
  
  // Card grid calculations
  twoColumnWidth: (screenWidth - (grid.container.paddingHorizontal * 2) - spacing.md) / 2,
  threeColumnWidth: (screenWidth - (grid.container.paddingHorizontal * 2) - (spacing.md * 2)) / 3,
};

// Tablet-specific layout configurations
export const tabletLayout = {
  // Maximum content width for tablets to prevent overly wide layouts
  maxContentWidth: isLargeTablet() ? 1200 : isTablet() ? 900 : screenWidth,
  
  // Sidebar width for master-detail layouts
  sidebarWidth: isLandscape() && isTablet() ? (isLargeTablet() ? 400 : 320) : 0,
  
  // Master-detail split ratios
  masterDetailSplit: {
    master: isLargeTablet() ? 0.35 : 0.4, // 35% or 40% for master panel
    detail: isLargeTablet() ? 0.65 : 0.6,  // 65% or 60% for detail panel
  },
  
  // Grid columns for different orientations
  gridColumns: {
    portrait: {
      phone: 1,
      tablet: isLargeTablet() ? 2 : 2,
      largeTablet: 3,
    },
    landscape: {
      phone: 1,
      tablet: isLargeTablet() ? 3 : 2,
      largeTablet: 4,
    },
  },
  
  // Touch target sizes for tablets
  touchTargets: {
    minimum: 44,      // Minimum accessible touch target
    comfortable: 48,  // Comfortable touch target for tablets
    spacious: 56,     // Spacious touch target for large tablets
  },
  
  // Tablet-specific spacing multipliers
  spacingMultiplier: isLargeTablet() ? 1.5 : isTablet() ? 1.25 : 1,
};

export default {
  spacing,
  typography,
  cardDimensions,
  grid,
  iconSizes,
  buttonDimensions,
  wp,
  hp,
  scale,
  fontScale,
  patientGrowthCard,
  layout,
  tabletLayout,
  getScreenSize,
  getScreenHeight,
  getDeviceType,
  getOrientation,
  isTablet,
  isLargeTablet,
  isExtraLargeTablet,
  isLandscape,
  isPortrait,
}; 