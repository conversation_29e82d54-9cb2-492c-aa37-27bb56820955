import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams } from 'expo-router';
import { useDispatch } from 'react-redux';

import VideoCall from '@/screens/PatientDetails/VideoCall';
import { setCurrentPatientId } from '@/services/actions/patientActions';
import { Colors } from '@/constants';

export default function VideoCallRoute() {
  const { id } = useLocalSearchParams();
  const dispatch = useDispatch();
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    try {
      console.log('VideoCallRoute: Received ID parameter:', id);
      if (id) {
        console.log('VideoCallRoute: Setting current patient ID:', id);
        dispatch(setCurrentPatientId(id as string));
      } else {
        console.warn('VideoCallRoute: No ID parameter received');
        setError('No patient ID provided');
      }
    } catch (err) {
      console.error('VideoCallRoute: Error in useEffect:', err);
      setError('Error initializing video call');
    }
  }, [id, dispatch]);

  if (error) {
    return (
      <SafeAreaView style={styles.container} edges={['left', 'right']}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      <VideoCall />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: Colors.InnearColor,
    textAlign: 'center',
  },
});
