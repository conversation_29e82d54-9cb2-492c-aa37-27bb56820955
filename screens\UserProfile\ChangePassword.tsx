import {StackActions, useNavigation} from '@react-navigation/native';
import React, {memo, useCallback, useEffect, useState} from 'react';
import {<PERSON><PERSON>, ScrollView, StyleSheet, View} from 'react-native';
import {useDispatch} from 'react-redux';
import ButtonLinear from '@/components/Buttons/ButtonLinear';
import InputApp from '@/components/InputApp';
import Container from '@/components/Layout/Container';
import Loader from '@/components/Loader/Loader';
import Text from '@/components/Text';
import {Colors, Routes} from '@/constants';
import {userLogOut} from '@/services/actions/logOutActions';
import {apiPostWithToken} from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import {
  fetchCredentialsSInfo,
  resetCredentials,
} from '@/services/secure-storage';
import Theme from '@/constants/Theme';
import {isInternetAvailable} from '@/utils/internetCheck/connectivityCheck';
import scale from '@/utils/scale';
import passwordValidation from '@/utils/validation/PasswordValidation';
import NavigationHeader from '@/components/NavigationHeader';
import TextWrapper from '@/components/TextWrapper';
import {MaterialIcons, Feather} from '@expo/vector-icons';
interface RecoveryPasswordProps {}

const ChangePassword = memo((props: RecoveryPasswordProps) => {
  const [existingPassword, setExistingPassword] = useState('');
  const [existingUserName, setExistingUserName] = useState('');
  const [loader, setLoader] = useState(false);

  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const [visibleNewPassword, setVisibleNewPassword] = useState(false);
  const [visibleConfirmPassword, setVisibleConfirmPassword] = useState(false);

  const [currentPasswordError, setCurrentPasswordError] = useState('');
  const [newPasswordError, setNewPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');
  const [touched, setTouched] = useState(false);

  const navigation = useNavigation();
  const dispatch = useDispatch();

  const onShowHideNewPassword = useCallback(() => {
    setVisibleNewPassword(prev => !prev);
  }, []);

  const onShowHideConfirmPassword = useCallback(() => {
    setVisibleConfirmPassword(prev => !prev);
  }, []);

  useEffect(() => {
    getCurrentPassword();
  }, []);

  const changepasswordApiCall = async () => {
    setLoader(true);
    const response = await apiPostWithToken(
      {loginId: existingUserName, password: newPassword},
      URLS.caregiverUrl + 'careGiverPasswordReset',
    );
    if (response?.status == 200) {
      console.log('Response:', JSON.stringify(response?.data?.responseMessage));
      setLoader(false);
      if (response?.data?.responseMessage === 'Operation successful') {
        resetCredentials();
        dispatch(userLogOut());
        navigation.dispatch(
          StackActions.replace(Routes.ChangePasswordSuccessful, {}),
        );
      } else {
        Alert.alert('Error', 'Change password failed, Please try again...', [
          {text: 'Dismiss'},
        ]);
      }
    } else {
      setLoader(false);
      const errorMessage = response?.response?.data?.responseMessage
        ? response?.response?.data?.responseMessage
        : response.message === 'Network Error'
        ? 'Network error. Please check your data connection.'
        : response.message;
      Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
    }
  };

  const getCurrentPassword = async () => {
    return fetchCredentialsSInfo().then(async cred => {
      if (cred) {
        setExistingPassword(cred.password);
        setExistingUserName(cred.username);
      }
    });
  };

  return (
    <Container style={styles.container}>
      <NavigationHeader 
        title="Change Password"
        showBackButton={true}
        showLogo={false}
        showRightLogo={true}
      />
      <Loader modalVisible={loader} />
      
      <TextWrapper style={styles.contentContainer}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}>
          
          {/* Password Form Section */}
          <TextWrapper style={styles.sectionCard}>
            <TextWrapper style={styles.sectionHeader}>
              <MaterialIcons name="lock" size={24} color={Colors.TealBlue} />
              <TextWrapper style={styles.headerTextContainer}>
                <Text size={18} bold color={Colors.DarkJungleGreen}>
                  Password Settings
                </Text>
              </TextWrapper>
            </TextWrapper>
            
            <TextWrapper style={styles.inputGroup}>
              {/* Current Password */}
              <InputApp
                title={'Current Password'}
                value={currentPassword}
                onChangeText={text => {
                  setCurrentPassword(text);
                  if (existingPassword !== text) {
                    setCurrentPasswordError('Current password not matching');
                  } else {
                    setCurrentPasswordError('');
                  }
                }}
                marginTop={0}
                secureTextEntry={true}
                isShowIcon={true}
                icon={<Feather name="eye" size={20} color={Colors.GrayBlue} />}
                iconPress={() => {}}
                placeholder="Enter your current password"
              />
              
              {currentPasswordError ? (
                <TextWrapper style={styles.errorContainer}>
                  <MaterialIcons name="error-outline" size={16} color={Colors.Tomato} />
                  <Text style={styles.errorText}>{currentPasswordError}</Text>
                </TextWrapper>
              ) : null}
              
              {/* New Password */}
              <InputApp
                title={'New Password'}
                value={newPassword}
                onChangeText={text => {
                  setNewPassword(text);
                  if (text) {
                    if (passwordValidation(text)) {
                      setNewPasswordError('');
                    } else {
                      setNewPasswordError(
                        'Password must be alphanumeric with min 8 - max 16 characters, 1 uppercase, 1 lowercase, 1 special character and no space.',
                      );
                    }
                  } else {
                    setNewPasswordError(
                      'Password must be alphanumeric with min 8 - max 16 characters, 1 uppercase, 1 lowercase, 1 special character and no space.',
                    );
                  }
                }}
                marginTop={24}
                secureTextEntry={!visibleNewPassword}
                isShowIcon={true}
                icon={<Feather name={visibleNewPassword ? "eye-off" : "eye"} size={20} color={Colors.GrayBlue} />}
                iconPress={onShowHideNewPassword}
                placeholder="Enter your new password"
              />
              
              {newPasswordError ? (
                <TextWrapper style={styles.errorContainer}>
                  <MaterialIcons name="error-outline" size={16} color={Colors.Tomato} />
                  <Text style={styles.errorText}>{newPasswordError}</Text>
                </TextWrapper>
              ) : null}
              
              {/* Confirm Password */}
              <InputApp
                title={'Confirm New Password'}
                value={confirmPassword}
                onChangeText={text => {
                  setConfirmPassword(text);
                  setTouched(true);
                }}
                marginTop={24}
                secureTextEntry={!visibleConfirmPassword}
                isShowIcon={true}
                icon={<Feather name={visibleConfirmPassword ? "eye-off" : "eye"} size={20} color={Colors.GrayBlue} />}
                iconPress={onShowHideConfirmPassword}
                placeholder="Confirm your new password"
              />
              
              {touched && confirmPassword !== newPassword ? (
                <TextWrapper style={styles.errorContainer}>
                  <MaterialIcons name="error-outline" size={16} color={Colors.Tomato} />
                  <Text style={styles.errorText}>Password Mismatch</Text>
                </TextWrapper>
              ) : null}
            </TextWrapper>
            
            
          </TextWrapper>
          
          <ButtonLinear
            white
            title={'Change Password'}
            onPress={async () => {
              if (await isInternetAvailable()) {
                changepasswordApiCall();
              }
            }}
            disabled={
              currentPasswordError?.length !== 0 ||
              currentPassword?.length === 0 ||
              newPasswordError?.length !== 0 ||
              newPassword?.length <= 8 ||
              confirmPasswordError?.length !== 0 ||
              confirmPassword?.length === 0 ||
              confirmPassword !== newPassword
            }
            children={
              <MaterialIcons name="lock" size={24} color={Colors.White} style={styles.buttonIcon} />
            }
            style={styles.updateButton}
          />
        </ScrollView>
      </TextWrapper>
    </Container>
  );
});

export default ChangePassword;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8fafc",
  },
  contentContainer: {
    flex: 1,
    marginHorizontal: 20,
    marginTop: 16,
    marginBottom: 20,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  sectionCard: {
    backgroundColor: Colors.White,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    paddingBottom: 12,
  },
  headerTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  inputGroup: {
    marginBottom: 8,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
    marginBottom: 4,
  },
  errorText: {
    fontSize: 14,
    color: Colors.Tomato,
    marginLeft: 6,
  },
  passwordRequirements: {
    marginTop: 24,
    backgroundColor: '#f8fafc',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  updateButton: {
    marginTop: 32,
    height: 56,
    borderRadius: 12,
  },
  buttonIcon: {
    marginLeft: 8,
  },
});
