import { useNavigation } from '@react-navigation/native';
import React, { memo, useCallback, useState } from 'react';
import {
  Alert,
  Image,
  StyleSheet,
  View,
  KeyboardAvoidingView,
  Platform,
  ScrollView
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { getStatusBarHeight } from 'react-native-iphone-x-helper';
import ButtonLinear from '@/components/Buttons/ButtonLinear';
import HeaderButton from '@/components/HeaderButton';
import Text from '@/components/Text';
import InputCodeOtp from '@/components/VerifyPhoneNumber/InputCodeOtp';
import { Colors, Routes } from '@/constants';
import scale from '@/utils/scale';

interface VerifyPhoneNumberProps {
  navigation?: any;
  route?: any;
}

const codeLength = 6;

const VerifyPhoneNumber = memo((props: VerifyPhoneNumberProps) => {
  const [code, setCode] = useState('');
  const [isResending, setIsResending] = useState(false);
  const navigation = useNavigation<any>();

  const onSendAgain = useCallback(async () => {
    setIsResending(true);
    // TODO: Implement resend code functionality
    setTimeout(() => {
      setIsResending(false);
      Alert.alert(
        'Code Sent!',
        'A new verification code has been sent to your phone number.',
        [{ text: 'OK' }]
      );
    }, 2000);
  }, []);

  const onVerify = useCallback(() => {
    navigation.navigate(Routes.SignUpSuccessful);
  }, [navigation]);

  return (
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      {/* Background Gradient */}
      <LinearGradient
        colors={['#f8fffe', '#e8f8f7', '#ffffff']}
        style={styles.background}
      />

      <KeyboardAvoidingView 
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header */}
          <View style={styles.headerContainer}>
            <HeaderButton style={styles.headerButton} />
          </View>

          {/* Main Content */}
          <View style={styles.mainContent}>
            {/* Icon Section */}
            <View style={styles.iconContainer}>
              <View style={styles.iconWrapper}>
                <Image 
                  source={require('@/assets/images/ic_accept.png')} 
                  style={styles.phoneIcon}
                  resizeMode="contain"
                />
              </View>
            </View>

            {/* Text Section */}
            <View style={styles.textSection}>
              <Text style={styles.title}>Phone Verification</Text>
              <Text style={styles.subtitle}>
                We've sent a 6-digit verification code to your phone number. 
                Please enter the code below to continue.
              </Text>
            </View>

            {/* Code Input Section */}
            <View style={styles.codeSection}>
              <Text style={styles.codeLabel}>Enter Verification Code</Text>
              <InputCodeOtp
                style={styles.enterCode}
                code={code}
                setCode={setCode}
                codeLength={codeLength}
                autoFocus
              />
            </View>

            {/* Resend Section */}
            <View style={styles.resendSection}>
              <Text style={styles.resendText}>
                Didn't receive the code?{' '}
                <Text
                  style={[
                    styles.resendLink,
                    { opacity: isResending ? 0.5 : 1 }
                  ]}
                  onPress={isResending ? undefined : onSendAgain}
                >
                  {isResending ? 'Sending...' : 'Send Again'}
                </Text>
              </Text>
            </View>

            {/* Verify Button */}
            <View style={styles.buttonContainer}>
              <ButtonLinear
                title="Verify Phone Number"
                style={styles.buttonLinear}
                disabled={code.length !== codeLength}
                onPress={onVerify}
              />
            </View>

            {/* Security Info */}
            <View style={styles.securityInfo}>
              <Text style={styles.securityText}>
                🔒 This helps us keep your account secure and ensures you can 
                receive important notifications about your care management.
              </Text>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
});

export default VerifyPhoneNumber;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.White,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: getStatusBarHeight(),
    paddingBottom: 40,
  },
  headerContainer: {
    marginBottom: 20,
  },
  headerButton: {
    marginTop: 0,
  },
  mainContent: {
    flex: 1,
    justifyContent: 'center',
    paddingVertical: 20,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  iconWrapper: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.TiffanyBlueOpacity,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.TiffanyBlue,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  phoneIcon: {
    width: 40,
    height: 40,
    tintColor: Colors.TiffanyBlue,
  },
  textSection: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.DarkJungleGreen,
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: Colors.GrayBlue,
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  codeSection: {
    alignItems: 'center',
    marginBottom: 32,
  },
  codeLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.DarkJungleGreen,
    marginBottom: 20,
  },
  enterCode: {
    width: '100%',
  },
  resendSection: {
    alignItems: 'center',
    marginBottom: 32,
  },
  resendText: {
    fontSize: 16,
    color: Colors.GrayBlue,
    textAlign: 'center',
  },
  resendLink: {
    color: Colors.TiffanyBlue,
    fontWeight: '600',
  },
  buttonContainer: {
    marginBottom: 24,
  },
  buttonLinear: {
    height: 56,
    borderRadius: 16,
    shadowColor: Colors.TiffanyBlue,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  securityInfo: {
    backgroundColor: Colors.TiffanyBlueOpacity,
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: Colors.TiffanyBlue,
  },
  securityText: {
    fontSize: 14,
    color: Colors.DarkJungleGreen,
    lineHeight: 20,
    textAlign: 'center',
  },
});
