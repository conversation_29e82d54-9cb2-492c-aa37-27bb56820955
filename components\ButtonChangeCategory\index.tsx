import React, { memo, useCallback } from 'react';
import { Image, StyleSheet, TouchableOpacity, View } from 'react-native';
import Text from '@/components/Text';
import { Colors } from '@/constants';
import { useTheme } from '@/constants/Theme';
import { categoryList } from '@/types/category';

interface ButtonChangeCategoryProps {
  category: categoryList;
  onPress: () => void;
}

const ButtonChangeCategory = memo((props: ButtonChangeCategoryProps) => {
  const { category } = props;
  const onShowModal = useCallback(() => {
    props.onPress && props.onPress();
  }, [props.onPress]);
  
  const { theme } = useTheme();
  
  return (
    <TouchableOpacity
      style={[
        styles.categoryList,
        {
          backgroundColor: Colors.White,
          borderColor: Colors.InnearColor,
        },
      ]}
      onPress={onShowModal}
      activeOpacity={0.7}
      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
    >
      <View style={styles.contentContainer}>
        <View style={styles.textContainer}>
          <Text style={styles.label}>Professional Role</Text>
          <Text style={styles.value}>
            {category?.displayName || category?.name || 'Select Role'}
          </Text>
        </View>
        
        <View style={styles.iconContainer}>
          <Image 
            source={require('@/assets/images/ic_accept.png')} 
            style={styles.dropdownIcon}
            resizeMode="contain"
          />
        </View>
      </View>
    </TouchableOpacity>
  );
});

export default ButtonChangeCategory;

const styles = StyleSheet.create({
  categoryList: {
    width: '100%',
    minHeight: 64,
    borderRadius: 12,
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: Colors.Black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  contentContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  textContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  label: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.GrayBlue,
    marginBottom: 4,
  },
  value: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.DarkJungleGreen,
    lineHeight: 20,
  },
  iconContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  dropdownIcon: {
    width: 16,
    height: 16,
    tintColor: Colors.TiffanyBlue,
    transform: [{ rotate: '90deg' }], // Rotate to make it a dropdown arrow
  },
});
