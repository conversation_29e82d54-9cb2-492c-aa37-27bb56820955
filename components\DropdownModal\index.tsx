import React from 'react';
import {Modal, StyleSheet, TouchableOpacity, View} from 'react-native';
import Text from '../Text';

interface DropDownProps {
  visible: boolean;
  onPress: (item: string) => void;
}

const DropdDown = ({visible, onPress}: DropDownProps) => {
  const options: {id: string; label: string}[] = [
    {
      id: 'BeforeFood',
      label: 'BeforeFood',
    },
    {
      id: 'AnyTime',
      label: 'AnyTime',
    },
    {
      id: 'AfterFood',
      label: 'AfterFood',
    },
  ];

  return (
    <Modal visible={visible} animationType={'slide'} transparent={true}>
      <View
        style={{
          flex: 1,
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 10,
          position: 'absolute',
          backgroundColor: 'rgba(0,0,0,0.5)',
        }}>
        <View style={styles.centeredView}>
          {options.map((label, index) => (
            <TouchableOpacity
              key={index}
              style={styles.option}
              onPress={async () => {
                onPress(label.id);
              }}>
              <Text center medium>
                {label.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </Modal>
  );
};

export default DropdDown;

const styles = StyleSheet.create({
  option: {
    marginVertical: 10,
    borderBottomWidth: 0.2,
    paddingHorizontal: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomColor: 'black',
    width: '100%',
  },
  centeredView: {
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
    position: 'absolute',
    backgroundColor: 'white',
    height: 150,
    width: '90%',
  },
});
