// Font configuration for the app
export const FontFamily = {
  // Primary font family
  primary: {
    regular: 'Inter-Regular',
    medium: 'Inter-Medium',
    semiBold: 'Inter-SemiBold',
    bold: 'Inter-Bold',
  },
  // Legacy font support
  legacy: {
    spaceMono: 'SpaceMono',
  }
};

// Font sizes used throughout the app
export const FontSize = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 32,
};

// Font weights
export const FontWeight = {
  regular: '400',
  medium: '500',
  semiBold: '600',
  bold: '700',
};

// Line heights
export const LineHeight = {
  xs: 16,
  sm: 20,
  md: 24,
  lg: 28,
  xl: 32,
  xxl: 36,
  xxxl: 40,
};

// Typography styles for consistent text appearance
export const Typography = {
  // Headings
  h1: {
    fontFamily: FontFamily.primary.bold,
    fontSize: FontSize.xxxl,
    lineHeight: LineHeight.xxxl,
  },
  h2: {
    fontFamily: FontFamily.primary.bold,
    fontSize: FontSize.xxl,
    lineHeight: LineHeight.xxl,
  },
  h3: {
    fontFamily: FontFamily.primary.semiBold,
    fontSize: FontSize.xl,
    lineHeight: LineHeight.xl,
  },
  
  // Body text
  bodyLarge: {
    fontFamily: FontFamily.primary.regular,
    fontSize: FontSize.lg,
    lineHeight: LineHeight.lg,
  },
  bodyMedium: {
    fontFamily: FontFamily.primary.regular,
    fontSize: FontSize.md,
    lineHeight: LineHeight.md,
  },
  bodySmall: {
    fontFamily: FontFamily.primary.regular,
    fontSize: FontSize.sm,
    lineHeight: LineHeight.sm,
  },
  
  // Labels and buttons
  labelLarge: {
    fontFamily: FontFamily.primary.medium,
    fontSize: FontSize.md,
    lineHeight: LineHeight.md,
  },
  labelMedium: {
    fontFamily: FontFamily.primary.medium,
    fontSize: FontSize.sm,
    lineHeight: LineHeight.sm,
  },
  labelSmall: {
    fontFamily: FontFamily.primary.medium,
    fontSize: FontSize.xs,
    lineHeight: LineHeight.xs,
  },
  
  // Special cases
  button: {
    fontFamily: FontFamily.primary.semiBold,
    fontSize: FontSize.md,
    lineHeight: LineHeight.md,
  },
  link: {
    fontFamily: FontFamily.primary.medium,
    fontSize: FontSize.md,
    lineHeight: LineHeight.md,
    color: '#0a7ea4',
  },
};

export default {
  FontFamily,
  FontSize,
  FontWeight,
  LineHeight,
  Typography,
};
