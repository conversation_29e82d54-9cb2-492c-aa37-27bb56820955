import { apiPostWithToken } from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import { Task, ApiResponse, Patient } from '../types';
import moment from 'moment';

/**
 * TaskService
 * Service for handling task-related API calls for the calendar feature
 * Updated to use the new calendar task APIs
 */
class TaskService {
  private static BASE_URL = URLS.baseUrl;
  
  // API endpoints matching the new API documentation
  private static readonly ENDPOINTS = {
    GET_TASKS: '/_ah/api/caregiverapi/v1/getCalendarTasks',
    CREATE_TASK: '/_ah/api/caregiverapi/v1/createCalendarTask',
    UPDATE_TASK: '/_ah/api/caregiverapi/v1/editCalendarTask',
    DELETE_TASK: '/_ah/api/caregiverapi/v1/deleteCalendarTask'
  };
  
  /**
   * Helper method to get CSS class based on priority
   * @param priority Task priority
   * @returns CSS class name
   */
  private static getPriorityClass(priority: string): string {
    switch (priority) {
      case 'Low':
        return '';
      case 'Medium':
        return '';
      case 'High':
        return 'bg-warning';
      case 'Critical':
        return 'bg-primary';
      default:
        return '';
    }
  }

  /**
   * Get all tasks for the logged-in caregiver
   */
  static async getTasks(): Promise<Task[]> {
    try {
      // Get the current user ID and org ID from Redux store
      const { store } = require('@/services/store');
      const state = store.getState();
      const caregiverId = state?.loginReducer?.data?.userId;
      const orgId = state?.currentOrgIdReducer?.orgId;
      const userRole = state?.loginReducer?.userRole || 'caregiver';
      
      if (!caregiverId || !orgId) {
        throw new Error('User ID or Organization ID not found');
      }
      
      console.log('TaskService.getTasks - Fetching tasks for user:', caregiverId, 'orgId:', orgId, 'role:', userRole);
      
      const response = await apiPostWithToken(
        {
          userId: String(caregiverId),
          orgId: String(orgId)
        },
        `${this.BASE_URL}${this.ENDPOINTS.GET_TASKS}`
      );

      if (response.error) {
        throw new Error(response.error.message || 'Failed to fetch tasks');
      }

      // Check if the API returned success: false
      if (response.data && response.data.success === false) {
        if (userRole === 'physician') {
          return [];
        }
        throw new Error('API returned success: false - may be unauthorized or invalid parameters');
      }

      console.log('TaskService.getTasks - API response:', JSON.stringify(response));
      
      // Transform the response data to match our Task interface
      const tasks: Task[] = Array.isArray(response.data?.data) 
        ? response.data.data.map((task: any) => ({
            id: task.id || '',
            title: task.title || '',
            description: task.description || '',
            priority: task.priority || 'Low',
            start: task.start || new Date().toISOString(),
            end: task.end || new Date().toISOString(),
            patientId: task.patientId || '',
            patientName: task.patinetName || '', // Note: API has typo "patinetName"
            userId: task.userId || caregiverId,
            className: task.className || this.getPriorityClass(task.priority || 'Low'),
            timezone: 'America/New_York', // Default timezone
          }))
        : [];
        
      console.log('TaskService.getTasks - Transformed tasks:', tasks);

      return tasks;
    } catch (error) {
      console.error('Error in getTasks:', error);
      throw error;
    }
  }

  /**
   * Create a new task
   */
  static async createTask(task: Partial<Task>): Promise<Task> {
    try {
      // Get the current user ID from Redux store
      const { store } = require('@/services/store');
      const state = store.getState();
      const caregiverId = state?.loginReducer?.data?.userId;
      
      if (!caregiverId) {
        throw new Error('User ID not found');
      }
      
      // Ensure we have valid date objects first
      let startMoment, endMoment;
      
      // Handle both string and Date object inputs
      if (typeof task.start === 'string') {
        // Try to parse the date string safely
        startMoment = moment(task.start, ['MM-DD-YYYY hh:mm A', 'YYYY-MM-DDTHH:mm:ss', moment.ISO_8601]);
        if (!startMoment.isValid()) {
          console.error('Invalid start date format:', task.start);
          throw new Error('Invalid start date format');
        }
      } else if (task.start && typeof task.start === 'object' && 'getTime' in task.start) {
        startMoment = moment(task.start);
      } else {
        console.error('Start date is neither string nor Date:', task.start);
        throw new Error('Invalid start date');
      }
      
      if (typeof task.end === 'string') {
        // Try to parse the date string safely
        endMoment = moment(task.end, ['MM-DD-YYYY hh:mm A', 'YYYY-MM-DDTHH:mm:ss', moment.ISO_8601]);
        if (!endMoment.isValid()) {
          console.error('Invalid end date format:', task.end);
          throw new Error('Invalid end date format');
        }
      } else if (task.end && typeof task.end === 'object' && 'getTime' in task.end) {
        endMoment = moment(task.end);
      } else {
        console.error('End date is neither string nor Date:', task.end);
        throw new Error('Invalid end date');
      }
      
      // Format dates according to the API documentation: "MM-DD-YYYY hh:mm A"
      const startDateTime = startMoment.format('MM-DD-YYYY hh:mm A');
      const endDateTime = endMoment.format('MM-DD-YYYY hh:mm A');
      
      console.log('Formatted start date:', startDateTime);
      console.log('Formatted end date:', endDateTime);

      // Prepare payload for API according to the documentation
      // Ensure all IDs are sent as proper types
      const payload = {
        title: task.title,
        description: task.description,
        start: startDateTime,
        end: endDateTime,
        patientId: parseInt(String(task.patientId || '0')),
        className: task.className || this.getPriorityClass(task.priority || 'Low'),
        priority: task.priority,
        timezone: task.timezone || 'America/New_York',
        userId: parseInt(String(caregiverId))
      };
      
      console.log('TaskService.createTask - Request payload:', JSON.stringify(payload));
      
      // Call the API to create a task
      const response = await apiPostWithToken(
        payload,
        `${this.BASE_URL}${this.ENDPOINTS.CREATE_TASK}`
      );

      // Check for error in response
      if (response.error) {
        throw new Error(response.error.message || 'Failed to create task');
      }

      if (!response.data?.success) {
        const errorMessage = response.data?.messages?.[0] || 'Failed to create task';
        throw new Error(errorMessage);
      }

      console.log('TaskService.createTask - Task created successfully');
      console.log('TaskService.createTask - API response data:', JSON.stringify(response.data));

      // Check if the API returns the created task with its actual ID
      let createdTaskId = response.data?.data?.id || response.data?.taskId || response.data?.id;
      
      if (!createdTaskId) {
        console.log('TaskService.createTask - API did not return task ID, fetching latest tasks to get proper ID');
        // If API doesn't return the task ID, we need to fetch tasks again to get the proper server-generated ID
        // This is not ideal but necessary if the API doesn't return the created task data
        try {
          const allTasks = await this.getTasks();
          // Find the most recently created task that matches our criteria
          // Since we just created it, it should be the most recent one with matching title and dates
          const recentTask = allTasks.find(t => 
            t.title === task.title && 
            t.description === task.description &&
            t.patientId === task.patientId &&
            t.priority === task.priority
          );
          
          if (recentTask) {
            createdTaskId = recentTask.id;
            console.log('TaskService.createTask - Found created task with ID:', createdTaskId);
          } else {
            console.warn('TaskService.createTask - Could not find the newly created task in the task list');
            // Fall back to a temporary ID, but log this as it may cause issues with updates
            createdTaskId = `temp_${Date.now()}`;
          }
        } catch (fetchError) {
          console.error('TaskService.createTask - Error fetching tasks to get proper ID:', fetchError);
          // Fall back to a temporary ID
          createdTaskId = `temp_${Date.now()}`;
        }
      }

      // Return the created task with the proper server-generated ID
      return {
        ...task,
        id: createdTaskId,
        userId: parseInt(String(caregiverId)),
        className: task.className || this.getPriorityClass(task.priority || 'Low'),
        timezone: task.timezone || 'America/New_York'
      } as Task;
    } catch (error) {
      console.error('Error in createTask:', error);
      throw error;
    }
  }

  /**
   * Update an existing task
   */
  static async updateTask(task: Partial<Task>): Promise<Task> {
    try {
      if (!task.id) {
        throw new Error('Task ID is required for update');
      }
      
      // Validate that we're not trying to update a task with a temporary ID
      if (!this.isValidTaskId(task.id)) {
        throw new Error('Cannot update task with temporary or invalid ID. Please refresh the task list and try again.');
      }
      
      // Ensure we have valid date objects first
      let startMoment, endMoment;
      
      // Handle both string and Date object inputs
      if (typeof task.start === 'string') {
        // Try to parse the date string safely
        startMoment = moment(task.start, ['MM-DD-YYYY hh:mm A', 'YYYY-MM-DDTHH:mm:ss', moment.ISO_8601]);
        if (!startMoment.isValid()) {
          console.error('Invalid start date format:', task.start);
          throw new Error('Invalid start date format');
        }
      } else if (task.start && typeof task.start === 'object' && 'getTime' in task.start) {
        startMoment = moment(task.start);
      } else {
        console.error('Start date is neither string nor Date:', task.start);
        throw new Error('Invalid start date');
      }
      
      if (typeof task.end === 'string') {
        // Try to parse the date string safely
        endMoment = moment(task.end, ['MM-DD-YYYY hh:mm A', 'YYYY-MM-DDTHH:mm:ss', moment.ISO_8601]);
        if (!endMoment.isValid()) {
          console.error('Invalid end date format:', task.end);
          throw new Error('Invalid end date format');
        }
      } else if (task.end && typeof task.end === 'object' && 'getTime' in task.end) {
        endMoment = moment(task.end);
      } else {
        console.error('End date is neither string nor Date:', task.end);
        throw new Error('Invalid end date');
      }
      
      // Format dates according to the API documentation: "MM-DD-YYYY hh:mm A"
      const startDateTime = startMoment.format('MM-DD-YYYY hh:mm A');
      const endDateTime = endMoment.format('MM-DD-YYYY hh:mm A');
      
      console.log('Formatted start date:', startDateTime);
      console.log('Formatted end date:', endDateTime);

      // Prepare payload for API according to the documentation
      const payload = {
        id: task.id,
        start: startDateTime,
        end: endDateTime,
        className: task.className || this.getPriorityClass(task.priority || 'Low'),
        priority: task.priority,
        timezone: task.timezone || 'America/New_York'
      };
      
      console.log('TaskService.updateTask - Request payload:', JSON.stringify(payload));
      
      // Call the API to update the task
      const response = await apiPostWithToken(
        payload,
        `${this.BASE_URL}${this.ENDPOINTS.UPDATE_TASK}`
      );

      // Check for error in response
      if (response.error) {
        throw new Error(response.error.message || 'Failed to update task');
      }

      if (!response.data?.success) {
        throw new Error(response.data?.messages?.[0] || 'Failed to update task');
      }

      console.log('TaskService.updateTask - Task updated successfully');

      // Return the updated task
      return task as Task;
    } catch (error) {
      console.error('Error in updateTask:', error);
      throw error;
    }
  }

  /**
   * Helper method to validate task ID
   * @param taskId The task ID to validate
   * @returns true if the task ID is valid (not temporary)
   */
  private static isValidTaskId(taskId: string): boolean {
    if (!taskId || taskId.trim() === '') {
      return false;
    }
    if (taskId.startsWith('temp_')) {
      return false;
    }
    return true;
  }

  /**
   * Delete a task by ID
   */
  static async deleteTask(taskId: string): Promise<boolean> {
    try {
      if (!taskId) {
        throw new Error('Task ID is required for deletion');
      }
      
      // Validate that we're not trying to delete a task with a temporary ID
      if (!this.isValidTaskId(taskId)) {
        throw new Error('Cannot delete task with temporary or invalid ID. Please refresh the task list and try again.');
      }
      
      console.log('TaskService.deleteTask - Deleting task ID:', taskId);
      
      // Call the API to delete the task according to the documentation
      const response = await apiPostWithToken(
        {
          id: taskId
        },
        `${this.BASE_URL}${this.ENDPOINTS.DELETE_TASK}`
      );
      
      console.log('TaskService.deleteTask - API response:', JSON.stringify(response));

      // Check for error in response
      if (response.error) {
        throw new Error(response.error.message || 'Failed to delete task');
      }

      if (!response.data?.success) {
        throw new Error(response.data?.messages?.[0] || 'Failed to delete task');
      }

      console.log('TaskService.deleteTask - Task deleted successfully');
      return true;
    } catch (error) {
      console.error('Error in deleteTask:', error);
      throw error;
    }
  }
  
  /**
   * Get patients for the logged-in caregiver
   * This is a helper method to populate the patient dropdown
   */
  static async getPatients(): Promise<Patient[]> {
    // Implementation would be similar to getTasks
    // For now, return an empty array as this is not part of the current task
    return [];
  }
}

export default TaskService;
