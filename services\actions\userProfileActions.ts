import {
  FETCH_USER_PROFILE,
  FETCH_USER_PROFILE_FAILURE,
  FETCH_USER_PROFILE_SUCCESS,
} from './type';

export const fetchUserProfile = (payload: any) => {
  return {
    type: FETCH_USER_PROFILE,
    payload,
  };
};

export const fetchUserProfileSuccess = (data: any) => {
  return {
    type: FETCH_USER_PROFILE_SUCCESS,
    data,
  };
};

export const fetchUserProfileFailure = (error: any) => {
  return {
    type: FETCH_USER_PROFILE_FAILURE,
    error,
  };
};