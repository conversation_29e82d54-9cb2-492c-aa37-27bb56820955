import { Routes } from "@/constants";
import { useTheme } from "@/constants/Theme";
import { PatientItemProps } from "@/models";
import { setCurentPatientId } from "@/services/actions/currentPatientId";
import { setCurrentPatientName } from "@/services/actions/currentPatientName";
import { formatPatientDOB } from "@/utils/dateFormatter";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { useRouter } from "expo-router";
import moment from "moment";
import React, { memo, useCallback, useState } from "react";
import {
  Alert,
  Linking,
  Modal,
  Platform,
  StyleSheet,
  TextStyle,
  TouchableOpacity,
  Vibration,
  View,
  ViewStyle,
} from "react-native";
import { useDispatch } from "react-redux";
import Loader from "../Loader/Loader";
import SendMessageModal from "../Modals/SendMessage";
import Text from "../Text";

// Export tablet-optimized components
export { default as PatientListTablet } from './PatientListTablet';

// Haptics utility
const hapticFeedback = {
  light: () => {
    try {
      if (Platform.OS === "ios") {
        Vibration.vibrate(10);
      }
    } catch (error) {
      console.log("Haptics not available");
    }
  },
  medium: () => {
    try {
      if (Platform.OS === "ios") {
        Vibration.vibrate(50);
      }
    } catch (error) {
      console.log("Haptics not available");
    }
  },
};

// Compact Design System optimized for 6 patients per screen
const DESIGN_SYSTEM = {
  spacing: {
    xs: 2,
    sm: 4,
    md: 6,
    lg: 8,
    xl: 10,
    xxl: 12,
    xxxl: 16,
  },
  borderRadius: {
    xs: 4,
    sm: 6,
    md: 8,
    lg: 10,
    xl: 12,
    full: 999,
  },
  typography: {
    sizes: {
      caption: 10,
      body2: 11,
      body1: 12,
      subtitle2: 12,
      subtitle1: 13,
      h6: 14,
      h5: 16,
    },
    weights: {
      regular: "400" as const,
      medium: "500" as const,
      semibold: "600" as const,
      bold: "700" as const,
      heavy: "800" as const,
    },
    lineHeights: {
      tight: 1.2,
      normal: 1.4,
      relaxed: 1.6,
    },
  },
  elevation: {
    level0: {
      shadowColor: "transparent",
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0,
      shadowRadius: 0,
      elevation: 0,
    },
    level1: {
      shadowColor: "#000000",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    level2: {
      shadowColor: "#000000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.08,
      shadowRadius: 4,
      elevation: 2,
    },
    level3: {
      shadowColor: "#000000",
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.12,
      shadowRadius: 8,
      elevation: 4,
    },
  },
  colors: {
    // Surface colors
    surface: "#FFFFFF",
    surfaceVariant: "#F7F9FC",
    surfaceContainer: "#F1F5F9",

    // Text colors
    onSurface: "#0F172A",
    onSurfaceVariant: "#475569",
    onSurfaceSecondary: "#64748B",
    onSurfaceTertiary: "#94A3B8",

    // Brand colors
    primary: "#0EA5E9",
    primaryContainer: "#E0F2FE",
    onPrimary: "#FFFFFF",
    onPrimaryContainer: "#0C4A6E",

    // Action colors
    success: "#10B981",
    successContainer: "#ECFDF5",
    warning: "#F59E0B",
    warningContainer: "#FEF3C7",
    error: "#EF4444",
    errorContainer: "#FEF2F2",
    info: "#3B82F6",
    infoContainer: "#EFF6FF",
    purple: "#8B5CF6",
    purpleContainer: "#F3F4F6",

    // Border and divider
    outline: "#E2E8F0",
    outlineVariant: "#F1F5F9",

    // States
    pressed: "rgba(0, 0, 0, 0.08)",
    hover: "rgba(0, 0, 0, 0.04)",
    focused: "rgba(0, 0, 0, 0.12)",
  },
};

interface Props extends PatientItemProps {
  index: number;
}

const PatientItem = memo((props: Props) => {
  const { theme } = useTheme();
  const dispatch = useDispatch();
  const [sendMessageModal, setSendMessageModal] = useState(false);

  const navigation = useNavigation() as any;
  const router = useRouter();

  // Action handlers
  function handleVideoCall() {
    hapticFeedback.medium();
    try {
      if (!props.patientId) {
        console.warn("No patient ID available for video call");
        return;
      }
      const patientIdStr = String(props.patientId);
      router.push(`/patient/video-call/${patientIdStr}`);
    } catch (error) {
      console.error("Error navigating to video call:", error);
      navigation.navigate(Routes.PatientVideoCall, {
        patientId: props.patientId,
        patientName: props.patientName,
      });
    }
  }

  function handlePhoneCall() {
    hapticFeedback.medium();

    if (!props.patientId) {
      Alert.alert(
        "Error",
        "Patient ID is required to start a voice call.",
        [{ text: "OK" }]
      );
      return;
    }

    // Direct navigation to pre-call screen without confirmation popup
    try {
      const patientIdStr = String(props.patientId);
      router.push(`/patient/voip-call/${patientIdStr}`);
    } catch (error) {
      console.error("Error navigating to VOIP call:", error);
      Alert.alert(
        "Navigation Error",
        "Unable to start voice call. Please try again.",
        [{ text: "OK" }]
      );
    }
  }

  function handleChat() {
    hapticFeedback.medium();
    try {
      router.push(`/patient/chat/${props.patientId}`);
    } catch (error) {
      navigation.navigate(Routes.ChatDetail, {
        patientId: props.patientId,
        patientName: props.patientName,
        programs: props.programs || [],
      });
    }
  }

  function handleSummary() {
    hapticFeedback.medium();
    try {
      router.push({
        pathname: "/patient-summary",
        params: {
          patientId: props.patientId.toString(),
          patientName: props.patientName,
        },
      });
    } catch (error) {
      console.error("Error navigating to summary:", error);
      navigation.navigate("patient-summary", {
        patientId: props.patientId,
        patientName: props.patientName,
      });
    }
  }

  const onCardPress = useCallback(() => {
    dispatch(setCurentPatientId(props.patientId));
    dispatch(setCurrentPatientName(props.patientName));

    hapticFeedback.light();

    try {
      const params = {
        id: props.patientId,
        patientName: props.patientName,
        phone: props.phone || "",
        address: props.address || "",
        image: props.image || "",
        dob: props.dob || "",
        mrn: props.mrn || "",
      };

      router.push({
        pathname: `/patient/${props.patientId}` as any,
        params: params,
      });
    } catch (error) {
      console.error("Error navigating to patient details:", error);
      navigation.navigate(Routes.PatientDetails, {
        patientId: props.patientId,
        image: props.image,
        patientName: props.patientName,
        address: props.address,
        phone: props.phone,
        dob: props.dob,
        mrn: props.mrn,
        patientAlertsCount: props,
        imeiNo: props.imeiNo,
        gpsStatus: props.gpsStatus,
        trackingStatus: props.trackingStatus,
        radius: props.radius,
        latLong: props.latLong,
        reachableStatus: props.reachableStatus,
        programs: props.programs || [],
      });
    }
  }, [dispatch, router, navigation, props]);

  // Enhanced formatting helpers
  const formatDate = useCallback((dateString: string | undefined) => {
    return formatPatientDOB(dateString);
  }, []);

  const formatPhone = useCallback((phone: string | undefined) => {
    if (!phone) return "Not provided";
    const cleaned = phone.replace(/\D/g, "");
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(
        6
      )}`;
    }
    return phone.length > 15 ? phone.slice(0, 15) + "..." : phone;
  }, []);

  const formatMRN = useCallback(
    (mrn: string | number | undefined) => {
      if (!mrn) return props.patientId?.toString() || "N/A";
      return mrn.toString();
    },
    [props.patientId]
  );

  // Get alternating background colors
  const getCardBackgroundColor = useCallback(() => {
    return props.index % 2 === 0
      ? "#E0F2FE" // Light blue
      : "#E5E7EB"; // Light gray
  }, [props.index]);

  // Compact action buttons optimized for smaller screens
  const actionButtons = [
    {
      id: "video",
      icon: "videocam" as const,
      color: DESIGN_SYSTEM.colors.success,
      containerColor: DESIGN_SYSTEM.colors.successContainer,
      action: handleVideoCall,
      label: "Video Call",
    },
    {
      id: "call",
      icon: "call" as const,
      color: DESIGN_SYSTEM.colors.info,
      containerColor: DESIGN_SYSTEM.colors.infoContainer,
      action: handlePhoneCall,
      label: "Voice Call",
    },
    {
      id: "chat",
      icon: "chatbubbles" as const,
      color: DESIGN_SYSTEM.colors.warning,
      containerColor: DESIGN_SYSTEM.colors.warningContainer,
      action: handleChat,
      label: "Messages",
    },
    {
      id: "summary",
      icon: "document-text" as const,
      color: DESIGN_SYSTEM.colors.purple,
      containerColor: DESIGN_SYSTEM.colors.purpleContainer,
      action: handleSummary,
      label: "Summary",
    },
  ];

  return (
    <View style={styles.container}>
      <Loader modalVisible={false} />

      <TouchableOpacity
        style={[
          styles.patientCard,
          { backgroundColor: getCardBackgroundColor() },
        ]}
        onPress={onCardPress}
        activeOpacity={0.95}
        accessible={true}
        accessibilityLabel={`Patient ${props.patientName}, MRN ${formatMRN(
          props.mrn
        )}, born ${formatDate(props.dob)}, phone ${formatPhone(props.phone)}`}
        accessibilityRole="button"
        accessibilityHint="Tap to view patient details"
      >
        {/* Patient Info Section */}
        <View style={styles.patientInfoSection}>
          {/* Row 1: Name and MRN */}
          <View style={styles.nameRow}>
            <Text style={styles.patientName} numberOfLines={1}>
              {props.patientName}
            </Text>
            <View style={styles.mrnContainer}>
              <Text style={styles.mrnText}>MRN: {formatMRN(props.mrn)}</Text>
            </View>
          </View>

          {/* Row 2: DOB and Phone */}
          <View style={styles.metaInfoRow}>
            <View style={styles.dobInfo}>
              <MaterialCommunityIcons
                name="calendar-outline"
                size={11}
                color={DESIGN_SYSTEM.colors.onSurfaceSecondary}
              />
              <Text style={styles.dobText}>
                {moment(props.dob, "MM-DD-YYYY").format("MMMM D, YYYY")}
              </Text>
            </View>

            <View style={styles.phoneInfo}>
              <Text style={styles.phoneLabel}>Ph: </Text>
              <Text style={styles.phoneText}>{formatPhone(props.phone)}</Text>
            </View>
          </View>
        </View>

        {/* Actions Section */}
        <View style={styles.actionsSection}>
          {actionButtons.map((button) => (
            <TouchableOpacity
              key={button.id}
              style={[
                styles.actionButton,
                { backgroundColor: button.containerColor },
              ]}
              onPress={(e) => {
                e.stopPropagation();
                button.action();
              }}
              activeOpacity={0.7}
              accessible={true}
              accessibilityLabel={button.label}
              accessibilityRole="button"
            >
              <Ionicons name={button.icon} size={14} color={button.color} />
            </TouchableOpacity>
          ))}
        </View>
      </TouchableOpacity>

      <Modal
        visible={sendMessageModal}
        onRequestClose={() => setSendMessageModal(false)}
        transparent
        animationType="fade"
      >
        <SendMessageModal
          close={() => setSendMessageModal(false)}
          patientId={props?.patientId}
          loader={() => {}}
          programs={props?.programs}
        />
      </Modal>
    </View>
  );
});

export default PatientItem;

const styles = StyleSheet.create({
  container: {
    marginHorizontal: DESIGN_SYSTEM.spacing.lg,
    marginBottom: DESIGN_SYSTEM.spacing.sm,
  } as ViewStyle,

  patientCard: {
    borderRadius: DESIGN_SYSTEM.borderRadius.lg,
    padding: DESIGN_SYSTEM.spacing.lg,
    ...DESIGN_SYSTEM.elevation.level2,
    borderWidth: 1,
    borderColor: DESIGN_SYSTEM.colors.outline,
  } as ViewStyle,

  // Patient Info Section
  patientInfoSection: {
    marginBottom: DESIGN_SYSTEM.spacing.md,
  } as ViewStyle,

  // Row 1: Name and MRN
  nameRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: DESIGN_SYSTEM.spacing.sm,
  } as ViewStyle,

  patientName: {
    fontSize: DESIGN_SYSTEM.typography.sizes.h6,
    fontWeight: DESIGN_SYSTEM.typography.weights.bold,
    fontFamily: "DMSans-Bold",
    color: DESIGN_SYSTEM.colors.onSurface,
    lineHeight:
      DESIGN_SYSTEM.typography.lineHeights.tight *
      DESIGN_SYSTEM.typography.sizes.h6,
    flex: 1,
    marginRight: DESIGN_SYSTEM.spacing.sm,
  } as TextStyle,

  mrnContainer: {
    backgroundColor: "#22D3EE",
    paddingHorizontal: DESIGN_SYSTEM.spacing.sm,
    paddingVertical: DESIGN_SYSTEM.spacing.xs,
    borderRadius: DESIGN_SYSTEM.borderRadius.sm,
  } as ViewStyle,

  mrnText: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body2,
    fontWeight: DESIGN_SYSTEM.typography.weights.bold,
    color: "#FFFFFF",
  } as TextStyle,

  // Row 2: DOB and Phone
  metaInfoRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  } as ViewStyle,

  dobInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  } as ViewStyle,

  dobText: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body2,
    fontWeight: DESIGN_SYSTEM.typography.weights.medium,
    color: DESIGN_SYSTEM.colors.onSurfaceSecondary,
    marginLeft: DESIGN_SYSTEM.spacing.xs,
  } as TextStyle,

  phoneInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    justifyContent: "flex-end",
  } as ViewStyle,

  phoneLabel: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body2,
    fontWeight: DESIGN_SYSTEM.typography.weights.medium,
    color: DESIGN_SYSTEM.colors.onSurfaceSecondary,
  } as TextStyle,

  phoneText: {
    fontSize: DESIGN_SYSTEM.typography.sizes.body2,
    fontWeight: DESIGN_SYSTEM.typography.weights.medium,
    color: DESIGN_SYSTEM.colors.onSurfaceSecondary,
  } as TextStyle,

  // Actions Section
  actionsSection: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingTop: DESIGN_SYSTEM.spacing.sm,
    borderTopWidth: 1,
    borderTopColor: DESIGN_SYSTEM.colors.outlineVariant,
  } as ViewStyle,

  actionButton: {
    width: 32,
    height: 32,
    borderRadius: DESIGN_SYSTEM.borderRadius.md,
    justifyContent: "center",
    alignItems: "center",
    ...DESIGN_SYSTEM.elevation.level1,
  } as ViewStyle,
});
