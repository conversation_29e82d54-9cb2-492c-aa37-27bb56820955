import {useNavigation} from '@react-navigation/native';
import axios from 'axios';
import React, {useEffect, useState} from 'react';
import {
  Alert,
  Image,
  PermissionsAndroid,
  Platform,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
  Modal,
  FlatList,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import {RadioButton} from 'react-native-paper';
import {useSelector} from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import {useRouter} from 'expo-router';
import ButtonLinear from '@/components/Buttons/ButtonLinear';
import CheckBox from '@/components/CheckBox';
import DaysSelection from '@/components/DaysSelection';
import DropdDown from '@/components/DropdownModal';
import InputApp from '@/components/InputApp';
import Container from '@/components/Layout/Container';
import Loader from '@/components/Loader/Loader';
import Text from '@/components/Text';
import {Colors, Routes} from '@/constants';
import {useTheme} from '@/constants/Theme';
import URLS from '@/services/config/config';
import {getAuthToken} from '@/services/secure-storage';
import {uploadImage, uploadPrescription, PrescriptionUploadParams} from '@/services/apis/apiManager';
import Theme from '@/constants/Theme';
import scale from '@/utils/scale';

// Dropdown options for timing
const timingOptions = [
  { label: 'Before Food', value: 'BeforeFood' },
  { label: 'After Food', value: 'AfterFood' },
  { label: 'With Food', value: 'WithFood' },
  { label: 'Empty Stomach', value: 'EmptyStomach' },
];

// Custom Dropdown Component
const CustomDropdown = ({ 
  value, 
  onSelect, 
  options, 
  placeholder = "Select option",
  style 
}: {
  value: string;
  onSelect: (value: string) => void;
  options: { label: string; value: string }[];
  placeholder?: string;
  style?: any;
}) => {
  const [visible, setVisible] = useState(false);
  
  const selectedOption = options.find(option => option.value === value);
  
  return (
    <View style={style}>
      <TouchableOpacity
        style={styles.customDropdownButton}
        onPress={() => setVisible(true)}
      >
        <Text size={15} lineHeight={20} color={Colors.DarkJungleGreen}>
          {selectedOption?.label || placeholder}
        </Text>
        <Ionicons name="chevron-down" size={20} color={Colors.GrayBlue} />
      </TouchableOpacity>
      
      <Modal
        visible={visible}
        transparent
        animationType="fade"
        onRequestClose={() => setVisible(false)}
      >
        <TouchableOpacity 
          style={styles.dropdownOverlay}
          activeOpacity={1}
          onPress={() => setVisible(false)}
        >
          <View style={styles.dropdownModal}>
            <View style={styles.dropdownHeader}>
              <Text size={18} lineHeight={22} bold color={Colors.DarkJungleGreen}>
                Select Timing
              </Text>
              <TouchableOpacity onPress={() => setVisible(false)}>
                <Ionicons name="close" size={24} color={Colors.GrayBlue} />
              </TouchableOpacity>
            </View>
            
            <FlatList
              data={options}
              keyExtractor={(item) => item.value}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.dropdownOption,
                    value === item.value && styles.dropdownOptionSelected
                  ]}
                  onPress={() => {
                    onSelect(item.value);
                    setVisible(false);
                  }}
                >
                  <Text 
                    size={16} 
                    lineHeight={20} 
                    color={value === item.value ? Colors.TealBlue : Colors.DarkJungleGreen}
                    bold={value === item.value}
                  >
                    {item.label}
                  </Text>
                  {value === item.value && (
                    <Ionicons name="checkmark" size={20} color={Colors.TealBlue} />
                  )}
                </TouchableOpacity>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

// Modern card component for sections
const SectionCard = ({ children, title, subtitle, style }: { 
  children: React.ReactNode; 
  title?: string; 
  subtitle?: string; 
  style?: any; 
}) => (
  <View style={[styles.sectionCard, style]}>
    {title && (
      <View style={styles.sectionHeader}>
        <Text size={18} lineHeight={22} bold color={Colors.DarkJungleGreen}>
          {title}
        </Text>
        {subtitle && (
          <Text size={14} lineHeight={18} color={Colors.GrayBlue} marginTop={4}>
            {subtitle}
          </Text>
        )}
      </View>
    )}
    {children}
  </View>
);

// Modern time slot component with fixed alignment
const TimeSlotCard = ({ 
  title, 
  icon, 
  checked, 
  onToggle, 
  dropdownValue, 
  quantity, 
  onQuantityChange,
  onDropdownSelect 
}: {
  title: string;
  icon: string;
  checked: boolean;
  onToggle: () => void;
  dropdownValue: string;
  quantity: string;
  onQuantityChange: (text: string) => void;
  onDropdownSelect: (value: string) => void;
}) => (
  <View style={styles.timeSlotCard}>
    <TouchableOpacity 
      style={styles.timeSlotHeader} 
      onPress={onToggle}
      activeOpacity={0.7}
    >
      <View style={styles.timeSlotInfo}>
        <Ionicons name={icon as any} size={20} color={checked ? Colors.TealBlue : Colors.GrayBlue} />
        <Text size={16} lineHeight={20} color={Colors.DarkJungleGreen} bold marginLeft={12}>
          {title}
        </Text>
      </View>
      <View style={[styles.checkbox, checked && styles.checkboxActive]}>
        {checked && <Ionicons name="checkmark" size={16} color={Colors.White} />}
      </View>
    </TouchableOpacity>
    
    {checked && (
      <View style={styles.timeSlotContent}>
        <View style={styles.timeSlotControlsContainer}>
          <View style={styles.timingSection}>
            <Text size={14} lineHeight={18} color={Colors.DarkJungleGreen} marginBottom={8} bold>
              Timing
            </Text>
            <CustomDropdown
              value={dropdownValue}
              onSelect={onDropdownSelect}
              options={timingOptions}
              placeholder="Select timing"
              style={styles.timingDropdown}
            />
          </View>
          
          <View style={styles.quantitySection}>
            <Text size={14} lineHeight={18} color={Colors.DarkJungleGreen} marginBottom={8} bold>
              Quantity
            </Text>
            <View style={styles.quantityInputWrapper}>
              <InputApp
                title=""
                value={quantity}
                onChangeText={onQuantityChange}
                placeholder="1"
                keyboardType="numeric"
                style={styles.quantityInput}
              />
            </View>
          </View>
        </View>
      </View>
    )}
  </View>
);

// Modern Day Selection Component
const DayButton = ({ 
  day, 
  selected, 
  onPress 
}: { 
  day: string; 
  selected: boolean; 
  onPress: () => void; 
}) => (
  <TouchableOpacity
    style={[styles.dayButton, selected && styles.dayButtonSelected]}
    onPress={onPress}
    activeOpacity={0.7}
  >
    <Text 
      size={16} 
      lineHeight={20} 
      color={selected ? Colors.White : Colors.DarkJungleGreen}
      bold={selected}
    >
      {day}
    </Text>
  </TouchableOpacity>
);

// Tab Selector Component
const TabSelector = ({ 
  activeTab, 
  onTabChange 
}: { 
  activeTab: 'manual' | 'ocr'; 
  onTabChange: (tab: 'manual' | 'ocr') => void; 
}) => (
  <View style={styles.modernTabContainer}>
    <TouchableOpacity
      style={[styles.modernTabButton, activeTab === 'manual' && styles.modernTabButtonActive]}
      onPress={() => onTabChange('manual')}
      activeOpacity={0.8}
    >
      <View style={styles.tabContentWrapper}>
        <Ionicons 
          name="create-outline" 
          size={20} 
          color={activeTab === 'manual' ? Colors.White : Colors.TealBlue} 
        />
        <Text 
          size={15} 
          lineHeight={18} 
          color={activeTab === 'manual' ? Colors.White : Colors.TealBlue}
          bold={activeTab === 'manual'}
          marginLeft={8}
        >
          Manual Entry
        </Text>
      </View>
    </TouchableOpacity>
    
    <TouchableOpacity
      style={[styles.modernTabButton, activeTab === 'ocr' && styles.modernTabButtonActive]}
      onPress={() => onTabChange('ocr')}
      activeOpacity={0.8}
    >
      <View style={styles.tabContentWrapper}>
        <Ionicons 
          name="scan-outline" 
          size={20} 
          color={activeTab === 'ocr' ? Colors.White : Colors.TealBlue} 
        />
        <Text 
          size={15} 
          lineHeight={18} 
          color={activeTab === 'ocr' ? Colors.White : Colors.TealBlue}
          bold={activeTab === 'ocr'}
          marginLeft={8}
        >
          OCR Scan
        </Text>
      </View>
    </TouchableOpacity>
  </View>
);

export default (props: any) => {
  // Get patient ID from props or Redux
  const reduxPatientId = useSelector(
    (state: any) => state?.currentPatientIdReducer?.patientId,
  );

  // Try to get patient ID from URL if it's not in props
  let urlPatientId = '';
  if (typeof window !== 'undefined' && window.location) {
    try {
      if (window.location.search) {
        const urlParams = new URLSearchParams(window.location.search);
        const idParam = urlParams.get('patientId');
        if (idParam) {
          urlPatientId = idParam;
        }
      }
    } catch (error) {
      console.warn('Error parsing URL params in AddMedication component:', error);
    }
  }

  // Use patientId from props, URL, or Redux (in that order of preference)
  let effectivePatientId = props.patientId || urlPatientId || reduxPatientId;

  // Make sure we're not using 'index' as a patientId
  if (effectivePatientId === 'index') {
    // Use Redux ID instead
    effectivePatientId = reduxPatientId;
  }

  // Log the patient ID for debugging
  console.log('AddMedication component - Patient ID sources:', {
    propsPatientId: props.patientId,
    urlPatientId,
    reduxPatientId,
    effectivePatientId,
    windowLocation: typeof window !== 'undefined' ? window.location?.href : 'not available'
  });

  const patientName = useSelector(
    (state: any) => state?.currentPatientNameReducer?.patientName,
  );
  
  // Get required IDs from Redux for OCR upload
  const userId = useSelector((state: any) => state?.loginReducer?.data?.userId);
  const orgId = useSelector((state: any) => state?.currentOrgIdReducer?.orgId);
  
  // Use router for navigation
  const router = useRouter();
  const {navigate} = useNavigation() as any;
  
  useEffect(() => {
    // Only set navigation options if using React Navigation
    if (props.navigation && props.navigation.setOptions) {
      props.navigation.setOptions({
        headerTitle: () => (
          <View
            style={{
              marginBottom: 0,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Text medium size={16} color={'#000'}>
              {'Add meds to '}
              {patientName}
            </Text>
          </View>
        ),
      });
    }
  }, [props.navigation, patientName]);

  const {theme} = useTheme();
  const [loader, setLoader] = useState(false);

  // Tab state - 'manual' or 'ocr'
  const [activeTab, setActiveTab] = useState<'manual' | 'ocr'>('manual');

  // Manual entry states (existing states)
  const [medicationName, setMedicationName] = useState('');
  const [dosgae, setDosage] = useState('');
  const [color, setColor] = useState('');

  // OCR states (new)
  const [prescriptionImage, setPrescriptionImage] = useState<string | null>(null);
  const [isProcessingOCR, setIsProcessingOCR] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);

  const [medcationType, setMedicationType] = React.useState('1');

  const [all, setAll] = useState(false);
  const [sun, setSun] = useState(false);
  const [mon, setMon] = useState(false);
  const [tue, setTue] = useState(false);
  const [wed, setWed] = useState(false);
  const [thu, setThu] = useState(false);
  const [fri, setFri] = useState(false);
  const [sat, setSat] = useState(false);

  const [earlyMorningCheck, setEarlyMorningCheck] = useState(false);
  const [earlyDropdownValue, setEarlyDropdownValue] = useState('BeforeFood');
  const [earlyMorningQnty, setEarlyMorningQnty] = useState('');

  const [breakFastCheck, setBreakFastCheck] = useState(false);
  const [breakFastDropdownValue, setBreakFastDropdownValue] = useState('BeforeFood');
  const [breakFastQnty, setBreakFastQnty] = useState('');

  const [lunchCheck, setLunchCheck] = useState(false);
  const [lunchDropdownValue, setLunchDropdownValue] = useState('BeforeFood');
  const [lunchQnty, setLunchQnty] = useState('');

  const [afternoonCheck, setAfternoonCheck] = useState(false);
  const [afternoonDropdownValue, setAfternoonDropdownValue] = useState('BeforeFood');
  const [afternoonQnty, setAfternoonQnty] = useState('');

  const [dinnerCheck, setDinnerCheck] = useState(false);
  const [dinnerDropdownValue, setDinnerDropdownValue] = useState('BeforeFood');
  const [dinnerQnty, setDinnerQnty] = useState('');

  const [bedCheck, setBedCheck] = useState(false);
  const [bedDropdownValue, setBedDropdownValue] = useState('BeforeFood');
  const [bedQnty, setBedQnty] = useState('');

  const [medicationImage, setMedicationImage] = useState(null) as any;
  const [image, setImage] = useState(null) as any;

  useEffect(() => {
    requestPermission();
  }, []);

  const requestPermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: 'This app need WRITE EXTERNAL STORAGE ',
          message:
            'Cool Photo App needs access to your camera ' +
            'so you can take awesome pictures.',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        },
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        console.log('You can use the camera');
      } else {
        console.log('Camera permission denied');
      }
    } catch (err) {
      console.warn(err);
    }
  };

  const handleChoosePhoto = async () => {
    try {
      // Request permission to access the media library
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Sorry, we need camera roll permissions to make this work!');
        return;
      }

      // Launch the image library
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        quality: 0.8, // Reduced quality for better upload performance
        aspect: [1, 1], // Square aspect ratio for consistency
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedAsset = result.assets[0];
        setMedicationImage(selectedAsset.uri);

        // Create an object with proper file info
        const imageObject = {
          uri: selectedAsset.uri,
          type: selectedAsset.mimeType || 'image/jpeg',
          fileName: selectedAsset.fileName || `medication_${Date.now()}.jpg`,
          name: selectedAsset.fileName || `medication_${Date.now()}.jpg`,
        };

        setImage(imageObject);
        console.log('Image selected:', imageObject);
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  const uploadImageToServer = async (medicationId: number) => {
    try {
      if (!image || !image.uri) {
        console.log('No image to upload');
        return true; // Return true to continue with medication addition even without an image
      }

      console.log('Uploading image:', {
        uri: image.uri,
        type: image.type,
        name: image.name || image.fileName,
        medicationId
      });

      // Validate upload URL
      if (!URLS.imageUploadUrl) {
        console.error('Image upload URL not configured');
        return true; // Continue with medication addition even if image upload URL is not configured
      }

      console.log('Using image upload URL:', URLS.imageUploadUrl);

      const formData = new FormData();
      formData.append('medicationId', medicationId.toString());
      
      // Append image with proper format
      formData.append('imageFile', {
        uri: Platform.OS === 'ios' ? image.uri.replace('file://', '') : image.uri,
        type: image.type || 'image/jpeg', // Provide default type if missing
        name: image.name || image.fileName || `medication_${Date.now()}.jpg`, // Provide default name if missing
      } as any);

      setLoader(true);

      // Use the uploadImage function from apiManager for better error handling
      const response = await uploadImage(formData, URLS.imageUploadUrl);

      setLoader(false);
      
      // Handle HTML responses - consider it a success if we get any response
      // This is a workaround for the server returning HTML instead of JSON
      if (response.data && typeof response.data === 'string' && 
          (response.data.includes('<!DOCTYPE html>') || response.data.includes('<html'))) {
        console.log('Image upload successful: HTML response received');
        return true;
      }
      
      // Check if there's an error in the response
      if (response.error) {
        console.error('Image upload error:', response.error);
        
        // Log specific error details
        if (response.error.details) {
          console.error('Error details:', response.error.details);
        }
        
        // Check if it's an HTML response (authentication issue)
        if (typeof response.error.details === 'string' && 
            (response.error.details.includes('<!DOCTYPE html>') || response.error.details.includes('<html'))) {
          console.log('Image upload returned HTML - considering it successful');
          return true; // Consider HTML response as successful to continue with medication addition
        }
        
        // Continue with medication addition even if image upload fails
        return true;
      }
      
      // Check if upload was successful
      if (response.status && response.status >= 200 && response.status < 300) {
        console.log('Image upload successful:', response.data);
        return true;
      } else {
        console.error('Image upload failed with status:', response.status);
        return true; // Continue with medication addition even if image upload fails
      }
    } catch (error: any) {
      setLoader(false);
      console.error('Image upload exception:', error);
      return true; // Continue with medication addition even if image upload fails
    }
  };

  const uploadPrescriptionToServer = async (imageFile: any) => {
    try {
      if (!imageFile || !imageFile.uri) {
        console.log('No prescription image to upload');
        return null;
      }

      if (!userId || !orgId || !effectivePatientId) {
        throw new Error('Missing required IDs: userId, orgId, or patientId');
      }

      setIsProcessingOCR(true);

      // Prepare parameters for API manager
      const uploadParams: PrescriptionUploadParams = {
        file: {
          uri: Platform.OS === 'ios' ? imageFile.uri.replace('file://', '') : imageFile.uri,
          type: imageFile.type || 'image/jpeg',
          name: imageFile.name || imageFile.fileName || `prescription_${Date.now()}.jpg`,
        },
        userId,
        orgId,
        patientId: effectivePatientId,
      };

      // Use API manager function
      const response = await uploadPrescription(uploadParams);

      setIsProcessingOCR(false);

      // Check for successful response
      if (response.status && response.status >= 200 && response.status < 300) {
        console.log('Prescription upload successful:', response.data);
        
        // Set success state to show inline success message
        setUploadSuccess(true);
        
        return response.data;
      } else {
        throw new Error(response.error?.message || 'Upload failed');
      }
    } catch (error: any) {
      setIsProcessingOCR(false);
      console.error('Prescription upload error:', error);
      
      // Show user-friendly error message
      const errorMessage = error.response?.data?.message || 
                          error.error?.message ||
                          error.message || 
                          'Failed to upload prescription. Please try again.';
      
      Alert.alert('Upload Error', errorMessage);
      throw error;
    }
  };



  const takePrescriptionPhoto = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setPrescriptionImage(imageUri);
        
        // Create file object and upload immediately
        const fileObject = {
          uri: imageUri,
          type: 'image/jpeg',
          name: `prescription_${Date.now()}.jpg`,
        };
        
        await uploadPrescriptionToServer(fileObject);
      }
    } catch (error) {
      console.error('Error taking prescription photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  const selectPrescriptionFromGallery = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        setPrescriptionImage(imageUri);
        
        // Create file object and upload immediately
        const fileObject = {
          uri: imageUri,
          type: 'image/jpeg',
          name: `prescription_${Date.now()}.jpg`,
        };
        
        await uploadPrescriptionToServer(fileObject);
      }
    } catch (error) {
      console.error('Error selecting prescription from gallery:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  const clearOCRData = () => {
    setPrescriptionImage(null);
    setIsProcessingOCR(false);
    setUploadSuccess(false);
  };

  const navigateBack = () => {
    try {
      // Try router.back() first (for Expo Router)
      if (router && router.back) {
        router.back();
      } else {
        // Fallback to React Navigation
        navigate('MedicationPage');
      }
    } catch (error) {
      console.error('Navigation error:', error);
      // Last resort - just go back
      if (router && router.back) {
        router.back();
      }
    }
  };

  const addMedicationToServer = async () => {
    try {
      // Basic validation
      if (!medicationName) {
        showError('Please enter medication name');
        return;
      }

      if (!medcationType) {
        showError('Please select medication type');
        return;
      }

      // Validate at least one day is selected
      if (!all && !sun && !mon && !tue && !wed && !thu && !fri && !sat) {
        showError('Please select at least one day');
        return;
      }

      // Debug time slot selections
      console.log('Time slot selections:', {
        breakFastCheck,
        lunchCheck,
        afternoonCheck,
        dinnerCheck,
        bedCheck,
        earlyMorningCheck
      });
      
      // Validate at least one time slot is selected (include earlyMorningCheck)
      if (
        !earlyMorningCheck &&
        !breakFastCheck &&
        !lunchCheck &&
        !afternoonCheck &&
        !dinnerCheck &&
        !bedCheck
      ) {
        showError('Please select at least one time slot');
        return;
      }

      // Debug quantities for time slots
      console.log('Time slot quantities:', {
        earlyMorningCheck, earlyMorningQnty,
        breakFastCheck, breakFastQnty,
        lunchCheck, lunchQnty,
        afternoonCheck, afternoonQnty,
        dinnerCheck, dinnerQnty,
        bedCheck, bedQnty
      });
      
      // Validate quantities for selected time slots
      if (earlyMorningCheck && (!earlyMorningQnty || earlyMorningQnty.trim() === '')) {
        showError('Please enter quantity for Early Morning');
        return;
      }
      
      if (breakFastCheck && (!breakFastQnty || breakFastQnty.trim() === '')) {
        showError('Please enter quantity for Morning');
        return;
      }

      if (lunchCheck && (!lunchQnty || lunchQnty.trim() === '')) {
        showError('Please enter quantity for Lunch');
        return;
      }

      if (afternoonCheck && (!afternoonQnty || afternoonQnty.trim() === '')) {
        showError('Please enter quantity for Afternoon');
        return;
      }

      if (dinnerCheck && (!dinnerQnty || dinnerQnty.trim() === '')) {
        showError('Please enter quantity for Dinner');
        return;
      }

      if (bedCheck && (!bedQnty || bedQnty.trim() === '')) {
        showError('Please enter quantity for Bed Time');
        return;
      }

      setLoader(true);

      // Prepare medication data with all time slots - using the expected API format
      // First, construct the time slots string and related data
      let timeSlots = [];
      let quantities = [];
      let medTimeRelativeToFood = [];

      if (earlyMorningCheck) {
        timeSlots.push('EarlyMorning');
        quantities.push(earlyMorningQnty);
        medTimeRelativeToFood.push(earlyDropdownValue);
      }

      if (breakFastCheck) {
        timeSlots.push('Breakfast');
        quantities.push(breakFastQnty);
        medTimeRelativeToFood.push(breakFastDropdownValue);
      }

      if (lunchCheck) {
        timeSlots.push('Lunch');
        quantities.push(lunchQnty);
        medTimeRelativeToFood.push(lunchDropdownValue);
      }

      if (afternoonCheck) {
        timeSlots.push('AfternoonSnack');
        quantities.push(afternoonQnty);
        medTimeRelativeToFood.push(afternoonDropdownValue);
      }

      if (dinnerCheck) {
        timeSlots.push('Dinner');
        quantities.push(dinnerQnty);
        medTimeRelativeToFood.push(dinnerDropdownValue);
      }

      if (bedCheck) {
        timeSlots.push('Bed');
        quantities.push(bedQnty);
        medTimeRelativeToFood.push(bedDropdownValue);
      }

      let timeSlotsStr = timeSlots.join('|');
      let quantitiesStr = quantities.join('|');
      let medTimeRelativeToFoodStr = medTimeRelativeToFood.join('|');

      // Prepare medication data in the format expected by the API
      const medicationData = {
        patientId: effectivePatientId,
        name: medicationName,
        dosage: dosgae || '',
        color: color || '',
        medicationType: medcationType,
        days: getDaysString(),
        timeSlots: timeSlotsStr,
        quantities: quantitiesStr,
        medTimeRelativeToFood: medTimeRelativeToFoodStr,
        // Include individual time slots for backward compatibility
        earlyMorning: earlyMorningCheck ? 1 : 0,
        morning: breakFastCheck ? 1 : 0,
        lunch: lunchCheck ? 1 : 0,
        afternoon: afternoonCheck ? 1 : 0,
        dinner: dinnerCheck ? 1 : 0,
        bedTime: bedCheck ? 1 : 0,
        // Include day flags
        sunday: sun ? 1 : 0,
        monday: mon ? 1 : 0,
        tuesday: tue ? 1 : 0,
        wednesday: wed ? 1 : 0,
        thursday: thu ? 1 : 0,
        friday: fri ? 1 : 0,
        saturday: sat ? 1 : 0,
      };
      
      console.log('Final medication data:', medicationData);

      // Get auth token
      const token = await getAuthToken();

      if (!token) {
        setLoader(false);
        showError('Authentication failed');
        return;
      }

      try {
        // Let's try a different API endpoint format based on the error
        // Try the savePrescription endpoint instead
        console.log('Trying alternative API endpoint with data:', medicationData);
        
        // Create a prescription-style object that matches the expected format
        const prescriptionData = {
          prescriptionId: null,
          imageExists: !!medicationImage,
          name: medicationName,
          dosage: dosgae || '',
          color: color || '',
          type: medcationType,
          existingOrNewschedule: 'Existing',
          beforeOrAfterFood: 'Before',
          timeOfMedicine: {
            earlymorning: earlyMorningCheck,
            breakfast: breakFastCheck,
            lunch: lunchCheck,
            afternoonsnack: afternoonCheck,
            dinner: dinnerCheck,
            bed: bedCheck,
          },
          timeSlots: timeSlots.join('|'),
          quantities: quantities.join('|'),
          medTimeRelativeToFood: medTimeRelativeToFood.join('|'),
          daysOfWeek: getDaysString(),
          day: {
            sunday: sun,
            monday: mon,
            tuesday: tue,
            wednesday: wed,
            thursday: thu,
            friday: fri,
            saturday: sat,
          },
          patientId: effectivePatientId,
        };
        
        console.log('Trying with prescription format:', prescriptionData);
        
        // Make API request to add medication using the savePrescription endpoint
        const response = await axios.post(
          `${URLS.caregiverUrl}savePrescription`,
          prescriptionData,
          {
            headers: {
              Authorization: 'Bearer ' + token,
              'Content-Type': 'application/json',
            },
          },
        );

        console.log('Medication API response:', response.data);

        // Handle response for savePrescription endpoint
        if (response.data && response.data.success) {
          // Handle image upload separately - don't wait for completion
          if (medicationImage && response.data.createdPrescription) {
            // Don't block medication addition on image upload
            // This allows the medication to be added even if the image upload fails
            setTimeout(() => {
              uploadImageToServer(response.data.createdPrescription)
                .then(success => {
                  console.log('Image upload result:', success ? 'success' : 'failed');
                })
                .catch(err => {
                  console.warn('Image upload error, but medication was added:', err);
                });
            }, 500); // Small delay to ensure UI is responsive
          }

          setLoader(false);
          Alert.alert('Success', 'Medication added successfully', [
            {
              text: 'OK',
              onPress: navigateBack,
            },
          ]);
        } else {
          setLoader(false);
          showError(
            response.data?.message || 'Failed to add medication. Please try again.',
          );
        }
      } catch (apiError: any) {
        // Handle API error with detailed logging
        console.error('API error:', apiError);
        
        // Log detailed error information
        if (apiError.response) {
          console.error('Error response status:', apiError.response.status);
          console.error('Error response data:', apiError.response.data);
          console.error('Error response headers:', apiError.response.headers);
        } else if (apiError.request) {
          console.error('Error request:', apiError.request);
        } else {
          console.error('Error message:', apiError.message);
        }
        
        setLoader(false);
        showError(
          apiError.response?.data?.message ||
            apiError.message ||
            'Failed to add medication. Please try again.',
        );
      }
    } catch (error: any) {
      setLoader(false);
      console.error('Error adding medication:', error);
      showError(
        error.response?.data?.message ||
          error.message ||
          'Failed to add medication. Please try again.',
      );
    }
  };

  const getDaysString = () => {
    let daysList: string[] = [];
    if (all) {
      return 'Su|Mo|Tu|We|Th|Fr|Sa';
    }
    if (sun) {
      daysList.push('Su');
    }
    if (mon) {
      daysList.push('Mo');
    }
    if (tue) {
      daysList.push('Tu');
    }
    if (wed) {
      daysList.push('We');
    }
    if (thu) {
      daysList.push('Th');
    }
    if (fri) {
      daysList.push('Fr');
    }
    if (sat) {
      daysList.push('Sa');
    }
    if (daysList?.length > 0) {
      return daysList.join('|');
    }
  };

  const showError = (message: string) => {
    Alert.alert('Error', message);
  };

  const medicationTypes = [
    { label: 'Tablet', value: '1', icon: 'medical-outline' },
    { label: 'Injection', value: '2', icon: 'medical-outline' },
    { label: 'Inhaler', value: '3', icon: 'fitness-outline' },
    { label: 'Nasal Spray', value: '4', icon: 'water-outline' },
    { label: 'Syrup', value: '5', icon: 'flask-outline' },
  ];

  return (
    <Container style={styles.container}>
      <Loader modalVisible={loader} />
      
      {/* Tab Selector */}
      <View style={styles.tabSelectorContainer}>
        <TabSelector activeTab={activeTab} onTabChange={setActiveTab} />
      </View>

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}>
        
        {activeTab === 'manual' ? (
          // Manual Entry Tab Content
          <>
            {/* Basic Information Section */}
            <SectionCard 
              title="Basic Information"
              subtitle="Enter the medication details"
            >
              <View style={styles.inputGroup}>
                <InputApp
                  title={'Medication Name'}
                  value={medicationName}
                  onChangeText={setMedicationName}
                  placeholder="e.g., Aspirin"
                  style={styles.input}
                />
                <View style={styles.inputRow}>
                  <View style={styles.inputHalf}>
                    <InputApp
                      title={'Dosage'}
                      value={dosgae}
                      onChangeText={setDosage}
                      placeholder="e.g., 100mg"
                      style={styles.input}
                    />
                  </View>
                  <View style={styles.inputHalf}>
                    <InputApp
                      title={'Color'}
                      value={color}
                      onChangeText={setColor}
                      placeholder="e.g., White"
                      style={styles.input}
                    />
                  </View>
                </View>
              </View>
            </SectionCard>

            {/* Medication Image Section */}
            <SectionCard 
              title="Medication Photo"
              subtitle="Add a photo to help identify the medication"
            >
              <View style={styles.imageSection}>
                {medicationImage ? (
                  <View style={styles.imagePreview}>
                    <Image
                      source={{uri: medicationImage}}
                      style={styles.medicationImagePreview}
                    />
                    <TouchableOpacity
                      style={styles.changeImageButton}
                      onPress={handleChoosePhoto}
                    >
                      <Ionicons name="camera" size={20} color={Colors.TealBlue} />
                      <Text size={14} color={Colors.TealBlue} marginLeft={8}>
                        Change Photo
                      </Text>
                    </TouchableOpacity>
                  </View>
                ) : (
                  <TouchableOpacity
                    style={styles.uploadButton}
                    onPress={handleChoosePhoto}
                  >
                    <View style={styles.uploadContent}>
                      <Ionicons name="camera-outline" size={32} color={Colors.GrayBlue} />
                      <Text size={16} lineHeight={20} color={Colors.DarkJungleGreen} marginTop={8} bold>
                        Add Medication Photo
                      </Text>
                      <Text size={14} lineHeight={18} color={Colors.GrayBlue} marginTop={4} center>
                        Take a photo or choose from gallery
                      </Text>
                    </View>
                  </TouchableOpacity>
                )}
              </View>
            </SectionCard>

            {/* Medication Type Section */}
            <SectionCard 
              title="Medication Type"
              subtitle="Select the type of medication"
            >
              <View style={styles.medicationTypes}>
                {medicationTypes.map((type) => (
                  <TouchableOpacity
                    key={type.value}
                    style={[
                      styles.medicationTypeCard,
                      medcationType === type.value && styles.medicationTypeCardActive
                    ]}
                    onPress={() => setMedicationType(type.value)}
                    activeOpacity={0.7}
                  >
                    <Ionicons 
                      name={type.icon as any} 
                      size={24} 
                      color={medcationType === type.value ? Colors.TealBlue : Colors.GrayBlue} 
                      style={styles.medicationTypeIcon}
                    />
                    <Text 
                      size={15} 
                      lineHeight={18} 
                      color={medcationType === type.value ? Colors.TealBlue : Colors.DarkJungleGreen}
                      bold={medcationType === type.value}
                      style={styles.medicationTypeText}
                    >
                      {type.label}
                    </Text>
                    <View style={[
                      styles.radioButton,
                      medcationType === type.value && styles.radioButtonActive
                    ]}>
                      {medcationType === type.value && (
                        <View style={styles.radioButtonInner} />
                      )}
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </SectionCard>

            {/* Days Selection Section */}
            <SectionCard 
              title="Schedule Days"
              subtitle="Select the days to take this medication"
            >
              <View style={styles.daysSection}>
                <TouchableOpacity
                  style={[styles.allDaysButton, all && styles.allDaysButtonActive]}
                  onPress={() => {
                    if (all) {
                      setAll(false);
                      setSun(false);
                      setMon(false);
                      setTue(false);
                      setWed(false);
                      setThu(false);
                      setFri(false);
                      setSat(false);
                    } else {
                      setAll(true);
                      setSun(true);
                      setMon(true);
                      setTue(true);
                      setWed(true);
                      setThu(true);
                      setFri(true);
                      setSat(true);
                    }
                  }}
                  activeOpacity={0.7}
                >
                  <Ionicons 
                    name="calendar" 
                    size={18} 
                    color={all ? Colors.White : Colors.TealBlue} 
                    style={styles.allDaysIcon}
                  />
                  <Text 
                    size={16} 
                    lineHeight={20} 
                    color={all ? Colors.White : Colors.TealBlue}
                    bold
                  >
                    Every Day
                  </Text>
                </TouchableOpacity>
                
                <Text size={14} lineHeight={18} color={Colors.GrayBlue} marginTop={20} marginBottom={12}>
                  Or select specific days:
                </Text>
                
                <View style={styles.individualDaysContainer}>
                  <DayButton day="Sun" selected={sun} onPress={() => setSun(!sun)} />
                  <DayButton day="Mon" selected={mon} onPress={() => setMon(!mon)} />
                  <DayButton day="Tue" selected={tue} onPress={() => setTue(!tue)} />
                  <DayButton day="Wed" selected={wed} onPress={() => setWed(!wed)} />
                  <DayButton day="Thu" selected={thu} onPress={() => setThu(!thu)} />
                  <DayButton day="Fri" selected={fri} onPress={() => setFri(!fri)} />
                  <DayButton day="Sat" selected={sat} onPress={() => setSat(!sat)} />
                </View>
              </View>
            </SectionCard>

            {/* Time Slots Section */}
            <SectionCard 
              title="Daily Schedule"
              subtitle="Choose when to take the medication"
            >
              <View style={styles.timeSlotsContainer}>
                <TimeSlotCard
                  title="Early Morning"
                  icon="sunny-outline"
                  checked={earlyMorningCheck}
                  onToggle={() => setEarlyMorningCheck(!earlyMorningCheck)}
                  dropdownValue={earlyDropdownValue}
                  quantity={earlyMorningQnty}
                  onQuantityChange={setEarlyMorningQnty}
                  onDropdownSelect={(value) => setEarlyDropdownValue(value)}
                />

                <TimeSlotCard
                  title="Breakfast"
                  icon="cafe-outline"
                  checked={breakFastCheck}
                  onToggle={() => setBreakFastCheck(!breakFastCheck)}
                  dropdownValue={breakFastDropdownValue}
                  quantity={breakFastQnty}
                  onQuantityChange={setBreakFastQnty}
                  onDropdownSelect={(value) => setBreakFastDropdownValue(value)}
                />

                <TimeSlotCard
                  title="Lunch"
                  icon="restaurant-outline"
                  checked={lunchCheck}
                  onToggle={() => setLunchCheck(!lunchCheck)}
                  dropdownValue={lunchDropdownValue}
                  quantity={lunchQnty}
                  onQuantityChange={setLunchQnty}
                  onDropdownSelect={(value) => setLunchDropdownValue(value)}
                />

                <TimeSlotCard
                  title="Afternoon Snack"
                  icon="fast-food-outline"
                  checked={afternoonCheck}
                  onToggle={() => setAfternoonCheck(!afternoonCheck)}
                  dropdownValue={afternoonDropdownValue}
                  quantity={afternoonQnty}
                  onQuantityChange={setAfternoonQnty}
                  onDropdownSelect={(value) => setAfternoonDropdownValue(value)}
                />

                <TimeSlotCard
                  title="Dinner"
                  icon="wine-outline"
                  checked={dinnerCheck}
                  onToggle={() => setDinnerCheck(!dinnerCheck)}
                  dropdownValue={dinnerDropdownValue}
                  quantity={dinnerQnty}
                  onQuantityChange={setDinnerQnty}
                  onDropdownSelect={(value) => setDinnerDropdownValue(value)}
                />

                <TimeSlotCard
                  title="Bedtime"
                  icon="moon-outline"
                  checked={bedCheck}
                  onToggle={() => setBedCheck(!bedCheck)}
                  dropdownValue={bedDropdownValue}
                  quantity={bedQnty}
                  onQuantityChange={setBedQnty}
                  onDropdownSelect={(value) => setBedDropdownValue(value)}
                />
              </View>
            </SectionCard>

            {/* Add Medication Button */}
            <View style={styles.submitButtonContainer}>
              <ButtonLinear
                title="Add Medication"
                onPress={addMedicationToServer}
                style={styles.submitButton}
              />
            </View>
          </>
        ) : (
          // OCR Scan Tab Content
          <>
            <SectionCard 
              title="Prescription Scanner"
              subtitle="Scan or upload prescription to automatically extract medication details"
            >
              <View style={styles.modernOcrSection}>
                {!prescriptionImage ? (
                  // Upload State
                  <View style={styles.cleanOcrUpload}>
                    <View style={styles.uploadArea}>
                      <View style={styles.iconContainer}>
                        <Ionicons name="camera-outline" size={48} color={Colors.TealBlue} />
                      </View>
                      
                      <Text size={18} lineHeight={22} color={Colors.DarkJungleGreen} bold center marginTop={20}>
                        Scan Your Prescription
                      </Text>
                      <Text size={15} lineHeight={20} color={Colors.GrayBlue} center marginTop={8} marginBottom={32}>
                        Take a clear photo or choose from your device
                      </Text>
                      
                      <View style={styles.cleanOcrButtons}>
                        <TouchableOpacity 
                          style={styles.cleanPrimaryButton}
                          onPress={takePrescriptionPhoto}
                          activeOpacity={0.8}
                        >
                          <Ionicons name="camera" size={20} color={Colors.White} />
                          <Text size={16} color={Colors.White} bold marginLeft={8}>
                            Take Photo
                          </Text>
                        </TouchableOpacity>
                        
                        <TouchableOpacity 
                          style={styles.cleanSecondaryButton}
                          onPress={selectPrescriptionFromGallery}
                          activeOpacity={0.8}
                        >
                          <Ionicons name="images" size={20} color={Colors.TealBlue} />
                          <Text size={16} color={Colors.TealBlue} bold marginLeft={8}>
                            Choose Photo
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                ) : (
                  // Results State
                  <View style={styles.ocrResultsSection}>
                    {/* Image Preview Card */}
                    <View style={styles.imagePreviewCard}>
                      <Image
                        source={{uri: prescriptionImage}}
                        style={styles.modernPrescriptionImage}
                      />
                      <TouchableOpacity
                        style={styles.modernRemoveButton}
                        onPress={clearOCRData}
                        activeOpacity={0.8}
                      >
                        <Ionicons name="close" size={18} color={Colors.White} />
                      </TouchableOpacity>
                    </View>

                    {isProcessingOCR ? (
                      // Processing State
                      <View style={styles.modernProcessingCard}>
                        <View style={styles.processingContent}>
                          <View style={styles.loadingIndicator}>
                            <Loader modalVisible={false} />
                          </View>
                          <Text size={18} lineHeight={22} color={Colors.DarkJungleGreen} bold center marginTop={24}>
                            Processing Prescription
                          </Text>
                          <Text size={15} lineHeight={20} color={Colors.GrayBlue} center marginTop={8}>
                            AI is extracting medication information from your prescription
                          </Text>
                        </View>
                      </View>
                    ) : uploadSuccess ? (
                      // Success State
                      <View style={styles.modernSuccessCard}>
                        <View style={styles.successHeader}>
                          <Ionicons name="checkmark-circle" size={48} color={Colors.ForestGreen} />
                          <Text size={20} lineHeight={24} color={Colors.DarkJungleGreen} bold center marginTop={16}>
                            Uploaded Successfully
                          </Text>
                          <Text size={16} lineHeight={22} color={Colors.GrayBlue} center marginTop={8}>
                            All medications will be added to medications list
                          </Text>
                        </View>

                        <View style={styles.modernActionButtons}>
                          <TouchableOpacity 
                            style={styles.modernSecondaryButton}
                            onPress={clearOCRData}
                            activeOpacity={0.8}
                          >
                            <Ionicons name="refresh" size={18} color={Colors.TealBlue} />
                            <Text size={15} color={Colors.TealBlue} bold marginLeft={8}>
                              Scan Again
                            </Text>
                          </TouchableOpacity>
                          
                          <TouchableOpacity 
                            style={styles.modernPrimaryButton}
                            onPress={() => setActiveTab('manual')}
                            activeOpacity={0.8}
                          >
                            <Ionicons name="create" size={18} color={Colors.White} />
                            <Text size={15} color={Colors.White} bold marginLeft={8}>
                              Add More
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    ) : null}
                  </View>
                )}
              </View>
            </SectionCard>
          </>
        )}
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.Snow,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 40,
  },
  sectionCard: {
    backgroundColor: Colors.White,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    ...Theme.shadow,
  },
  sectionHeader: {
    marginBottom: 20,
  },
  inputGroup: {
    gap: 16,
  },
  input: {
    marginTop: 0,
  },
  inputRow: {
    flexDirection: 'row',
    gap: 12,
  },
  inputHalf: {
    flex: 1,
  },
  imageSection: {
    alignItems: 'center',
  },
  imagePreview: {
    alignItems: 'center',
    width: '100%',
  },
  medicationImagePreview: {
    width: 200,
    height: 200,
    borderRadius: 12,
    marginBottom: 16,
  },
  changeImageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.TealBlue,
  },
  uploadButton: {
    width: '100%',
    height: 120,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.Platinum,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.Snow,
  },
  uploadContent: {
    alignItems: 'center',
  },
  medicationTypes: {
    gap: 12,
  },
  medicationTypeCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.Platinum,
    backgroundColor: Colors.White,
  },
  medicationTypeCardActive: {
    borderColor: Colors.TealBlue,
    backgroundColor: Colors.TealBlue20,
  },
  medicationTypeIcon: {
    marginRight: 12,
  },
  medicationTypeText: {
    flex: 1,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: Colors.Platinum,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonActive: {
    borderColor: Colors.TealBlue,
  },
  radioButtonInner: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.TealBlue,
  },
  daysSection: {
    gap: 16,
  },
  allDaysButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: Colors.TealBlue,
  },
  allDaysButtonActive: {
    backgroundColor: Colors.TealBlue,
  },
  allDaysIcon: {
    marginRight: 8,
  },
  individualDaysContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
    paddingHorizontal: 4,
  },
  dayButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.Platinum,
    backgroundColor: Colors.White,
  },
  dayButtonSelected: {
    backgroundColor: Colors.TealBlue,
    borderColor: Colors.TealBlue,
  },
  timeSlotsContainer: {
    gap: 16,
  },
  timeSlotCard: {
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.Platinum,
    backgroundColor: Colors.White,
    overflow: 'hidden',
  },
  timeSlotHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  timeSlotInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.Platinum,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxActive: {
    backgroundColor: Colors.TealBlue,
    borderColor: Colors.TealBlue,
  },
  timeSlotContent: {
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.Snow,
  },
  timeSlotControlsContainer: {
    flexDirection: 'row',
    gap: 16,
    alignItems: 'flex-end',
  },
  timingSection: {
    flex: 2,
  },
  timingDropdown: {
    flex: 1,
  },
  quantitySection: {
    flex: 1,
  },
  quantityInputWrapper: {
    flex: 1,
  },
  customDropdownContainer: {
    flex: 1,
  },
  customDropdownButton: {
    height: 48,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.Platinum,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.White,
  },
  dropdownOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  dropdownModal: {
    backgroundColor: Colors.White,
    borderRadius: 16,
    width: '100%',
    maxWidth: 320,
    maxHeight: '80%',
    ...Theme.shadow,
  },
  dropdownHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.Snow,
  },
  dropdownOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.Snow,
  },
  dropdownOptionSelected: {
    backgroundColor: Colors.TealBlue20,
  },
  quantityInput: {
    marginTop: 0,
  },
  submitButtonContainer: {
    marginTop: 20,
  },
  submitButton: {
    height: 56,
    borderRadius: 12,
  },
  tabSelectorContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.Snow,
  },
  ocrSection: {
    alignItems: 'center',
  },
  ocrUploadButton: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.TealBlue,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.Snow,
  },
  ocrUploadContent: {
    alignItems: 'center',
  },
  ocrButtons: {
    flexDirection: 'row',
    gap: 16,
    marginTop: 24,
  },
  ocrActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.TealBlue,
    backgroundColor: Colors.White,
  },
  ocrResultSection: {
    alignItems: 'center',
  },
  prescriptionImageContainer: {
    position: 'relative',
    marginBottom: 24,
  },
  prescriptionImage: {
    width: 300,
    height: 200,
    borderRadius: 12,
    backgroundColor: Colors.Snow,
  },
  changePrescriptionButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: Colors.White,
    borderRadius: 16,
    padding: 4,
    ...Theme.shadow,
  },
  processingContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  ocrResultsContainer: {
    width: '100%',
    marginTop: 20,
  },
  detectedInfo: {
    backgroundColor: Colors.Snow,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  detectedItem: {
    marginBottom: 16,
  },
  ocrActionButtons: {
    flexDirection: 'row',
    gap: 12,
    justifyContent: 'center',
  },
  ocrSecondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    borderWidth: 1.5,
    borderColor: Colors.TealBlue,
    backgroundColor: Colors.White,
  },
  ocrPrimaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    backgroundColor: Colors.TealBlue,
    ...Theme.shadow,
  },
  ocrPlaceholderContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  ocrPlaceholderButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    backgroundColor: Colors.White,
    borderWidth: 1,
    borderColor: Colors.TealBlue,
    marginTop: 20,
  },
  modernTabContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.Platinum,
    borderRadius: 12,
    padding: 4,
    ...Theme.shadow,
  },
  modernTabButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginHorizontal: 2,
  },
  modernTabButtonActive: {
    backgroundColor: Colors.TealBlue,
    ...Theme.shadow,
  },
  tabContentWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  modernOcrSection: {
    alignItems: 'center',
  },
  ocrUploadContainer: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.TealBlue,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.Snow,
  },
  ocrUploadCard: {
    alignItems: 'center',
  },
  ocrIconContainer: {
    marginBottom: 20,
  },
  modernOcrButtons: {
    flexDirection: 'row',
    gap: 16,
    marginTop: 24,
  },
  primaryOcrButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 10,
    backgroundColor: Colors.TealBlue,
    ...Theme.shadow,
  },
  secondaryOcrButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 10,
    borderWidth: 1.5,
    borderColor: Colors.TealBlue,
    backgroundColor: Colors.White,
  },
  ocrResultsSection: {
    alignItems: 'center',
  },
  imagePreviewCard: {
    position: 'relative',
    marginBottom: 24,
  },
  modernPrescriptionImage: {
    width: 300,
    height: 200,
    borderRadius: 12,
    backgroundColor: Colors.Snow,
  },
  modernRemoveButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: Colors.White,
    borderRadius: 16,
    padding: 4,
    ...Theme.shadow,
  },
  modernProcessingCard: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  processingContent: {
    alignItems: 'center',
  },
  loadingIndicator: {
    marginBottom: 20,
  },
  modernResultsCard: {
    width: '100%',
    marginTop: 20,
  },
  resultsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  modernSuccessCard: {
    width: '100%',
    marginTop: 20,
    alignItems: 'center',
    paddingVertical: 20,
  },
  successHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  modernDetectedInfo: {
    backgroundColor: Colors.Snow,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  modernInfoItem: {
    marginBottom: 16,
  },
  modernActionButtons: {
    flexDirection: 'row',
    gap: 12,
    justifyContent: 'center',
  },

  modernSecondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    borderWidth: 1.5,
    borderColor: Colors.TealBlue,
    backgroundColor: Colors.White,
  },
  modernPrimaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    backgroundColor: Colors.TealBlue,
    ...Theme.shadow,
  },

  cleanOcrUpload: {
    width: '100%',
    paddingVertical: 40,
    alignItems: 'center',
  },
  uploadArea: {
    alignItems: 'center',
    width: '100%',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.TealBlue20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cleanOcrButtons: {
    flexDirection: 'row',
    gap: 12,
    width: '100%',
    justifyContent: 'center',
  },
  cleanPrimaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 10,
    backgroundColor: Colors.TealBlue,
    ...Theme.shadow,
  },
  cleanSecondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 10,
    borderWidth: 1.5,
    borderColor: Colors.TealBlue,
    backgroundColor: Colors.White,
  },
});
