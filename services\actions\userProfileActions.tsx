import {
  FETCH_USER_PROFILE,
  FETCH_USER_PROFILE_FAILURE,
  FETCH_USER_PROFILE_SUCCESS,
} from './type';

export const fetchUserProfile = (user: Object) => {
  return {
    type: FETCH_USER_PROFILE,
    payload: user,
  };
};

export const fetchUserProfileSuccess = (data: Object) => {
  return {
    type: FETCH_USER_PROFILE_SUCCESS,
    data,
  };
};

export const fetchUserProfileFailure = (msg: String) => {
  return {
    type: FETCH_USER_PROFILE_FAILURE,
    msg,
  };
};
