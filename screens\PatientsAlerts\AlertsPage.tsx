import React from 'react';
import {FlatList, StyleSheet, View, RefreshControl} from 'react-native';
import {Ionicons} from '@expo/vector-icons';
import AlertsItem from '@/components/AlertsItem';
import Text from '@/components/Text';
import {width} from '@/constants/Const';
import {Colors} from '@/constants';
import {AlertsProps} from '@/models';
import { getBottomTabSafePadding } from '@/utils/layoutHelper';

interface Props {
  data: AlertsProps[] | undefined;
  title: string;
  nameDisplay: boolean;
  onRefresh?: () => void;
  refreshing?: boolean;
}

const AlertsPage = ({data, nameDisplay, title, onRefresh, refreshing = false}: Props) => {
  const renderItem = ({item}: {item: AlertsProps}) => (
    <AlertsItem {...item} key={item.alertId.toString()} nameDisplay={nameDisplay} />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <View style={styles.emptyIconContainer}>
        <Ionicons name="notifications-off" size={60} color={Colors.GrayBlue} />
      </View>
      <Text size={18} bold color={Colors.DarkJungleGreen} marginBottom={8}>
        No {title} Alerts
      </Text>
      <Text size={14} color={Colors.GrayBlue} center style={styles.emptyText}>
        You don't have any {title.toLowerCase()} alerts for the selected period
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {data && data.length > 0 ? (
        <FlatList
          data={data}
          renderItem={renderItem}
          keyExtractor={(item) => item.alertId.toString()}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            onRefresh ? (
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                tintColor={Colors.TealBlue}
                colors={[Colors.TealBlue]}
              />
            ) : undefined
          }
        />
      ) : (
        renderEmptyState()
      )}
    </View>
  );
};

export default AlertsPage;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginTop: 0,
    paddingTop: 10,
  },
  listContent: { 
    paddingTop: 8,
    paddingBottom: getBottomTabSafePadding() + 20, // Extra padding to ensure content stays above FAB and tab bar
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  emptyIconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.Snow,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  emptyText: {
    maxWidth: 240,
    textAlign: 'center',
    lineHeight: 20,
  }
});
