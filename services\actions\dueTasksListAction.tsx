import { FETCH_DTASKS_LIST, FETCH_DTASKS_LIST_FAILURE, FETCH_DTASKS_LIST_SUCCESS } from './type';

export const fetchDtasksList = (params: Object) => {
  return {
    type: FETCH_DTASKS_LIST,
    payload: params,
  };
};

export const fetchDtasksListSuccess = (data: Object) => {
  return {
    type: FETCH_DTASKS_LIST_SUCCESS,
    data,
  };
};

export const fetchDtasksListFailure = (msg: String) => {
  return {
    type: FETCH_DTASKS_LIST_FAILURE,
    msg,
  };
};
