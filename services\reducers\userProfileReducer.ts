import {
  FETCH_USER_PROFILE,
  FETCH_USER_PROFILE_FAILURE,
  FETCH_USER_PROFILE_SUCCESS,
} from '../actions/type';

const initialState = {
  data: '',
  isFetching: false,
  msg: '',
};

export default function userProfileReducer(
  state = initialState,
  action: {type: any; data: any; msg: any},
) {
  switch (action.type) {
    case FETCH_USER_PROFILE:
      return {...state, isFetching: true, data: ''};
    case FETCH_USER_PROFILE_SUCCESS:
      return {
        ...state,
        isFetching: false,
        data: action.data,
      };
    case FETCH_USER_PROFILE_FAILURE:
      return {
        ...state,
        isFetching: false,
        msg: action.msg,
      };

    default:
      return state;
  }
}
