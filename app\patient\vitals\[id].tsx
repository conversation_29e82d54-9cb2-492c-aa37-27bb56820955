import React from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams } from 'expo-router';

import VitalsReportItemScreen from '@/screens/PatientDetails/VitalsReport/vitalsReportItem';

export default function VitalsReportItemRoute() {
  // Get both id and title from URL params with enhanced error handling
  const params = useLocalSearchParams();
  
  // Ensure id and title are properly handled
  const id = params.id ? String(params.id) : '3'; // Default to Heart Rate (id: 3) if not provided
  const title = params.title ? String(params.title) : '';

  console.log('VitalsReportItemRoute params:', { id, title });

  // Create a safe props object with default values for all required properties
  const safeProps = {
    vitalId: id,
    title: title,
  };

  return (
    // Explicitly exclude the top edge to prevent double padding with the header
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      <VitalsReportItemScreen {...safeProps} />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});