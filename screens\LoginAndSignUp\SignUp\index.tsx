import { useRouter } from 'expo-router';
import React, {useCallback, useEffect, useState} from 'react';
import {Alert, Linking, Modal, StyleSheet, View} from 'react-native';
import Loader from '@/components/Loader/Loader';
import ModalChangeCategory from '@/components/ModalChangeCategory';
import ModalSlideBottom from '@/components/ModalSlideBottom';
// Routes import removed
import {CATEGORY_LIST_EXAMPLE} from '@/constants/Data';
import useModalAnimation from '@/hooks/useModalAnimation';
import {apiPost} from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import Theme from '@/constants/Theme';
import {categoryList} from '@/types/category';
import scale from '@/utils/scale';
import validationEmail from '@/utils/validation/email';
import SignUpUi from './UI/SignUpUi';
import { isTablet } from '@/utils/responsive';
import { TabletSignup } from '@/components/Auth';

interface SignUpProps {}

const SignUp = (props: SignUpProps) => {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [visiblePassword, setVisiblePassword] = useState(false);
  const [isValidEmail, setIsValidEmail] = useState(false);
  const [loader, setLoader] = useState(false);
  const [category, setCategory] = React.useState(CATEGORY_LIST_EXAMPLE[0]);
  const [consumerType, setConsumerType] = useState('Individual');
  const [organizationName, setOrganizationName] = useState('');

  const router = useRouter();
  const {
    open: openCategory,
    close: closeCategory,
    visible: visibleCategory,
    transY: transYCategory,
  } = useModalAnimation();

  // Use tablet-optimized component for tablets
  if (isTablet()) {
    return (
      <TabletSignup
        onSignup={(data) => {
          setFirstName(data.firstName);
          setLastName(data.lastName);
          setEmail(data.email);
          setPassword(data.password);
          setOrganizationName(data.organization);
          onSignUp();
        }}
        onLogin={() => router.push('/auth/login')}
        loading={loader}
      />
    );
  }

  const onShowHidePassword = useCallback(() => {
    setVisiblePassword(prev => !prev);
  }, []);

  const changeConsumerType = useCallback((index: any) => {
    console.log('Consumer Type Changed.', index);
    if (index === 1) {
      setConsumerType('Individual');
    } else if (index === 2) {
      setConsumerType('Organization');
    } else {
      setConsumerType('Individual');
    }
  }, []);

  const onSignUp = async () => {
    setLoader(true);
    let user = {
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      userName: email.trim(),
      password: password.trim(),
      userRole: category?.displayName,
      userWork: consumerType,
      userWorkType:
        consumerType === 'Individual' ? '' : organizationName.trim(),
    };
    const registrationResponse = await apiPost(
      URLS.authUrl + 'caregiver/registerUser',
      user,
    );

    if (registrationResponse?.status == 200) {
      setLoader(false);
      if (registrationResponse?.data?.success) {
        router.replace('/auth/signup-successful');
      } else {
        Alert.alert('Error', registrationResponse?.data?.responseMessage, [
          {text: 'Dismiss'},
        ]);
      }
    } else {
      setLoader(false);
      const errorMessage = registrationResponse?.response?.data?.responseMessage
        ? registrationResponse?.response?.data?.responseMessage
        : registrationResponse.message === 'Network Error'
        ? 'Network error. Please check your data connection.'
        : registrationResponse.message;

      Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
    }
  };

  const onTermOfUse = useCallback(() => {
    Linking.openURL('https://watchrx.io/watchrx-terms-and-conditions/');
  }, []);

  const onPrivacyPolicy = useCallback(() => {
    Linking.openURL('https://watchrx.io/privacy-policy/');
  }, []);

  useEffect(() => {
    const validation = validationEmail(email);
    setIsValidEmail(validation);
  }, [email]);

  const onGoToLogin = useCallback(() => {
    router.replace('/auth/login');
  }, [router]);

  const onChangeCategory = useCallback((item: categoryList) => {
    setCategory(item);
    closeCategory();
  }, []);

  return (
    <View style={styles.container}>
      <Loader modalVisible={loader} />
      <SignUpUi
        {...{
          firstName,
          setFirstName,
          lastName,
          setLastName,
          organizationName,
          setOrganizationName,
          email,
          setEmail,
          isValidEmail,
          password,
          setPassword,
          visiblePassword,
          onShowHidePassword,
          onSignUp,
          onTermOfUse,
          onPrivacyPolicy,
          onGoToLogin,
          category,
          changeConsumerType,
          consumerType,
        }}
        openCategory={openCategory}
      />

      <Modal
        visible={visibleCategory}
        onRequestClose={closeCategory}
        transparent
        animationType={'fade'}>
        <ModalSlideBottom onClose={closeCategory} transY={transYCategory}>
          <ModalChangeCategory
            onChangeCategory={onChangeCategory}
            data={CATEGORY_LIST_EXAMPLE}
          />
        </ModalSlideBottom>
      </Modal>
    </View>
  );
};

export default SignUp;

const styles = StyleSheet.create({
  container: {flex: 1},
  password: {
    marginTop: 24,
  },
  phoneNumber: {
    marginLeft: 8,
    flex: 1,
  },
  emailInput: {
    marginTop: scale(34),
  },
  bottom: {
    ...Theme.flexOne,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  flag: {
    width: 32,
    height: 20,
  },
  changePhoneCode: {
    position: 'absolute',
    right: 16,
    alignSelf: 'center',
  },
  phoneView: {
    ...Theme.flexRow,
    marginTop: 4,
  },
});
