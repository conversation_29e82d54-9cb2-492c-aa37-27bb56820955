import { clearPatientNotification, ClearNotificationResponse } from '../apis/apiManager';

/**
 * Redux action types for clear notification
 */
export const CLEAR_NOTIFICATION_REQUEST = 'CLEAR_NOTIFICATION_REQUEST';
export const CLEAR_NOTIFICATION_SUCCESS = 'C<PERSON>AR_NOTIFICATION_SUCCESS';
export const CLEAR_NOTIFICATION_FAILURE = 'CLEAR_NOTIFICATION_FAILURE';

export interface ClearNotificationRequestAction {
  type: typeof CLEAR_NOTIFICATION_REQUEST;
  payload: { patientId: string };
}

export interface ClearNotificationSuccessAction {
  type: typeof CLEAR_NOTIFICATION_SUCCESS;
  payload: { patientId: string; success: boolean };
}

export interface ClearNotificationFailureAction {
  type: typeof CLEAR_NOTIFICATION_FAILURE;
  payload: { patientId: string; error: string };
}

export type ClearNotificationActionTypes = 
  | ClearNotificationRequestAction
  | ClearNotificationSuccessAction
  | ClearNotificationFailureAction;

/**
 * Redux action creators
 */
export const clearNotificationRequest = (patientId: string): ClearNotificationRequestAction => ({
  type: CLEAR_NOTIFICATION_REQUEST,
  payload: { patientId },
});

export const clearNotificationSuccess = (patientId: string, success: boolean): ClearNotificationSuccessAction => ({
  type: CLEAR_NOTIFICATION_SUCCESS,
  payload: { patientId, success },
});

export const clearNotificationFailure = (patientId: string, error: string): ClearNotificationFailureAction => ({
  type: CLEAR_NOTIFICATION_FAILURE,
  payload: { patientId, error },
});

/**
 * Async action to clear a notification for a specific patient
 * Uses the centralized API from apiManager.ts
 */
export const clearNotification = (patientId: string) => {
  return async (dispatch: any) => {
    try {
      dispatch(clearNotificationRequest(patientId));

      // Use the centralized API from apiManager.ts
      const response = await clearPatientNotification(patientId);

      if (response.error) {
        dispatch(clearNotificationFailure(patientId, response.error.message));
        return false;
      }

      if (!response.data) {
        dispatch(clearNotificationFailure(patientId, 'No data received from clear notification API'));
        return false;
      }

      const success = response.data.success === true;
      
      dispatch(clearNotificationSuccess(patientId, success));
      return success;
    } catch (error: any) {
      dispatch(clearNotificationFailure(patientId, error.message || 'Unknown error occurred'));
      return false;
    }
  };
}; 