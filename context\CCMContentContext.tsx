import React, { createContext, useContext, useState, ReactNode } from 'react';

type CCMContentContextType = {
  ccmContentTitle: string;
  setCcmContentTitle: (title: string) => void;
  vitalsTitle: string;
  setVitalsTitle: (title: string) => void;
  dynamicTitle: string;
  setDynamicTitle: (title: string) => void;
};

// Create context with default values
const CCMContentContext = createContext<CCMContentContextType>({
  ccmContentTitle: '',
  setCcmContentTitle: () => {},
  vitalsTitle: '',
  setVitalsTitle: () => {},
  dynamicTitle: '',
  setDynamicTitle: () => {},
});

// Create provider component
export const CCMContentProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [ccmContentTitle, setCcmContentTitle] = useState('');
  const [vitalsTitle, setVitalsTitle] = useState('');
  const [dynamicTitle, setDynamicTitle] = useState('');

  return (
    <CCMContentContext.Provider value={{ 
      ccmContentTitle, 
      setCcmContentTitle,
      vitalsTitle,
      setVitalsTitle,
      dynamicTitle,
      setDynamicTitle
    }}>
      {children}
    </CCMContentContext.Provider>
  );
};

// Custom hook for consuming the context
export const useCCMContentContext = () => useContext(CCMContentContext);
