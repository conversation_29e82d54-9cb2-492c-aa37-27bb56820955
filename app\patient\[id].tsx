import React, { useEffect, useState } from 'react';
import { useLocalSearchParams } from 'expo-router';
import { useDispatch, useSelector } from 'react-redux';

import PatientDetailsScreen from '@/screens/PatientDetails';
import { setCurrentPatientId, setCurrentPatientName } from '@/services/actions/patientActions';
import { apiPostWithToken } from '@/services/apis/apiManager';
import URLS from '@/services/config/config';

export default function PatientDetailsRoute() {
  const params = useLocalSearchParams();
  const { id, patientName, phone, address, image, dob, dateOfBirth, mrn } = params;
  const dispatch = useDispatch();
  const [patientDetails, setPatientDetails] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // Create a patient details object from URL parameters if available
  const urlPatientDetails = patientName ? {
    patientId: id,
    patientName,
    phone,
    address,
    image,
    dob: dob || dateOfBirth, // Handle both field names
    dateOfBirth: dateOfBirth || dob, // Handle both field names
    mrn: mrn || String(id), // Use MRN if available, otherwise fall back to ID
  } : null;

  const caregiverId = useSelector((state: any) => state?.loginReducer?.data?.userId);
  const orgId = useSelector((state: any) => state?.currentOrgIdReducer?.orgId);

  // Use a ref to track if we've already fetched data
  const dataFetchedRef = React.useRef(false);

  // Set the current patient ID in Redux
  useEffect(() => {
    // Only run this effect once per component mount
    if (id && !dataFetchedRef.current) {
      console.log('Setting current patient ID in Redux:', id);
      dispatch(setCurrentPatientId(id as string));
      dataFetchedRef.current = true;

      // Check if we have complete patient details from URL params
      // We need both dob and phone for a complete profile
      const hasCompleteData = urlPatientDetails && urlPatientDetails.dob && urlPatientDetails.phone;

      if (hasCompleteData) {
        console.log('Using complete patient details from URL params:', urlPatientDetails);
        setPatientDetails(urlPatientDetails);
        dispatch(setCurrentPatientName(patientName as string));
        setLoading(false);
      } else {
        // Fetch patient details from API when data is incomplete or missing
        console.log('Fetching patient details from API due to incomplete data');
        fetchPatientDetails(id as string);
      }
    }
  }, [id, dispatch]); // Minimal dependency array

  // Function to fetch patient details
  const fetchPatientDetails = async (patientId: string) => {
    // Don't fetch if we already have complete patient details
    if (patientDetails && patientDetails.dob && patientDetails.phone) {
      console.log('Complete patient details already loaded, skipping fetch');
      return;
    }
    try {
      setLoading(true);
      console.log('Fetching patient details for ID:', patientId);

      const response = await apiPostWithToken(
        {
          careGiverId: caregiverId,
          orgId: orgId,
          roleId: '5' // Role ID for caregiver
        },
        URLS.caregiverUrl + 'getpatientsfornurse'
      );

      if (response?.status === 200 && response?.data) {
        console.log('Patient details fetched successfully:', response.data);

        // Extract the patient info array from the response
        const patients = response.data.patientInfo || response.data;

        // Find the patient with the matching ID in the response
        if (Array.isArray(patients) && patients.length > 0) {
          // If we have an array of patients, find the one with matching ID
          const patient = patients.find((p: any) => p.patientId.toString() === patientId.toString());

          if (patient) {
            console.log('Found patient in response:', patient);
            setPatientDetails(patient);

            // Set patient name in Redux
            if (patient.patientName) {
              dispatch(setCurrentPatientName(patient.patientName));
            }
          } else {
            // If we can't find the specific patient, use the first one
            console.log('Patient not found in response, using first patient:', patients[0]);
            setPatientDetails(patients[0]);

            if (patients[0].patientName) {
              dispatch(setCurrentPatientName(patients[0].patientName));
            }
          }
        } else if (typeof response.data === 'object' && response.data.patientName) {
          // If we have a single patient object
          console.log('Single patient object in response');
          setPatientDetails(response.data);
          dispatch(setCurrentPatientName(response.data.patientName));
        } else {
          console.error('No patient data found in response. Response structure:', response.data);
        }
      } else {
        console.error('Failed to fetch patient details:', response);
      }
    } catch (error) {
      console.error('Error fetching patient details:', error);
    } finally {
      setLoading(false);
    }
  };

  // Directly render the PatientDetailsScreen without SafeAreaView
  // The Container component inside PatientDetailsScreen will handle
  // the appropriate padding for the GlobalNavigationHeader
  return (
    <PatientDetailsScreen
      patientId={id as string}
      patientDetails={patientDetails}
      loading={loading}
      dob={dob as string}
      mrn={mrn as string}
    />
  );
}