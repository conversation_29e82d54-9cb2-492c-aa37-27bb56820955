import React, { memo } from 'react';
import { StyleSheet, View } from 'react-native';
import Animated, { useAnimatedStyle, interpolate, Extrapolate } from 'react-native-reanimated';
import { Colors, Constants } from '@/constants';

interface DotProgressProps {
  numberOfDots: number;
  scrollX: Animated.SharedValue<number>;
}

const DotProgress = memo(({ numberOfDots, scrollX }: DotProgressProps) => {
  const dots = Array.from({ length: numberOfDots }, (_, i) => i);

  return (
    <View style={styles.container}>
      {dots.map((_, i) => {
        const dotAnimatedStyle = useAnimatedStyle(() => {
          const inputRange = [
            (i - 1) * Constants.width,
            i * Constants.width,
            (i + 1) * Constants.width,
          ];
          
          const width = interpolate(
            scrollX.value,
            inputRange,
            [8, 16, 8],
            Extrapolate.CLAMP
          );
          
          const opacity = interpolate(
            scrollX.value,
            inputRange,
            [0.5, 1, 0.5],
            Extrapolate.CLAMP
          );
          
          return {
            width,
            opacity,
          };
        });

        return (
          <Animated.View key={i.toString()} style={[styles.dot, dotAnimatedStyle]} />
        );
      })}
    </View>
  );
});

export default DotProgress;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dot: {
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.White,
    marginHorizontal: 4,
  },
});
