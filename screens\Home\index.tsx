import { useIsFocused, useNavigation } from '@react-navigation/native';
import React, {useEffect} from 'react';
import { BackHandler, StyleSheet, View, StatusBar, Platform } from 'react-native';
import {useSelector} from 'react-redux';

import MainControl from '@/components/Home/MainControl';
import {useTheme} from '@/constants/Theme';
import { useAlertsCount } from '@/context/AlertsCountContext';

/**
 * Home Screen
 * Main screen of the app showing statistics and main controls
 */
const Home = () => {
  const isFocused = useIsFocused();
  const navigation = useNavigation();
  const {theme} = useTheme();
  const { refreshAlertsCount } = useAlertsCount();

  const caregiverId = useSelector(
    (state: {loginReducer?: {data?: {userId?: string}}}) =>
      state?.loginReducer?.data?.userId || '',
  );

  const userProfileDetails = useSelector(
    (state: {userProfileReducer?: {data?: any}}) =>
      state?.userProfileReducer?.data,
  );

  // Handle back button to exit app
  useEffect(() => {
    const exitApp = () => {
      BackHandler.exitApp();
      return true;
    };
    if (isFocused) {
      BackHandler.addEventListener('hardwareBackPress', exitApp);
    }
    return () => {
      BackHandler.removeEventListener('hardwareBackPress', exitApp);
    };
  }, [isFocused]);

  useEffect(() => {
    if (isFocused) {
      const timer = setTimeout(() => {
        refreshAlertsCount();
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [isFocused, refreshAlertsCount]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      refreshAlertsCount();
    });

    return unsubscribe;
  }, [navigation, refreshAlertsCount]);

  // Note: Notification setup is now handled by the centralized NotificationManager
  // which is imported and used throughout the app via the NotificationProvider context

  return (
    <View style={styles.container}>
      <StatusBar 
        barStyle="dark-content" 
        backgroundColor="#FAFBFC" 
        translucent={Platform.OS === 'android'}
      />
      <View style={styles.contentContainer}>
        <MainControl />
      </View>
    </View>
  );
};

export default Home;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFBFC',
  },
  contentContainer: {
    flex: 1,
  },
});
