import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, Alert, StatusBar, SafeAreaView, Platform } from 'react-native';
import { useSelector } from 'react-redux';
import Container from '@/components/Layout/Container';
import CalendarComponent from './components/CalendarComponent';
import TaskCreateEditScreen from './components/TaskCreateEditScreen';
import TaskService from './services/TaskService';
import { Task } from './types';

/**
 * Schedule Screen - Reimagined with Modern UI/UX
 * Full screen calendar with clean task management
 */
const Schedule = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [showTaskScreen, setShowTaskScreen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isTaskActionLoading, setIsTaskActionLoading] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);

  // Get caregiver ID from Redux store
  const caregiverId = useSelector(
    (state: {loginReducer?: {data?: {userId?: string}}}) =>
      state?.loginReducer?.data?.userId || '',
  );

  const orgId = useSelector(
    (state: {currentOrgIdReducer?: {orgId?: string}}) =>
      state?.currentOrgIdReducer?.orgId || '',
  );

  // Load tasks on component mount and when user/org changes
  useEffect(() => {
    if (caregiverId && orgId) {
      loadTasks();
    }
  }, [caregiverId, orgId]);

  // Load tasks function with enhanced error handling
  const loadTasks = async () => {
    setIsLoading(true);
    try {
      const tasksList = await TaskService.getTasks();
      setTasks(tasksList);
    } catch (error: any) {
      console.error('Error loading tasks:', error);
      
      Alert.alert(
        'Error Loading Tasks',
        error?.message || 'Failed to load tasks. Please check your connection and try again.',
        [
          {
            text: 'Retry',
            onPress: loadTasks,
            style: 'default',
          },
          {
            text: 'Cancel',
            style: 'cancel',
          },
        ]
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Handle add task button click
  const handleAddTask = (date: Date) => {
    setSelectedDate(date);
    setSelectedTask(null);
    setIsEditMode(false);
    setShowTaskScreen(true);
  };

  // Handle task selection in calendar
  const handleTaskSelect = (task: Task) => {
    setSelectedTask(task);
    setSelectedDate(new Date(task.start));
    setIsEditMode(true);
    setShowTaskScreen(true);
  };

  // Handle task creation with success feedback
  const handleCreateTask = async (task: Task) => {
    setIsTaskActionLoading(true);
    try {
      const newTask = await TaskService.createTask(task);
      
      setTasks(prevTasks => [...prevTasks, newTask]);
      setShowTaskScreen(false);
      setSelectedTask(null);
      
      Alert.alert(
        '✅ Success',
        'Task created successfully!',
        [{ text: 'OK', style: 'default' }]
      );
    } catch (error: any) {
      console.error('Error creating task:', error);
      
      Alert.alert(
        '❌ Error Creating Task',
        error?.message || 'Failed to create task. Please try again.',
        [
          { text: 'Retry', onPress: () => handleCreateTask(task) },
          { text: 'Cancel', style: 'cancel' }
        ]
      );
    } finally {
      setIsTaskActionLoading(false);
    }
  };

  // Handle task update with success feedback
  const handleUpdateTask = async (task: Task) => {
    setIsTaskActionLoading(true);
    try {
      const updatedTask = await TaskService.updateTask(task);
      
      setTasks(prevTasks => 
        prevTasks.map(t => t.id === updatedTask.id ? updatedTask : t)
      );
      setShowTaskScreen(false);
      setSelectedTask(null);
      
      Alert.alert(
        '✅ Success',
        'Task updated successfully!',
        [{ text: 'OK', style: 'default' }]
      );
    } catch (error: any) {
      console.error('Error updating task:', error);
      
      Alert.alert(
        '❌ Error Updating Task',
        error?.message || 'Failed to update task. Please try again.',
        [
          { text: 'Retry', onPress: () => handleUpdateTask(task) },
          { text: 'Cancel', style: 'cancel' }
        ]
      );
    } finally {
      setIsTaskActionLoading(false);
    }
  };

  // Handle task deletion from calendar (simplified - no confirmation since CalendarComponent handles it)
  const handleDeleteTaskFromCalendar = async (taskId: string) => {
    setTasks(prevTasks => prevTasks.filter(t => t.id !== taskId));
  };

  // Handle task deletion with confirmation (for task edit screen)
  const handleDeleteTask = async (taskId: string) => {
    setIsTaskActionLoading(true);
    try {
      await TaskService.deleteTask(taskId);
      setTasks(prevTasks => prevTasks.filter(t => t.id !== taskId));
      setShowTaskScreen(false);
      setSelectedTask(null);
      
      Alert.alert('✅ Success', 'Task deleted successfully!');
    } catch (error: any) {
      console.error('Error deleting task:', error);
      Alert.alert('❌ Error', 'Failed to delete task. Please try again.');
    } finally {
      setIsTaskActionLoading(false);
    }
  };

  // Handle task submit
  const handleTaskSubmit = async (task: Task) => {
    if (isEditMode && selectedTask) {
      await handleUpdateTask({ ...task, id: selectedTask.id });
    } else {
      await handleCreateTask(task);
    }
  };

  // Close task screen
  const handleCloseTaskScreen = () => {
    setShowTaskScreen(false);
    setSelectedTask(null);
    setIsEditMode(false);
  };

  // If task screen is shown, render it instead of the main content
  if (showTaskScreen) {
    return (
      <TaskCreateEditScreen
        selectedDate={selectedDate}
        selectedTask={selectedTask}
        isEditMode={isEditMode}
        onSubmit={handleTaskSubmit}
        onDelete={handleDeleteTask}
        onClose={handleCloseTaskScreen}
        isLoading={isTaskActionLoading}
      />
    );
  }

  return (
    <Container style={styles.container}>
      <CalendarComponent
        tasks={tasks}
        onDateSelect={handleAddTask}
        onTaskSelect={handleTaskSelect}
        onTaskDelete={handleDeleteTaskFromCalendar}
        isLoading={isLoading}
      />
    </Container>
  );
};

export default Schedule;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 0, // Remove horizontal padding since calendar needs full width
  },
});
