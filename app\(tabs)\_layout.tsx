import { Tabs, usePathname } from 'expo-router';
import React, { useMemo, useRef, useEffect, useState } from 'react';
import { Platform, View, TouchableOpacity, StyleSheet, Dimensions, Animated, SafeAreaView } from 'react-native';
import { createShadow } from '@/utils/shadowStyles';
import { getBottomSpace } from 'react-native-iphone-x-helper';
import { useSelector } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';

import Text from '@/components/Text';
import { CoreColors } from '@/constants/Colors';
import Theme from '@/constants/Theme';
import NavigationHeader from '@/components/NavigationHeader';
import Constants from '@/constants/Const';
import { 
  spacing, 
  typography, 
  iconSizes, 
  layout, 
  getScreenSize, 
  getScreenHeight,
  scale,
  fontScale,
  isTablet,
  isLargeTablet,
  getOrientation,
  tabletLayout
} from '@/utils/responsive';

// Dynamic screen dimensions that update on rotation
const useResponsiveDimensions = () => {
  const [screenData, setScreenData] = useState(() => {
    const { width, height } = Dimensions.get('window');
    return { width, height };
  });

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenData({ width: window.width, height: window.height });
    });

    return () => subscription?.remove?.();
  }, []);

  return screenData;
};

// Enhanced tablet-aware tab bar configuration
const getTabBarConfig = (screenWidth: number, screenHeight: number) => {
  // Use responsive.ts layout helpers for consistency
  const isSmallScreen = layout.isSmallScreen;
  const isShortScreen = layout.isShortScreen;
  const isMediumScreen = layout.isMediumScreen;
  const isLargeScreen = layout.isLargeScreen;
  const currentOrientation = getOrientation();
  
  // Tablet-specific configurations
  const tabletConfig = {
    // Enhanced height for tablets with orientation awareness
    height: isLargeTablet() 
      ? (currentOrientation === 'landscape' ? scale(80) : scale(76))
      : isTablet() 
      ? (currentOrientation === 'landscape' ? scale(72) : scale(68))
      : isShortScreen ? scale(56) : isSmallScreen ? scale(62) : scale(68),
    
    // Tablet-aware margins with orientation adjustments
    marginHorizontal: isLargeTablet() 
      ? (currentOrientation === 'landscape' ? spacing.xl : spacing.lg)
      : isTablet() 
      ? (currentOrientation === 'landscape' ? spacing.lg : spacing.md)
      : isSmallScreen ? spacing.sm : spacing.md,
      
    marginBottom: Platform.OS === 'ios' ? 
      (isTablet() ? (currentOrientation === 'landscape' ? spacing.md : spacing.sm) : 
       isSmallScreen ? spacing.xs : spacing.sm) : 
      (isTablet() ? (currentOrientation === 'landscape' ? spacing.lg : spacing.md) :
       isSmallScreen ? spacing.sm : spacing.md),
    
    // Enhanced border radius for tablets
    borderRadius: isLargeTablet() ? scale(28) : isTablet() ? scale(24) : 
                  isSmallScreen ? scale(16) : scale(20),
    
    // Tablet-optimized padding
    paddingVertical: isLargeTablet() ? spacing.md : isTablet() ? spacing.sm : 
                     isSmallScreen ? spacing.xs : spacing.sm,
    paddingHorizontal: isLargeTablet() ? spacing.lg : isTablet() ? spacing.md : 
                       isSmallScreen ? spacing.xs : spacing.sm,
    
    // Enhanced icon sizes for tablets
    iconSize: isLargeTablet() ? iconSizes.xl : isTablet() ? iconSizes.lg : 
              isShortScreen ? iconSizes.md : iconSizes.lg,
    
    // Tablet-aware font sizing
    fontSize: isTablet() ? fontScale(typography.body.fontSize) : 
              fontScale(typography.caption.fontSize),
    
    // Enhanced container dimensions for tablets
    maxWidth: isLargeTablet() ? scale(120) : isTablet() ? scale(100) :
              isSmallScreen ? scale(55) : isMediumScreen ? scale(70) : scale(80),
    minHeight: isLargeTablet() ? scale(56) : isTablet() ? scale(52) :
               isShortScreen ? scale(40) : scale(46),
    
    // Tablet-optimized content padding
    tabContentPadding: {
      vertical: isLargeTablet() ? spacing.sm : isTablet() ? spacing.xs : 
                isSmallScreen ? spacing.xs : spacing.sm,
      horizontal: isLargeTablet() ? spacing.sm : isTablet() ? spacing.xs :
                  isSmallScreen ? scale(2) : spacing.xs,
    },
    
    // Enhanced shadow for tablets
    shadowConfig: {
      elevation: isLargeTablet() ? 16 : isTablet() ? 12 : isSmallScreen ? 4 : 8,
      shadowRadius: isLargeTablet() ? scale(16) : isTablet() ? scale(12) :
                    scale(isSmallScreen ? 6 : 10),
      shadowOpacity: isLargeTablet() ? 0.15 : isTablet() ? 0.12 : 
                     isSmallScreen ? 0.06 : 0.1,
    },
    
    // Compact mode - disabled for tablets to show full labels
    compactMode: isSmallScreen && isShortScreen && !isTablet(),
    
    // Enhanced spacing and indicators for tablets
    tabSpacing: isLargeTablet() ? scale(4) : isTablet() ? scale(3) :
                isSmallScreen ? scale(1) : scale(2),
    indicatorHeight: isLargeTablet() ? scale(3) : isTablet() ? scale(2.5) :
                     scale(isSmallScreen ? 1.5 : 2),
    labelMarginTop: isLargeTablet() ? scale(3) : isTablet() ? scale(2) :
                    isSmallScreen ? scale(1) : scale(2),
    
    // Tablet-specific enhancements
    touchTargetSize: isTablet() ? tabletLayout.touchTargets.comfortable : 
                     tabletLayout.touchTargets.minimum,
    animationScale: isLargeTablet() ? 1.08 : isTablet() ? 1.06 : 
                    layout.isSmallScreen ? 1.04 : 1.06,
    labelOpacityRange: isTablet() ? [0.8, 1] : [0.6, 1],
    
    // Orientation-specific adjustments
    orientationAdjustments: {
      landscape: {
        additionalPadding: isTablet() ? spacing.sm : 0,
        enhancedSpacing: isTablet() ? spacing.xs : 0,
      },
      portrait: {
        additionalPadding: 0,
        enhancedSpacing: 0,
      },
    },
  };
  
  return tabletConfig;
};

// Modern tab bar with improved design principles
function CustomTabBar(props: any) {
  const { state, descriptors, navigation } = props;
  const animatedValues = useRef(
    state.routes.map(() => new Animated.Value(0))
  ).current;

  const focusedOptions = descriptors[state.routes[state.index].key].options;
  if (focusedOptions.tabBarVisible === false) {
    return null;
  }

  // Get responsive configuration with dynamic dimensions
  const screenData = useResponsiveDimensions();
  const tabBarConfig = getTabBarConfig(screenData.width, screenData.height);

  // Animate tab changes
  useEffect(() => {
    animatedValues.forEach((value: Animated.Value, index: number) => {
      Animated.timing(value, {
        toValue: state.index === index ? 1 : 0,
        duration: 200,
        useNativeDriver: false,
      }).start();
    });
  }, [state.index]);

  return useMemo(() => {
    return (
      <View style={styles.safeArea}>
        <View style={[styles.tabsContainer, {
          marginHorizontal: tabBarConfig.marginHorizontal,
          marginBottom: tabBarConfig.marginBottom,
          borderRadius: tabBarConfig.borderRadius,
          paddingVertical: tabBarConfig.paddingVertical,
          paddingHorizontal: tabBarConfig.paddingHorizontal,
          height: tabBarConfig.height,
          elevation: tabBarConfig.shadowConfig.elevation,
          shadowRadius: tabBarConfig.shadowConfig.shadowRadius,
          shadowOpacity: tabBarConfig.shadowConfig.shadowOpacity,
        }]}>
          {state.routes.map((route: any, index: number) => {
              const { options } = descriptors[route.key];
              const isFocused = state.index === index;
              const animatedValue = animatedValues[index];

              const onPress = () => {
                navigation.navigate(route.name);
              };

              // Enhanced icon system with better visual hierarchy
              const getIconConfig = (): { name: string; outlineName: string; label: string; badge?: boolean } => {
                switch (route.name) {
                  case 'index':
                    return { 
                      name: 'home',
                      outlineName: 'home-outline',
                      label: tabBarConfig.compactMode ? 'Home' : 'Home'
                    };
                  case 'patients':
                    return { 
                      name: 'people',
                      outlineName: 'people-outline',
                      label: tabBarConfig.compactMode ? 'Patients' : 'Patients'
                    };
                  case 'schedule':
                    return { 
                      name: 'calendar',
                      outlineName: 'calendar-outline',
                      label: tabBarConfig.compactMode ? 'Schedule' : 'Schedule'
                    };
                  case 'profile':
                    return { 
                      name: 'person',
                      outlineName: 'person-outline',
                      label: tabBarConfig.compactMode ? 'Profile' : 'Profile'
                    };
                  case 'tasks':
                    return { 
                      name: 'checkmark-circle',
                      outlineName: 'checkmark-circle-outline',
                      label: tabBarConfig.compactMode ? 'Tasks' : 'Tasks'
                    };
                  default:
                    return { 
                      name: 'ellipse',
                      outlineName: 'ellipse-outline',
                      label: options.title || route.name
                    };
                }
              };

              const iconConfig = getIconConfig();

              // Enhanced animated styles using tablet-aware configuration
              const iconScale = animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: [1, tabBarConfig.animationScale],
              });

              const labelOpacity = animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: tabBarConfig.labelOpacityRange,
              });

              const indicatorWidth = animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: [0, tabBarConfig.compactMode ? scale(18) : layout.isSmallScreen ? scale(22) : scale(26)],
              });

              return (
                <View key={index} style={styles.tabItem}>
                  <TouchableOpacity
                    accessibilityRole="button"
                    accessibilityState={{ selected: isFocused }}
                    accessibilityLabel={options.tabBarAccessibilityLabel}
                    testID={options.tabBarTestID}
                    onPress={onPress}
                    style={[styles.tabButton, {
                      borderRadius: tabBarConfig.borderRadius - scale(4),
                    }]}
                    activeOpacity={0.7}
                  >
                    <View style={[styles.tabContent, {
                      paddingVertical: tabBarConfig.tabContentPadding.vertical,
                      paddingHorizontal: tabBarConfig.tabContentPadding.horizontal,
                      minHeight: tabBarConfig.minHeight,
                      maxWidth: tabBarConfig.maxWidth,
                    }]}>
                      {/* Icon with scale animation */}
                      <Animated.View 
                        style={[
                          styles.iconContainer,
                          { 
                            transform: [{ scale: iconScale }],
                            marginBottom: tabBarConfig.compactMode ? 0 : tabBarConfig.tabSpacing,
                          }
                        ]}
                      >
                        <Ionicons
                          name={(isFocused ? iconConfig.name : iconConfig.outlineName) as keyof typeof Ionicons.glyphMap}
                          size={tabBarConfig.iconSize}
                          color={isFocused ? CoreColors.TurquoiseBlue : '#6B7280'}
                        />
                      </Animated.View>
                      
                      {/* Label with fade animation and responsive font size */}
                      {!tabBarConfig.compactMode && (
                        <Animated.Text 
                          style={[
                            styles.tabLabel,
                            {
                              opacity: labelOpacity,
                              color: isFocused ? CoreColors.TurquoiseBlue : '#6B7280',
                              fontSize: tabBarConfig.fontSize,
                              lineHeight: tabBarConfig.fontSize + scale(2),
                              maxWidth: tabBarConfig.maxWidth - scale(8),
                              marginTop: tabBarConfig.labelMarginTop,
                            }
                          ]}
                          numberOfLines={1}
                          adjustsFontSizeToFit={true}
                          minimumFontScale={0.75}
                        >
                          {iconConfig.label}
                        </Animated.Text>
                      )}
                      
                      {/* Modern active indicator with responsive width */}
                      <Animated.View 
                        style={[
                          styles.activeIndicator,
                          { 
                            width: indicatorWidth,
                            opacity: animatedValue,
                            bottom: tabBarConfig.compactMode ? scale(1) : scale(-1),
                            height: tabBarConfig.indicatorHeight,
                            borderRadius: tabBarConfig.indicatorHeight / 2,
                          }
                        ]} 
                      />
                    </View>
                  </TouchableOpacity>
                </View>
              );
            })}
        </View>
      </View>
    );
  }, [state, descriptors, navigation, animatedValues, screenData, tabBarConfig]);
}

export default function TabLayout() {
  const screenData = useResponsiveDimensions();
  const pathname = usePathname();

  // Get user profile data from Redux
  const userProfileDetails = useSelector(
    (state: any) => state?.userProfileReducer?.data,
  );

  const username = userProfileDetails?.name || 'Welcome';

  // Determine if we should show the username based on pathname
  const shouldShowUsername = pathname === '/' ||
                            pathname === '/index';

  // Determine title based on pathname
  const getScreenTitle = () => {
    if (shouldShowUsername) {
      return username;
    }

    switch(pathname) {
      case '/':
      case '/index':
        return '';
      case '/patients':
        return 'Patients';
      case '/schedule':
        return 'Schedule';
      case '/profile':
        return 'Profile';
      case '/tasks':
        return 'Tasks';
      default:
        return '';
    }
  };

  return (
    <View style={{
      flex: 1,
      backgroundColor: '#FFFFFF',
      position: 'relative',
    }}>
      {/* Enhanced NavigationHeader - Hide for Home screen */}
      {pathname !== '/' && pathname !== '/index' && (
        <NavigationHeader
          title={getScreenTitle()}
          showLogo={true}
          showLogout={true}
          showRightLogo={false}
        />
      )}

      <Tabs
        screenOptions={{
          headerShown: false,
          tabBarStyle: {
            display: 'none',
          },
        }}
        tabBar={props => <CustomTabBar {...props} />}
      >
        <Tabs.Screen
          name="index"
          options={{
            title: 'Home',
            headerStyle: {
              backgroundColor: CoreColors.TurquoiseBlue,
            },
          }}
        />
        <Tabs.Screen
          name="patients"
          options={{
            title: 'Patients',
            headerStyle: {
              backgroundColor: CoreColors.TurquoiseBlue,
            },
          }}
        />
        <Tabs.Screen
          name="schedule"
          options={{
            title: 'Schedule',
            headerStyle: {
              backgroundColor: CoreColors.TurquoiseBlue,
            },
          }}
        />
        <Tabs.Screen
          name="profile"
          options={{
            title: 'Profile',
            headerStyle: {
              backgroundColor: CoreColors.TurquoiseBlue,
            },
          }}
        />
      </Tabs>
    </View>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
  },

  tabsContainer: {
    backgroundColor: '#FFFFFF',
    borderTopWidth: 0,
    overflow: 'hidden',
    ...createShadow(
      8, 
      0.12, 
      16, 
      '#000000', 
      { width: 0, height: -2 }, 
      true
    ),
    // Enhanced shadow for modern look - responsive values applied via config
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: -2 },
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // All dimensional values now handled by responsive config
  },
  tabItem: {
    flex: 1,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabButton: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    // borderRadius dynamically set based on responsive config
  },
  tabContent: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    width: '100%',
    // All padding, dimensions handled by responsive config
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    // marginBottom dynamically calculated
  },
  tabLabel: {
    fontWeight: '600',
    textAlign: 'center',
    letterSpacing: 0.1,
    // All dimensional properties handled by responsive config
    // fontSize, lineHeight, maxWidth, marginTop now dynamic
  },
  activeIndicator: {
    position: 'absolute',
    backgroundColor: CoreColors.TurquoiseBlue,
    // All dimensional properties (bottom, height, borderRadius, width) now dynamic
  },
});
