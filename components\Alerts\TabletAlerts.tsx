import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import TabletAwareContainer from '@/components/Layout/TabletAwareContainer';
import AdaptiveLayout from '@/components/Layout/AdaptiveLayout';
import ResponsiveGrid from '@/components/Layout/ResponsiveGrid';
import AccessibleText from '@/components/Accessibility/AccessibleText';
import AccessibleButton from '@/components/Accessibility/AccessibleButton';
import { isTablet, isLargeTablet, spacing } from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';

interface Alert {
  id: string;
  title: string;
  message: string;
  patientName: string;
  patientId: string;
  type: 'critical' | 'warning' | 'info' | 'medication' | 'vitals';
  priority: 'high' | 'medium' | 'low';
  timestamp: string;
  status: 'unread' | 'read' | 'acknowledged' | 'resolved';
  category: string;
}

interface TabletAlertsProps {
  alerts?: Alert[];
  onAlertPress?: (alert: Alert) => void;
  onAlertAcknowledge?: (alertId: string) => void;
  onAlertResolve?: (alertId: string) => void;
  onRefresh?: () => void;
}

/**
 * Tablet-optimized Alerts screen component
 */
const TabletAlerts: React.FC<TabletAlertsProps> = ({
  alerts = [],
  onAlertPress,
  onAlertAcknowledge,
  onAlertResolve,
  onRefresh,
}) => {
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'critical' | 'unread' | 'acknowledged'>('all');
  const [selectedAlert, setSelectedAlert] = useState<Alert | null>(null);

  // Sample alerts data
  const sampleAlerts: Alert[] = [
    {
      id: '1',
      title: 'Critical Blood Pressure Reading',
      message: 'Patient blood pressure reading of 180/120 mmHg requires immediate attention',
      patientName: 'John Smith',
      patientId: 'P001',
      type: 'critical',
      priority: 'high',
      timestamp: '2024-01-15T10:30:00Z',
      status: 'unread',
      category: 'Vitals',
    },
    {
      id: '2',
      title: 'Medication Reminder',
      message: 'Patient has not taken prescribed medication for 2 days',
      patientName: 'Sarah Johnson',
      patientId: 'P002',
      type: 'medication',
      priority: 'medium',
      timestamp: '2024-01-15T09:15:00Z',
      status: 'read',
      category: 'Medication',
    },
    {
      id: '3',
      title: 'Abnormal Heart Rate',
      message: 'Heart rate consistently above 100 BPM for the past hour',
      patientName: 'Mike Davis',
      patientId: 'P003',
      type: 'vitals',
      priority: 'high',
      timestamp: '2024-01-15T08:45:00Z',
      status: 'acknowledged',
      category: 'Vitals',
    },
    {
      id: '4',
      title: 'Missed Appointment',
      message: 'Patient did not attend scheduled video consultation',
      patientName: 'Emily Wilson',
      patientId: 'P004',
      type: 'info',
      priority: 'low',
      timestamp: '2024-01-15T08:00:00Z',
      status: 'resolved',
      category: 'Appointments',
    },
  ];

  const alertsToShow = alerts.length > 0 ? alerts : sampleAlerts;

  const filteredAlerts = alertsToShow.filter(alert => {
    switch (selectedFilter) {
      case 'critical':
        return alert.type === 'critical';
      case 'unread':
        return alert.status === 'unread';
      case 'acknowledged':
        return alert.status === 'acknowledged';
      case 'all':
      default:
        return true;
    }
  });

  const getAlertTypeColor = (type: Alert['type']) => {
    switch (type) {
      case 'critical':
        return CoreColors.RedOrange;
      case 'warning':
        return CoreColors.YellowOrange;
      case 'medication':
        return CoreColors.TurquoiseBlue;
      case 'vitals':
        return CoreColors.TealBlue;
      case 'info':
      default:
        return CoreColors.SlateGray;
    }
  };

  const getStatusColor = (status: Alert['status']) => {
    switch (status) {
      case 'unread':
        return CoreColors.RedOrange;
      case 'read':
        return CoreColors.YellowOrange;
      case 'acknowledged':
        return CoreColors.TurquoiseBlue;
      case 'resolved':
        return CoreColors.TealBlue;
      default:
        return CoreColors.SlateGray;
    }
  };

  const getTypeIcon = (type: Alert['type']) => {
    switch (type) {
      case 'critical':
        return '🚨';
      case 'warning':
        return '⚠️';
      case 'medication':
        return '💊';
      case 'vitals':
        return '❤️';
      case 'info':
      default:
        return 'ℹ️';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const renderAlertCard = ({ item }: { item: Alert }) => (
    <TouchableOpacity
      style={[
        styles.alertCard,
        selectedAlert?.id === item.id && styles.selectedAlertCard,
        item.status === 'unread' && styles.unreadAlertCard,
      ]}
      onPress={() => {
        setSelectedAlert(item);
        onAlertPress?.(item);
      }}
      accessible={true}
      accessibilityLabel={`Alert: ${item.title} for ${item.patientName}`}
      accessibilityRole="button"
    >
      <View style={styles.alertHeader}>
        <View style={styles.alertType}>
          <AccessibleText variant="body" style={styles.typeIcon}>
            {getTypeIcon(item.type)}
          </AccessibleText>
          <View style={[styles.typeIndicator, { backgroundColor: getAlertTypeColor(item.type) }]} />
        </View>
        <AccessibleText variant="caption" style={styles.timestamp}>
          {formatTimestamp(item.timestamp)}
        </AccessibleText>
      </View>

      <View style={styles.alertContent}>
        <AccessibleText variant="h3" style={styles.alertTitle}>
          {item.title}
        </AccessibleText>
        <AccessibleText variant="bodySmall" style={styles.alertMessage}>
          {item.message}
        </AccessibleText>
        <AccessibleText variant="body" style={styles.patientName}>
          Patient: {item.patientName}
        </AccessibleText>
      </View>

      <View style={styles.alertFooter}>
        <AccessibleText variant="caption" style={styles.category}>
          {item.category}
        </AccessibleText>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <AccessibleText variant="caption" style={styles.statusText}>
            {item.status.toUpperCase()}
          </AccessibleText>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderAlertFilters = () => (
    <View style={styles.filtersContainer}>
      <AccessibleText variant="h3" style={styles.filtersTitle}>
        Filter Alerts
      </AccessibleText>
      <View style={styles.filterButtons}>
        {(['all', 'critical', 'unread', 'acknowledged'] as const).map((filter) => (
          <TouchableOpacity
            key={filter}
            style={[
              styles.filterButton,
              selectedFilter === filter && styles.activeFilterButton,
            ]}
            onPress={() => setSelectedFilter(filter)}
            accessible={true}
            accessibilityLabel={`Filter by ${filter}`}
            accessibilityRole="button"
          >
            <AccessibleText
              variant="bodySmall"
              style={[
                styles.filterButtonText,
                selectedFilter === filter && styles.activeFilterButtonText,
              ]}
            >
              {filter.toUpperCase()}
            </AccessibleText>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderAlertsList = () => (
    <View style={styles.alertsListContainer}>
      <View style={styles.alertsHeader}>
        <AccessibleText variant="h2" style={styles.alertsTitle}>
          Alerts ({filteredAlerts.length})
        </AccessibleText>
        <AccessibleButton
          title="Refresh"
          onPress={onRefresh}
          size="small"
          variant="outline"
        />
      </View>

      <FlatList
        data={filteredAlerts}
        renderItem={renderAlertCard}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.alertsList}
      />
    </View>
  );

  const renderAlertDetail = () => {
    if (!selectedAlert) {
      return (
        <View style={styles.alertDetailEmpty}>
          <AccessibleText variant="body" style={styles.emptyText}>
            Select an alert to view details
          </AccessibleText>
        </View>
      );
    }

    return (
      <ScrollView style={styles.alertDetail} showsVerticalScrollIndicator={false}>
        <AccessibleText variant="h2" style={styles.detailTitle}>
          Alert Details
        </AccessibleText>

        <View style={styles.detailSection}>
          <View style={styles.detailHeader}>
            <AccessibleText variant="body" style={styles.detailTypeIcon}>
              {getTypeIcon(selectedAlert.type)}
            </AccessibleText>
            <View style={[styles.detailTypeIndicator, { backgroundColor: getAlertTypeColor(selectedAlert.type) }]} />
          </View>
          <AccessibleText variant="h3" style={styles.detailAlertTitle}>
            {selectedAlert.title}
          </AccessibleText>
          <AccessibleText variant="body" style={styles.detailMessage}>
            {selectedAlert.message}
          </AccessibleText>
        </View>

        <View style={styles.detailSection}>
          <AccessibleText variant="h3" style={styles.detailSectionTitle}>
            Patient Information
          </AccessibleText>
          <AccessibleText variant="body" style={styles.detailPatientName}>
            {selectedAlert.patientName}
          </AccessibleText>
          <AccessibleText variant="bodySmall" style={styles.detailPatientId}>
            ID: {selectedAlert.patientId}
          </AccessibleText>
        </View>

        <View style={styles.detailSection}>
          <AccessibleText variant="h3" style={styles.detailSectionTitle}>
            Alert Information
          </AccessibleText>
          <View style={styles.detailRow}>
            <AccessibleText variant="bodySmall" style={styles.detailLabel}>
              Priority:
            </AccessibleText>
            <AccessibleText variant="body" style={styles.detailValue}>
              {selectedAlert.priority.toUpperCase()}
            </AccessibleText>
          </View>
          <View style={styles.detailRow}>
            <AccessibleText variant="bodySmall" style={styles.detailLabel}>
              Category:
            </AccessibleText>
            <AccessibleText variant="body" style={styles.detailValue}>
              {selectedAlert.category}
            </AccessibleText>
          </View>
          <View style={styles.detailRow}>
            <AccessibleText variant="bodySmall" style={styles.detailLabel}>
              Status:
            </AccessibleText>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(selectedAlert.status) }]}>
              <AccessibleText variant="caption" style={styles.statusText}>
                {selectedAlert.status.toUpperCase()}
              </AccessibleText>
            </View>
          </View>
          <View style={styles.detailRow}>
            <AccessibleText variant="bodySmall" style={styles.detailLabel}>
              Time:
            </AccessibleText>
            <AccessibleText variant="body" style={styles.detailValue}>
              {new Date(selectedAlert.timestamp).toLocaleString()}
            </AccessibleText>
          </View>
        </View>

        <View style={styles.detailActions}>
          {selectedAlert.status === 'unread' && (
            <AccessibleButton
              title="Acknowledge"
              onPress={() => onAlertAcknowledge?.(selectedAlert.id)}
              variant="primary"
              style={styles.detailActionButton}
            />
          )}
          {selectedAlert.status !== 'resolved' && (
            <AccessibleButton
              title="Mark Resolved"
              onPress={() => onAlertResolve?.(selectedAlert.id)}
              variant="outline"
              style={styles.detailActionButton}
            />
          )}
          <AccessibleButton
            title="View Patient"
            onPress={() => {}}
            variant="text"
            style={styles.detailActionButton}
          />
        </View>
      </ScrollView>
    );
  };

  return (
    <TabletAwareContainer style={styles.container}>
      <AdaptiveLayout
        mobileLayout={() => (
          <ScrollView style={styles.mobileLayout}>
            {renderAlertFilters()}
            {renderAlertsList()}
          </ScrollView>
        )}
        tabletLayout={() => (
          <View style={styles.tabletLayout}>
            <View style={styles.leftPanel}>
              {renderAlertFilters()}
              {renderAlertsList()}
            </View>
            <View style={styles.rightPanel}>
              {renderAlertDetail()}
            </View>
          </View>
        )}
      />
    </TabletAwareContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  mobileLayout: {
    flex: 1,
  },
  tabletLayout: {
    flex: 1,
    flexDirection: 'row',
    padding: spacing.md,
  },
  leftPanel: {
    flex: 1,
    marginRight: spacing.md,
  },
  rightPanel: {
    width: isLargeTablet() ? 400 : 320,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: spacing.lg,
  },
  filtersContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: isTablet() ? 12 : 8,
    padding: spacing.lg,
    marginBottom: spacing.md,
  },
  filtersTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.md,
  },
  filterButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  filterButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  activeFilterButton: {
    backgroundColor: CoreColors.TurquoiseBlue,
    borderColor: CoreColors.TurquoiseBlue,
  },
  filterButtonText: {
    color: CoreColors.SlateGray,
  },
  activeFilterButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  alertsListContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: isTablet() ? 12 : 8,
    padding: spacing.lg,
    flex: 1,
  },
  alertsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  alertsTitle: {
    color: CoreColors.DarkJungleGreen,
  },
  alertsList: {
    paddingBottom: spacing.lg,
  },
  alertCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  selectedAlertCard: {
    backgroundColor: '#EBF8FF',
    borderColor: CoreColors.TurquoiseBlue,
  },
  unreadAlertCard: {
    borderLeftWidth: 4,
    borderLeftColor: CoreColors.RedOrange,
  },
  alertHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  alertType: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeIcon: {
    fontSize: 20,
    marginRight: spacing.xs,
  },
  typeIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  timestamp: {
    color: CoreColors.SlateGray,
  },
  alertContent: {
    marginBottom: spacing.md,
  },
  alertTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.xs,
  },
  alertMessage: {
    color: CoreColors.SlateGray,
    marginBottom: spacing.sm,
  },
  patientName: {
    color: CoreColors.DarkJungleGreen,
    fontWeight: '500',
  },
  alertFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  category: {
    color: CoreColors.SlateGray,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  alertDetail: {
    flex: 1,
  },
  alertDetailEmpty: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    color: CoreColors.SlateGray,
    textAlign: 'center',
  },
  detailTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.lg,
  },
  detailSection: {
    marginBottom: spacing.lg,
  },
  detailHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  detailTypeIcon: {
    fontSize: 24,
    marginRight: spacing.sm,
  },
  detailTypeIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  detailAlertTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.sm,
  },
  detailMessage: {
    color: CoreColors.SlateGray,
  },
  detailSectionTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.sm,
  },
  detailPatientName: {
    color: CoreColors.DarkJungleGreen,
    fontWeight: '500',
    marginBottom: spacing.xs,
  },
  detailPatientId: {
    color: CoreColors.SlateGray,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  detailLabel: {
    color: CoreColors.SlateGray,
    flex: 1,
  },
  detailValue: {
    color: CoreColors.DarkJungleGreen,
    flex: 1,
    textAlign: 'right',
  },
  detailActions: {
    marginTop: 'auto',
    paddingTop: spacing.lg,
  },
  detailActionButton: {
    marginBottom: spacing.sm,
  },
});

export default TabletAlerts;