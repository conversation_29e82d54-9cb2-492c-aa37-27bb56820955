import { getAlertsCountByDate, AlertsCountRequest, AlertsCountResponse } from '../apis/apiManager';

/**
 * Redux action types for alerts count
 */
export const FETCH_ALERTS_COUNT_REQUEST = 'FETCH_ALERTS_COUNT_REQUEST';
export const FETCH_ALERTS_COUNT_SUCCESS = 'FETCH_ALERTS_COUNT_SUCCESS';
export const FETCH_ALERTS_COUNT_FAILURE = 'FETCH_ALERTS_COUNT_FAILURE';

export interface FetchAlertsCountRequestAction {
  type: typeof FETCH_ALERTS_COUNT_REQUEST;
}

export interface FetchAlertsCountSuccessAction {
  type: typeof FETCH_ALERTS_COUNT_SUCCESS;
  payload: AlertsCountResponse;
}

export interface FetchAlertsCountFailureAction {
  type: typeof FETCH_ALERTS_COUNT_FAILURE;
  payload: string;
}

export type AlertsCountActionTypes = 
  | FetchAlertsCountRequestAction
  | FetchAlertsCountSuccessAction
  | FetchAlertsCountFailureAction;

/**
 * Redux action creators
 */
export const fetchAlertsCountRequest = (): FetchAlertsCountRequestAction => ({
  type: FETCH_ALERTS_COUNT_REQUEST,
});

export const fetchAlertsCountSuccess = (data: AlertsCountResponse): FetchAlertsCountSuccessAction => ({
  type: FETCH_ALERTS_COUNT_SUCCESS,
  payload: data,
});

export const fetchAlertsCountFailure = (error: string): FetchAlertsCountFailureAction => ({
  type: FETCH_ALERTS_COUNT_FAILURE,
  payload: error,
});

/**
 * Async action to fetch alerts count for dynamic date range
 * Uses the centralized API from apiManager.ts
 */
export const fetchAlertsCount = (params: AlertsCountRequest) => {
  return async (dispatch: any) => {
    try {
      dispatch(fetchAlertsCountRequest());

      // Use the centralized API from apiManager.ts
      const response = await getAlertsCountByDate(params);

      if (response.error) {
        dispatch(fetchAlertsCountFailure(response.error.message));
        return;
      }

      if (!response.data) {
        dispatch(fetchAlertsCountFailure('No data received from alerts count API'));
        return;
      }

      dispatch(fetchAlertsCountSuccess(response.data));
    } catch (error: any) {
      dispatch(fetchAlertsCountFailure(error.message || 'Unknown error occurred'));
    }
  };
};

// Action to get date range for alerts count (dynamically calculated last 30 days)
export const getAlertsCountDateRange = () => {
  // Always calculate from current date/time to ensure dynamic range
  const now = new Date();
  const endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate()); // Today at midnight
  const startDate = new Date(endDate);
  startDate.setDate(startDate.getDate() - 30); // Exactly 30 days ago

  const startDateString = startDate.toISOString().split('T')[0];
  const endDateString = endDate.toISOString().split('T')[0];

  return {
    startDate: startDateString, // YYYY-MM-DD format
    endDate: endDateString // YYYY-MM-DD format
  };
}; 