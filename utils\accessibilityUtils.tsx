import React, { useEffect } from 'react';
import { AccessibilityInfo, Platform, TouchableOpacity, View, ViewStyle } from 'react-native';
import { isTablet, isLargeTablet } from './responsive';

/**
 * Get minimum touch target size based on device type
 */
export const getMinimumTouchTargetSize = () => {
  // Following WCAG 2.1 Success Criterion 2.5.5 (Target Size)
  // Minimum touch target should be 44x44 pixels
  // For tablets, we use larger sizes for better usability
  if (isLargeTablet()) {
    return 56; // 56x56 for large tablets
  } else if (isTablet()) {
    return 48; // 48x48 for tablets
  } else {
    return 44; // 44x44 minimum for phones (WCAG standard)
  }
};

/**
 * Ensure a component meets minimum touch target size requirements
 */
export const withMinimumTouchTarget = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options?: { extraHorizontal?: number; extraVertical?: number }
) => {
  const MinimumTouchTarget: React.FC<P> = (props) => {
    const minSize = getMinimumTouchTargetSize();
    const extraHorizontal = options?.extraHorizontal || 0;
    const extraVertical = options?.extraVertical || 0;
    
    const containerStyle: ViewStyle = {
      minWidth: minSize + extraHorizontal,
      minHeight: minSize + extraVertical,
      justifyContent: 'center',
      alignItems: 'center',
    };
    
    return (
      <View style={containerStyle}>
        <WrappedComponent {...props} />
      </View>
    );
  };
  
  MinimumTouchTarget.displayName = `MinimumTouchTarget(${WrappedComponent.displayName || WrappedComponent.name || 'Component'})`;
  return MinimumTouchTarget;
};

/**
 * Enhanced TouchableOpacity with accessibility features
 */
export const AccessibleTouchable: React.FC<React.ComponentProps<typeof TouchableOpacity> & {
  accessibilityLabel: string;
  accessibilityHint?: string;
}> = ({
  children,
  accessibilityLabel,
  accessibilityHint,
  style,
  ...props
}) => {
  const minSize = getMinimumTouchTargetSize();
  
  return (
    <TouchableOpacity
      accessible={true}
      accessibilityLabel={accessibilityLabel}
      accessibilityHint={accessibilityHint}
      accessibilityRole="button"
      style={[
        { minWidth: minSize, minHeight: minSize },
        style,
      ]}
      {...props}
    >
      {children}
    </TouchableOpacity>
  );
};

/**
 * Hook to detect screen reader status
 */
export const useScreenReader = () => {
  const [isScreenReaderEnabled, setScreenReaderEnabled] = React.useState(false);
  
  useEffect(() => {
    let isMounted = true;
    
    const checkScreenReader = async () => {
      try {
        const enabled = await AccessibilityInfo.isScreenReaderEnabled();
        if (isMounted) {
          setScreenReaderEnabled(enabled);
        }
      } catch (error) {
        console.error('Error checking screen reader status:', error);
      }
    };
    
    checkScreenReader();
    
    const subscription = AccessibilityInfo.addEventListener(
      'screenReaderChanged',
      (enabled) => {
        if (isMounted) {
          setScreenReaderEnabled(enabled);
        }
      }
    );
    
    return () => {
      isMounted = false;
      subscription.remove();
    };
  }, []);
  
  return isScreenReaderEnabled;
};

/**
 * Hook to detect reduced motion preference
 */
export const useReducedMotion = () => {
  const [isReducedMotionEnabled, setReducedMotionEnabled] = React.useState(false);
  
  useEffect(() => {
    let isMounted = true;
    
    const checkReducedMotion = async () => {
      try {
        const enabled = await AccessibilityInfo.isReduceMotionEnabled();
        if (isMounted) {
          setReducedMotionEnabled(enabled);
        }
      } catch (error) {
        console.error('Error checking reduced motion status:', error);
      }
    };
    
    checkReducedMotion();
    
    const subscription = AccessibilityInfo.addEventListener(
      'reduceMotionChanged',
      (enabled) => {
        if (isMounted) {
          setReducedMotionEnabled(enabled);
        }
      }
    );
    
    return () => {
      isMounted = false;
      subscription.remove();
    };
  }, []);
  
  return isReducedMotionEnabled;
};

/**
 * Get font scaling factor for accessibility
 */
export const getAccessibilityFontScale = () => {
  // This would typically come from the system's font scale setting
  // For now, we'll use a placeholder implementation
  return 1.0;
};

/**
 * Announce a message to screen readers
 */
export const announceForAccessibility = (message: string) => {
  if (Platform.OS === 'ios' || Platform.OS === 'android') {
    AccessibilityInfo.announceForAccessibility(message);
  }
};

/**
 * Check if a component is accessible to screen readers
 */
export const isComponentAccessible = async () => {
  return await AccessibilityInfo.isScreenReaderEnabled();
};

/**
 * Get tablet-specific accessibility configurations
 */
export const getTabletAccessibilityConfig = () => {
  return {
    minimumTouchSize: getMinimumTouchTargetSize(),
    focusRingColor: '#2196F3',
    focusRingWidth: isTablet() ? 3 : 2,
    labelFontSize: isLargeTablet() ? 18 : isTablet() ? 16 : 14,
    buttonSpacing: isTablet() ? 16 : 12,
    formFieldSpacing: isTablet() ? 24 : 16,
  };
};

export default {
  getMinimumTouchTargetSize,
  withMinimumTouchTarget,
  AccessibleTouchable,
  useScreenReader,
  useReducedMotion,
  getAccessibilityFontScale,
  announceForAccessibility,
  isComponentAccessible,
  getTabletAccessibilityConfig,
};