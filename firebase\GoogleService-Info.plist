<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>132597499285-hsoe0ilmm4s6p1cvkktvhjle9a9mpca1.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.132597499285-hsoe0ilmm4s6p1cvkktvhjle9a9mpca1</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>132597499285-56qutt68hnhafk05jmkodeefm70togb9.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyB2LvSZUp_CDSLfY_tyRCbuy_6UvzKvGio</string>
	<key>GCM_SENDER_ID</key>
	<string>132597499285</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.watchrxhealth.caregiver</string>
	<key>PROJECT_ID</key>
	<string>watchrx-1007</string>
	<key>STORAGE_BUCKET</key>
	<string>watchrx-1007.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:132597499285:ios:32235e6021bb6bfa19e29c</string>
	<key>DATABASE_URL</key>
	<string>https://watchrx-1007.firebaseio.com</string>
</dict>
</plist>