/**
 * Password validation regex requirements:
 * - At least one lowercase letter
 * - At least one uppercase letter
 * - At least one digit
 * - At least one special character
 * - Minimum length of 8 characters, maximum of 99
 * - No whitespace characters allowed
 */
const STRONG_PASSWORD_REGEX = 
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[=+\-^$*.\[\]{}()?"!@#%&/\\,><':;|_~`])\S{8,99}$/;

/**
 * Interface for detailed password validation results
 */
export interface PasswordValidationResult {
  isValid: boolean;
  hasLowercase: boolean;
  hasUppercase: boolean;
  hasDigit: boolean;
  hasSpecialChar: boolean;
  hasValidLength: boolean;
  hasNoWhitespace: boolean;
}

/**
 * Validates a password string against strong password requirements
 * 
 * @param password The password to validate
 * @param detailed Whether to return detailed validation results
 * @returns Boolean if detailed is false, otherwise returns PasswordValidationResult object
 */
function validatePassword(
  password: string | null | undefined,
  detailed: true
): PasswordValidationResult;
function validatePassword(
  password: string | null | undefined,
  detailed?: false
): boolean;
function validatePassword(
  password: string | null | undefined,
  detailed: boolean = false
): boolean | PasswordValidationResult {
  // Handle null, undefined, or empty passwords
  if (!password) {
    return detailed 
      ? { 
          isValid: false,
          hasLowercase: false,
          hasUppercase: false,
          hasDigit: false,
          hasSpecialChar: false,
          hasValidLength: false,
          hasNoWhitespace: false
        }
      : false;
  }

  // If not detailed, simply return regex test result
  if (!detailed) {
    return STRONG_PASSWORD_REGEX.test(password);
  }

  // For detailed results, check each requirement individually
  const hasLowercase = /[a-z]/.test(password);
  const hasUppercase = /[A-Z]/.test(password);
  const hasDigit = /[0-9]/.test(password);
  const hasSpecialChar = /[=+\-^$*.\[\]{}()?"!@#%&/\\,><':;|_~`]/.test(password);
  const hasValidLength = password.length >= 8 && password.length <= 99;
  const hasNoWhitespace = !/\s/.test(password);
  const isValid = hasLowercase && hasUppercase && hasDigit && 
                 hasSpecialChar && hasValidLength && hasNoWhitespace;

  return {
    isValid,
    hasLowercase,
    hasUppercase,
    hasDigit,
    hasSpecialChar,
    hasValidLength,
    hasNoWhitespace
  };
}

export default validatePassword;
