import {useNavigation} from '@react-navigation/native';
import React from 'react';
import {Image, Pressable, StyleSheet, ViewStyle} from 'react-native';
import {useDispatch} from 'react-redux';
import {Routes} from '@/constants';
import {useTheme} from '@/constants/Theme';
import {userLogOut} from '@/services/actions/logOutActions';
import {resetCredentials} from '@/services/secure-storage';
import Theme from '@/constants/Theme';

interface IconNotificationProps {
  style?: ViewStyle;
}

const IconNotification = (props: IconNotificationProps) => {
  const {theme} = useTheme();
  const navigation = useNavigation();
  const dispatch = useDispatch();

  return (
    <Pressable
      style={[
        styles.container,
        props.style,
        {backgroundColor: theme.backgroundItem},
      ]}
      onPress={() => {
        dispatch(userLogOut());
        resetCredentials();
        navigation.reset({
          index: 0,
          routes: [{name: Routes.Login}],
        });
      }}>
      <Image source={require('images/logout.png')} style={Theme.icons} />
    </Pressable>
  );
};

export default IconNotification;

const styles = StyleSheet.create({
  container: {
    width: 40,
    height: 40,
    borderRadius: 12,
    ...Theme.center,
    minWidth: 16,
    ...Theme.shadow,
  },
});
