import React, { memo, useCallback } from 'react';
import { FlatList, StyleSheet, TouchableOpacity, View, Image } from 'react-native';
import Text from '@/components/Text';
import { Colors } from '@/constants';
import { categoryList } from '@/types/category';
import scale from '@/utils/scale';

interface ModalChangeCategoryProps {
  onChangeCategory: (item: categoryList) => void;
  data: any[];
}

const ModalChangeCategory = memo((props: ModalChangeCategoryProps) => {
  const renderItem = useCallback(
    ({ item, index }: { item: any; index: number }) => {
      const onPress = () => {
        props.onChangeCategory && props.onChangeCategory(item);
      };
      
      return (
        <TouchableOpacity
          style={styles.item}
          onPress={onPress}
          key={index + item.id}
          activeOpacity={0.7}
        >
          <View style={styles.itemContent}>
            <View style={styles.roleInfo}>
              <Text style={styles.roleName}>
                {item.displayName || item.name}
              </Text>
              {item.description && (
                <Text style={styles.roleDescription}>
                  {item.description}
                </Text>
              )}
            </View>
            
            <View style={styles.checkIconContainer}>
              <Image
                source={require('@/assets/images/ic_accept.png')}
                style={styles.checkIcon}
                resizeMode="contain"
              />
            </View>
          </View>
        </TouchableOpacity>
      );
    },
    [props.onChangeCategory],
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <Text style={styles.headerTitle}>Select Professional Role</Text>
      <Text style={styles.headerSubtitle}>
        Choose your professional role to customize your experience
      </Text>
    </View>
  );

  const renderSeparator = () => <View style={styles.separator} />;

  return (
    <View style={styles.container}>
      <FlatList
        data={props.data}
        renderItem={renderItem}
        ListHeaderComponent={renderHeader}
        ItemSeparatorComponent={renderSeparator}
        contentContainerStyle={styles.contentContainerStyle}
        showsVerticalScrollIndicator={false}
        keyExtractor={item => item.id.toString()}
        scrollEnabled={true}
        initialNumToRender={10}
        maxToRenderPerBatch={10}
        windowSize={5}
      />
    </View>
  );
});

export default ModalChangeCategory;

const styles = StyleSheet.create({
  container: {
    maxHeight: scale(500, true),
    minHeight: scale(300, true),
    backgroundColor: Colors.White,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: 24,
    paddingBottom: 16,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: Colors.Isabelline,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.DarkJungleGreen,
    marginBottom: 8,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 14,
    color: Colors.GrayBlue,
    textAlign: 'center',
    lineHeight: 20,
  },
  contentContainerStyle: {
    paddingBottom: 24,
  },
  item: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: Colors.White,
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  roleInfo: {
    flex: 1,
    marginRight: 16,
  },
  roleName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.DarkJungleGreen,
    marginBottom: 4,
    lineHeight: 20,
  },
  roleDescription: {
    fontSize: 14,
    color: Colors.GrayBlue,
    lineHeight: 18,
  },
  checkIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.TiffanyBlueOpacity,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkIcon: {
    width: 12,
    height: 12,
    tintColor: Colors.TiffanyBlue,
  },
  separator: {
    height: 1,
    backgroundColor: Colors.Isabelline,
    marginHorizontal: 24,
  },
});
