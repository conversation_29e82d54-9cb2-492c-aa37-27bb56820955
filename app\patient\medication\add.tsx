import React from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams } from 'expo-router';
import { Stack } from 'expo-router';

import AddMedicationScreen from '@/screens/PatientDetails/Medications/addMedication';

export default function AddMedicationRoute() {
  const params = useLocalSearchParams();

  // Extract patientId from params
  let patientId = params.patientId as string;

  // Log all params for debugging
  console.log('Add Medication Route - All Params:', params);
  console.log('Add Medication Route - Patient ID:', patientId);

  return (
    <>
      <Stack.Screen options={{ title: 'Add Medication' }} />
      <SafeAreaView style={styles.container} edges={['left', 'right']}>
        <AddMedicationScreen patientId={patientId as string} />
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});