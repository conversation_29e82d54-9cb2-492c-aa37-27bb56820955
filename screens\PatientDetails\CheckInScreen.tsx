import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ScrollView, TouchableOpacity, StatusBar, Alert } from 'react-native';
import { useRoute } from '@react-navigation/native';
import Container from '@/components/Layout/Container';
import Text from '@/components/Text';
import { Colors } from '@/constants';
import { Feather, MaterialIcons, FontAwesome5, AntDesign, Ionicons } from '@expo/vector-icons';
import NavigationHeader from '@/components/NavigationHeader';
import AlertsDatePicker from '@/components/DatePickerButtons/alerts-date-picker';
import Moment from 'moment';
import { useSelector } from 'react-redux';
import { apiPostWithToken } from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import Loader from '@/components/Loader/Loader';

interface CheckInScreenProps {
  patientId?: string;
  patientName?: string;
  navigation?: any;
}

interface RouteParams {
  patientId?: string;
  patientName?: string;
}

interface CheckIn {
  checkInId: string;
  checkInType: string;
  checkInDate: string;
  careManagerName: string;
  notes?: string;
}

/**
 * Patient Check In Screen
 * Allows care managers to log check-ins with patients
 */
const CheckInScreen: React.FC<CheckInScreenProps> = (props) => {
  const route = useRoute();
  const params = route.params as RouteParams;
  const patientId = props.patientId || params.patientId || '';
  const patientName = params.patientName || 'Patient';
  const [loading, setLoading] = useState(false);
  
  // Get organization ID and caregiver ID from Redux store
  const orgId = useSelector((state: any) => state?.currentOrgIdReducer?.orgId);
  const caregiverId = useSelector((state: any) => state?.loginReducer?.data?.userId);
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  // Placeholder check-in options
  const checkInOptions = [
    { id: 'call', title: 'Phone Call', icon: 'phone' },
    { id: 'video', title: 'Video Call', icon: 'video' },
    { id: 'inperson', title: 'In-Person Visit', icon: 'users' },
    { id: 'message', title: 'Message', icon: 'message-circle' },
  ];

  // State for date range selection - using the same approach as other screens
  const [fromDate, setFromDate] = useState<Date>();
  const [toDate, setToDate] = useState<Date>();
  const [checkIns, setCheckIns] = useState<CheckIn[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  
  // Initialize date range on component mount - from 1st May to current date
  useEffect(() => {
    const today = new Date();
    setToDate(today);
    
    // Set start date to May 1st of the current year
    const currentYear = today.getFullYear();
    const mayFirst = new Date(currentYear, 4, 1); // Month is 0-indexed, so 4 = May
    
    // If we're before May 1st this year, use May 1st of previous year
    if (today < mayFirst) {
      setFromDate(new Date(currentYear - 1, 4, 1));
    } else {
      setFromDate(mayFirst);
    }
  }, []);
  
  // Fetch check-in data when date range changes
  useEffect(() => {
    if (fromDate && toDate && patientId) {
      // fetchCheckIns();
    }
  }, [patientId, fromDate, toDate]);
  
  // Format date for display
  const formatCheckInDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date);
    } catch (error) {
      return dateString;
    }
  };
  
  // Fetch check-in data
  // const fetchCheckIns = async () => {
  //   if (!patientId) return;
  //   setLoading(true);
  //   try {
  //     const fDate = Moment(fromDate).format("YYYY-MM-DD");
  //     const tDate = Moment(toDate).format("YYYY-MM-DD");
  //     const response = await apiPostWithToken(
  //       {
  //         patientId: patientId,
  //         startDate: fDate,
  //         endDate: tDate,
  //         orgId: orgId,
  //         careGiverId: caregiverId
  //       },
  //       URLS.caregiverUrl + "getPatientCheckIns"
  //     );
  //     if (response?.status === 200) {
  //       setCheckIns(response?.data?.data || []);
  //     } else {
  //       const errorMessage = response?.response?.data?.responseMessage
  //         ? response?.response?.data?.responseMessage
  //         : response.message === "Network Error"
  //         ? "Network error. Please check your data connection."
  //         : response.message;
  //       Alert.alert("Error", errorMessage, [{ text: "Dismiss" }]);
  //     }
  //   } catch (error) {
  //     setLoading(false);
  //     Alert.alert("Error", "Failed to load check-ins. Please try again.");
  //   } finally {
  //     setLoading(false);
  //   }
  // };
  
  // Handle refresh action
  const handleRefresh = () => {
    setRefreshing(true);
    // fetchCheckIns();
  };

  return (
    <Container style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.TurquoiseBlue} />
      <NavigationHeader 
        title={`${patientName} Check In`}
        showBackButton={true}
        showLogo={false}
        showRightLogo={true}
      />
      <Loader modalVisible={loading} />
      
      {!loading && (
      <View style={styles.contentContainer}>

        <ScrollView style={styles.scrollView}>
        {/* Check-in options */}
        <View style={styles.section}>
          <Text size={16} color={Colors.DarkJungleGreen} bold marginBottom={8}>
            Select Check-In Type
          </Text>
          
          <View style={styles.optionsContainer}>
            {checkInOptions.map((option) => (
              <TouchableOpacity
                key={option.id}
                style={[
                  styles.optionCard,
                  selectedOption === option.id && styles.selectedOption,
                ]}
                onPress={() => setSelectedOption(option.id)}
                activeOpacity={0.7}
              >
                <Feather
                  name={option.icon as any}
                  size={24}
                  color={selectedOption === option.id ? Colors.White : Colors.TealBlue}
                />
                <Text
                  size={14}
                  color={selectedOption === option.id ? Colors.White : Colors.DarkJungleGreen}
                  marginTop={8}
                >
                  {option.title}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Notes section */}
        <View style={styles.section}>
          <Text size={16} color={Colors.DarkJungleGreen} bold marginBottom={8}>
            Check-In Notes
          </Text>
          <View style={styles.placeholderCard}>
            <Text size={14} color={Colors.GrayBlue} style={{fontStyle: 'italic'}}>
              This is a placeholder for the notes input field. The actual implementation will include a text input area for care managers to document their check-in notes.
            </Text>
          </View>
        </View>

        {/* Previous check-ins */}
        <View style={styles.section}>
          <Text size={16} color={Colors.DarkJungleGreen} bold marginBottom={8}>
            Previous Check-Ins
          </Text>
          <View style={styles.placeholderCard}>
            <Text size={14} color={Colors.DarkJungleGreen}>
              This section will display a history of previous check-ins, including:
            </Text>
            <View style={styles.bulletList}>
              <View style={styles.bulletItem}>
                <FontAwesome5 name="circle" size={8} color={Colors.TealBlue} />
                <Text size={14} color={Colors.DarkJungleGreen} marginLeft={8}>
                  Date and time of check-in
                </Text>
              </View>
              <View style={styles.bulletItem}>
                <FontAwesome5 name="circle" size={8} color={Colors.TealBlue} />
                <Text size={14} color={Colors.DarkJungleGreen} marginLeft={8}>
                  Type of check-in (call, video, in-person)
                </Text>
              </View>
              <View style={styles.bulletItem}>
                <FontAwesome5 name="circle" size={8} color={Colors.TealBlue} />
                <Text size={14} color={Colors.DarkJungleGreen} marginLeft={8}>
                  Care manager who performed the check-in
                </Text>
              </View>
              <View style={styles.bulletItem}>
                <FontAwesome5 name="circle" size={8} color={Colors.TealBlue} />
                <Text size={14} color={Colors.DarkJungleGreen} marginLeft={8}>
                  Summary of notes and outcomes
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Action button placeholder */}
        <TouchableOpacity style={styles.submitButton} activeOpacity={0.8}>
          <Text size={16} color={Colors.White} bold center>
            Submit Check-In
          </Text>
        </TouchableOpacity>
        </ScrollView>
      </View>
      )}
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
    marginTop: 10, // Matches the main CriticalAlerts screen spacing
  },
  scrollView: {
    flex: 1,
  },
  datePickersContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  datePickerWrapper: {
    width: '46%',
  },
  dateSeparator: {
    width: '8%',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.WhiteSmoke,
  },
  section: {
    marginBottom: 20,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  optionCard: {
    width: '48%',
    backgroundColor: Colors.White,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.8,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedOption: {
    backgroundColor: Colors.TealBlue,
  },
  placeholderCard: {
    backgroundColor: Colors.White,
    borderRadius: 12,
    padding: 16,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.8,
    shadowRadius: 4,
    elevation: 3,
  },
  bulletList: {
    marginTop: 12,
  },
  bulletItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  submitButton: {
    backgroundColor: Colors.TealBlue,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.8,
    shadowRadius: 4,
    elevation: 3,
  },
});

export default CheckInScreen;
