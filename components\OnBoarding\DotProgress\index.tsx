import React, {memo} from 'react';
import {StyleSheet, View} from 'react-native';
import Animated, {
  useAnimatedStyle,
  interpolate,
  Extrapolation,
  SharedValue,
} from 'react-native-reanimated';
import {Colors, Constants} from '@/constants';

interface DotProgressProps {
  numberOfDots: number;
  scrollX: SharedValue<number>;
}

const DotProgress = memo(({numberOfDots, scrollX}: DotProgressProps) => {
  const renderDots = () => {
    return Array.from({length: numberOfDots}, (_, index) => {
      const animatedStyle = useAnimatedStyle(() => {
        const inputRange = [
          (index - 1) * Constants.width,
          index * Constants.width,
          (index + 1) * Constants.width,
        ];

        const width = interpolate(
          scrollX.value,
          inputRange,
          [8, 24, 8],
          Extrapolation.CLAMP,
        );

        const opacity = interpolate(
          scrollX.value,
          inputRange,
          [0.4, 1, 0.4],
          Extrapolation.CLAMP,
        );

        return {
          width,
          opacity,
        };
      });

      return (
        <Animated.View
          key={index}
          style={[styles.dot, animatedStyle]}
        />
      );
    });
  };

  return <View style={styles.container}>{renderDots()}</View>;
});

export default DotProgress;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    gap: 8,
  },
  dot: {
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.White,
  },
});
