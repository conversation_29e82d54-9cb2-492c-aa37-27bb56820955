import React, {memo, useCallback} from 'react';
import {StyleSheet, TouchableOpacity, ViewProps, Animated, View} from 'react-native';
import Layout from '@/components/Layout/Layout';
import Text from '@/components/Text';
import {Colors} from '@/constants';
import Theme from '@/constants/Theme';
import {Ionicons} from '@expo/vector-icons';

interface Props {
  tabs?: string[];
  style?: ViewProps;
  activeTintColor?: string;
  inactiveTintColor?: string;
  activeBackgroundColor?: string;
  inactiveBackgroundColor?: string;
  tabStyle?: ViewProps;
  onChangeTab?: (index: number) => void;
  initialTab?: number;
}

export default memo(
  ({
    tabs,
    onChangeTab,
    tabStyle,
    activeBackgroundColor = Colors.TealBlue,
    initialTab = 0,
  }: Props) => {
    const [tabActive, setTabActive] = React.useState<number>(initialTab);

    const _onChangeTab = useCallback(
      (number: number) => {
        setTabActive(number);
        onChangeTab && onChangeTab(number);
      },
      [onChangeTab],
    );

    // Get icon for tab based on index
    const getTabIcon = (index: number) => {
      switch (index) {
        case 0: return "time-outline" as const; 
        case 1: return "calendar-outline" as const;
        default: return "list-outline" as const;
      }
    };

    // Get color for tab based on index
    const getTabColor = (index: number) => {
      switch (index) {
        case 0: return Colors.pastelRed;
        case 1: return activeBackgroundColor;
        default: return activeBackgroundColor;
      }
    };

    return (
      <Layout style={styles.container}>
        <View style={styles.tabsWrapper}>
          {tabs?.map((item, index) => {
            const isActive = tabActive === index;
            const tabColor = getTabColor(index);
            const tabIcon = getTabIcon(index);
            
            // Determine styles based on active state
            const textColor = isActive ? tabColor : Colors.GrayBlue;
            
            return (
              <TouchableOpacity
                onPress={() => _onChangeTab(index)}
                key={index}
                style={[styles.tabStyle, tabStyle]}
                activeOpacity={0.7}
              >
                <View
                  style={[
                    styles.tabContent,
                    isActive && { borderBottomWidth: 3, borderBottomColor: tabColor }
                  ]}
                >
                  <Ionicons 
                    name={tabIcon} 
                    size={20} 
                    color={textColor}
                    style={styles.tabIcon}
                  />
                  <Text
                    color={textColor}
                    size={15}
                    bold
                  >
                    {item}
                  </Text>
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
      </Layout>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    height: 56,
    ...Theme.flexDirection,
    justifyContent: 'space-between',
    borderRadius: 12,
    marginHorizontal: 0,
    marginVertical: 0,
    backgroundColor: Colors.White,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    overflow: 'hidden',
  },
  tabsWrapper: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  tabStyle: {
    flex: 1,
    ...Theme.center,
  },
  tabContent: {
    flex: 1,
    width: '100%',
    ...Theme.center,
    paddingVertical: 16,
    paddingHorizontal: 8,
    position: 'relative',
    flexDirection: 'row',
  },
  tabIcon: {
    marginRight: 6,
  },
});
