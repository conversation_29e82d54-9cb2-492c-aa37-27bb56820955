import React, { useState } from "react";
import {
  View,
  ScrollView,
  StyleSheet,
  Image,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import { TabletAwareContainer, AdaptiveLayout } from "@/components/Layout";
import { AccessibleText, AccessibleButton } from "@/components/Accessibility";
import { TabletTextInput } from "@/components/Forms";
import { isTablet, isLargeTablet, getOrientation, spacing, typography } from "@/utils/responsive";
import { CoreColors } from "@/constants/Colors";
import { ONBOARDING_IMAGES } from "@/assets/images/OnBoardingImages";

interface TabletLoginProps {
  onLogin?: (email: string, password: string) => void;
  onForgotPassword?: () => void;
  onSignUp?: () => void;
  loading?: boolean;
}

/**
 * Tablet-optimized Login screen component
 */
const TabletLogin: React.FC<TabletLoginProps> = ({
  onLogin,
  onForgotPassword,
  onSignUp,
  loading = false,
}) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const orientation = getOrientation();

  // Onboarding content for the image carousel
  const onboardingContent = [
    {
      id: 1,
      image: ONBOARDING_IMAGES.img1,
      title: 'Quality Care',
      description: 'Provide value-based care, improve patient outcomes and increase quality of care!',
    },
    {
      id: 2,
      image: ONBOARDING_IMAGES.img2,
      title: 'Virtual Care',
      description: 'With virtual Care and care team coordination, we reduce hospital admission!',
    },
    {
      id: 3,
      image: ONBOARDING_IMAGES.img3,
      title: 'Patient Engagement',
      description: 'Increase patient engagement with voice, video and text!',
    },
    {
      id: 4,
      image: ONBOARDING_IMAGES.img4,
      title: 'Independent Living',
      description: 'We help seniors stay healthy, live independently with dignity!',
    },
  ];

  React.useEffect(() => {
    // Auto-rotate images every 5 seconds
    const imageRotationInterval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        (prevIndex + 1) % onboardingContent.length
      );
    }, 5000);

    return () => clearInterval(imageRotationInterval);
  }, []);

  const handleLogin = () => {
    if (email && password) {
      onLogin?.(email, password);
    }
  };

  // Desktop-like landscape layout for tablets
  const renderLandscapeLayout = () => (
    <View style={styles.landscapeContainer}>
      {/* Left Panel - Images and Content */}
      <View style={styles.leftPanel}>
        <View style={styles.imageSection}>
          <Image
            source={onboardingContent[currentImageIndex].image}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <View style={styles.imageOverlay}>
            <AccessibleText variant="h1" style={styles.heroTitle}>
              {onboardingContent[currentImageIndex].title}
            </AccessibleText>
            <AccessibleText variant="body" style={styles.heroDescription}>
              {onboardingContent[currentImageIndex].description}
            </AccessibleText>
          </View>
        </View>

        {/* Image indicators */}
        <View style={styles.indicators}>
          {onboardingContent.map((_, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.indicator,
                index === currentImageIndex && styles.activeIndicator,
              ]}
              onPress={() => setCurrentImageIndex(index)}
            />
          ))}
        </View>
      </View>

      {/* Right Panel - Login Form */}
      <View style={styles.rightPanel}>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={styles.keyboardAvoid}
        >
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            <View style={styles.authSection}>
              {/* Logo Section */}
              <View style={styles.logoSection}>
                <Image
                  source={require("@/assets/images/img_logo.png")}
                  style={styles.logo}
                  resizeMode="contain"
                />
                <AccessibleText variant="h1" style={styles.title}>
                  WatchRx Care Team
                </AccessibleText>
                <AccessibleText variant="body" style={styles.subtitle}>
                  Sign in to your account
                </AccessibleText>
              </View>

            {/* Form Section */}
            <View style={styles.formSection}>
              <View style={styles.formContainer}>
                <AccessibleText variant="h2" style={styles.formTitle}>
                  Welcome Back
                </AccessibleText>

                <View style={styles.inputContainer}>
                  <TabletTextInput
                    label="Email Address"
                    value={email}
                    onChangeText={setEmail}
                    placeholder="Enter your email"
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoComplete="email"
                    accessibilityLabel="Email address input"
                    style={styles.input}
                  />
                </View>

                <View style={styles.inputContainer}>
                  <TabletTextInput
                    label="Password"
                    value={password}
                    onChangeText={setPassword}
                    placeholder="Enter your password"
                    secureTextEntry={!showPassword}
                    autoComplete="password"
                    accessibilityLabel="Password input"
                    style={styles.input}
                    rightIcon="eye"
                    onRightIconPress={() => setShowPassword(!showPassword)}
                  />
                </View>

                <AccessibleButton
                  title="Sign In"
                  onPress={handleLogin}
                  loading={loading}
                  disabled={!email || !password || loading}
                  style={styles.loginButton}
                  accessibilityLabel="Sign in button"
                />

                <AccessibleButton
                  title="Forgot Password?"
                  onPress={onForgotPassword}
                  variant="text"
                  style={styles.forgotButton}
                  accessibilityLabel="Forgot password link"
                />

                <View style={styles.divider}>
                  <View style={styles.dividerLine} />
                  <AccessibleText
                    variant="bodySmall"
                    style={styles.dividerText}
                  >
                    OR
                  </AccessibleText>
                  <View style={styles.dividerLine} />
                </View>

                  <AccessibleButton
                    title="Create New Account"
                    onPress={onSignUp}
                    variant="outline"
                    style={styles.signupButton}
                    accessibilityLabel="Create new account button"
                  />
                </View>
              </View>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </View>
    </View>
  );

  // Portrait layout (similar to original)
  const renderPortraitLayout = () => (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={styles.keyboardAvoid}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.portraitContainer}>
          {/* Logo Section */}
          <View style={styles.logoSection}>
            <Image
              source={require("@/assets/images/img_logo.png")}
              style={styles.logo}
              resizeMode="contain"
            />
            <AccessibleText variant="h1" style={styles.title}>
              WatchRx Care Team
            </AccessibleText>
            <AccessibleText variant="body" style={styles.subtitle}>
              Sign in to your account
            </AccessibleText>
          </View>

          {/* Form Section */}
          <View style={styles.formSection}>
            <View style={styles.formContainer}>
              <AccessibleText variant="h2" style={styles.formTitle}>
                Welcome Back
              </AccessibleText>

              <View style={styles.inputContainer}>
                <TabletTextInput
                  label="Email Address"
                  value={email}
                  onChangeText={setEmail}
                  placeholder="Enter your email"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoComplete="email"
                  accessibilityLabel="Email address input"
                  style={styles.input}
                />
              </View>

              <View style={styles.inputContainer}>
                <TabletTextInput
                  label="Password"
                  value={password}
                  onChangeText={setPassword}
                  placeholder="Enter your password"
                  secureTextEntry={!showPassword}
                  autoComplete="password"
                  accessibilityLabel="Password input"
                  style={styles.input}
                  rightIcon="eye"
                  onRightIconPress={() => setShowPassword(!showPassword)}
                />
              </View>

              <AccessibleButton
                title="Sign In"
                onPress={handleLogin}
                loading={loading}
                disabled={!email || !password || loading}
                style={styles.loginButton}
                accessibilityLabel="Sign in button"
              />

              <AccessibleButton
                title="Forgot Password?"
                onPress={onForgotPassword}
                variant="text"
                style={styles.forgotButton}
                accessibilityLabel="Forgot password link"
              />

              <View style={styles.divider}>
                <View style={styles.dividerLine} />
                <AccessibleText
                  variant="bodySmall"
                  style={styles.dividerText}
                >
                  OR
                </AccessibleText>
                <View style={styles.dividerLine} />
              </View>

              <AccessibleButton
                title="Create New Account"
                onPress={onSignUp}
                variant="outline"
                style={styles.signupButton}
                accessibilityLabel="Create new account button"
              />
            </View>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );

  return (
    <TabletAwareContainer style={styles.container}>
      <AdaptiveLayout
        phoneLayout={renderPortraitLayout()}
        tabletPortraitLayout={renderPortraitLayout()}
        tabletLandscapeLayout={renderLandscapeLayout()}
        largeTabletLayout={renderLandscapeLayout()}
      />
    </TabletAwareContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB",
  },

  // Landscape Layout Styles (Desktop-like)
  landscapeContainer: {
    flex: 1,
    flexDirection: 'row',
    minHeight: Dimensions.get('window').height,
  },
  leftPanel: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    position: 'relative',
  },
  rightPanel: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageSection: {
    flex: 1,
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  imageOverlay: {
    position: 'absolute',
    bottom: spacing.xxl,
    left: spacing.xl,
    right: spacing.xl,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 16,
    padding: spacing.xl,
  },
  heroTitle: {
    color: '#FFFFFF',
    fontSize: isLargeTablet() ? typography.h1.fontSize * 1.2 : typography.h1.fontSize,
    fontWeight: '700',
    marginBottom: spacing.md,
    textAlign: 'left',
  },
  heroDescription: {
    color: '#FFFFFF',
    fontSize: isLargeTablet() ? typography.body.fontSize * 1.1 : typography.body.fontSize,
    opacity: 0.9,
    lineHeight: 24,
    textAlign: 'left',
  },
  indicators: {
    position: 'absolute',
    bottom: spacing.xl,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  indicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
    marginHorizontal: 6,
  },
  activeIndicator: {
    backgroundColor: '#FFFFFF',
    width: 16,
    height: 16,
    borderRadius: 8,
  },
  authSection: {
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  },

  // Portrait Layout Styles
  portraitContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },

  // Common Styles
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: "center",
    minHeight: "100%",
  },
  logoSection: {
    alignItems: "center",
    justifyContent: "center",
    marginBottom: spacing.xxl,
  },
  logo: {
    width: isLargeTablet() ? 100 : isTablet() ? 80 : 70,
    height: isLargeTablet() ? 100 : isTablet() ? 80 : 70,
    marginBottom: spacing.lg,
  },
  title: {
    color: getOrientation() === 'landscape' && isTablet() ? CoreColors.TurquoiseBlue : CoreColors.DarkJungleGreen,
    textAlign: "center",
    marginBottom: spacing.sm,
    fontSize: isLargeTablet() ? typography.h1.fontSize * 1.1 : typography.h1.fontSize,
  },
  subtitle: {
    color: getOrientation() === 'landscape' && isTablet() ? CoreColors.TealBlue : CoreColors.SlateGray,
    textAlign: "center",
    fontSize: isLargeTablet() ? typography.body.fontSize * 1.05 : typography.body.fontSize,
  },
  formSection: {
    width: "100%",
    maxWidth: isTablet() ? 500 : 400,
    alignItems: "center",
  },
  formContainer: {
    width: "100%",
    backgroundColor: getOrientation() === 'landscape' && isTablet() ? "transparent" : "#FFFFFF",
    borderRadius: isTablet() ? 16 : 12,
    padding: spacing.xl,
    shadowColor: getOrientation() === 'landscape' && isTablet() ? "transparent" : "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: getOrientation() === 'landscape' && isTablet() ? 0 : 0.1,
    shadowRadius: 8,
    elevation: getOrientation() === 'landscape' && isTablet() ? 0 : 5,
  },
  formTitle: {
    color: CoreColors.DarkJungleGreen,
    textAlign: "center",
    marginBottom: spacing.xl,
    fontSize: isLargeTablet() ? typography.h2.fontSize * 1.1 : typography.h2.fontSize,
  },
  inputContainer: {
    marginBottom: spacing.lg,
  },
  input: {
    backgroundColor: "#F9FAFB",
  },
  loginButton: {
    marginTop: spacing.md,
    marginBottom: spacing.lg,
    backgroundColor: CoreColors.TurquoiseBlue,
    borderRadius: 12,
    paddingVertical: isTablet() ? 16 : 14,
    minHeight: isTablet() ? 56 : 48,
  },
  forgotButton: {
    alignSelf: "center",
    marginBottom: spacing.lg,
  },
  divider: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: spacing.lg,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: "#E5E7EB",
  },
  dividerText: {
    color: CoreColors.SlateGray,
    paddingHorizontal: spacing.md,
  },
  signupButton: {
    marginTop: spacing.sm,
    backgroundColor: 'transparent',
    borderColor: CoreColors.TurquoiseBlue,
    borderWidth: 2,
    borderRadius: 12,
    paddingVertical: isTablet() ? 16 : 14,
    minHeight: isTablet() ? 56 : 48,
  },
});

export default TabletLogin;
