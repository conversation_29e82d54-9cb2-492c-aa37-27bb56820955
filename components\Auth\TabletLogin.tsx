import React, { useState } from "react";
import {
  View,
  ScrollView,
  StyleSheet,
  Image,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import TabletAwareContainer from "@/components/Layout/TabletAwareContainer";
import AccessibleText from "@/components/Accessibility/AccessibleText";
import AccessibleButton from "@/components/Accessibility/AccessibleButton";
import TabletTextInput from "@/components/Forms/TabletTextInput";
import { isTablet, isLargeTablet, spacing } from "@/utils/responsive";
import { CoreColors } from "@/constants/Colors";

interface TabletLoginProps {
  onLogin?: (email: string, password: string) => void;
  onForgotPassword?: () => void;
  onSignUp?: () => void;
  loading?: boolean;
}

/**
 * Tablet-optimized Login screen component
 */
const TabletLogin: React.FC<TabletLoginProps> = ({
  onLogin,
  onForgotPassword,
  onSignUp,
  loading = false,
}) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);

  const handleLogin = () => {
    if (email && password) {
      onLogin?.(email, password);
    }
  };

  return (
    <TabletAwareContainer style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoid}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.content}>
            {/* Logo Section */}
            <View style={styles.logoSection}>
              <Image
                source={require("@/assets/images/img_logo.png")}
                style={styles.logo}
                resizeMode="contain"
              />
              <AccessibleText variant="h1" style={styles.title}>
                WatchRx Care Team
              </AccessibleText>
              <AccessibleText variant="body" style={styles.subtitle}>
                Sign in to your account
              </AccessibleText>
            </View>

            {/* Form Section */}
            <View style={styles.formSection}>
              <View style={styles.formContainer}>
                <AccessibleText variant="h2" style={styles.formTitle}>
                  Welcome Back
                </AccessibleText>

                <View style={styles.inputContainer}>
                  <TabletTextInput
                    label="Email Address"
                    value={email}
                    onChangeText={setEmail}
                    placeholder="Enter your email"
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoComplete="email"
                    accessibilityLabel="Email address input"
                    style={styles.input}
                  />
                </View>

                <View style={styles.inputContainer}>
                  <TabletTextInput
                    label="Password"
                    value={password}
                    onChangeText={setPassword}
                    placeholder="Enter your password"
                    secureTextEntry={!showPassword}
                    autoComplete="password"
                    accessibilityLabel="Password input"
                    style={styles.input}
                    rightIcon="eye"
                    onRightIconPress={() => setShowPassword(!showPassword)}
                  />
                </View>

                <AccessibleButton
                  title="Sign In"
                  onPress={handleLogin}
                  loading={loading}
                  disabled={!email || !password || loading}
                  style={styles.loginButton}
                  accessibilityLabel="Sign in button"
                />

                <AccessibleButton
                  title="Forgot Password?"
                  onPress={onForgotPassword}
                  variant="text"
                  style={styles.forgotButton}
                  accessibilityLabel="Forgot password link"
                />

                <View style={styles.divider}>
                  <View style={styles.dividerLine} />
                  <AccessibleText
                    variant="bodySmall"
                    style={styles.dividerText}
                  >
                    OR
                  </AccessibleText>
                  <View style={styles.dividerLine} />
                </View>

                <AccessibleButton
                  title="Create New Account"
                  onPress={onSignUp}
                  variant="outline"
                  style={styles.signupButton}
                  accessibilityLabel="Create new account button"
                />
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </TabletAwareContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB",
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: "center",
    minHeight: "100%",
  },
  content: {
    flex: 1,
    flexDirection: isLargeTablet() ? "row" : "column",
    alignItems: "center",
    justifyContent: "center",
    padding: spacing.xl,
  },
  logoSection: {
    flex: isLargeTablet() ? 1 : 0,
    alignItems: "center",
    justifyContent: "center",
    paddingRight: isLargeTablet() ? spacing.xl : 0,
    paddingBottom: isLargeTablet() ? 0 : spacing.xl,
  },
  logo: {
    width: isTablet() ? 120 : 100,
    height: isTablet() ? 120 : 100,
    marginBottom: spacing.lg,
  },
  title: {
    color: CoreColors.DarkJungleGreen,
    textAlign: "center",
    marginBottom: spacing.sm,
  },
  subtitle: {
    color: CoreColors.SlateGray,
    textAlign: "center",
  },
  formSection: {
    flex: isLargeTablet() ? 1 : 0,
    width: "100%",
    maxWidth: isTablet() ? 500 : 400,
    alignItems: "center",
  },
  formContainer: {
    width: "100%",
    backgroundColor: "#FFFFFF",
    borderRadius: isTablet() ? 16 : 12,
    padding: spacing.xl,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  formTitle: {
    color: CoreColors.DarkJungleGreen,
    textAlign: "center",
    marginBottom: spacing.xl,
  },
  inputContainer: {
    marginBottom: spacing.lg,
  },
  input: {
    backgroundColor: "#F9FAFB",
  },
  loginButton: {
    marginTop: spacing.md,
    marginBottom: spacing.lg,
  },
  forgotButton: {
    alignSelf: "center",
    marginBottom: spacing.lg,
  },
  divider: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: spacing.lg,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: "#E5E7EB",
  },
  dividerText: {
    color: CoreColors.SlateGray,
    paddingHorizontal: spacing.md,
  },
  signupButton: {
    marginTop: spacing.sm,
  },
});

export default TabletLogin;
