import React, { ReactNode } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  ViewStyle,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';

import Text from '@/components/Text';
import { TabletAwareContainer } from '@/components/Layout';
import { useOrientation } from '@/hooks/useOrientation';
import { useTabletTheme } from '@/hooks/useTabletTheme';
import {
  isTablet,
  isLargeTablet,
  spacing,
  typography,
} from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';

interface FormSection {
  id: string;
  title?: string;
  description?: string;
  children: ReactNode;
  columns?: 1 | 2 | 3;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
}

interface TabletFormProps {
  title?: string;
  description?: string;
  sections?: FormSection[];
  children?: ReactNode;
  
  // Layout options
  layout?: 'single' | 'two-column' | 'adaptive';
  maxWidth?: number;
  centerContent?: boolean;
  
  // Keyboard handling
  keyboardAvoidingView?: boolean;
  keyboardVerticalOffset?: number;
  
  // Styling
  style?: ViewStyle;
  contentContainerStyle?: ViewStyle;
  
  // Actions
  headerActions?: ReactNode;
  footerActions?: ReactNode;
}

/**
 * Form Section Component
 */
const FormSection: React.FC<{
  section: FormSection;
  isTabletLayout: boolean;
  tabletTheme: any;
}> = ({ section, isTabletLayout, tabletTheme }) => {
  const [collapsed, setCollapsed] = React.useState(section.defaultCollapsed || false);

  const toggleCollapsed = React.useCallback(() => {
    if (section.collapsible) {
      setCollapsed(!collapsed);
    }
  }, [collapsed, section.collapsible]);

  const getColumnStyles = React.useCallback(() => {
    if (!isTabletLayout || !section.columns || section.columns === 1) {
      return styles.singleColumn;
    }

    return section.columns === 2 ? styles.twoColumn : styles.threeColumn;
  }, [isTabletLayout, section.columns]);

  return (
    <View style={[
      styles.section,
      {
        marginBottom: tabletTheme.spacing.get('xl'),
        padding: tabletTheme.spacing.get('lg'),
        borderRadius: tabletTheme.borderRadius('medium'),
        ...tabletTheme.shadow('light'),
      }
    ]}>
      {/* Section Header */}
      {(section.title || section.description) && (
        <View style={styles.sectionHeader}>
          {section.title && (
            <Text style={[
              styles.sectionTitle,
              { fontSize: tabletTheme.typography.get('h3').fontSize }
            ]}>
              {section.title}
            </Text>
          )}
          
          {section.description && (
            <Text style={[
              styles.sectionDescription,
              { fontSize: tabletTheme.typography.get('body').fontSize }
            ]}>
              {section.description}
            </Text>
          )}
        </View>
      )}

      {/* Section Content */}
      {(!section.collapsible || !collapsed) && (
        <View style={getColumnStyles()}>
          {section.children}
        </View>
      )}
    </View>
  );
};

/**
 * Main Tablet Form Component
 */
const TabletForm: React.FC<TabletFormProps> = ({
  title,
  description,
  sections = [],
  children,
  
  layout = 'adaptive',
  maxWidth,
  centerContent = true,
  
  keyboardAvoidingView = true,
  keyboardVerticalOffset = 0,
  
  style,
  contentContainerStyle,
  
  headerActions,
  footerActions,
}) => {
  const { orientation } = useOrientation();
  const tabletTheme = useTabletTheme();

  // Determine layout based on device and orientation
  const getFormLayout = React.useCallback(() => {
    if (layout === 'single') return 'single';
    if (layout === 'two-column') return 'two-column';
    
    // Adaptive layout
    if (isLargeTablet() && orientation === 'landscape') {
      return 'two-column';
    } else if (isTablet() && orientation === 'landscape') {
      return 'two-column';
    } else {
      return 'single';
    }
  }, [layout, orientation]);

  const formLayout = getFormLayout();
  const isTabletLayout = isTablet();

  // Calculate form width
  const getFormWidth = React.useCallback(() => {
    if (maxWidth) return maxWidth;
    
    if (isLargeTablet()) {
      return formLayout === 'two-column' ? '100%' : 800;
    } else if (isTablet()) {
      return formLayout === 'two-column' ? '100%' : 600;
    } else {
      return '100%';
    }
  }, [maxWidth, formLayout]);

  const formWidth = getFormWidth();

  // Form content
  const renderFormContent = React.useCallback(() => (
    <View style={[
      styles.formContent,
      {
        maxWidth: typeof formWidth === 'number' ? formWidth : undefined,
        width: typeof formWidth === 'string' ? formWidth : undefined,
        alignSelf: centerContent ? 'center' : 'stretch',
      }
    ]}>
      {/* Form Header */}
      {(title || description || headerActions) && (
        <View style={[
          styles.formHeader,
          { marginBottom: tabletTheme.spacing.get('xl') }
        ]}>
          <View style={styles.headerContent}>
            {title && (
              <Text style={[
                styles.formTitle,
                { fontSize: tabletTheme.typography.get('h2').fontSize }
              ]}>
                {title}
              </Text>
            )}
            
            {description && (
              <Text style={[
                styles.formDescription,
                { fontSize: tabletTheme.typography.get('body').fontSize }
              ]}>
                {description}
              </Text>
            )}
          </View>
          
          {headerActions && (
            <View style={styles.headerActions}>
              {headerActions}
            </View>
          )}
        </View>
      )}

      {/* Form Sections */}
      {sections.length > 0 ? (
        <View style={formLayout === 'two-column' ? styles.sectionsGrid : styles.sectionsList}>
          {sections.map((section) => (
            <View
              key={section.id}
              style={formLayout === 'two-column' ? styles.sectionGridItem : undefined}
            >
              <FormSection
                section={section}
                isTabletLayout={isTabletLayout}
                tabletTheme={tabletTheme}
              />
            </View>
          ))}
        </View>
      ) : (
        // Direct children
        <View style={[
          styles.section,
          {
            padding: tabletTheme.spacing.get('lg'),
            borderRadius: tabletTheme.borderRadius('medium'),
            ...tabletTheme.shadow('light'),
          }
        ]}>
          {children}
        </View>
      )}

      {/* Form Footer */}
      {footerActions && (
        <View style={[
          styles.formFooter,
          { marginTop: tabletTheme.spacing.get('xl') }
        ]}>
          {footerActions}
        </View>
      )}
    </View>
  ), [
    formWidth,
    centerContent,
    title,
    description,
    headerActions,
    sections,
    children,
    footerActions,
    formLayout,
    isTabletLayout,
    tabletTheme,
  ]);

  const formContainer = (
    <TabletAwareContainer style={[styles.container, style]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[styles.scrollContent, contentContainerStyle]}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {renderFormContent()}
      </ScrollView>
    </TabletAwareContainer>
  );

  if (keyboardAvoidingView && Platform.OS === 'ios') {
    return (
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior="padding"
        keyboardVerticalOffset={keyboardVerticalOffset}
      >
        {formContainer}
      </KeyboardAvoidingView>
    );
  }

  return formContainer;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFBFC',
  } as ViewStyle,

  keyboardAvoidingView: {
    flex: 1,
  } as ViewStyle,

  scrollView: {
    flex: 1,
  } as ViewStyle,

  scrollContent: {
    paddingVertical: spacing.lg,
  } as ViewStyle,

  formContent: {
    width: '100%',
  } as ViewStyle,

  // Header Styles
  formHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  } as ViewStyle,

  headerContent: {
    flex: 1,
  } as ViewStyle,

  formTitle: {
    fontWeight: '700',
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.sm,
  } as ViewStyle,

  formDescription: {
    color: CoreColors.GrayBlue,
    lineHeight: typography.body.lineHeight,
  } as ViewStyle,

  headerActions: {
    marginLeft: spacing.lg,
  } as ViewStyle,

  // Section Styles
  sectionsList: {
    // Single column layout
  } as ViewStyle,

  sectionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -spacing.sm,
  } as ViewStyle,

  sectionGridItem: {
    width: '50%',
    paddingHorizontal: spacing.sm,
  } as ViewStyle,

  section: {
    backgroundColor: '#FFFFFF',
  } as ViewStyle,

  sectionHeader: {
    marginBottom: spacing.lg,
  } as ViewStyle,

  sectionTitle: {
    fontWeight: '600',
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.xs,
  } as ViewStyle,

  sectionDescription: {
    color: CoreColors.GrayBlue,
    lineHeight: typography.body.lineHeight,
  } as ViewStyle,

  // Column Layouts
  singleColumn: {
    // Default single column
  } as ViewStyle,

  twoColumn: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -spacing.sm,
  } as ViewStyle,

  threeColumn: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -spacing.xs,
  } as ViewStyle,

  // Footer Styles
  formFooter: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  } as ViewStyle,
});

export default TabletForm;