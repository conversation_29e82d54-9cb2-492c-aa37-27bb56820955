import React, { useEffect, useRef, useState } from 'react';
import {
  Animated,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Platform,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { PanGestureHandler, GestureHandlerRootView, State } from 'react-native-gesture-handler';
import { CoreColors } from '@/constants/Colors';
import { 
  spacing, 
  typography, 
  iconSizes, 
  layout,
  scale,
  wp,
  hp
} from '@/utils/responsive';

// Responsive notification dimensions
const getNotificationHeight = () => {
  // Adaptive height based on screen size and content
  if (layout.isShortScreen) {
    return scale(80); // Compact for short screens
  } else if (layout.isSmallScreen) {
    return scale(90); // Medium for small screens
  } else if (layout.isMediumScreen) {
    return scale(95); // Standard for medium screens
  } else {
    return scale(100); // Spacious for large screens
  }
};

const getNotificationPadding = () => {
  return {
    horizontal: layout.isSmallScreen ? spacing.sm : spacing.md,
    vertical: layout.isShortScreen ? spacing.xs : spacing.sm,
  };
};

// Responsive positioning
const getNotificationMargins = () => {
  return {
    horizontal: layout.isSmallScreen ? spacing.sm : spacing.md,
    top: layout.isShortScreen ? spacing.xs : spacing.sm,
  };
};

// Singleton instance for global toast management
let toastRef: {
  show: (title: string, body: string, onPress?: () => void) => void;
  hide: () => void;
} | null = null;

export const showToast = (title: string, body: string, onPress?: () => void) => {
  if (title && body && toastRef?.show) {
    toastRef.show(title, body, onPress);
  }
};


export const hideToast = () => {
  if (toastRef?.hide) {
    toastRef.hide();
  }
};

interface ToastState {
  title: string;
  body: string;
  onPress?: () => void;
}

const ToastNotification: React.FC = () => {
  const insets = useSafeAreaInsets();
  const [isVisible, setIsVisible] = useState(false);
  const [toastState, setToastState] = useState<ToastState>({ title: '', body: '' });
  
  // Animation values
  const translateY = useRef(new Animated.Value(-200)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(0.85)).current;
  
  // Gesture values
  const gestureTranslateY = useRef(new Animated.Value(0)).current;
  const gestureOpacity = useRef(new Animated.Value(1)).current;
  
  // Auto-hide timer
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isDismissingRef = useRef(false);

  // Register singleton instance
  useEffect(() => {
    toastRef = {
      show: (title: string, body: string, onPress?: () => void) => {
        showNotification(title, body, onPress);
      },
      hide: hideNotification,
    };

    return () => {
      toastRef = null;
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const showNotification = (title: string, body: string, onPress?: () => void) => {
    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Update state
    setToastState({ title, body, onPress });
    setIsVisible(true);

    // Animate in with spring physics
    Animated.parallel([
      Animated.spring(translateY, {
        toValue: 0,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(scale, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Auto-hide after 4 seconds
    timeoutRef.current = setTimeout(() => {
      hideNotification();
    }, 4000);
  };

  const hideNotification = (fromGesture = false) => {
    if (isDismissingRef.current) return;
    isDismissingRef.current = true;

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    // Animate out
    const animations = [];

    if (fromGesture) {
      // Continue the gesture animation
      animations.push(
        Animated.timing(gestureTranslateY, {
          toValue: -200,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(gestureOpacity, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        })
      );
    } else {
      // Normal hide animation
      animations.push(
        Animated.timing(translateY, {
          toValue: -200,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.spring(scale, {
          toValue: 0.85,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        })
      );
    }

    Animated.parallel(animations).start(() => {
      setIsVisible(false);
      isDismissingRef.current = false;
      // Reset gesture values
      gestureTranslateY.setValue(0);
      gestureOpacity.setValue(1);
    });
  };

  const handlePress = () => {
    hideNotification();
    setTimeout(() => {
      toastState.onPress?.();
    }, 100);
  };

  // Gesture handler for swipe to dismiss
  const onGestureEvent = Animated.event(
    [{
      nativeEvent: {
        translationY: gestureTranslateY,
      },
    }],
    { useNativeDriver: true }
  );

  const onHandlerStateChange = (event: any) => {
    if (isDismissingRef.current) return;

    const { state, translationY, velocityY } = event.nativeEvent;

    if (state === State.ACTIVE) {
      // Update opacity based on swipe distance
      const progress = Math.max(0, Math.min(1, Math.abs(translationY) / 100));
      const newOpacity = 1 - progress * 0.7;
      gestureOpacity.setValue(newOpacity);
    } else if (state === State.END) {
      const shouldDismiss = translationY < -50 || velocityY < -500;

      if (shouldDismiss) {
        // Dismiss the notification
        hideNotification(true);
      } else {
        // Snap back to original position
        Animated.parallel([
          Animated.spring(gestureTranslateY, {
            toValue: 0,
            tension: 300,
            friction: 20,
            useNativeDriver: true,
          }),
          Animated.timing(gestureOpacity, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start();
      }
    }
  };

  if (!isVisible) return null;

  const notificationHeight = getNotificationHeight();
  const padding = getNotificationPadding();
  const margins = getNotificationMargins();
  const topOffset = insets.top + margins.top;

  return (
    <PanGestureHandler
      onGestureEvent={onGestureEvent}
      onHandlerStateChange={onHandlerStateChange}
      minPointers={1}
      maxPointers={1}
    >
      <Animated.View
        style={[
          styles.wrapper,
          {
            top: topOffset,
            transform: [
              { translateY: Animated.add(translateY, gestureTranslateY) },
              { scale },
            ],
            opacity: Animated.multiply(opacity, gestureOpacity),
          },
        ]}
      >
        {/* Main Notification Container */}
        <Animated.View
          style={[
            styles.container,
            {
              height: notificationHeight,
            },
          ]}
        >
          <BlurView
            intensity={Platform.OS === 'ios' ? 95 : 80}
            tint="light"
            style={styles.blurContainer}
          >
            <TouchableOpacity
              activeOpacity={0.95}
              style={[styles.touchable, { padding: padding.vertical }]}
              onPress={handlePress}
            >
              <View style={[styles.content, { paddingHorizontal: padding.horizontal }]}>
                {/* Main Content Row */}
                <View style={styles.mainContent}>
                  {/* Icon Container */}
                  <View style={styles.iconContainer}>
                    <View style={styles.iconBackground}>
                      <Ionicons 
                        name="chatbubble-ellipses" 
                        size={iconSizes.lg} 
                        color={CoreColors.TurquoiseBlue} 
                      />
                    </View>
                  </View>

                  {/* Text Content */}
                  <View style={styles.textContainer}>
                    <Text style={[styles.title, typography.h3]} numberOfLines={1}>
                      {toastState.title}
                    </Text>
                    <Text style={[styles.body, typography.bodySmall]} numberOfLines={2}>
                      {toastState.body}
                    </Text>
                  </View>

                  {/* Action Indicator */}
                  <View style={styles.actionContainer}>
                    <Ionicons 
                      name="chevron-forward" 
                      size={iconSizes.sm} 
                      color={CoreColors.GrayBlue} 
                    />
                  </View>
                </View>
              </View>
            </TouchableOpacity>
          </BlurView>
        </Animated.View>

        {/* Swipe Indicator Below Container */}
        <View style={styles.swipeIndicator}>
          <View style={styles.swipeBar} />
        </View>
      </Animated.View>
    </PanGestureHandler>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    position: 'absolute',
    left: 0,
    right: 0,
    zIndex: 10000,
  },
  container: {
    marginHorizontal: layout.isSmallScreen ? spacing.sm : spacing.md,
    borderRadius: layout.isSmallScreen ? scale(12) : scale(16),
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: CoreColors.Black,
        shadowOffset: { width: 0, height: layout.isShortScreen ? scale(4) : scale(8) },
        shadowOpacity: layout.isSmallScreen ? 0.1 : 0.15,
        shadowRadius: layout.isShortScreen ? scale(12) : scale(16),
      },
      android: {
        elevation: layout.isSmallScreen ? 8 : 12,
      },
    }),
  },
  blurContainer: {
    flex: 1,
    backgroundColor: Platform.OS === 'android' ? CoreColors.White : 'transparent',
  },
  touchable: {
    flex: 1,
    justifyContent: 'center',
  },
  content: {
    flexDirection: 'column',
    justifyContent: 'center',
    minHeight: scale(56),
  },
  swipeIndicator: {
    alignItems: 'center',
    paddingTop: layout.isShortScreen ? scale(4) : scale(6),
    paddingBottom: layout.isShortScreen ? scale(2) : scale(4),
  },
  swipeBar: {
    width: layout.isSmallScreen ? wp(10) : layout.isMediumScreen ? wp(8) : wp(6),
    height: layout.isShortScreen ? scale(2.5) : scale(3),
    backgroundColor: CoreColors.GrayBlue + (layout.isSmallScreen ? '50' : '40'),
    borderRadius: layout.isShortScreen ? scale(1.5) : scale(2),
  },
  mainContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    marginRight: layout.isSmallScreen ? spacing.sm : spacing.md,
  },
  iconBackground: {
    width: layout.isSmallScreen ? scale(40) : scale(44),
    height: layout.isSmallScreen ? scale(40) : scale(44),
    borderRadius: layout.isSmallScreen ? scale(20) : scale(22),
    backgroundColor: CoreColors.TurquoiseBlue + '15',
    justifyContent: 'center',
    alignItems: 'center',
  },
  textContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  title: {
    color: CoreColors.DarkJungleGreen,
    fontWeight: '600',
    marginBottom: scale(2),
  },
  body: {
    color: CoreColors.GrayBlue,
    fontWeight: '400',
    lineHeight: typography.bodySmall.lineHeight * 1.1,
  },
  actionContainer: {
    marginLeft: spacing.sm,
    opacity: 0.6,
  },
});

export default ToastNotification;
