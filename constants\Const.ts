import {Dimensions, Platform, PixelRatio, ScaledSize} from 'react-native';
import ExpoConstants from 'expo-constants';

// Get device window dimensions
let {width: SCREEN_WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');

// Normalize for different screen sizes
const widthBaseScale = SCREEN_WIDTH / 375;
const heightBaseScale = SCREEN_HEIGHT / 812;

// Function to dynamically calculate size based on screen dimensions
function normalize(size: number, based = 'width') {
  const newSize = based === 'height' ? size * heightBaseScale : size * widthBaseScale;
  return Math.round(PixelRatio.roundToNearestPixel(newSize));
}

// Helper functions for responsive values
const widthScale = (size: number) => normalize(size, 'width');
const heightScale = (size: number) => normalize(size, 'height');
const fontScale = (size: number) => heightScale(size);

// Handle dimension changes (like rotation)
Dimensions.addEventListener('change', ({window}: {window: ScaledSize}) => {
  SCREEN_WIDTH = window.width;
  SCREEN_HEIGHT = window.height;
});

// Replacement for react-native-iphone-x-helper's getStatusBarHeight
const getStatusBarHeight = () => {
  return ExpoConstants.statusBarHeight || 20;
};

const Constants = {
  width: SCREEN_WIDTH,
  height: SCREEN_HEIGHT,
  FONTS_APP: 'Muli',
  widthScale,
  heightScale,
  fontScale,
  isSmallDevice: SCREEN_WIDTH < 375,
};

export const width = SCREEN_WIDTH;
export const height = SCREEN_HEIGHT;
const topHeaderHeight = Platform.OS === 'ios' ? 0 : getStatusBarHeight();
export const HEADER_HEIGHT = 108 - topHeaderHeight;
export enum TasksType {
  LiveChat = 'LiveChat',
  Message = 'Message',
  VoiceCall = 'VoiceCall',
  Appointment = 'Appointment',
  VideoCall = 'VideoCall',
}

export enum TasksStatus {
  stillInProgress = 1,
  accepted = 2,
  unConFirmed = 3,
  completed = 4,
  patientCanceled = 5,
  iDeclined = 6,
  iCanceled = 7,
}

export default Constants;

export const HEALTH_FEED_CREATE_OPTION = [
  {
    id: 0,
    name: 'Create Health Tip',
  },
  {
    id: 1,
    name: 'Create Health Guide',
  },
  {
    id: 2,
    name: 'Create Topic',
  },
];
