import React from 'react';
import {
  View, ViewStyle, Platform, StyleSheet, StatusBar
} from 'react-native';
// Safe area insets are handled by the NavigationHeader component
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { getStatusBarHeight } from 'react-native-iphone-x-helper';

// Theme is handled at the app level
import { getHeaderHeight } from '@/utils/layoutHelper';
import { usePathname } from 'expo-router';

interface Props {
  children?: React.ReactNode;
  style?: ViewStyle;
  disableHeaderPadding?: boolean;
}

/**
 * Enhanced Container component that works with the NavigationHeader
 * to ensure content is properly positioned beneath the header with consistent spacing
 */
const Container = (props: Props) => {
  const pathname = usePathname();
  const insets = useSafeAreaInsets();

  // Determine if we should apply header padding based on pathname or props
  // Auth screens, splash screen and onboarding don't need header padding
  const shouldApplyHeaderPadding = !props.disableHeaderPadding &&
    !pathname.startsWith('/auth/') &&
    pathname !== '/splash' &&
    pathname !== '/onboarding';

  // Calculate the proper padding top value with platform-specific adjustments
  const getContentPaddingTop = () => {
    if (!shouldApplyHeaderPadding) return 0;

    if (Platform.OS === 'android') {
      // For Android, calculate based on status bar height + header content height + padding
      const statusBarHeight = StatusBar.currentHeight || 0;
      const safeAreaTop = insets.top;
      const actualStatusBarHeight = Math.max(statusBarHeight, safeAreaTop);
      const headerContentHeight = 72; // Header content + padding
      return actualStatusBarHeight + headerContentHeight + 16; // Extra padding for spacing
    } else {
      // For iOS, use the existing calculation
      return getHeaderHeight() + 40;
    }
  };

  const paddingTop = getContentPaddingTop();

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: '#FFFFFF',
          paddingTop: paddingTop,
        },
        // We're filtering out paddingTop from props.style to prevent overrides
        // This ensures our header padding is always applied consistently
        props.style && {
          ...props.style,
          paddingTop: shouldApplyHeaderPadding ? paddingTop : (props.style?.paddingTop || 0)
        }
      ]}
    >
      {props?.children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingBottom: 0,
    marginBottom: 0,
    position: 'relative',
    zIndex: 1, // Lower than header and tab bar
    marginTop: 0, // Ensure no extra margin pushes content down
    overflow: 'visible', // Allow header to be visible
  }
});

export default Container;
