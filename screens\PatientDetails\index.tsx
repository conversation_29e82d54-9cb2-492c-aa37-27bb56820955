import Container from "@/components/Layout/Container";
import Loader from "@/components/Loader/Loader";
import NavigationHeader from "@/components/NavigationHeader";
import PatientProfile from "@/components/PatientDetails/PatientProfile";
import Text from "@/components/Text";
import { ThemedText } from "@/components/ThemedText";
import { Routes } from "@/constants";
import {
  AntDesign,
  Feather,
  FontAwesome5,
  Ionicons,
  MaterialCommunityIcons,
  MaterialIcons,
} from "@expo/vector-icons";
import { useFocusEffect, useRoute } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";
import { useLocalSearchParams, useRouter } from "expo-router";
import React, { memo, useRef } from "react";
import {
  Animated,
  Dimensions,
  Easing,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import { useSelector } from "react-redux";

const { width: screenWidth, height: screenHeight } = Dimensions.get("window");

interface Feature {
  id: number;
  icon: {
    family: string;
    name: string;
    size: number;
  }; // Icon details
  title: string;
  subtitle: string;
  route: string;
  color: string;
  gradientColors: string[];
  priority: string;
  animation: string;
}

interface PatientData {
  patientId: string | number;
  patientName?: string;
  image?: any;
  address?: string;
  phone?: string;
  patientAlertsCount?: number;
  imeiNo?: string;
  gpsStatus?: string;
  trackingStatus?: string;
  radius?: string;
  latLong?: string;
  reachableStatus?: boolean;
  dob?: string; // Date of birth
  dateOfBirth?: string; // Alternative field name for date of birth
  Date_Of_Birth?: string; // Another possible field name
  DOB?: string; // Yet another possible field name
  date_of_birth?: string; // Snake case variant
  mrn?: string; // Medical Record Number
  programs?: {
    selectedPrograms: {
      mins: number;
      programName: string;
      programActivated: boolean;
      programId: string;
    }[];
    availablePrograms: {
      mins: number;
      programName: string;
      programActivated: boolean;
      programId: string;
    }[];
  };
}

interface PatientDetailsScreenProps {
  patientId?: string;
  patientDetails?: PatientData;
  loading?: boolean;
  navigation?: any; // Navigation prop
  dob?: string; // Date of birth from URL params
  mrn?: string; // Medical Record Number from URL params
}

// Enhanced features with modern gradients and subtitles like main dashboard
const FEATURES: Feature[] = [
  {
    id: 7,
    icon: {
      family: "AntDesign",
      name: "warning",
      size: 28,
    },
    title: "Critical Alerts",
    subtitle: "Urgent notifications",
    route: "patient-critical-alerts",
    color: "#F25F5C",
    gradientColors: ["#F25F5C", "#EF4444"],
    priority: "high",
    animation: "flash",
  },
  {
    id: 1,
    icon: {
      family: "FontAwesome5",
      name: "heartbeat",
      size: 28,
    },
    title: "Vitals Report",
    subtitle: "Health monitoring",
    route: Routes.VitalRepors,
    color: "#4ECDC4",
    gradientColors: ["#4ECDC4", "#44A08D"],
    priority: "high",
    animation: "pulse",
  },
  {
    id: 2,
    icon: {
      family: "MaterialCommunityIcons",
      name: "pill",
      size: 28,
    },
    title: "Medications",
    subtitle: "Drug management",
    route: Routes.MedicationPage,
    color: "#7A77FF",
    gradientColors: ["#7A77FF", "#6366F1"],
    priority: "high",
    animation: "scale",
  },
  {
    id: 3,
    icon: {
      family: "MaterialIcons",
      name: "location-on",
      size: 28,
    },
    title: "GPS",
    subtitle: "Location tracking",
    route: Routes.PatientMapView,
    color: "#FF6B6B",
    gradientColors: ["#FF6B6B", "#EE5A52"],
    priority: "medium",
    animation: "shake",
  },
  {
    id: 4,
    icon: {
      family: "Ionicons",
      name: "notifications",
      size: 28,
    },
    title: "Alerts",
    subtitle: "System notifications",
    route: Routes.PatientAlerts,
    color: "#36B5E0",
    gradientColors: ["#36B5E0", "#3490DC"],
    priority: "medium",
    animation: "bounce",
  },
  {
    id: 5,
    icon: {
      family: "MaterialCommunityIcons",
      name: "calendar-clock",
      size: 28,
    },
    title: "Custom Reminders",
    subtitle: "Scheduled tasks",
    route: Routes.CustomAlerts,
    color: "#FF9F1C",
    gradientColors: ["#FF9F1C", "#F59E0B"],
    priority: "low",
    animation: "rotate",
  },
  {
    id: 6,
    icon: {
      family: "FontAwesome5",
      name: "notes-medical",
      size: 26,
    },
    title: "Care Team Notes",
    subtitle: "Clinical records",
    route: Routes.CCMReports,
    color: "#9381FF",
    gradientColors: ["#9381FF", "#8B5CF6"],
    priority: "low",
    animation: "pulse",
  },

  {
    id: 8,
    icon: {
      family: "Feather",
      name: "check-circle",
      size: 28,
    },
    title: "Check In",
    subtitle: "Patient status",
    route: "patient-check-in",
    color: "#5FB49C",
    gradientColors: ["#5FB49C", "#10B981"],
    priority: "medium",
    animation: "scale",
  },
];

// Individual dashboard grid item component matching main dashboard style
const PatientDashboardGridItem = memo(
  ({
    item,
    patientId,
    style,
  }: {
    item: Feature;
    patientId: string;
    style?: any;
  }) => {
    const router = useRouter();
    const scaleAnim = useRef(new Animated.Value(1)).current;

    const handlePressIn = () => {
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 150,
        useNativeDriver: true,
        easing: Easing.bezier(0.4, 0.0, 0.2, 1),
      }).start();
    };

    const handlePressOut = () => {
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 4,
        tension: 100,
        useNativeDriver: true,
      }).start();
    };

    const handlePress = () => {
      // Map old routes to new Expo Router paths with patient ID
      const routeMap: Record<string, any> = {
        [Routes.VitalRepors]: { path: "/patient/vitals", useParams: false },
        [Routes.MedicationPage]: {
          path: "/patient/medications",
          useParams: true,
        },
        [Routes.PatientMapView]: {
          path: "/patient/map/[id]",
          generateId: true,
        },
        [Routes.PatientAlerts]: { path: "/patient/alerts", useParams: true },
        [Routes.CustomAlerts]: {
          path: "/patient/custom-alerts",
          useParams: true,
        },
        [Routes.CCMReports]: { path: "/patient/ccm", useParams: true },
        "patient-critical-alerts": {
          path: "/patient-critical-alerts",
          useParams: true,
        },
        "patient-check-in": { path: "/patient-check-in", useParams: true },
      };

      const routeInfo = routeMap[item.route];

      if (routeInfo) {
        let finalPath = routeInfo.path;
        const params: Record<string, string> = {
          patientId: patientId,
        };

        if (routeInfo.generateId) {
          finalPath = finalPath.replace("[id]", patientId);
        }

        if (routeInfo.useParams) {
          router.push({
            pathname: finalPath as any,
            params: params,
          });
        } else {
          router.push(finalPath as any);
        }
      }
    };

    const renderIcon = () => {
      const iconProps = {
        size: 24, // Increased from 18 to 24 for better visibility
        color: "#FFFFFF",
      };

      switch (item.icon.family) {
        case "MaterialIcons":
          return <MaterialIcons name={item.icon.name as any} {...iconProps} />;
        case "MaterialCommunityIcons":
          return (
            <MaterialCommunityIcons
              name={item.icon.name as any}
              {...iconProps}
            />
          );
        case "Ionicons":
          return <Ionicons name={item.icon.name as any} {...iconProps} />;
        case "FontAwesome5":
          return <FontAwesome5 name={item.icon.name as any} {...iconProps} />;
        case "Feather":
          return <Feather name={item.icon.name as any} {...iconProps} />;
        case "AntDesign":
          return <AntDesign name={item.icon.name as any} {...iconProps} />;
        default:
          return <MaterialIcons name="dashboard" {...iconProps} />;
      }
    };

    return (
      <TouchableOpacity
        activeOpacity={1}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        style={style}
      >
        <Animated.View
          style={[
            styles.gridItem,
            {
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <LinearGradient
            colors={[item.gradientColors[0], item.gradientColors[0]]}
            style={styles.gridItemGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            {/* Icon container with enhanced styling */}
            <View style={styles.gridIconContainer}>
              <View style={styles.iconBackground}>{renderIcon()}</View>
            </View>

            {/* Content with improved typography */}
            <View style={styles.gridItemContent}>
              <Text style={styles.gridItemTitle} numberOfLines={1}>
                {item.title}
              </Text>
              <Text style={styles.gridItemSubtitle} numberOfLines={1}>
                {item.subtitle}
              </Text>
            </View>
          </LinearGradient>
        </Animated.View>
      </TouchableOpacity>
    );
  }
);

/**
 * Patient Details Screen
 * Displays patient profile and available features
 */
export const PatientDetailsScreen = memo((props: PatientDetailsScreenProps) => {
  const [profileManagement, setProfileManagement] = React.useState<PatientData>(
    {
      patientId: "",
    }
  );
  const scrollY = React.useRef(new Animated.Value(0)).current;
  const scrollDistance = 300;
  const opacityAnim = scrollY.interpolate({
    inputRange: [0, scrollDistance / 5, scrollDistance],
    outputRange: [1, 0, 0],
    extrapolate: "clamp",
  });
  const opacity = { opacity: opacityAnim };
  const route = useRoute();
  const params = useLocalSearchParams();
  const patientIdFromParams = params.id as string;
  const patientNameFromParams = params.patientName as string;

  // Animation references for the grid items
  const gridAnimationRefs = FEATURES.map(
    () => useRef(new Animated.Value(0)).current
  );

  // Animation timing function for staggered grid animation
  React.useEffect(() => {
    const animations = gridAnimationRefs.map((anim, index) => {
      return Animated.timing(anim, {
        toValue: 1,
        duration: 300, // Faster animation
        delay: 50 + index * 30, // Reduced staggered delay for each item
        useNativeDriver: true,
      });
    });

    Animated.stagger(30, animations).start();
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      // Use patient details from props, route params, or URL params
      if (props.patientDetails) {
        // Make a copy of patientDetails without patientId to avoid duplication
        const { patientId: _, ...otherDetails } = props.patientDetails;
        setProfileManagement({
          ...otherDetails,
          patientId: props.patientId || patientIdFromParams || "",
        });
      } else {
        // Fallback to route params or URL params
        let routeParams = (route?.params as PatientData) || {
          patientId: patientIdFromParams || "",
        };

        // If routeParams comes with a patientId and we also have one from props/params, handle the conflict
        if (
          routeParams &&
          "patientId" in routeParams &&
          (props.patientId || patientIdFromParams)
        ) {
          const { patientId: _, ...otherRouteParams } = routeParams;
          setProfileManagement({
            ...otherRouteParams,
            patientId: props.patientId || patientIdFromParams || "",
          });
        } else {
          setProfileManagement(routeParams);
        }
      }
    }, [
      props.patientDetails,
      props.patientId,
      patientIdFromParams,
      route?.params,
    ])
  );

  const patientName = useSelector(
    (state: { currentPatientNameReducer?: { patientName: string } }) =>
      state?.currentPatientNameReducer?.patientName
  );

  // Get effective patient name from all available sources
  const effectivePatientName = React.useMemo(() => {
    return (
      profileManagement.patientName ||
      patientName ||
      patientNameFromParams ||
      (route?.params as any)?.patientName ||
      props.patientDetails?.patientName ||
      "Patient Details"
    );
  }, [
    profileManagement.patientName,
    patientName,
    patientNameFromParams,
    route?.params,
    props.patientDetails?.patientName,
  ]);

  // Get the effective patient ID for dashboard items
  const effectivePatientId =
    props.patientId ||
    patientIdFromParams ||
    String(profileManagement.patientId);

  // Fixed grid layout that ensures all 8 tiles are visible
  const renderDashboardGrid = () => {
    const pairs = [];
    for (let i = 0; i < FEATURES.length; i += 2) {
      pairs.push(FEATURES.slice(i, i + 2));
    }

    return pairs.map((pair, rowIndex) => (
      <View key={rowIndex} style={styles.gridRow}>
        {pair.map((item, itemIndex) => (
          <PatientDashboardGridItem
            key={item.id}
            item={item}
            patientId={effectivePatientId}
            style={styles.gridItemContainer}
          />
        ))}
        {/* If odd number of items, add spacer for last row */}
        {pair.length === 1 && <View style={styles.gridItemContainer} />}
      </View>
    ));
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <ThemedText type="bodyMedium">
        No features available for this patient.
      </ThemedText>
    </View>
  );

  // Render loading skeleton for better UX
  const renderLoadingSkeleton = () => (
    <View style={styles.loadingContainer}>
      <View style={styles.skeletonProfile} />
      <View style={styles.skeletonGrid}>
        {[...Array(4)].map((_, index) => (
          <View key={index} style={styles.skeletonItem} />
        ))}
      </View>
    </View>
  );

  return (
    <Container style={styles.container}>
      <NavigationHeader
        title={effectivePatientName}
        showBackButton={true}
        showLogo={false}
        showRightLogo={true}
      />
      <Loader modalVisible={props.loading} />
      {props.loading ? (
        renderLoadingSkeleton()
      ) : (
        <>
          {/* Patient Profile Section */}
          <View style={styles.profileContainer}>
            {/* Create a clean props object for PatientProfile to avoid property conflicts */}
            <PatientProfile
              patientId={Number(props.patientId || patientIdFromParams || 0)}
              patientName={
                profileManagement.patientName || patientName || "Patient"
              }
              image={profileManagement.image || null}
              address={profileManagement.address || "No address available"}
              phone={profileManagement.phone || "No phone available"}
              patientAlertsCount={
                (profileManagement.patientAlertsCount as number) || 0
              }
              imeiNo={(profileManagement.imeiNo as string) || ""}
              gpsStatus={(profileManagement.gpsStatus as string) || ""}
              trackingStatus={
                (profileManagement.trackingStatus as string) || ""
              }
              radius={(profileManagement.radius as string) || ""}
              latLong={(profileManagement.latLong as string) || ""}
              reachableStatus={
                Boolean(profileManagement.reachableStatus) || false
              }
              dob={
                profileManagement.dob ||
                String(props.dob) ||
                profileManagement.dateOfBirth ||
                profileManagement.Date_Of_Birth ||
                profileManagement.DOB ||
                profileManagement.date_of_birth ||
                ""
              }
              mrn={
                props.mrn ||
                profileManagement.mrn ||
                String(
                  props.patientId ||
                    patientIdFromParams ||
                    profileManagement.patientId ||
                    ""
                )
              }
              programs={{
                selectedPrograms:
                  profileManagement.programs?.selectedPrograms || [],
                availablePrograms:
                  profileManagement.programs?.availablePrograms || [],
              }}
            />
          </View>

          {/* Feature Options Section */}
          <View style={styles.featuresContainer}>{renderDashboardGrid()}</View>
        </>
      )}
    </Container>
  );
});

// Export the component as default
export default PatientDetailsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FAFBFC",
  },
  profileContainer: {
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 4,
    borderRadius: 16,
  },
  featuresContainer: {
    paddingHorizontal: 16,
    marginTop: 8,
    paddingBottom: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 24,
  },
  loadingContainer: {
    flex: 1,
    padding: 16,
  },
  skeletonProfile: {
    height: 140,
    borderRadius: 16,
    backgroundColor: "#e0e0e0",
    marginBottom: 16,
  },
  skeletonGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  skeletonItem: {
    width: "48%",
    height: 90,
    backgroundColor: "#e0e0e0",
    borderRadius: 16,
    marginBottom: 12,
  },
  gridItem: {
    width: "100%",
    height: "100%",
    borderRadius: 12,
    backgroundColor: "#FFFFFF",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    overflow: "hidden",
  },
  gridItemGradient: {
    flex: 1,
    padding: 10,
    justifyContent: "space-between",
  },
  gridIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
  },
  gridItemContent: {
    marginTop: 8,
  },
  gridItemTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 2,
    letterSpacing: -0.1,
    lineHeight: 17,
  },
  gridItemSubtitle: {
    fontSize: 11,
    color: "rgba(255, 255, 255, 0.85)",
    fontWeight: "500",
    lineHeight: 13,
  },
  iconBackground: {
    backgroundColor: "rgba(255, 255, 255, 0.25)",
    borderRadius: 18,
    padding: 4,
    justifyContent: "center",
    alignItems: "center",
  },
  gridRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  gridItemContainer: {
    flex: 1,
    marginHorizontal: 4,
    height: 100,
  },
});
