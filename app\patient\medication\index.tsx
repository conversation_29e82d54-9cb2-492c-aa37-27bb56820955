import React from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Stack } from 'expo-router';
import { useEffect } from 'react';

import MedicationScreen from '@/screens/PatientDetails/Medications';

export default function MedicationRoute() {
  const params = useLocalSearchParams();
  const router = useRouter();

  // Extract patientId from params
  let patientId = params.patientId as string;

  // Log all params for debugging
  console.log('Medication Route (singular) - All Params:', params);
  console.log('Medication Route (singular) - Patient ID:', patientId);

  // Debugging - log all available routes
  useEffect(() => {
    console.log('Current route info:', router);
    console.log('Available routes in router:', Object.keys(router));
  }, [router]);

  return (
    <>
      <Stack.Screen options={{ title: 'Medications' }} />
      <SafeAreaView style={styles.container} edges={['left', 'right']}>
        <MedicationScreen patientId={patientId as string} />
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});