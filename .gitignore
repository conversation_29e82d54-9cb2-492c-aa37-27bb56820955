# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/
ios
android
# Expo
.expo/
dist/
web-build/
expo-env.d.ts
*.apk
# Native
*.orig.*
*.p8
*.p12
*.key
*.mobileprovision
vendor/
# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

app-example

*.aab