import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Colors } from '@/constants';

interface CloseIconProps {
  color?: string;
  size?: number;
}

const CloseIcon: React.FC<CloseIconProps> = ({ 
  color = Colors.DarkJungleGreen, 
  size = 16 
}) => {
  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <View style={[
        styles.line1, 
        { 
          backgroundColor: color,
          width: size,
          height: size * 0.15,
          transform: [{ rotate: '45deg' }]
        }
      ]} />
      <View style={[
        styles.line2, 
        { 
          backgroundColor: color,
          width: size,
          height: size * 0.15,
          transform: [{ rotate: '-45deg' }]
        }
      ]} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  line1: {
    position: 'absolute',
    borderRadius: 1,
  },
  line2: {
    position: 'absolute',
    borderRadius: 1,
  }
});

export default CloseIcon;
