import Constants from 'expo-constants';

/**
 * Checks if the app is running in Expo Go
 * @returns boolean - true if running in Expo Go, false otherwise
 */
export const isRunningInExpoGo = (): boolean => {
  // Check if we're running in Expo Go
  const appOwnership = Constants.appOwnership;
  return appOwnership === 'expo';
};

/**
 * Checks if a specific native module is available
 * @param moduleName The name of the native module to check
 * @returns boolean - true if the module is available, false otherwise
 */
export const isNativeModuleAvailable = (moduleName: string): boolean => {
  try {
    // Try to access the native module
    const NativeModules = require('react-native').NativeModules;
    return !!NativeModules[moduleName];
  } catch (error) {
    console.error(`Error checking for native module ${moduleName}:`, error);
    return false;
  }
};
