import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Colors } from '@/constants';

interface SearchIconProps {
  color?: string;
  size?: number;
}

const SearchIcon: React.FC<SearchIconProps> = ({ 
  color = Colors.GrayBlue, 
  size = 16 
}) => {
  return (
    <View style={[
      styles.container, 
      { 
        width: size, 
        height: size,
        borderColor: color
      }
    ]}>
      <View style={[
        styles.handle, 
        { 
          backgroundColor: color,
          right: size * 0.1,
          bottom: size * 0.1,
          width: size * 0.4,
          height: size * 0.15,
          transform: [{ rotate: '45deg' }]
        }
      ]} />
      <View style={[
        styles.circle, 
        { 
          borderColor: color,
          width: size * 0.7,
          height: size * 0.7,
          borderWidth: size * 0.1
        }
      ]} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  circle: {
    borderRadius: 100,
    position: 'absolute',
    top: 0,
    left: 0,
  },
  handle: {
    position: 'absolute',
    borderRadius: 2,
  }
});

export default SearchIcon;
