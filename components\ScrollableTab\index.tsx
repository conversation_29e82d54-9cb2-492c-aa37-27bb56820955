import React, {memo, useCallback, useRef, useState} from 'react';
import {StyleSheet, TextStyle, View, ViewStyle} from 'react-native';
import Animated, {event, set, Value} from 'react-native-reanimated';
import Container from '@/components/Layout/Container';
import {width} from '@/constants/Const';
import ScrollTab from './ScrollTab';

interface Props {
  titles: string[];
  children: any;
  tabStyle?: ViewStyle;
  renderHeader?: any;
  labelStyle?: TextStyle;
  titleSize?: number;
  titleFocusStyle?: boolean;
}

const ScrollableTab = memo(
  ({
    titles,
    children,
    tabStyle,
    renderHeader,
    labelStyle,
    titleSize,
    titleFocusStyle,
  }: Props) => {
    const [layouts, setLayouts] = useState<number[]>([]);
    const [indexPage, setIndex] = useState(0);
    const scrollX = useRef(new Value(0)).current;
    const scrollRef: any = useRef<Animated.ScrollView>(null);
    const tabScrollRef: any = useRef<Animated.ScrollView>(null);
    const onScrollTo = useCallback(
      (index: number, isPress?: boolean) => {
        let _layouts = [...layouts];
        _layouts.sort(function (a, b) {
          return a - b;
        });
        if (isPress) {
          setIndex(index);
        }
        tabScrollRef.current?.scrollTo({
          x: _layouts[index] - 28,
          y: 0,
          animated: true,
        });
      },
      [layouts],
    );
    const onPressTab = useCallback(
      (index: number) => {
        scrollRef?.current?.scrollTo({x: width * index, y: 0, animated: true});
        onScrollTo(index);
      },
      [onScrollTo],
    );

    const _renderHeader = useCallback(() => {
      if (!renderHeader) {
        return null;
      }
      if (renderHeader) {
        return renderHeader(indexPage);
      }
    }, [renderHeader, indexPage]);

    return (
      <Container style={styles.container}>
        {_renderHeader()}

        <View style={[styles.scrollableTab, tabStyle]}>
          <ScrollTab
            {...{
              scrollX,
              titles,
              onPressTab,
              tabScrollRef,
              onScrollTo,
              indexPage,
              setIndex,
              layouts,
              setLayouts,
              labelStyle,
              titleSize,
              titleFocusStyle,
            }}
          />
        </View>
        <Animated.ScrollView
          ref={scrollRef}
          horizontal
          pagingEnabled
          scrollEventThrottle={16}
          onScroll={event([
            {
              nativeEvent: {
                contentOffset: {x: (x: any) => set(scrollX, x)},
              },
            },
          ])}
          showsHorizontalScrollIndicator={false}>
          {children}
        </Animated.ScrollView>
      </Container>
    );
  },
);

export default ScrollableTab;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollableTab: {
    flexDirection: 'row',
    paddingTop: 8,
  },
});
