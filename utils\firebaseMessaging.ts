import messaging, { FirebaseMessagingTypes } from '@react-native-firebase/messaging';
import { getFirebaseApp } from './firebaseConfig';

let messagingInstance: FirebaseMessagingTypes.Module | null = null;

export function getMessagingInstance(): FirebaseMessagingTypes.Module {
  if (messagingInstance) {
    return messagingInstance;
  }

  try {
    const app = getFirebaseApp();
    
    messagingInstance = messaging();
    
    console.log('Firebase messaging instance created successfully');
    return messagingInstance;
  } catch (error) {
    console.error('Error creating Firebase messaging instance:', error);
    messagingInstance = messaging();
    return messagingInstance;
  }
}

export async function getMessagingToken(): Promise<string | null> {
  try {
    const messagingService = getMessagingInstance();
    const token = await messagingService.getToken();
    return token;
  } catch (error) {
    console.error('Error getting FCM token:', error);
    return null;
  }
}

export function setBackgroundMessageHandler(
  handler: (message: FirebaseMessagingTypes.RemoteMessage) => Promise<any>
) {
  const messagingService = getMessagingInstance();
  messagingService.setBackgroundMessageHandler(handler);
}

export function onMessage(
  handler: (message: FirebaseMessagingTypes.RemoteMessage) => any
): () => void {
  const messagingService = getMessagingInstance();
  return messagingService.onMessage(handler);
}

export function onNotificationOpenedApp(
  handler: (message: FirebaseMessagingTypes.RemoteMessage) => any
): () => void {
  const messagingService = getMessagingInstance();
  return messagingService.onNotificationOpenedApp(handler);
}

export async function getInitialNotification(): Promise<FirebaseMessagingTypes.RemoteMessage | null> {
  const messagingService = getMessagingInstance();
  return await messagingService.getInitialNotification();
}

export async function requestPermission(): Promise<FirebaseMessagingTypes.AuthorizationStatus> {
  const messagingService = getMessagingInstance();
  return await messagingService.requestPermission();
}

export async function isDeviceRegisteredForRemoteMessages(): Promise<boolean> {
  const messagingService = getMessagingInstance();
  return await messagingService.isDeviceRegisteredForRemoteMessages;
}

export default {
  getMessagingInstance,
  getMessagingToken,
  setBackgroundMessageHandler,
  onMessage,
  onNotificationOpenedApp,
  getInitialNotification,
  requestPermission,
  isDeviceRegisteredForRemoteMessages,
}; 