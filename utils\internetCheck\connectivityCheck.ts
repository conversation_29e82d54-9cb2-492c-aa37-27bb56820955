import NetInfo, { NetInfoState, NetInfoSubscription } from '@react-native-community/netinfo';
import { Alert, Platform } from 'react-native';

/**
 * Configuration options for connectivity checks
 */
export interface ConnectivityOptions {
  /** Whether to show alerts for connectivity issues (default: true) */
  showAlerts?: boolean;
  /** Custom message for no network connection */
  noConnectionMessage?: string;
  /** Custom message for no internet reachability */
  noInternetMessage?: string;
  /** Timeout for connectivity check in milliseconds (default: 5000) */
  timeout?: number;
}

const DEFAULT_OPTIONS: ConnectivityOptions = {
  showAlerts: true,
  noConnectionMessage: 'No internet connection. Please connect to a network.',
  noInternetMessage: 'Internet is not reachable. Please check your connection.',
  timeout: 5000
};

/**
 * Checks if the device has an active internet connection
 * 
 * @param options Configuration options for the connectivity check
 * @returns Promise resolving to a boolean indicating if internet is available
 */
export const isInternetAvailable = async (
  options: ConnectivityOptions = DEFAULT_OPTIONS
): Promise<boolean> => {
  // Merge with default options
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  try {
    // Set up timeout for connectivity check
    const timeoutPromise = new Promise<boolean>((resolve) => {
      setTimeout(() => resolve(false), opts.timeout);
    });
    
    // Fetch network state
    const networkStatePromise = NetInfo.fetch()
      .then(state => {
        // Check if connected to a network
        if (!state.isConnected) {
          if (opts.showAlerts) {
            Alert.alert('Error', opts.noConnectionMessage || DEFAULT_OPTIONS.noConnectionMessage!, [{ text: 'OK' }]);
          }
          return false;
        }
        
        // Check if internet is reachable
        if (state.isInternetReachable === false) {
          if (opts.showAlerts) {
            Alert.alert('Error', opts.noInternetMessage || DEFAULT_OPTIONS.noInternetMessage!, [{ text: 'OK' }]);
          }
          return false;
        }
        
        // Assume internet is available if connected and not explicitly unreachable
        const isReachable = state.isInternetReachable === true || state.isInternetReachable === null;
        return state.isConnected && isReachable;
      });
      
    // Race between network check and timeout
    return Promise.race([networkStatePromise, timeoutPromise]);
  } catch (err) {
    console.error('Error checking internet connection:', err);
    return false;
  }
};

/**
 * NetworkListener callback type
 */
export type NetworkCallback = (isConnected: boolean, state: NetInfoState) => void;

/**
 * Sets up a listener for network state changes
 * 
 * @param callback Function to call when network state changes
 * @returns Unsubscribe function to remove the listener
 */
export const setupNetworkListener = (callback: NetworkCallback): NetInfoSubscription => {
  return NetInfo.addEventListener(state => {
    // Consider connected if isConnected is true and isInternetReachable is not explicitly false
    const isReachable = state.isInternetReachable === true || state.isInternetReachable === null;
    const isConnected = !!state.isConnected && isReachable;
    callback(isConnected, state);
  });
};
