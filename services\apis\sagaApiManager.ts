import axios, { AxiosError, AxiosResponse } from 'axios';
import { ServerError } from '@/models';
import URLS from '../config/config';
import { getAuthToken } from '../secure-storage';
import { ApiResponse } from './apiManager';

/**
 * Makes a POST request with authentication token specifically for patient data
 * @param endpoint The API endpoint to call (without base URL)
 * @param params Request body parameters
 * @returns Promise with the response or error details
 */
async function apiPostWithTokenBase<T = any>(
  endpoint: string,
  params: Record<string, any>
): Promise<ApiResponse<T>> {
  try {
    const token = await getAuthToken();
    if (!token) {
      const errorMsg = 'Authentication token not found';
      return {
        error: {
          message: errorMsg,
          status: 401
        },
        message: errorMsg // backward compatibility
      };
    }

    const url = URLS.caregiverUrl + endpoint;
    
    const response = await axios.post<T>(url, params, {
      headers: {
        Authorization: 'Bearer ' + token,
        'Content-Type': 'application/json',
      },
      timeout: 15000, // 15 seconds timeout
    });
    
    // Return both new format and backward compatible format
    return {
      data: response.data,
      status: response.status,
      // Backward compatibility
      response: {
        status: response.status,
        data: response.data
      }
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMsg = axiosError.message || 'Server Error !!';
    
    // Return both new format and backward compatible format
    return {
      error: {
        message: errorMsg,
        status: axiosError.response?.status,
        details: axiosError.response?.data
      },
      // Backward compatibility
      message: errorMsg
    };
  }
}

/**
 * Gets the patient list for a nurse/physician with pagination support
 * @param params Request parameters including careGiverId, orgId, roleType, pageNumber, pageSize
 * @returns Promise with the paginated patient list response
 */
export const getPatientListCall = async <T = any>(params: Record<string, any>): Promise<ApiResponse<T>> => {
  return apiPostWithTokenBase<T>('getpatientsfornurseV2', params);
};

/**
 * Gets user profile details for caregiver or physician
 * @param params Request parameters including careGiverId and roleType
 * @returns Promise with the clinician details response
 */
export const getUserProfiletCall = async <T = any>(params: Record<string, any>): Promise<ApiResponse<T>> => {
  return apiPostWithTokenBase<T>('getClinicianDetailsById', params);
};

/**
 * Gets all alerts for a nurse/physician
 * @param params Request parameters including careGiverId, orgId, startDate, endDate
 * @returns Promise with the alerts response
 */
export const getAllAlertsCall = async <T = any>(params: Record<string, any>): Promise<ApiResponse<T>> => {
  return apiPostWithTokenBase<T>('getAlertbynurseid', params);
};

/**
 * Gets tasks for a user (caregiver or physician)
 * @param params Request parameters including userId, orgId, roleId and filters
 * @returns Promise with the tasks response
 */
export const getTasksApiCall = async <T = any>(params: Record<string, any>): Promise<ApiResponse<T>> => {
  return apiPostWithTokenBase<T>('getTasks', params);
};

/**
 * Gets patients for summary/tasks for a user (caregiver or physician)
 * @param params Request parameters including userId, orgId, roleId
 * @returns Promise with the patients for tasks response
 */
export const getPatientsForTasksCall = async <T = any>(params: Record<string, any>): Promise<ApiResponse<T>> => {
  return apiPostWithTokenBase<T>('getPatientsForTasks', params);
};

/**
 * Gets monthly summary for a user (caregiver or physician)
 * @param params Request parameters including userId, orgId, roleType, startDate, endDate
 * @returns Promise with the monthly summary response
 */
export const getMonthlySummaryCall = async <T = any>(params: Record<string, any>): Promise<ApiResponse<T>> => {
  return apiPostWithTokenBase<T>('monthlySummary', params);
};

/**
 * Gets organization details for a user
 * @param params Request parameters including userId
 * @returns Promise with the organization response
 */
export const getOrganizationCall = async <T = any>(params: Record<string, any>): Promise<ApiResponse<T>> => {
  return apiPostWithTokenBase<T>('getOrganization', params);
};
