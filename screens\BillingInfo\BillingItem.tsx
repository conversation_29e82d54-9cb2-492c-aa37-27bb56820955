import React from 'react';
import {StyleSheet, View} from 'react-native';
import Layout from '@/components/Layout/Layout';
import Text from '@/components/Text';
import {Colors} from '@/constants';
import {width} from '@/constants/Const';
import Theme from '@/constants/Theme';

interface Props {
  index: number;
  patientId: string;
  patientName: string;
  billingStatus: string;
  dob: string;
  noOfDays: number;
  totalMins: number;
}

const BillingItem = (props: Props) => {
  const checkBackgroundColor = (bStatus: any) => {
    console.log(bStatus);
    if (bStatus === 'IN PROGRESS') {
      return '#90EE90';
    } else {
      return '#D9512C';
    }
  };

  return (
    <View
      style={{
        ...styles.container,
      }}
      key={props.patientId}>
      <Layout
        style={{
          ...styles.content,
          backgroundColor: Colors.Snow,
        }}>
        <View
          style={{
            ...Theme.flexRow,
            // height: 50,
          }}>
          <Text
            marginBottom={8}
            color={checkBackgroundColor(props.billingStatus)}>
            {'\u2B24'}
            {'  '}
          </Text>
          <Text
            bold
            size={16}
            color={checkBackgroundColor(props.billingStatus)}>
            {props.billingStatus?.replace(/[^a-zA-Z0-9]/g, ' ')}
          </Text>
        </View>

        <View style={styles.contentView}>
          <View style={{...Theme.flexRow, marginTop: 5}}>
            <Text>Patient Name: </Text>
            <Text bold>{props.patientName}</Text>
          </View>

          <View style={{...Theme.flexRow, marginTop: 10}}>
            <Text>Vital Measured (Days): </Text>
            <Text bold>{props.noOfDays}</Text>
          </View>
          <View style={{...Theme.flexRow, marginTop: 10}}>
            <Text>Phone Minutes: </Text>
            <Text bold>{props.totalMins}</Text>
          </View>
          <View style={{...Theme.flexRow, marginTop: 10}}>
            <Text>Date Of Birth: </Text>
            <Text bold>{props.dob}</Text>
          </View>
        </View>
      </Layout>
    </View>
  );
};

export default BillingItem;

const styles = StyleSheet.create({
  container: {
    width: width,
    flex: 1,
    padding: 2,
  },

  content: {
    marginVertical: 5,
    marginHorizontal: 10,
    padding: 10,
    borderRadius: 5,
    shadowOffset: {width: 10, height: 10},
  },

  contentView: {
    borderTopColor: Colors.Black68,
    borderTopWidth: 1,
    marginTop: 5,
    paddingTop: 10,
  },
});
