import { Stack, usePathname } from 'expo-router';
import { View, Dimensions } from 'react-native';
import NavigationHeader from '@/components/NavigationHeader';

export default function ProfileLayout() {
  const pathname = usePathname();
  const screenHeight = Dimensions.get('window').height;

  // Determine the title based on the pathname
  const getTitle = () => {
    if (pathname.includes('update')) {
      return 'Update Profile';
    } else if (pathname.includes('change-password')) {
      return 'Change Password';
    } else {
      return 'Profile';
    }
  };

  return (
    <View style={{ flex: 1, height: screenHeight }}>
      <NavigationHeader
        title={getTitle()}
        showBackButton={true}
        showLogout={false}
        showLogo={true}
        showRightLogo={false}
      />
      <Stack
        screenOptions={{
          headerShown: false,
        }}
      >
      <Stack.Screen name="update" />
      <Stack.Screen name="change-password" />
    </Stack>
    </View>
  );
}