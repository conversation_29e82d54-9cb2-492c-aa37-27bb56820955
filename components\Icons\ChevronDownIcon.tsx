import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Colors } from '@/constants';

interface ChevronDownIconProps {
  color?: string;
  size?: number;
}

const ChevronDownIcon: React.FC<ChevronDownIconProps> = ({ 
  color = Colors.GrayBlue, 
  size = 12 
}) => {
  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <View style={[
        styles.leftLine, 
        { 
          backgroundColor: color,
          width: size * 0.5,
          height: size * 0.15,
          left: 0,
          transform: [{ rotate: '45deg' }]
        }
      ]} />
      <View style={[
        styles.rightLine, 
        { 
          backgroundColor: color,
          width: size * 0.5,
          height: size * 0.15,
          right: 0,
          transform: [{ rotate: '-45deg' }]
        }
      ]} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  leftLine: {
    position: 'absolute',
    borderRadius: 1,
    top: '50%',
  },
  rightLine: {
    position: 'absolute',
    borderRadius: 1,
    top: '50%',
  }
});

export default ChevronDownIcon;
