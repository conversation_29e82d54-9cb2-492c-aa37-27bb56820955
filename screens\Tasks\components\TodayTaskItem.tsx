import { Ionicons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import moment from "moment";
import React, { useState } from "react";
import { Alert, StyleSheet, TouchableOpacity, View } from "react-native";

import Layout from "@/components/Layout/Layout";
import Text from "@/components/Text";
import { Colors } from "@/constants";
import { TodayTaskItem } from "@/models";
import TaskService from "@/screens/Schedule/services/TaskService";

interface Props {
  task: TodayTaskItem;
  onPress: () => void;
  onRefresh: () => void;
  onDelete?: (taskId: string) => void;
  onEdit?: (task: TodayTaskItem) => void;
}

const TodayTaskItemComponent: React.FC<Props> = ({
  task,
  onPress,
  onRefresh,
  onDelete,
  onEdit,
}) => {
  const navigation = useNavigation();
  const [isDeleting, setIsDeleting] = useState(false);

  // Handle edit task (same functionality as calendar tasks)
  const handleEditTask = () => {
    if (onEdit) {
      onEdit(task);
    } else {
      onPress(); // Fallback to original onPress
    }
  };

  // Get priority color
  const getPriorityColor = (priority: string): string => {
    switch (priority.toLowerCase()) {
      case "low":
        return "#059669"; // Modern green
      case "medium":
        return "#D97706"; // Modern amber
      case "high":
        return "#DC2626"; // Modern red
      case "critical":
        return "#7C2D12"; // Dark red
      default:
        return Colors.TealBlue;
    }
  };

  // Get priority background color
  const getPriorityBgColor = (priority: string): string => {
    switch (priority.toLowerCase()) {
      case "low":
        return "#ECFDF5"; // Very light green
      case "medium":
        return "#FFFBEB"; // Very light amber
      case "high":
        return "#FEF2F2"; // Very light red
      case "critical":
        return "#FEF2F2"; // Very light red
      default:
        return "#F0F9FF"; // Very light blue
    }
  };

  // Format time
  const formatTime = (dateString: string): string => {
    return moment(dateString).format("h:mm A");
  };

  // Format duration
  const getDuration = (): string => {
    const start = moment(task.start);
    const end = moment(task.end);
    const duration = moment.duration(end.diff(start));

    const hours = Math.floor(duration.asHours());
    const minutes = duration.minutes();

    if (hours > 0) {
      return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
    }
    return `${minutes}m`;
  };

  // Check if task is ongoing
  const isOngoing = (): boolean => {
    const now = moment();
    const start = moment(task.start);
    const end = moment(task.end);
    return now.isBetween(start, end);
  };

  // Check if task is upcoming (within next 30 minutes)
  const isUpcoming = (): boolean => {
    const now = moment();
    const start = moment(task.start);
    return start.isAfter(now) && start.diff(now, "minutes") <= 30;
  };

  // Get status indicator
  const getStatusIndicator = () => {
    if (isOngoing()) {
      return {
        color: Colors.ForestGreen,
        text: "ONGOING",
        icon: "play-circle",
      };
    }
    if (isUpcoming()) {
      return { color: Colors.Orange, text: "UPCOMING", icon: "time" };
    }
    return { color: Colors.GrayBlue, text: "SCHEDULED", icon: "calendar" };
  };

  const status = getStatusIndicator();
  const priorityColor = getPriorityColor(task.priority);
  const priorityBgColor = getPriorityBgColor(task.priority);

  // Handle delete task
  const handleDeleteTask = () => {
    Alert.alert(
      "Delete Task",
      "Are you sure you want to delete this task? This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: () => deleteTask(),
        },
      ]
    );
  };

  // Delete task using TaskService (same as calendar tasks)
  const deleteTask = async () => {
    setIsDeleting(true);
    try {
      console.log("Deleting today task with ID:", task.id);

      // Use TaskService.deleteTask method (same as calendar tasks)
      await TaskService.deleteTask(task.id);

      console.log("Today task deleted successfully");
      if (onDelete) {
        onDelete(task.id);
      }
      onRefresh(); // Refresh the task list
      Alert.alert("Success", "Task deleted successfully!");
    } catch (error: any) {
      console.error("Error deleting today task:", error);
      const errorMessage =
        error?.message || "Failed to delete task. Please try again.";
      Alert.alert("Error", errorMessage);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <View style={styles.container}>
      <Layout style={styles.content}>
        {/* Main Content - Tappable */}
        <TouchableOpacity
          onPress={handleEditTask}
          activeOpacity={0.7}
          style={styles.mainContent}
        >
          {/* Header with status and priority */}
          <View style={styles.header}>
            <View style={styles.statusContainer}>
              <View
                style={[styles.statusDot, { backgroundColor: status.color }]}
              />
              <Text size={10} color={status.color} bold lineHeight={12}>
                {status.text}
              </Text>
            </View>
            <View
              style={[
                styles.priorityBadge,
                { backgroundColor: priorityBgColor },
              ]}
            >
              <Text
                size={9}
                color={priorityColor}
                bold
                style={styles.priorityText}
                lineHeight={11}
              >
                {task.priority.toUpperCase()}
              </Text>
            </View>
          </View>

          {/* Task Title */}
          <View style={styles.titleSection}>
            <Text
              size={16}
              bold
              color={Colors.DarkJungleGreen}
              numberOfLines={2}
              lineHeight={18}
            >
              {task.title}
            </Text>
          </View>

          {/* Patient Info */}
          <View style={styles.patientSection}>
            <Ionicons name="person" size={14} color={Colors.TealBlue} />
            <Text
              size={12}
              color={Colors.TealBlue}
              bold
              style={styles.patientText}
              lineHeight={14}
            >
              {task.patinetName}
            </Text>
          </View>

          {/* Time Information */}
          <View style={styles.timeSection}>
            <View style={styles.timeRow}>
              <Ionicons name="time" size={14} color={Colors.GrayBlue} />
              <Text
                size={12}
                color={Colors.GrayBlue}
                style={styles.timeText}
                lineHeight={14}
              >
                {formatTime(task.start)} - {formatTime(task.end)}
              </Text>
              <View style={styles.durationBadge}>
                <Text size={10} color={Colors.GrayBlue} lineHeight={12}>
                  {getDuration()}
                </Text>
              </View>
            </View>
          </View>

          {/* Description */}
          {task.description && (
            <View style={styles.descriptionSection}>
              <Text
                size={12}
                color={Colors.Black68}
                numberOfLines={2}
                style={styles.description}
                lineHeight={14}
              >
                {task.description}
              </Text>
            </View>
          )}
        </TouchableOpacity>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.editButton}
            onPress={handleEditTask}
            activeOpacity={0.7}
          >
            <Ionicons name="create-outline" size={16} color={Colors.TealBlue} />
            <Text
              size={12}
              color={Colors.TealBlue}
              bold
              style={styles.actionButtonText}
              lineHeight={14}
            >
              Edit
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.deleteButton,
              isDeleting && styles.deleteButtonDisabled,
            ]}
            onPress={handleDeleteTask}
            disabled={isDeleting}
            activeOpacity={0.7}
          >
            <Ionicons
              name={isDeleting ? "hourglass-outline" : "trash-outline"}
              size={16}
              color={isDeleting ? Colors.GrayBlue : Colors.pastelRed}
            />
            <Text
              size={12}
              color={isDeleting ? Colors.GrayBlue : Colors.pastelRed}
              bold
              style={styles.actionButtonText}
              lineHeight={14}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </Text>
          </TouchableOpacity>
        </View>
      </Layout>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 8,
  },
  content: {
    padding: 12,
    backgroundColor: Colors.White,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  mainContent: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 4,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.06)",
    minWidth: 50,
    alignItems: "center",
  },
  titleSection: {
    marginBottom: 6,
  },
  patientSection: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 6,
  },
  patientText: {
    marginLeft: 4,
    flex: 1,
  },
  timeSection: {
    marginBottom: 6,
  },
  timeRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  timeText: {
    marginLeft: 4,
    flex: 1,
  },
  durationBadge: {
    backgroundColor: Colors.Snow,
    paddingHorizontal: 6,
    paddingVertical: 1,
    borderRadius: 4,
  },
  descriptionSection: {
    marginBottom: 4,
  },
  description: {
    lineHeight: 16,
  },
  priorityText: {
    letterSpacing: 0.5,
    fontWeight: "600",
  },
  actionButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: Colors.Snow,
  },
  editButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: Colors.Snow,
    flex: 1,
    marginRight: 6,
    justifyContent: "center",
  },
  deleteButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: "#FEF2F2",
    flex: 1,
    marginLeft: 6,
    justifyContent: "center",
  },
  deleteButtonDisabled: {
    backgroundColor: Colors.Snow,
    opacity: 0.6,
  },
  actionButtonText: {
    marginLeft: 4,
    fontSize: 12,
    fontWeight: "600",
  },
});

export default TodayTaskItemComponent;
