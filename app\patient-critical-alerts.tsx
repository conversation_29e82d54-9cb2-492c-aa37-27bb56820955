import React from 'react';
import { Stack, useLocalSearchParams } from 'expo-router';
import { Colors } from '@/constants';
import CriticalAlertsScreen from '@/screens/PatientDetails/CriticalAlertsScreen';

/**
 * Patient Critical Alerts Screen Route
 * This screen displays critical alerts specific to the current patient
 */
export default function PatientCriticalAlertsRoute() {
  const params = useLocalSearchParams();
  const patientName = params.patientName as string || 'Patient';
  const patientId = params.patientId as string;
  
  return (
    <>
      <Stack.Screen 
        options={{
          headerShown: false // Hide the Stack.Screen header
        }} 
      />
      <CriticalAlertsScreen 
        patientId={patientId} 
        patientName={patientName} 
      />
    </>
  );
}
