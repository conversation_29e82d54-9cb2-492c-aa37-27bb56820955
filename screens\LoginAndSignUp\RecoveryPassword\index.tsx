import {
  StackActions,
  useFocusEffect,
  useNavigation,
  useRoute
} from '@react-navigation/native';
import React, { memo, useCallback, useState } from 'react';
import { 
  Alert, 
  Image, 
  ScrollView, 
  StyleSheet, 
  View,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useDispatch } from 'react-redux';
import { getStatusBarHeight } from 'react-native-iphone-x-helper';
import ButtonLinear from '@/components/Buttons/ButtonLinear';
import HeaderButton from '@/components/HeaderButton';
import InputApp from '@/components/InputApp';
import Loader from '@/components/Loader/Loader';
import Text from '@/components/Text';
import { Colors, Routes } from '@/constants';
import { userLogOut } from '@/services/actions/logOutActions';
import { apiPost } from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import { resetCredentials } from '@/services/secure-storage';
import { isInternetAvailable } from '@/utils/internetCheck/connectivityCheck';
import passwordValidation from '@/utils/validation/PasswordValidation';

interface RecoveryPasswordProps {}

const RecoveryPassword = memo((props: RecoveryPasswordProps) => {
  const [resetCode, setResetCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [visibleNewPassword, setVisibleNewPassword] = useState(false);
  const [visibleConfirmPassword, setVisibleConfirmPassword] = useState(false);
  const [newPasswordError, setNewPasswordError] = useState('');
  const [touched, setTouched] = useState(false);
  const [loader, setLoader] = useState(false);
  const [otpTouched, setOtpTouched] = useState(false);
  const [email, setEmail] = useState<string>('');

  const navigation = useNavigation();
  const dispatch = useDispatch();
  const route = useRoute();

  const onShowHideNewPassword = useCallback(() => {
    setVisibleNewPassword(prev => !prev);
  }, []);

  const onShowHideConfirmPassword = useCallback(() => {
    setVisibleConfirmPassword(prev => !prev);
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      if (route?.params) {
        const params = route.params as { email: string };
        setEmail(params.email || '');
      }
    }, [route?.params]),
  );

  const submitPasswordReset = async () => {
    setLoader(true);

    try {
      const response = await apiPost(
        URLS.authUrl + 'caregiver/validateOTPAndSetPassword',
        {
          emailId: email,
          otp: resetCode,
          password: confirmPassword,
        },
      );

      setLoader(false);
      
      if (response?.status === 200) {
        if (response?.data?.responseMessage === 'Success') {
          resetCredentials();
          dispatch(userLogOut());
          navigation.dispatch(
            StackActions.replace(Routes.ChangePasswordSuccessful, {}),
          );
        } else {
          Alert.alert('Error', 'Password change failed. Please try again.', [
            {text: 'Dismiss'},
          ]);
        }
      } else {
        const errorMessage = response?.response?.data?.responseMessage
          ? response?.response?.data?.responseMessage
          : response.message === 'Network Error'
          ? 'Network error. Please check your data connection.'
          : response.message || 'An unknown error occurred';
        
        Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
      }
    } catch (error) {
      setLoader(false);
      Alert.alert(
        'Error', 
        'An unexpected error occurred. Please try again.', 
        [{text: 'Dismiss'}]
      );
    }
  };

  const isFormValid = () => {
    return (
      resetCode?.length === 6 &&
      newPasswordError?.length === 0 &&
      newPassword?.length > 8 &&
      confirmPassword?.length > 0 &&
      confirmPassword === newPassword
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      <Loader modalVisible={loader} />
      
      {/* Background Gradient */}
      <LinearGradient
        colors={['#f8fffe', '#e8f8f7', '#ffffff']}
        style={styles.background}
      />

      <KeyboardAvoidingView 
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header */}
          <View style={styles.headerContainer}>
            <HeaderButton style={styles.headerButton} />
          </View>

          {/* Main Content */}
          <View style={styles.mainContent}>
            {/* Icon Section */}
            <View style={styles.iconContainer}>
              <View style={styles.iconWrapper}>
                <Image 
                  source={require('@/assets/images/ic_accept.png')} 
                  style={styles.keyIcon}
                  resizeMode="contain"
                />
              </View>
            </View>

            {/* Text Section */}
            <View style={styles.textSection}>
              <Text style={styles.title}>Reset Password</Text>
              <Text style={styles.subtitle}>
                Enter the 6-digit code sent to {email} and create your new password.
              </Text>
            </View>

            {/* Form Section */}
            <View style={styles.formSection}>
              {/* Reset Code Input */}
              <InputApp
                title="Verification Code"
                value={resetCode}
                onChangeText={text => {
                  setResetCode(text);
                  setOtpTouched(true);
                }}
                placeholder="Enter 6-digit code"
                keyboardType="number-pad"
                borderColor={resetCode.length === 6 ? Colors.TiffanyBlue : Colors.InnearColor}
                style={styles.input}
              />

              {otpTouched && resetCode?.length < 6 && resetCode.length > 0 && (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>
                    Verification code must be 6 digits
                  </Text>
                </View>
              )}

              {/* New Password */}
              <InputApp
                title="New Password"
                value={newPassword}
                onChangeText={text => {
                  setNewPassword(text);
                  if (text) {
                    if (passwordValidation(text)) {
                      setNewPasswordError('');
                    } else {
                      setNewPasswordError(
                        'Password must be alphanumeric with min 8 - max 16 characters, 1 uppercase, 1 lowercase, 1 special character and no space.',
                      );
                    }
                  } else {
                    setNewPasswordError('');
                  }
                }}
                placeholder="Create new password"
                marginTop={24}
                secureTextEntry={!visibleNewPassword}
                isShowIcon={true}
                borderColor={newPassword.length > 0 ? Colors.TiffanyBlue : Colors.InnearColor}
                icon={
                  <Image
                    source={require('@/assets/images/ic_eye_on.png')}
                    style={styles.inputIcon}
                  />
                }
                iconPress={onShowHideNewPassword}
                style={styles.input}
              />

              {newPasswordError !== '' && (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>{newPasswordError}</Text>
                </View>
              )}

              {/* Confirm Password */}
              <InputApp
                title="Confirm New Password"
                value={confirmPassword}
                onChangeText={text => {
                  setConfirmPassword(text);
                  setTouched(true);
                }}
                placeholder="Confirm new password"
                marginTop={24}
                secureTextEntry={!visibleConfirmPassword}
                isShowIcon={true}
                borderColor={confirmPassword.length > 0 ? Colors.TiffanyBlue : Colors.InnearColor}
                icon={
                  <Image
                    source={require('@/assets/images/ic_eye_on.png')}
                    style={styles.inputIcon}
                  />
                }
                iconPress={onShowHideConfirmPassword}
                style={styles.input}
              />

              {touched && confirmPassword !== newPassword && confirmPassword.length > 0 && (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>Passwords do not match</Text>
                </View>
              )}

              {/* Submit Button */}
              <View style={styles.buttonContainer}>
                <ButtonLinear
                  title="Reset Password"
                  onPress={async () => {
                    if (await isInternetAvailable()) {
                      submitPasswordReset();
                    }
                  }}
                  disabled={!isFormValid()}
                  style={styles.submitButton}
                />
              </View>

              {/* Help Section */}
              <View style={styles.helpSection}>
                <Text style={styles.helpText}>
                  Didn't receive the code?{' '}
                  <Text 
                    style={styles.helpLink}
                    onPress={() => navigation.goBack()}
                  >
                    Try again
                  </Text>
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
});

export default RecoveryPassword;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.White,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: getStatusBarHeight(),
    paddingBottom: 40,
  },
  headerContainer: {
    marginBottom: 20,
  },
  headerButton: {
    marginTop: 0,
  },
  mainContent: {
    flex: 1,
    justifyContent: 'center',
    paddingVertical: 20,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  iconWrapper: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.TiffanyBlueOpacity,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.TiffanyBlue,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  keyIcon: {
    width: 40,
    height: 40,
    tintColor: Colors.TiffanyBlue,
  },
  textSection: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.DarkJungleGreen,
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: Colors.GrayBlue,
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  formSection: {
    marginBottom: 40,
  },
  input: {
    borderRadius: 12,
    backgroundColor: Colors.White,
    shadowColor: Colors.Black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  inputIcon: {
    width: 20,
    height: 20,
    tintColor: Colors.TiffanyBlue,
  },
  errorContainer: {
    marginTop: 8,
    paddingHorizontal: 12,
  },
  errorText: {
    fontSize: 12,
    color: Colors.RedNeonFuchsia,
    lineHeight: 16,
  },
  buttonContainer: {
    marginTop: 32,
  },
  submitButton: {
    height: 56,
    borderRadius: 16,
    shadowColor: Colors.TiffanyBlue,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  helpSection: {
    alignItems: 'center',
    marginTop: 24,
  },
  helpText: {
    fontSize: 16,
    color: Colors.GrayBlue,
    textAlign: 'center',
  },
  helpLink: {
    color: Colors.TiffanyBlue,
    fontWeight: '600',
  },
});
