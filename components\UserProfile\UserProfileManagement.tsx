import React, {memo, useMemo} from 'react';
import {Image, StyleSheet, View} from 'react-native';
import {UserProfileProps} from '@/models';
import Text from '../Text';

const UserProfileManagement = memo((props: UserProfileProps) => {
  // Get initials for avatar
  const getInitials = useMemo(() => {
    const firstName = props.firstName || props.name?.split(' ')?.[0] || '';
    const lastName = props.lastName || props.name?.split(' ')?.[1] || '';

    const firstInitial = firstName.charAt(0).toUpperCase();
    const lastInitial = lastName.charAt(0).toUpperCase();

    return firstInitial + (lastInitial || '');
  }, [props.firstName, props.lastName, props.name]);

  // We're using a fixed color now (#F9C134) as shown in the screenshot

  return (
    <View style={styles.container}>
      {/* Row layout with avatar on left and info on right */}
      <View style={styles.rowContainer}>
        {/* Left side - Avatar with status indicator */}
        <View style={styles.avatarSection}>
          <View style={styles.avatarContainer}>
            {props.picPath ? (
              <Image style={styles.avatar} source={{ uri: props.picPath }} />
            ) : (
              <View style={[styles.avatar, styles.initialsAvatar, { backgroundColor: '#F9C134' }]}>
                <Text style={styles.initialsText}>{getInitials}</Text>
              </View>
            )}
          </View>
          {/* Status indicator */}
          <View style={styles.statusIndicator} />
        </View>

        {/* Right side - User information */}
        <View style={styles.infoContainer}>
          <Text bold size={20} lineHeight={24} style={styles.nameText}>
            {props.name || 'User'}
          </Text>
          {props.phoneNumber && (
            <Text bold size={16} lineHeight={22} style={styles.phoneText}>
              {props.phoneNumber}
            </Text>
          )}
          {props.userName && (
            <Text size={16} lineHeight={22} style={styles.infoText}>
              {props.userName}
            </Text>
          )}
          {(props.address1 || props.address2) && (
            <Text size={16} lineHeight={22} style={styles.infoText}>
              {props.address1} {props.address2}
            </Text>
          )}
          {(props.state || props.city) && (
            <Text size={16} lineHeight={22} style={styles.infoText}>
              {props.state} {props.city}
            </Text>
          )}
          {(props.country || props.zip) && (
            <Text size={16} lineHeight={22} style={styles.infoText}>
              {props.country} {props.zip}
            </Text>
          )}
        </View>
      </View>
    </View>
  );
});

export default UserProfileManagement;

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'transparent',
    width: '100%',
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarSection: {
    position: 'relative',
    marginRight: 25,
  },
  infoContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  nameText: {
    color: '#000000',
    marginBottom: 6,
    fontWeight: 'bold',
  },
  phoneText: {
    marginBottom: 6,
    color: '#000000',
  },
  infoText: {
    marginBottom: 6,
    color: '#000000',
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  avatarContainer: {
    width: 80,
    height: 80,
  },
  initialsAvatar: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  initialsText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
  },
  statusIndicator: {
    width: 15,
    height: 15,
    borderRadius: 7.5,
    backgroundColor: '#00FF00',
    position: 'absolute',
    top: 5,
    left: 0,
  },
});
