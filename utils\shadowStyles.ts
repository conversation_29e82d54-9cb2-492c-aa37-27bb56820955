import { Platform } from 'react-native';

/**
 * Creates consistent shadow styles across platforms
 *
 * @param elevation - Android elevation value (1-24)
 * @param opacity - iOS shadow opacity (0-1)
 * @param radius - iOS shadow radius
 * @param color - Shadow color
 * @param offset - iOS shadow offset {width, height}
 * @param preserveBackgroundColor - If true, doesn't set a background color on Android (for colored components)
 * @returns Platform-specific shadow styles
 */
export const createShadow = (
  elevation = 2,
  opacity = 0.1,
  radius = 3,
  color = '#000',
  offset = { width: 0, height: 2 },
  preserveBackgroundColor = false
) => {
  return Platform.select({
    ios: {
      shadowColor: color,
      shadowOffset: offset,
      shadowOpacity: opacity,
      shadowRadius: radius,
    },
    android: {
      elevation: elevation,
      // Only add background color if we're not preserving the original background
      // This allows components like headers to keep their original background color
      ...(!preserveBackgroundColor && { backgroundColor: '#fff' }),
    },
  });
};

/**
 * Removes all shadow styles for both platforms
 */
export const removeShadow = {
  ...Platform.select({
    ios: {
      shadowColor: 'transparent',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0,
      shadowRadius: 0,
    },
    android: {
      elevation: 0,
    },
  }),
};
