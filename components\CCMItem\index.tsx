import {useNavigation} from '@react-navigation/native';
import {useRouter} from 'expo-router';
import React, {memo, useCallback} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import { CoreColors, Routes } from '@/constants';
import Theme from '@/constants/Theme';
import CheckBox from '../CheckBox';
import Text from '../Text';

interface ItemProps {
  id: number;
  title: string;
  status: boolean;
  lastUpdated: any;
  patientId: any;
  latestVitals: any[];
}

export default memo((props: ItemProps) => {
  const {navigate} = useNavigation() as any;
  const router = useRouter();

  const onPress = useCallback(() => {
    try {
      // Use Expo Router for navigation
      console.log('Navigating to care plan details with ID:', props.id, 'Patient ID:', props.patientId);
      // Use a static pathname to avoid type errors
      router.push({
        pathname: '/patient/care-plan/[id]' as any,
        params: {
          id: props.patientId,
          carePlanId: props.id.toString(),
          patientId: props.patientId,
          latestVitals: JSON.stringify(props.latestVitals || [])
        }
      });
    } catch (error) {
      console.error('Error navigating to care plan details:', error);
      // Fallback to old navigation
      navigate(Routes.CarePlanDetails, {
        carePlanId: props.id,
        patientId: props.patientId,
        latestVitals: props.latestVitals,
      });
    }
  }, [navigate, router, props.id, props.patientId, props.latestVitals]);

  return (
    <TouchableOpacity style={{...styles.container}} onPress={onPress}>
      <View style={styles.box}>
        <View style={{margin: 2}}>
          <Text bold size={16} lineHeight={18}>
            {props.title}
          </Text>
          <Text bold size={12} lineHeight={16}>
            {'Last Update:'} {props.lastUpdated}
          </Text>
        </View>
        <View style={styles.avatarView}>
          <CheckBox
            isCheck={props.status}
            onPress={() => {}}
            style={styles.avatar}
          />
        </View>
      </View>
    </TouchableOpacity>
  );
});

const styles = StyleSheet.create({
  container: {
    padding: 15,
    borderRadius: 12,
    width: '100%',
    ...Theme.shadow,
    justifyContent: 'center',
    marginBottom: 16,
    flex: 1,
    flexDirection: 'column',
    backgroundColor: CoreColors.WhiteGray,
  },
  box: {
    marginTop: 0,
    paddingLeft: '20%',
    paddingTop: 10,
    paddingBottom: 5,
    paddingRight: 5,
    borderBottomWidth: 0,
    borderBottomColor: CoreColors.Snow,
    height: '100%',
  },
  avatar: {
    width: 40,
    height: 40,
  },
  avatarView: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
});
