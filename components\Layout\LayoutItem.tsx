import React from 'react';
import {
  PressableStateCallbackType,
  View as DefaultView,
  ViewProps,
  ViewStyle,
} from 'react-native';
import {useTheme} from '@/constants/Theme';
interface Props extends ViewProps {
  children?:
    | React.ReactNode
    | ((state: PressableStateCallbackType) => React.ReactNode);
  style?: ViewStyle;
}

const LayoutItem = ({children, style}: Props) => {
  const {theme} = useTheme();
  return (
    <DefaultView style={[{backgroundColor: theme.modalColor}, style]}>
      {children}
    </DefaultView>
  );
};
export default LayoutItem;
