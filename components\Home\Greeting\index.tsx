import React, {memo} from 'react';
import {Image, StyleSheet, View} from 'react-native';
import {useSelector} from 'react-redux';
import Text from '@/components/Text';
import Theme from '@/constants/Theme';

interface GreetingProps {}

const Greeting = memo((props: GreetingProps) => {
  const userProfileDetails = useSelector(
    (state: any) => state?.userProfileReducer?.data?.name,
  );

  return (
    <View style={styles.container}>
      <Image
        source={require('images/avatar/Rounded.png')}
        style={{width: 40, height: 40, marginRight: 16}}
      />
      <View style={{justifyContent: 'space-between', height: 40}}>
        <Text size={17} lineHeight={20} bold>
          Hi {userProfileDetails?.name},
        </Text>
      </View>
    </View>
  );
});

export default Greeting;

const styles = StyleSheet.create({
  container: {
    ...Theme.flexRow,
  },
});
