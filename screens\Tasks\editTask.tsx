import { useRouter } from 'expo-router';
import Moment from 'moment';
import React, {useCallback, useEffect, useState} from 'react';
import {Alert, Image, Modal, ScrollView, StyleSheet, View} from 'react-native';
import {useSelector} from 'react-redux';
import ButtonChangeCategory from '@/components/ButtonChangeCategory';
import ButtonLinear from '@/components/Buttons/ButtonLinear';
import AlertsDatePicker from '@/components/DatePickerButtons/alerts-date-picker';
import InputApp from '@/components/InputApp';
import Container from '@/components/Layout/Container';
import Loader from '@/components/Loader/Loader';
import ModalChangeCategory from '@/components/ModalChangeCategory';
import ModalSlideBottom from '@/components/ModalSlideBottom';
import Text from '@/components/Text';
import Colors from '@/constants/Colors';
import {TASK_PRIORITY, TASK_STATUS} from '@/constants/Data';
import {useTheme} from '@/constants/Theme';
import useModalAnimation from '@/hooks/useModalAnimation';
import {apiPostWithToken} from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import Theme from '@/constants/Theme';
import scale from '@/utils/scale';

interface TaskData {
  taskId: number;
  taskTitle: string;
  taskDesc: string;
  taskPriority: string;
  taskStartDate: string;
  taskEndDate: string;
  taskStatus: string;
  patientId: number;
  patientName: string;
}

interface CategoryOption {
  id: number;
  name: string;
  displayName: string;
}

interface EditTaskProps {
  taskId?: string;
}

/**
 * Edit Task Screen
 * Allows editing of task details
 */

const EditTask = ({ taskId: propTaskId }: EditTaskProps) => {
  const [taskId, setTaskId] = useState(0);
  const [taskTitle, setTaskTitle] = useState('');
  const [taskDesc, setTaskDesc] = useState('');
  const [taskPriority, setTaskPriority] = useState('');
  const [taskStartDate, setTaskStartDate] = useState('');
  const [taskEndDate, setTaskEndDate] = useState('');
  const [taskStatus, setTaskStatus] = useState('');
  const [patientId, setPatientId] = useState(0);
  const [patientName, setPatientName] = useState('');

  const currentTask = useSelector(
    (state: {currentTaskReducer?: {currentTask?: TaskData}}) => 
      state?.currentTaskReducer?.currentTask,
  );

  const caregiverId = useSelector(
    (state: {loginReducer?: {data?: {userId?: string}}}) => 
      state?.loginReducer?.data?.userId,
  );

  const {theme} = useTheme();
  const [endDate, setEndDate] = useState<Date>();
  const [startDate, setStartDate] = useState<Date>();
  const [loader, setLoader] = useState(false);

  const router = useRouter();

  useEffect(() => {

    // Use the task data from Redux store
    setTaskId(currentTask?.taskId || 0);
    setTaskTitle(currentTask?.taskTitle || '');
    setTaskDesc(currentTask?.taskDesc || '');
    setTaskPriority(currentTask?.taskPriority || '');
    setTaskStartDate(currentTask?.taskStartDate || '');
    setTaskEndDate(currentTask?.taskEndDate || '');
    setTaskStatus(currentTask?.taskStatus || '');
    setPatientId(currentTask?.patientId || 0);
    setPatientName(currentTask?.patientName || '');

    let statusOption = TASK_STATUS?.find(
      s => s.name === currentTask?.taskStatus,
    ) as any;
    setCategory(statusOption);

    let priorityOption = TASK_PRIORITY?.find(
      s => s.name === currentTask?.taskPriority,
    ) as any;
    setSelectedPriority(priorityOption);
    let tEndDate = currentTask?.taskEndDate?.split(' ')[0];
    setEndDate(tEndDate ? Moment(tEndDate, 'MM-DD-YYYY').toDate() : new Date());

    let tStartDate = currentTask?.taskStartDate?.split(' ')[0];
    setStartDate(tStartDate ? Moment(tStartDate, 'MM-DD-YYYY').toDate() : new Date());
  }, [currentTask, propTaskId]);

  const {
    open: openCategory,
    close: closeCategory,
    visible: visibleCategory,
    transY: transYCategory,
  } = useModalAnimation();

  const {
    open: openPriority,
    close: closePriority,
    visible: visiblePriority,
    transY: transYPriority,
  } = useModalAnimation();

  const [category, setCategory] = React.useState<CategoryOption>({
    id: 0,
    name: '',
    displayName: '',
  });

  const [selectedPriority, setSelectedPriority] = React.useState<CategoryOption>({
    id: 1,
    name: 'High',
    displayName: 'High',
  });

  const onChangeCategory = useCallback((item: CategoryOption) => {
    setCategory(item);
    closeCategory();
  }, []);

  const onChangePriority = useCallback((item: CategoryOption) => {
    setSelectedPriority(item);
    closePriority();
  }, []);

  const updateTask = async () => {
    let updateTaskObj = {
      taskTitle: taskTitle,
      patientId: patientId,
      taskDesc: taskDesc,
      taskStartDate: Moment(startDate).format('YYYY-MM-DD HH:mm:ss'),
      taskEndDate: Moment(endDate).format('YYYY-MM-DD HH:mm:ss'),
      taskPriority: selectedPriority?.name,
      taskId: taskId,
      taskStatus: category?.name,
      userId: caregiverId,
    };
    setLoader(true);
    const response = await apiPostWithToken(
      updateTaskObj,
      URLS.caregiverUrl + 'saveTasks',
    );
    if (response?.status == 200) {
      setLoader(false);
      Alert.alert('Success', 'Task updated successfully.', [
        {
          text: 'Ok',
          onPress: () => {
            router.back();
          },
        },
      ]);
    } else {
      setLoader(false);
      const errorMessage = response?.response?.data?.responseMessage
        ? response?.response?.data?.responseMessage
        : response.message === 'Network Error'
        ? 'Network error. Please check your data connection.'
        : response.message;
      Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
    }
  };

  const renderHeader1 = () => {
    return (
      <View style={{padding: 5, margin: 10}}>
        <Modal
          visible={visibleCategory}
          onRequestClose={closeCategory}
          transparent
          animationType={'fade'}>
          <ModalSlideBottom onClose={closeCategory} transY={transYCategory}>
            <ModalChangeCategory
              onChangeCategory={onChangeCategory}
              data={TASK_STATUS}
            />
          </ModalSlideBottom>
        </Modal>
      </View>
    );
  };

  const renderHeader2 = () => {
    return (
      <View style={{padding: 5, margin: 0}}>
        <Modal
          visible={visiblePriority}
          onRequestClose={closePriority}
          transparent
          animationType={'fade'}>
          <ModalSlideBottom onClose={closePriority} transY={transYPriority}>
            <ModalChangeCategory
              onChangeCategory={onChangePriority}
              data={TASK_PRIORITY}
            />
          </ModalSlideBottom>
        </Modal>
      </View>
    );
  };

  return (
    <Container style={styles.container}>
      <Loader modalVisible={loader} />
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={Theme.updateProfileContentScrollStyle}>
        <InputApp
          title={'Associated Patient Name'}
          marginTop={0}
          value={patientName}
          onChangeText={setPatientName}
          editable={false}
        />
        <InputApp
          title={'Task title*'}
          marginTop={24}
          value={taskTitle}
          onChangeText={setTaskTitle}
        />
        <InputApp
          title={'Description*'}
          marginTop={24}
          value={taskDesc}
          onChangeText={setTaskDesc}
          multiline={true}
        />

        <Text size={13} lineHeight={16} marginTop={24} semibold>
          Status*
        </Text>
        <View style={styles.phoneView}>
          <ButtonChangeCategory category={category} onPress={openCategory} />
        </View>

        <Text size={13} lineHeight={16} marginTop={24} semibold>
          Priority*
        </Text>
        <View style={styles.phoneView}>
          <ButtonChangeCategory
            category={selectedPriority}
            onPress={openPriority}
          />
        </View>

        <Text size={13} lineHeight={16} marginTop={24} semibold>
          Due Date*
        </Text>
        <View
          style={[styles.datePicker, {backgroundColor: theme.colors.White}]}>
          <AlertsDatePicker
            date={endDate}
            onDateChange={date => setEndDate(date)}
          />
        </View>

        <ButtonLinear
          white
          title={'Update'}
          children={
            <Image
              source={require('@/assets/images/ic_next.png')}
              style={styles.buttonChildren}
            />
          }
          disabled={
            patientName.length === 0 ||
            taskTitle.length === 0 ||
            taskDesc.length === 0 ||
            taskStatus.length === 0 ||
            taskPriority.length === 0 ||
            taskEndDate.length === 0
          }
          onPress={() => {
            updateTask();
          }}
          style={styles.buttonLinear}
        />
        {renderHeader1()}
        {renderHeader2()}
      </ScrollView>
    </Container>
  );
};

export default EditTask;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  buttonChildren: {
    ...Theme.icons,
    marginLeft: 8,
  },
  inputApp: {
    marginTop: 24,
  },
  firstName: {
    marginTop: scale(10),
  },
  homeAddress: {
    marginTop: 32,
  },
  buttonLinear: {
    marginTop: 52,
  },
  phoneView: {
    ...Theme.flexRow,
    marginTop: 4,
  },
  datePicker: {
    // width: 180,
    height: 50,
    borderRadius: 5,
    borderColor: Colors.GrayBlue,
    borderWidth: 1,
    justifyContent: 'center',
    marginTop: 4,
  },
});
