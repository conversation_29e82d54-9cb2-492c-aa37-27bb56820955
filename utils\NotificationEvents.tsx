import { EventEmitter } from 'events';

// Create a global event emitter for FCM notification events
class FCMNotificationEventEmitter extends EventEmitter {
  constructor() {
    super();
    this.setMaxListeners(20); // Increase max listeners to avoid warnings
  }

  // Emit when a notification is marked as read
  notificationRead(notificationId: string) {
    this.emit('notification_read', notificationId);
    this.emit('notifications_changed');
  }

  // Emit when a notification is deleted
  notificationDeleted(notificationId: string) {
    this.emit('notification_deleted', notificationId);
    this.emit('notifications_changed');
  }

  // Emit when all notifications are cleared
  allNotificationsCleared() {
    this.emit('all_notifications_cleared');
    this.emit('notifications_changed');
  }

  // Emit when a new notification arrives
  newNotificationReceived(notification: any) {
    this.emit('new_notification', notification);
    this.emit('notifications_changed');
  }

  // Generic notification change event
  notificationsChanged() {
    this.emit('notifications_changed');
  }

  // Listen for any notification changes
  onNotificationsChanged(callback: () => void) {
    this.on('notifications_changed', callback);
    return () => this.off('notifications_changed', callback);
  }

  // Listen for specific notification read events
  onNotificationRead(callback: (notificationId: string) => void) {
    this.on('notification_read', callback);
    return () => this.off('notification_read', callback);
  }

  // Listen for specific notification delete events
  onNotificationDeleted(callback: (notificationId: string) => void) {
    this.on('notification_deleted', callback);
    return () => this.off('notification_deleted', callback);
  }

  // Listen for new notifications
  onNewNotification(callback: (notification: any) => void) {
    this.on('new_notification', callback);
    return () => this.off('new_notification', callback);
  }

  // Listen for all notifications cleared
  onAllNotificationsCleared(callback: () => void) {
    this.on('all_notifications_cleared', callback);
    return () => this.off('all_notifications_cleared', callback);
  }
}

// Create a singleton instance
export const fcmNotificationEvents = new FCMNotificationEventEmitter();

// Helper function to get unread count from AsyncStorage
import AsyncStorage from '@react-native-async-storage/async-storage';

export const getUnreadNotificationCount = async (): Promise<number> => {
  try {
    // Clean up old notifications first
    await cleanupOldNotifications();
    
    const storedNotifications = await AsyncStorage.getItem('@FCMNotificationHistory');
    if (storedNotifications) {
      const notifications = JSON.parse(storedNotifications);
      const unreadCount = notifications.filter((n: any) => !n.read).length;
      console.log('FCMNotificationEvents: Unread count:', unreadCount, 'Total notifications:', notifications.length);
      return unreadCount;
    }
    console.log('FCMNotificationEvents: No stored notifications found');
    return 0;
  } catch (error) {
    console.error('FCMNotificationEvents: Error getting unread notification count:', error);
    return 0;
  }
};

// Helper function to trigger count update across all listeners
export const triggerNotificationCountUpdate = () => {
  fcmNotificationEvents.notificationsChanged();
};

// Helper function to clean up notifications older than 1 week
export const cleanupOldNotifications = async () => {
  try {
    const storedNotifications = await AsyncStorage.getItem('@FCMNotificationHistory');
    if (!storedNotifications) return;

    const notifications = JSON.parse(storedNotifications);
    const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000); // 1 week in milliseconds
    
    // Filter out notifications older than 1 week
    const recentNotifications = notifications.filter((notification: any) => {
      const notificationTime = notification.timestamp || 0;
      return notificationTime > oneWeekAgo;
    });
    
    if (recentNotifications.length !== notifications.length) {
      await AsyncStorage.setItem('@FCMNotificationHistory', JSON.stringify(recentNotifications));
      console.log(`Cleaned up ${notifications.length - recentNotifications.length} old notifications (older than 1 week)`);
      
      // Trigger notification count update after cleanup
      fcmNotificationEvents.notificationsChanged();
    }
  } catch (error) {
    console.error('Error cleaning up old notifications:', error);
  }
};

// Helper function to clean up old deleted notification IDs (older than 30 days)
export const cleanupOldDeletedNotifications = async () => {
  try {
    const deletedNotificationIds = await AsyncStorage.getItem('@FCMDeletedNotificationIds');
    if (!deletedNotificationIds) return;

    const deletedIds: string[] = JSON.parse(deletedNotificationIds);
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000); // 30 days in milliseconds
    
    // Filter out IDs that are older than 30 days
    // Since we can't determine age from ID alone, we'll limit to recent 100 entries
    const recentDeletedIds = deletedIds.slice(-100);
    
    if (recentDeletedIds.length !== deletedIds.length) {
      await AsyncStorage.setItem('@FCMDeletedNotificationIds', JSON.stringify(recentDeletedIds));
      console.log(`Cleaned up ${deletedIds.length - recentDeletedIds.length} old deleted notification IDs`);
    }
  } catch (error) {
    console.error('Error cleaning up old deleted notifications:', error);
  }
};

// Helper function to force refresh notification count
export const forceRefreshNotificationCount = async () => {
  await cleanupOldNotifications();
  fcmNotificationEvents.notificationsChanged();
};

// Helper function to clean up invalid notifications
export const cleanupInvalidNotifications = async () => {
  try {
    const storedNotifications = await AsyncStorage.getItem('@FCMNotificationHistory');
    if (!storedNotifications) return;

    const notifications = JSON.parse(storedNotifications);
    
    // Filter out invalid notifications
    const validNotifications = notifications.filter((notification: any) => {
      // Check if notification has valid structure
      const isValid = !!(
        notification &&
        typeof notification === 'object' &&
        notification.id &&
        typeof notification.id === 'string' &&
        notification.id.trim().length > 0 &&
        notification.timestamp &&
        typeof notification.timestamp === 'number' &&
        notification.timestamp > 0 &&
        notification.timestamp > 1000000000000 && // Must be a valid Unix timestamp (after year 2001)
        notification.title &&
        typeof notification.title === 'string' &&
        notification.title.trim().length > 0 &&
        notification.body &&
        typeof notification.body === 'string' &&
        notification.body.trim().length > 0
      );
      
      if (!isValid) {
        console.log('🗑️ Removing invalid notification:', {
          id: notification.id,
          title: notification.title,
          timestamp: notification.timestamp,
          hasValidId: !!(notification.id && typeof notification.id === 'string' && notification.id.trim().length > 0),
          hasValidTimestamp: !!(notification.timestamp && typeof notification.timestamp === 'number' && notification.timestamp > 1000000000000),
          hasValidTitle: !!(notification.title && typeof notification.title === 'string' && notification.title.trim().length > 0),
          hasValidBody: !!(notification.body && typeof notification.body === 'string' && notification.body.trim().length > 0)
        });
      }
      
      return isValid;
    });
    
    if (validNotifications.length !== notifications.length) {
      await AsyncStorage.setItem('@FCMNotificationHistory', JSON.stringify(validNotifications));
      console.log(`Cleaned up ${notifications.length - validNotifications.length} invalid notifications`);
      
      // Trigger notification count update after cleanup
      fcmNotificationEvents.notificationsChanged();
    } else {
      console.log('No invalid notifications found to clean up');
    }
  } catch (error) {
    console.error('Error cleaning up invalid notifications:', error);
  }
};
