import React from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
} from 'react-native';
// import { getMinimumTouchTargetSize } from '@/utils/accessibilityUtils';
import { isTablet, isLargeTablet, spacing } from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';

interface AccessibleButtonProps {
  title: string;
  onPress: () => void;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  disabled?: boolean;
  loading?: boolean;
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
  style?: ViewStyle;
  textStyle?: TextStyle;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

/**
 * Accessible Button component optimized for tablets
 */
const AccessibleButton: React.FC<AccessibleButtonProps> = ({
  title,
  onPress,
  accessibilityLabel,
  accessibilityHint,
  disabled = false,
  loading = false,
  variant = 'primary',
  size = 'medium',
  style,
  textStyle,
  leftIcon,
  rightIcon,
}) => {
  // Get minimum touch target size
  const getMinimumTouchTargetSize = () => {
    if (isLargeTablet()) {
      return 56; // 56x56 for large tablets
    } else if (isTablet()) {
      return 48; // 48x48 for tablets
    } else {
      return 44; // 44x44 minimum for phones (WCAG standard)
    }
  };
  const minSize = getMinimumTouchTargetSize();
  
  // Calculate button dimensions based on size
  const getButtonDimensions = () => {
    switch (size) {
      case 'small':
        return {
          height: isTablet() ? 44 : 40,
          paddingHorizontal: isTablet() ? 16 : 12,
          fontSize: isTablet() ? 14 : 12,
        };
      case 'large':
        return {
          height: isLargeTablet() ? 60 : isTablet() ? 56 : 48,
          paddingHorizontal: isTablet() ? 32 : 24,
          fontSize: isTablet() ? 18 : 16,
        };
      case 'medium':
      default:
        return {
          height: isTablet() ? 52 : 44,
          paddingHorizontal: isTablet() ? 24 : 16,
          fontSize: isTablet() ? 16 : 14,
        };
    }
  };
  
  // Get button styles based on variant
  const getButtonStyles = () => {
    const dimensions = getButtonDimensions();
    const baseStyle: ViewStyle = {
      height: dimensions.height,
      paddingHorizontal: dimensions.paddingHorizontal,
      minWidth: minSize,
      borderRadius: isTablet() ? 12 : 8,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    };
    
    switch (variant) {
      case 'secondary':
        return {
          ...baseStyle,
          backgroundColor: CoreColors.TealBlue,
        };
      case 'outline':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          borderWidth: 2,
          borderColor: CoreColors.TurquoiseBlue,
        };
      case 'text':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
        };
      case 'primary':
      default:
        return {
          ...baseStyle,
          backgroundColor: CoreColors.TurquoiseBlue,
        };
    }
  };
  
  // Get text styles based on variant
  const getTextStyles = () => {
    const dimensions = getButtonDimensions();
    const baseStyle: TextStyle = {
      fontSize: dimensions.fontSize,
      fontWeight: '600',
      textAlign: 'center',
    };
    
    switch (variant) {
      case 'outline':
        return {
          ...baseStyle,
          color: CoreColors.TurquoiseBlue,
        };
      case 'text':
        return {
          ...baseStyle,
          color: CoreColors.TurquoiseBlue,
        };
      case 'primary':
      case 'secondary':
      default:
        return {
          ...baseStyle,
          color: '#FFFFFF',
        };
    }
  };
  
  const buttonStyles = getButtonStyles();
  const textStyles = getTextStyles();
  
  return (
    <TouchableOpacity
      style={[
        buttonStyles,
        disabled && styles.disabledButton,
        style,
      ]}
      onPress={onPress}
      disabled={disabled || loading}
      accessible={true}
      accessibilityLabel={accessibilityLabel || title}
      accessibilityHint={accessibilityHint}
      accessibilityRole="button"
      accessibilityState={{
        disabled: disabled || loading,
        busy: loading,
      }}
    >
      {loading ? (
        <ActivityIndicator
          color={variant === 'outline' || variant === 'text' ? CoreColors.TurquoiseBlue : '#FFFFFF'}
          size={isTablet() ? 'large' : 'small'}
        />
      ) : (
        <>
          {leftIcon && <View style={styles.leftIcon}>{leftIcon}</View>}
          <Text style={[textStyles, disabled && styles.disabledText, textStyle]}>
            {title}
          </Text>
          {rightIcon && <View style={styles.rightIcon}>{rightIcon}</View>}
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  disabledButton: {
    backgroundColor: '#E5E7EB',
    borderColor: '#E5E7EB',
  },
  disabledText: {
    color: '#9CA3AF',
  },
  leftIcon: {
    marginRight: spacing.sm,
  },
  rightIcon: {
    marginLeft: spacing.sm,
  },
});

export default AccessibleButton;