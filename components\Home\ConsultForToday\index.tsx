import React, {memo} from 'react';
import {View, StyleSheet, ViewStyle} from 'react-native';
import Text from '@/components/Text';
import Theme from '@/constants/Theme';
import {Colors} from '@/constants';
import ConsultsProgress from './ConsultProgress';

interface ConsultForTodayProps {
  step: number;
  total: number;
  style?: ViewStyle;
}

const ConsultForToday = memo((props: ConsultForTodayProps) => {
  return (
    <View style={[styles.container, props.style]}>
      <View>
        <Text size={20} lineHeight={28} bold>
          Tasks
          <Text size={20} lineHeight={28}>
            {' '}
            for today
          </Text>
        </Text>
        <Text size={13} lineHeight={16} color={Colors.GrayBlue} marginTop={8}>
          {props.step || 0} of {props.total || 0} completed
        </Text>
      </View>
      <ConsultsProgress current={props.step || 0} total={props.total || 0} />
    </View>
  );
});

export default ConsultForToday;

const styles = StyleSheet.create({
  container: {
    ...Theme.flexRowSpace,
    marginTop: 32,
  },
});
