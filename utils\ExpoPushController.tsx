import React from 'react';
import { NotificationHandler } from './NotificationManager';

/**
 * ExpoPushController - <PERSON><PERSON> push notifications for the app
 *
 * This component is designed to mimic the behavior of the original React Native
 * PushController component, but using our centralized NotificationManager.
 */
const ExpoPushController: React.FC = () => {
  // Use the NotificationHandler component which handles all notification logic
  return <NotificationHandler />;
};

export default ExpoPushController;
