import { getApp, getApps, initializeApp } from '@react-native-firebase/app';

let firebaseApp: any = null;

export function getFirebaseApp() {
  try {
    if (firebaseApp) {
      return firebaseApp;
    }

    const apps = getApps();
    if (apps.length > 0) {
      firebaseApp = getApp();
      return firebaseApp;
    }

    console.warn('Firebase app not found. Ensure your firebase configuration is properly set up in app.json');

    firebaseApp = getApp();
    return firebaseApp;
  } catch (error) {
    console.error('Error getting Firebase app:', error);
    throw error;
  }
}

export function initializeFirebaseApp() {
  try {
    const apps = getApps();
    if (apps.length === 0) {
      console.log('Initializing Firebase app...');
      firebaseApp = getApp();
    } else {
      firebaseApp = apps[0];
    }
    
    console.log('Firebase app initialized successfully');
    return firebaseApp;
  } catch (error) {
    console.error('Error initializing Firebase app:', error);
    throw error;
  }
}

export function isFirebaseInitialized(): boolean {
  try {
    const apps = getApps();
    return apps.length > 0;
  } catch (error) {
    console.error('Error checking Firebase initialization:', error);
    return false;
  }
}

export default {
  getFirebaseApp,
  initializeFirebaseApp,
  isFirebaseInitialized,
}; 