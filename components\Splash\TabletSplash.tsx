import React, { useEffect } from 'react';
import {
  View,
  StyleSheet,
  Image,
  Animated,
} from 'react-native';
import { TabletAwareContainer } from '@/components/Layout/TabletAwareContainer';
import { AccessibleText } from '@/components/Accessibility/AccessibleText';
import { isTablet, isLargeTablet, spacing } from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';

interface TabletSplashProps {
  onAnimationComplete?: () => void;
}

/**
 * Tablet-optimized Splash screen component
 */
const TabletSplash: React.FC<TabletSplashProps> = ({
  onAnimationComplete,
}) => {
  const fadeAnim = new Animated.Value(0);
  const scaleAnim = new Animated.Value(0.8);

  useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Auto-navigate after animation
    const timer = setTimeout(() => {
      onAnimationComplete?.();
    }, 2500);

    return () => clearTimeout(timer);
  }, []);

  return (
    <TabletAwareContainer style={styles.container}>
      <View style={styles.content}>
        <Animated.View
          style={[
            styles.logoContainer,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <Image
            source={require('@/assets/images/img_logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <AccessibleText variant="h1" style={styles.title}>
            WatchRx Care Team
          </AccessibleText>
          <AccessibleText variant="body" style={styles.subtitle}>
            Healthcare Management Platform
          </AccessibleText>
        </Animated.View>

        <View style={styles.footer}>
          <AccessibleText variant="caption" style={styles.version}>
            Version 1.0.0
          </AccessibleText>
        </View>
      </View>
    </TabletAwareContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: CoreColors.TurquoiseBlue,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  logo: {
    width: isLargeTablet() ? 200 : isTablet() ? 150 : 120,
    height: isLargeTablet() ? 200 : isTablet() ? 150 : 120,
    marginBottom: spacing.xl,
  },
  title: {
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: spacing.md,
    fontWeight: '700',
  },
  subtitle: {
    color: '#FFFFFF',
    textAlign: 'center',
    opacity: 0.9,
  },
  footer: {
    position: 'absolute',
    bottom: spacing.xl,
    alignItems: 'center',
  },
  version: {
    color: '#FFFFFF',
    opacity: 0.7,
  },
});

export default TabletSplash;