import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  Image,
  Animated,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import { useRouter } from 'expo-router';
import { TabletAwareContainer, AdaptiveLayout } from '@/components/Layout';
import { AccessibleText, AccessibleButton } from '@/components/Accessibility';
import { isTablet, isLargeTablet, getOrientation, spacing, typography } from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';
import { ONBOARDING_IMAGES } from '@/assets/images/OnBoardingImages';

interface TabletSplashProps {
  onAnimationComplete?: () => void;
  onLogin?: () => void;
  onSignUp?: () => void;
}

/**
 * Tablet-optimized Splash screen component
 */
const TabletSplash: React.FC<TabletSplashProps> = ({
  onAnimationComplete,
  onLogin,
  onSignUp,
}) => {
  console.log('TabletSplash props:', { onAnimationComplete, onLogin, onSignUp });
  const router = useRouter();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const fadeAnim = new Animated.Value(0);
  const scaleAnim = new Animated.Value(0.8);
  const orientation = getOrientation();

  // Debug logging
  console.log('TabletSplash - Screen dimensions:', Dimensions.get('window'));
  console.log('TabletSplash - Is tablet:', isTablet());
  console.log('TabletSplash - Orientation:', orientation);
  console.log('TabletSplash - Router:', router);

  // Onboarding content for the image carousel
  const onboardingContent = [
    {
      id: 1,
      image: ONBOARDING_IMAGES.img1,
      title: 'Quality Care',
      description: 'Provide value-based care, improve patient outcomes and increase quality of care!',
    },
    {
      id: 2,
      image: ONBOARDING_IMAGES.img2,
      title: 'Virtual Care',
      description: 'With virtual Care and care team coordination, we reduce hospital admission!',
    },
    {
      id: 3,
      image: ONBOARDING_IMAGES.img3,
      title: 'Patient Engagement',
      description: 'Increase patient engagement with voice, video and text!',
    },
    {
      id: 4,
      image: ONBOARDING_IMAGES.img4,
      title: 'Independent Living',
      description: 'We help seniors stay healthy, live independently with dignity!',
    },
  ];

  useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Auto-rotate images every 4 seconds
    const imageRotationInterval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        (prevIndex + 1) % onboardingContent.length
      );
    }, 4000);

    // Auto-navigate after animation (optional)
    const timer = setTimeout(() => {
      onAnimationComplete?.();
    }, 3000);

    return () => {
      clearTimeout(timer);
      clearInterval(imageRotationInterval);
    };
  }, []);

  const handleLogin = () => {
    console.log('handleLogin called, onLogin:', onLogin);
    try {
      if (onLogin && typeof onLogin === 'function') {
        onLogin();
      } else {
        console.log('onLogin not available, using router');
        router.push('/auth/login');
      }
    } catch (error) {
      console.error('Error in handleLogin:', error);
      router.push('/auth/login');
    }
  };

  const handleSignUp = () => {
    console.log('handleSignUp called, onSignUp:', onSignUp);
    try {
      if (onSignUp && typeof onSignUp === 'function') {
        onSignUp();
      } else {
        console.log('onSignUp not available, using router');
        router.push('/auth/signup');
      }
    } catch (error) {
      console.error('Error in handleSignUp:', error);
      router.push('/auth/signup');
    }
  };

  // Desktop-like landscape layout for tablets
  const renderLandscapeLayout = () => {
    console.log('Rendering landscape layout');
    return (
    <View style={styles.landscapeContainer}>
      {/* Left Panel - Images and Content */}
      <View style={styles.leftPanel}>
        <Animated.View
          style={[
            styles.imageSection,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <Image
            source={onboardingContent[currentImageIndex].image}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <View style={styles.imageOverlay}>
            <AccessibleText variant="h1" style={styles.heroTitle}>
              {onboardingContent[currentImageIndex].title}
            </AccessibleText>
            <AccessibleText variant="body" style={styles.heroDescription}>
              {onboardingContent[currentImageIndex].description}
            </AccessibleText>
          </View>
        </Animated.View>

        {/* Image indicators */}
        <View style={styles.indicators}>
          {onboardingContent.map((_, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.indicator,
                index === currentImageIndex && styles.activeIndicator,
              ]}
              onPress={() => setCurrentImageIndex(index)}
            />
          ))}
        </View>
      </View>

      {/* Right Panel - Login/Signup */}
      <View style={styles.rightPanel}>
        <View style={styles.authSection}>
          <View style={styles.logoContainer}>
            <Image
              source={require('@/assets/images/img_logo.png')}
              style={styles.logo}
              resizeMode="contain"
            />
            <AccessibleText variant="h1" style={styles.title}>
              WatchRx Care Team
            </AccessibleText>
            <AccessibleText variant="body" style={styles.subtitle}>
              Healthcare Management Platform
            </AccessibleText>
          </View>

          <View style={styles.buttonContainer}>
            <AccessibleButton
              title="Log In"
              onPress={handleLogin}
              style={styles.loginButton}
              variant="primary"
              size="large"
              accessibilityLabel="Log in to your account"
            />
            <AccessibleButton
              title="Sign Up"
              onPress={handleSignUp}
              style={styles.signUpButton}
              variant="outline"
              size="large"
              accessibilityLabel="Create a new account"
            />
          </View>

          <View style={styles.footer}>
            <AccessibleText variant="caption" style={styles.version}>
              Version 1.0.0
            </AccessibleText>
          </View>
        </View>
      </View>
    </View>
    );
  };

  // Portrait layout (similar to original)
  const renderPortraitLayout = () => {
    console.log('Rendering portrait layout');
    return (
    <View style={styles.portraitContainer}>
      <Animated.View
        style={[
          styles.logoContainer,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <Image
          source={require('@/assets/images/img_logo.png')}
          style={styles.logo}
          resizeMode="contain"
        />
        <AccessibleText variant="h1" style={styles.title}>
          WatchRx Care Team
        </AccessibleText>
        <AccessibleText variant="body" style={styles.subtitle}>
          Healthcare Management Platform
        </AccessibleText>
      </Animated.View>

      <View style={styles.buttonContainer}>
        <AccessibleButton
          title="Log In"
          onPress={handleLogin}
          style={styles.loginButton}
          variant="primary"
          size="large"
        />
        <AccessibleButton
          title="Sign Up"
          onPress={handleSignUp}
          style={styles.signUpButton}
          variant="outline"
          size="large"
        />
      </View>

      <View style={styles.footer}>
        <AccessibleText variant="caption" style={styles.version}>
          Version 1.0.0
        </AccessibleText>
      </View>
    </View>
    );
  };

  return (
    <TabletAwareContainer style={styles.container}>
      <AdaptiveLayout
        phoneLayout={renderPortraitLayout()}
        tabletPortraitLayout={renderPortraitLayout()}
        tabletLandscapeLayout={renderLandscapeLayout()}
        largeTabletLayout={renderLandscapeLayout()}
      />
    </TabletAwareContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: CoreColors.TurquoiseBlue,
  },

  // Landscape Layout Styles (Desktop-like)
  landscapeContainer: {
    flex: 1,
    flexDirection: 'row',
    minHeight: Dimensions.get('window').height,
  },
  leftPanel: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    position: 'relative',
  },
  rightPanel: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: isLargeTablet() ? spacing.xxl : spacing.xl,
    paddingVertical: spacing.xl,
  },
  imageSection: {
    flex: 1,
    position: 'relative',
  },
  heroImage: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  imageOverlay: {
    position: 'absolute',
    bottom: spacing.xxl,
    left: spacing.xl,
    right: spacing.xl,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 16,
    padding: spacing.xl,
  },
  heroTitle: {
    color: '#FFFFFF',
    fontSize: isLargeTablet() ? typography.h1.fontSize * 1.2 : typography.h1.fontSize,
    fontWeight: '700',
    marginBottom: spacing.md,
    textAlign: 'left',
  },
  heroDescription: {
    color: '#FFFFFF',
    fontSize: isLargeTablet() ? typography.body.fontSize * 1.1 : typography.body.fontSize,
    opacity: 0.9,
    lineHeight: 24,
    textAlign: 'left',
  },
  indicators: {
    position: 'absolute',
    bottom: spacing.xl,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  indicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
    marginHorizontal: 6,
  },
  activeIndicator: {
    backgroundColor: '#FFFFFF',
    width: 16,
    height: 16,
    borderRadius: 8,
  },
  authSection: {
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
  },

  // Portrait Layout Styles
  portraitContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },

  // Common Styles
  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.xxl,
  },
  logo: {
    width: isLargeTablet() ? 120 : isTablet() ? 100 : 80,
    height: isLargeTablet() ? 120 : isTablet() ? 100 : 80,
    marginBottom: spacing.xl,
  },
  title: {
    color: getOrientation() === 'landscape' && isTablet() ? CoreColors.TurquoiseBlue : '#FFFFFF',
    textAlign: 'center',
    marginBottom: spacing.md,
    fontWeight: '700',
    fontSize: isLargeTablet() ? typography.h1.fontSize * 1.1 : typography.h1.fontSize,
  },
  subtitle: {
    color: getOrientation() === 'landscape' && isTablet() ? CoreColors.TealBlue : '#FFFFFF',
    textAlign: 'center',
    opacity: 0.9,
    fontSize: isLargeTablet() ? typography.body.fontSize * 1.05 : typography.body.fontSize,
  },
  buttonContainer: {
    width: '100%',
    maxWidth: 320,
    gap: spacing.lg,
    marginBottom: spacing.xxl,
  },
  loginButton: {
    backgroundColor: CoreColors.TurquoiseBlue,
    borderRadius: 12,
    paddingVertical: isTablet() ? 16 : 14,
    minHeight: isTablet() ? 56 : 48,
  },
  signUpButton: {
    backgroundColor: 'transparent',
    borderColor: CoreColors.TurquoiseBlue,
    borderWidth: 2,
    borderRadius: 12,
    paddingVertical: isTablet() ? 16 : 14,
    minHeight: isTablet() ? 56 : 48,
  },
  footer: {
    alignItems: 'center',
    marginTop: 'auto',
  },
  version: {
    color: getOrientation() === 'landscape' && isTablet() ? CoreColors.TealBlue : '#FFFFFF',
    opacity: 0.7,
    fontSize: typography.caption.fontSize,
  },
});

export default TabletSplash;