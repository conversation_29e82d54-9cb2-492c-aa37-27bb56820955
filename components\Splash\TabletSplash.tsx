import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  Image,
  Animated,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import { useRouter } from 'expo-router';
import { TabletAwareContainer, AdaptiveLayout } from '@/components/Layout';
import { AccessibleText, AccessibleButton } from '@/components/Accessibility';
import { isTablet, isLargeTablet, getOrientation, spacing, typography } from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';
import { ONBOARDING_IMAGES } from '@/assets/images/OnBoardingImages';

interface TabletSplashProps {
  onAnimationComplete?: () => void;
  onLogin?: () => void;
  onSignUp?: () => void;
}

/**
 * Tablet-optimized Splash screen component
 */
const TabletSplash: React.FC<TabletSplashProps> = ({
  onAnimationComplete,
  onLogin,
  onSignUp,
}) => {
  console.log('TabletSplash props:', { onAnimationComplete, onLogin, onSignUp });
  const router = useRouter();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const fadeAnim = new Animated.Value(0);
  const scaleAnim = new Animated.Value(0.8);
  const orientation = getOrientation();

  // Debug logging
  console.log('TabletSplash - Screen dimensions:', Dimensions.get('window'));
  console.log('TabletSplash - Is tablet:', isTablet());
  console.log('TabletSplash - Orientation:', orientation);
  console.log('TabletSplash - Router:', router);

  // Onboarding content for the image carousel
  const onboardingContent = [
    {
      id: 1,
      image: ONBOARDING_IMAGES.img1,
      title: 'Quality Care',
      description: 'Provide value-based care, improve patient outcomes and increase quality of care!',
    },
    {
      id: 2,
      image: ONBOARDING_IMAGES.img2,
      title: 'Virtual Care',
      description: 'With virtual Care and care team coordination, we reduce hospital admission!',
    },
    {
      id: 3,
      image: ONBOARDING_IMAGES.img3,
      title: 'Patient Engagement',
      description: 'Increase patient engagement with voice, video and text!',
    },
    {
      id: 4,
      image: ONBOARDING_IMAGES.img4,
      title: 'Independent Living',
      description: 'We help seniors stay healthy, live independently with dignity!',
    },
  ];

  useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();

    // Auto-rotate images every 4 seconds
    const imageRotationInterval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        (prevIndex + 1) % onboardingContent.length
      );
    }, 4000);

    // Auto-navigate after animation (optional)
    const timer = setTimeout(() => {
      onAnimationComplete?.();
    }, 3000);

    return () => {
      clearTimeout(timer);
      clearInterval(imageRotationInterval);
    };
  }, []);

  const handleLogin = () => {
    console.log('handleLogin called, onLogin:', onLogin);
    try {
      if (onLogin && typeof onLogin === 'function') {
        onLogin();
      } else {
        console.log('onLogin not available, using router');
        router.push('/auth/login');
      }
    } catch (error) {
      console.error('Error in handleLogin:', error);
      router.push('/auth/login');
    }
  };

  const handleSignUp = () => {
    console.log('handleSignUp called, onSignUp:', onSignUp);
    try {
      if (onSignUp && typeof onSignUp === 'function') {
        onSignUp();
      } else {
        console.log('onSignUp not available, using router');
        router.push('/auth/signup');
      }
    } catch (error) {
      console.error('Error in handleSignUp:', error);
      router.push('/auth/signup');
    }
  };

  // Modern clean landscape layout for tablets
  const renderLandscapeLayout = () => {
    console.log('Rendering landscape layout');
    return (
    <View style={styles.landscapeContainer}>
      {/* Left Panel - Brand and Visual Content */}
      <View style={styles.leftPanel}>
        <View style={styles.brandSection}>
          <View style={styles.logoHeader}>
            <Image
              source={require('@/assets/images/img_logo.png')}
              style={styles.brandLogo}
              resizeMode="contain"
            />
            <View style={styles.brandText}>
              <AccessibleText variant="h1" style={styles.brandTitle}>
                WatchRx Care Team
              </AccessibleText>
              <AccessibleText variant="body" style={styles.brandSubtitle}>
                Healthcare Management Platform
              </AccessibleText>
            </View>
          </View>
        </View>

        <Animated.View
          style={[
            styles.visualContent,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <View style={styles.imageContainer}>
            <Image
              source={onboardingContent[currentImageIndex].image}
              style={styles.heroImage}
              resizeMode="cover"
            />
            <View style={styles.gradientOverlay} />
          </View>

          <View style={styles.contentInfo}>
            <AccessibleText variant="h2" style={styles.contentTitle}>
              {onboardingContent[currentImageIndex].title}
            </AccessibleText>
            <AccessibleText variant="body" style={styles.contentDescription}>
              {onboardingContent[currentImageIndex].description}
            </AccessibleText>
          </View>

          {/* Clean indicators */}
          <View style={styles.indicators}>
            {onboardingContent.map((_, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.indicator,
                  index === currentImageIndex && styles.activeIndicator,
                ]}
                onPress={() => setCurrentImageIndex(index)}
                accessibilityLabel={`View slide ${index + 1}`}
              />
            ))}
          </View>
        </Animated.View>
      </View>

      {/* Right Panel - Clean Auth Interface */}
      <View style={styles.rightPanel}>
        <View style={styles.authContainer}>
          <View style={styles.authHeader}>
            <AccessibleText variant="h1" style={styles.welcomeTitle}>
              Welcome Back
            </AccessibleText>
            <AccessibleText variant="body" style={styles.welcomeSubtitle}>
              Sign in to your account or create a new one
            </AccessibleText>
          </View>

          <View style={styles.authActions}>
            <AccessibleButton
              title="Sign In"
              onPress={handleLogin}
              style={styles.primaryButton}
              variant="primary"
              size="large"
              accessibilityLabel="Sign in to your account"
            />

            <View style={styles.dividerContainer}>
              <View style={styles.dividerLine} />
              <AccessibleText variant="caption" style={styles.dividerText}>
                or
              </AccessibleText>
              <View style={styles.dividerLine} />
            </View>

            <AccessibleButton
              title="Create Account"
              onPress={handleSignUp}
              style={styles.secondaryButton}
              variant="outline"
              size="large"
              accessibilityLabel="Create a new account"
            />
          </View>

          <View style={styles.authFooter}>
            <AccessibleText variant="caption" style={styles.versionText}>
              Version 1.0.0 • Secure & HIPAA Compliant
            </AccessibleText>
          </View>
        </View>
      </View>
    </View>
    );
  };

  // Clean portrait layout for smaller tablets
  const renderPortraitLayout = () => {
    console.log('Rendering portrait layout');
    return (
    <View style={styles.portraitContainer}>
      <View style={styles.portraitContent}>
        <Animated.View
          style={[
            styles.portraitHeader,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <Image
            source={require('@/assets/images/img_logo.png')}
            style={styles.portraitLogo}
            resizeMode="contain"
          />
          <AccessibleText variant="h1" style={styles.portraitTitle}>
            WatchRx Care Team
          </AccessibleText>
          <AccessibleText variant="body" style={styles.portraitSubtitle}>
            Healthcare Management Platform
          </AccessibleText>
        </Animated.View>

        <View style={styles.portraitActions}>
          <AccessibleButton
            title="Sign In"
            onPress={handleLogin}
            style={styles.portraitPrimaryButton}
            variant="primary"
            size="large"
            accessibilityLabel="Sign in to your account"
          />

          <View style={styles.portraitDivider}>
            <View style={styles.dividerLine} />
            <AccessibleText variant="caption" style={styles.dividerText}>
              or
            </AccessibleText>
            <View style={styles.dividerLine} />
          </View>

          <AccessibleButton
            title="Create Account"
            onPress={handleSignUp}
            style={styles.portraitSecondaryButton}
            variant="outline"
            size="large"
            accessibilityLabel="Create a new account"
          />
        </View>

        <View style={styles.portraitFooter}>
          <AccessibleText variant="caption" style={styles.portraitVersionText}>
            Version 1.0.0 • Secure & HIPAA Compliant
          </AccessibleText>
        </View>
      </View>
    </View>
    );
  };

  return (
    <TabletAwareContainer style={styles.container}>
      <AdaptiveLayout
        phoneLayout={renderPortraitLayout()}
        tabletPortraitLayout={renderPortraitLayout()}
        tabletLandscapeLayout={renderLandscapeLayout()}
        largeTabletLayout={renderLandscapeLayout()}
      />
    </TabletAwareContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },

  // Modern Landscape Layout Styles
  landscapeContainer: {
    flex: 1,
    flexDirection: 'row',
    minHeight: Dimensions.get('window').height,
    backgroundColor: '#FFFFFF',
  },

  // Left Panel - Brand and Visual Content
  leftPanel: {
    flex: 1.2,
    backgroundColor: '#F8FAFC',
    position: 'relative',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
  },
  brandSection: {
    paddingBottom: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    marginBottom: spacing.lg,
  },
  logoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  brandLogo: {
    width: isLargeTablet() ? 48 : 40,
    height: isLargeTablet() ? 48 : 40,
    marginRight: spacing.md,
  },
  brandText: {
    flex: 1,
  },
  brandTitle: {
    color: CoreColors.DarkJungleGreen,
    fontSize: isLargeTablet() ? 24 : 20,
    fontWeight: '700',
    marginBottom: 2,
  },
  brandSubtitle: {
    color: CoreColors.SlateGray,
    fontSize: isLargeTablet() ? 14 : 12,
    fontWeight: '500',
  },

  visualContent: {
    flex: 1,
    justifyContent: 'center',
  },
  imageContainer: {
    height: isLargeTablet() ? 300 : 250,
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: spacing.lg,
    position: 'relative',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  gradientOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '50%',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },

  contentInfo: {
    paddingHorizontal: spacing.sm,
  },
  contentTitle: {
    color: CoreColors.DarkJungleGreen,
    fontSize: isLargeTablet() ? 22 : 18,
    fontWeight: '700',
    marginBottom: spacing.sm,
    textAlign: 'left',
  },
  contentDescription: {
    color: CoreColors.SlateGray,
    fontSize: isLargeTablet() ? 16 : 14,
    lineHeight: 22,
    textAlign: 'left',
  },

  indicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: spacing.lg,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#CBD5E0',
    marginHorizontal: 4,
  },
  activeIndicator: {
    backgroundColor: CoreColors.TurquoiseBlue,
    width: 24,
    height: 8,
    borderRadius: 4,
  },

  // Right Panel - Auth Interface
  rightPanel: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: isLargeTablet() ? spacing.xxl * 1.5 : spacing.xxl,
    paddingVertical: spacing.xl,
  },
  authContainer: {
    width: '100%',
    maxWidth: 420,
    alignItems: 'center',
  },
  authHeader: {
    alignItems: 'center',
    marginBottom: spacing.xxl,
  },
  welcomeTitle: {
    color: CoreColors.DarkJungleGreen,
    fontSize: isLargeTablet() ? 32 : 28,
    fontWeight: '700',
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  welcomeSubtitle: {
    color: CoreColors.SlateGray,
    fontSize: isLargeTablet() ? 16 : 14,
    textAlign: 'center',
    lineHeight: 22,
  },

  authActions: {
    width: '100%',
    marginBottom: spacing.xl,
  },
  primaryButton: {
    width: '100%',
    marginBottom: spacing.lg,
    backgroundColor: CoreColors.TurquoiseBlue,
    borderRadius: 12,
    height: isLargeTablet() ? 56 : 48,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing.lg,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#E2E8F0',
  },
  dividerText: {
    color: CoreColors.SlateGray,
    marginHorizontal: spacing.md,
    fontSize: 12,
    fontWeight: '500',
  },
  secondaryButton: {
    width: '100%',
    borderColor: CoreColors.TurquoiseBlue,
    borderWidth: 2,
    borderRadius: 12,
    height: isLargeTablet() ? 56 : 48,
    backgroundColor: 'transparent',
  },
  authFooter: {
    alignItems: 'center',
    marginTop: spacing.xl,
  },
  versionText: {
    color: CoreColors.SlateGray,
    fontSize: 12,
    textAlign: 'center',
  },

  // Portrait Layout Styles
  portraitContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  portraitContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
  },
  portraitHeader: {
    alignItems: 'center',
    marginBottom: spacing.xxl,
  },
  portraitLogo: {
    width: isLargeTablet() ? 80 : 64,
    height: isLargeTablet() ? 80 : 64,
    marginBottom: spacing.lg,
  },
  portraitTitle: {
    color: CoreColors.DarkJungleGreen,
    fontSize: isLargeTablet() ? 28 : 24,
    fontWeight: '700',
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  portraitSubtitle: {
    color: CoreColors.SlateGray,
    fontSize: isLargeTablet() ? 16 : 14,
    textAlign: 'center',
    lineHeight: 22,
  },
  portraitActions: {
    width: '100%',
    maxWidth: 320,
    marginBottom: spacing.xl,
  },
  portraitPrimaryButton: {
    width: '100%',
    marginBottom: spacing.lg,
    backgroundColor: CoreColors.TurquoiseBlue,
    borderRadius: 12,
    height: isLargeTablet() ? 56 : 48,
  },
  portraitDivider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing.lg,
  },
  portraitSecondaryButton: {
    width: '100%',
    borderColor: CoreColors.TurquoiseBlue,
    borderWidth: 2,
    borderRadius: 12,
    height: isLargeTablet() ? 56 : 48,
    backgroundColor: 'transparent',
  },
  portraitFooter: {
    alignItems: 'center',
  },
  portraitVersionText: {
    color: CoreColors.SlateGray,
    fontSize: 12,
    textAlign: 'center',
  },

  // Common Styles (Legacy - to be cleaned up)
  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.xxl,
  },
  logo: {
    width: isLargeTablet() ? 120 : isTablet() ? 100 : 80,
    height: isLargeTablet() ? 120 : isTablet() ? 100 : 80,
    marginBottom: spacing.xl,
  },
  title: {
    color: getOrientation() === 'landscape' && isTablet() ? CoreColors.TurquoiseBlue : '#FFFFFF',
    textAlign: 'center',
    marginBottom: spacing.md,
    fontWeight: '700',
    fontSize: isLargeTablet() ? typography.h1.fontSize * 1.1 : typography.h1.fontSize,
  },
  subtitle: {
    color: getOrientation() === 'landscape' && isTablet() ? CoreColors.TealBlue : '#FFFFFF',
    textAlign: 'center',
    opacity: 0.9,
    fontSize: isLargeTablet() ? typography.body.fontSize * 1.05 : typography.body.fontSize,
  },
  buttonContainer: {
    width: '100%',
    maxWidth: 320,
    gap: spacing.lg,
    marginBottom: spacing.xxl,
  },
  loginButton: {
    backgroundColor: CoreColors.TurquoiseBlue,
    borderRadius: 12,
    paddingVertical: isTablet() ? 16 : 14,
    minHeight: isTablet() ? 56 : 48,
  },
  signUpButton: {
    backgroundColor: 'transparent',
    borderColor: CoreColors.TurquoiseBlue,
    borderWidth: 2,
    borderRadius: 12,
    paddingVertical: isTablet() ? 16 : 14,
    minHeight: isTablet() ? 56 : 48,
  },
  footer: {
    alignItems: 'center',
    marginTop: 'auto',
  },
  version: {
    color: getOrientation() === 'landscape' && isTablet() ? CoreColors.TealBlue : '#FFFFFF',
    opacity: 0.7,
    fontSize: typography.caption.fontSize,
  },
});

export default TabletSplash;