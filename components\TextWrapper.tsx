import React, { ReactNode } from 'react';
import { View, ViewProps } from 'react-native';
import Text from './Text';

interface TextWrapperProps extends ViewProps {
  children: ReactNode;
}

/**
 * A wrapper component that ensures all text is properly wrapped in Text components.
 * This is a workaround for the "Text strings must be rendered within a <Text> component" error.
 */
const TextWrapper: React.FC<TextWrapperProps> = ({ children, ...props }) => {
  // Function to recursively wrap text nodes in Text components
  const wrapTextNodes = (node: ReactNode): ReactNode => {
    // If the node is a string or number, wrap it in a Text component
    if (typeof node === 'string' || typeof node === 'number') {
      return <Text>{node}</Text>;
    }

    // If the node is an array, map over it and wrap each child
    if (Array.isArray(node)) {
      return node.map((child, index) => (
        <React.Fragment key={index}>{wrapTextNodes(child)}</React.Fragment>
      ));
    }

    // If the node is a React element, check if it's already a Text component
    if (React.isValidElement(node)) {
      // If it's already a Text component or has no children, return it as is
      if (node.type === Text || !node.props.children) {
        return node;
      }

      // Otherwise, clone the element and wrap its children
      return React.cloneElement(node, {
        ...node.props,
        children: wrapTextNodes(node.props.children),
      });
    }

    // For any other type of node, return it as is
    return node;
  };

  return <View {...props}>{wrapTextNodes(children)}</View>;
};

export default TextWrapper;
