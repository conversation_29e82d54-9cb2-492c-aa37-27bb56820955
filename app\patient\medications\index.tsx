import React, { useEffect, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Stack } from 'expo-router';
import { useSelector } from 'react-redux';

import MedicationScreen from '@/screens/PatientDetails/Medications';
import Text from '@/components/Text';

export default function MedicationsRoute() {
  const params = useLocalSearchParams();
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  // Extract patientId from params
  const paramsObj = Object.fromEntries(
    Object.entries(params).map(([key, value]) => [key, value?.toString()])
  );

  // Get patient ID from URL params
  const urlPatientId = paramsObj.patientId || '';

  // Get patient ID from Redux as fallback
  const reduxPatientId = useSelector(
    (state: any) => state?.currentPatientIdReducer?.patientId
  );

  // Use URL param ID first, then Redux ID
  const effectivePatientId = urlPatientId || reduxPatientId;

  // Log detailed information for debugging
  useEffect(() => {
    console.log('Medications Route - Patient ID sources:', {
      urlPatientId,
      reduxPatientId,
      effectivePatientId,
      allParams: paramsObj
    });
  }, [urlPatientId, reduxPatientId, effectivePatientId, paramsObj]);

  // Check if we have a valid patient ID
  useEffect(() => {
    if (!effectivePatientId) {
      console.error('Medications Route: No patient ID available from any source');
    } else if (effectivePatientId === 'index' || effectivePatientId === 'undefined') {
      console.error('Medications Route: Invalid patient ID:', effectivePatientId);
    }
  }, [effectivePatientId]);

  // Show/hide loading indicator
  useEffect(() => {
    setLoading(true);
    // Simulate slight delay to ensure state is updated
    const timeout = setTimeout(() => {
      setLoading(false);
    }, 100);
    return () => clearTimeout(timeout);
  }, [effectivePatientId]);

  return (
    <>
      <Stack.Screen options={{ title: 'Medications' }} />
      <SafeAreaView style={styles.container} edges={['left', 'right']}>
        {effectivePatientId ? (
          <MedicationScreen
            patientId={effectivePatientId}
            directPatientId={effectivePatientId} // Double-ensure ID gets through
            loading={loading}
            key={`medications-${effectivePatientId}`} // Force re-render when ID changes
          />
        ) : (
          // Show message if no patient ID is available
          <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20}}>
            <Text style={{fontSize: 16, color: 'red', textAlign: 'center'}}>
              Missing patient ID. Please return to patient list and try again.
            </Text>
          </View>
        )}
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
