import React, { useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useIsFocused } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';

import HomeScreen from '@/screens/Home';
import { setCurentPatientId } from '@/services/actions/currentPatientId';

export default function HomeRoute() {
  const isFocused = useIsFocused();
  const dispatch = useDispatch();
  
  // Get current patient ID to check if it needs to be cleared
  const currentPatientId = useSelector(
    (state: any) => state?.currentPatientIdReducer?.patientId
  );

  // Clear patient context when returning to home tab
  useEffect(() => {
    if (isFocused && currentPatientId && currentPatientId !== 0) {
      console.log('Home tab focused, clearing patient context. Previous patient ID:', currentPatientId);
      dispatch(setCurentPatientId(0));
    }
  }, [isFocused, currentPatientId, dispatch]);

  return (
    // Explicitly exclude the top and bottom edges to prevent double padding with the header and remove bottom safe area
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      <HomeScreen />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    marginTop: 0,
    paddingTop: 0, // The HomeScreen will use Container which now handles padding
    paddingBottom: 0,
    position: 'relative',
    zIndex: 1,
  },
});