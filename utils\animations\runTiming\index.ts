import Animated from "react-native-reanimated";

/**
 * Enhanced animation utility for react-native-reanimated
 * Provides a type-safe wrapper around animation functionality
 * 
 * @param clock The animation clock 
 * @param duration Duration in milliseconds
 * @returns An animated value for the animation
 */
export function runTiming(
  clock: any,
  duration: number = 200
): any {
  try {
    // Dynamically import from react-native-redash
    const { timing } = require("react-native-redash/lib/module/v1");

    // Try to get Easing from reanimated (works with both v1 and v2)
    let easing;
    try {
      const { EasingNode } = require("react-native-reanimated");
      easing = EasingNode.linear;
    } catch (err) {
      const { Easing } = require("react-native-reanimated");
      easing = Easing.linear;
    }

    // Return animation
    return timing({
      to: 1,
      clock,
      duration: duration || 400,
      easing: easing,
    });
  } catch (error) {
    console.warn("Error in runTiming animation:", error);
    return clock; // Fallback to just returning the clock
  }
}

/**
 * Alias for runTiming - maintained for backward compatibility
 */
export const runBasicTiming = runTiming;
