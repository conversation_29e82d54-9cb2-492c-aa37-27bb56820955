import { useRouter } from 'expo-router';
import React, {useCallback, useEffect, useState} from 'react';
import {Alert, Image, Modal, StyleSheet, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import { useRouteContext } from '@/context/RouteContext';
import ButtonChangeCategory from '@/components/ButtonChangeCategory';
import ButtonLinear from '@/components/Buttons/ButtonLinear';
import Container from '@/components/Layout/Container';
import Loader from '@/components/Loader/Loader';
import ModalChangeCategory from '@/components/ModalChangeCategory';
import ModalSlideBottom from '@/components/ModalSlideBottom';
import { CoreColors, Constants } from '@/constants';
import useModalAnimation from '@/hooks/useModalAnimation';
import {setCurrentOrgId, setCurrentOrgDetails} from '@/services/actions/organizationActions';
import {apiPostWithToken} from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import Theme from '@/constants/Theme';
import {categoryList} from '@/types/category';
import scale from '@/utils/scale';

interface OrganizationProps {
  navigation?: any;
}

interface OrganizationItem {
  id: number;
  name: string;
  displayName: string;
}

/**
 * Organization Selection Screen
 * Allows users to select their organization after login
 */
const Organization = (props: OrganizationProps) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [loader, setLoader] = useState(false);
  const { setHasSelectedOrganization } = useRouteContext();
  const caregiverId = useSelector(
    (state: {loginReducer?: {data?: {userId?: string}}}) =>
      state?.loginReducer?.data?.userId || '',
  );

  const {
    open: openCategory,
    close: closeCategory,
    visible: visibleCategory,
    transY: transYCategory,
  } = useModalAnimation();

  const [category, setCategory] = useState<OrganizationItem>({
    id: 0,
    name: 'Select organization',
    displayName: 'Select organization',
  });
  const [organizationList, setOrganizationList] = useState<categoryList[]>([]);

  const [selectedOrganizationId, setSelectedOrganizationId] = useState(0);

  const orgId = useSelector(
    (state: {currentOrgIdReducer?: {orgId?: string}}) =>
      state?.currentOrgIdReducer?.orgId || ''
  );

  const getOrganizationList = useCallback(async () => {
    setLoader(true);
    try {
      const response = await apiPostWithToken(
        {
          userId: caregiverId,
        },
        URLS.caregiverUrl + 'getOrganization',
      );

      if (response?.status === 200) {
        setLoader(false);
        const organizationData = response?.data?.data || [];

        const formattedList: categoryList[] = [];
        if (organizationData.length > 0) {
          organizationData.forEach((org: any) => {
            formattedList.push({
              id: parseInt(org.groupId) || 0,
              name: org.groupName || '',
              displayName: org.groupName || '',
            });
          });
          setOrganizationList(formattedList);
        }
      } else {
        setLoader(false);
        const errorMessage = response?.response?.data?.responseMessage
          ? response?.response?.data?.responseMessage
          : response.message === 'Network Error'
          ? 'Network error. Please check your data connection.'
          : response.message || 'Unknown error';

        Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
      }
    } catch (error) {
      setLoader(false);
      Alert.alert(
        'Error',
        'An unexpected error occurred while fetching organizations. Please try again.',
        [{text: 'Dismiss'}]
      );
    }
  }, [caregiverId]);

  const onChangeCategory = useCallback((item: categoryList) => {
    setCategory(item);
    closeCategory();
    if (item) {
      setSelectedOrganizationId(item.id);
    }
  }, [closeCategory]);

  useEffect(() => {
    getOrganizationList();
  }, [getOrganizationList]);

  const renderHeader = () => {
    return (
      <View style={{padding: 5, margin: 10}}>
        <Modal
          visible={visibleCategory}
          onRequestClose={closeCategory}
          transparent
          animationType={'fade'}>
          <ModalSlideBottom onClose={closeCategory} transY={transYCategory}>
            <ModalChangeCategory
              onChangeCategory={onChangeCategory}
              data={organizationList}
            />
          </ModalSlideBottom>
        </Modal>
      </View>
    );
  };

  return (
    <Container style={styles.container}>
      <Loader modalVisible={loader} />
      {renderHeader()}
      <View style={{marginHorizontal: 10}}>
        <View style={styles.logoApp}>
          <Image source={require('@/assets/images/logo_1.png')} style={styles.logo} />
        </View>

        <View style={{padding: 0, marginHorizontal: 5, marginTop: 25}}>
          <ButtonChangeCategory category={category} onPress={openCategory} />
        </View>

        <View style={{marginTop: 15}}>
          <ButtonLinear
            title={'Continue'}
            onPress={async () => {
              if (selectedOrganizationId === 0) {
                Alert.alert('Error', 'You must select an organization', [{text: 'OK'}]);
              } else {
                // Store both organization ID and details
                dispatch(setCurrentOrgId(selectedOrganizationId.toString()));
                dispatch(setCurrentOrgDetails({
                  id: selectedOrganizationId.toString(),
                  name: category.name
                }));

                // Set that organization has been selected to enable notifications
                setHasSelectedOrganization(true);

                try {
                  // Navigate directly to the tabs
                  router.replace('/(tabs)');
                } catch (error) {
                  // Silently handle error and show user-friendly message
                  // Fallback navigation if the tabs route fails
                  Alert.alert('Navigation Error', 'There was an error navigating to the dashboard. Please try again.', [
                    {text: 'OK'}
                  ]);
                }
              }
            }}
            disabled={false}
            loader={loader}
          />
        </View>
      </View>
    </Container>
  );
};

export default Organization;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: CoreColors.Platinum,
  },
  item: {
    padding: 15,
    borderRadius: 5,
    width: '100%',
    ...Theme.flexRow,
    marginBottom: 10,
    opacity: 0.75,
    backgroundColor: '#ced4da',
  },
  minMax: {
    backgroundColor: 'white',
    padding: 10,
    borderRadius: 10,
    width: (Constants.width - 40) / 2,
    height: (Constants.width - 250) / 2,
    ...Theme.shadow,
    justifyContent: 'center',
    marginBottom: 10,
    marginLeft: 10,
    marginRight: 10,
    marginTop: 0,
  },
  verticalDivider: {
    width: 1,
    height: '85%',
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    marginVertical: scale(5),
  },
  logoApp: {
    marginTop: 5,
    alignSelf: 'center',
    alignItems: 'center',
  },
  logo: {
    marginBottom: 0,
    marginLeft: 0,
  },
});
