import React, { useState, useEffect, useCallback } from 'react';
import {
  TouchableOpacity,
  View,
  StyleSheet,
  Animated,
  AppState,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useAlertsCount } from '@/context/AlertsCountContext';
import { Colors } from '@/constants';
import Text from '@/components/Text';

interface FCMNotificationIconProps {
  color?: string;
  size?: number;
}

const FCMNotificationIcon: React.FC<FCMNotificationIconProps> = ({ 
  color = Colors.White, 
  size = 24 
}) => {
  const router = useRouter();
  const scaleAnim = React.useRef(new Animated.Value(1)).current;
  
  // Use the new alerts count context instead of FCM notifications
  const { messageCount, loading } = useAlertsCount();


  // Animate when count changes
  useEffect(() => {
    if (messageCount > 0) {
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.2,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [messageCount]);

  // Navigate to notifications screen (unread messages)
  const handlePress = () => {
    
    // Animation feedback
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    // Navigate to the notifications screen (which now shows unread messages)
    router.push('/fcm-notifications');
  };

  const containerStyle = color === '#6B7280' ? styles.homeContainer : styles.container;

  return (
    <TouchableOpacity
      style={containerStyle}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <Animated.View style={[styles.iconContainer, { transform: [{ scale: scaleAnim }] }]}>
        <Ionicons name="notifications-outline" size={size} color={color} />
        
        {/* Badge for message count */}
        {typeof messageCount === 'number' && messageCount > 0 && (
          <Animated.View style={[styles.badge, { transform: [{ scale: scaleAnim }] }]}>
            <Text style={styles.badgeText}>
              {messageCount > 99 ? '99+' : String(messageCount)}
            </Text>
          </Animated.View>
        )}
        
        {/* Loading indicator */}
        {loading && (
          <View style={styles.loadingIndicator}>
            <View style={styles.loadingDot} />
          </View>
        )}
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: 42,
    height: 42,
    borderRadius: 21,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  homeContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(107, 114, 128, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(107, 114, 128, 0.2)',
  },
  iconContainer: {
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: Colors.RedNeonFuchsia,
    borderRadius: 10,
    minWidth: 18,
    height: 18,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
    borderWidth: 2,
    borderColor: Colors.White,
  },
  badgeText: {
    color: Colors.White,
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  loadingIndicator: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.TealBlue,
    opacity: 0.7,
  },
  loadingDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: Colors.White,
    margin: 2,
  },
});

export default FCMNotificationIcon;
