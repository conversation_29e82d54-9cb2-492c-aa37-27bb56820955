/**
 * Truncates a string to a specified length and adds an ellipsis if needed
 * 
 * @param input The input string to truncate (will convert non-strings to strings)
 * @param maxLength The maximum length allowed before truncating
 * @param suffix The string to add after truncation (default: "...")
 * @returns The truncated string with suffix if needed, or the original string
 */
export default function truncateString(
  input: string | number | undefined | null,
  maxLength: number,
  suffix: string = '...'
): string {
  // Handle edge cases
  if (input === undefined || input === null) {
    return '';
  }
  
  // Convert to string if not already
  const str = String(input);
  
  // Validate maxLength
  if (maxLength <= 0) {
    return str;
  }
  
  // Return original string if it's shorter than max length
  if (str.length <= maxLength) {
    return str;
  }
  
  // Calculate how many characters we can include, accounting for suffix length
  const truncateAt = maxLength - suffix.length;
  
  // If truncation point is negative or zero, return just the first character + suffix
  if (truncateAt <= 0) {
    return str.charAt(0) + suffix;
  }
  
  // Return truncated string with suffix
  return str.substring(0, truncateAt) + suffix;
}
