import { InteractionManager, Platform } from 'react-native';
import { isTablet, isLargeTablet } from './responsive';

interface PerformanceMetrics {
  memoryUsage?: number;
  renderTime?: number;
  interactionTime?: number;
  bundleSize?: number;
  timestamp: number;
}

interface MemoryWarning {
  level: 'low' | 'medium' | 'high' | 'critical';
  usage: number;
  threshold: number;
  timestamp: number;
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetrics[] = [];
  private memoryWarnings: MemoryWarning[] = [];
  private isMonitoring = false;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private memoryThresholds = {
    tablet: {
      low: 0.6,      // 60% of available memory
      medium: 0.75,  // 75% of available memory
      high: 0.85,    // 85% of available memory
      critical: 0.95, // 95% of available memory
    },
    phone: {
      low: 0.7,      // 70% of available memory
      medium: 0.8,   // 80% of available memory
      high: 0.9,     // 90% of available memory
      critical: 0.95, // 95% of available memory
    },
  };

  private constructor() {}

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Start performance monitoring
   */
  startMonitoring(interval: number = 5000): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, interval);

    console.log('Performance monitoring started');
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    console.log('Performance monitoring stopped');
  }

  /**
   * Collect current performance metrics
   */
  private collectMetrics(): void {
    const metrics: PerformanceMetrics = {
      timestamp: Date.now(),
    };

    // Collect memory usage (if available)
    if (Platform.OS === 'android' && (global as any).nativePerformanceObserver) {
      try {
        const memoryInfo = (global as any).nativePerformanceObserver.memory;
        if (memoryInfo) {
          metrics.memoryUsage = memoryInfo.usedJSHeapSize / memoryInfo.totalJSHeapSize;
          this.checkMemoryThreshold(metrics.memoryUsage);
        }
      } catch (error) {
        console.warn('Failed to collect memory metrics:', error);
      }
    }

    this.metrics.push(metrics);

    // Keep only last 100 metrics to prevent memory leaks
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }
  }

  /**
   * Check if memory usage exceeds thresholds
   */
  private checkMemoryThreshold(usage: number): void {
    const thresholds = isTablet() ? this.memoryThresholds.tablet : this.memoryThresholds.phone;
    let level: MemoryWarning['level'] = 'low';

    if (usage >= thresholds.critical) {
      level = 'critical';
    } else if (usage >= thresholds.high) {
      level = 'high';
    } else if (usage >= thresholds.medium) {
      level = 'medium';
    }

    if (level !== 'low') {
      const warning: MemoryWarning = {
        level,
        usage,
        threshold: thresholds[level],
        timestamp: Date.now(),
      };

      this.memoryWarnings.push(warning);
      this.handleMemoryWarning(warning);

      // Keep only last 50 warnings
      if (this.memoryWarnings.length > 50) {
        this.memoryWarnings = this.memoryWarnings.slice(-50);
      }
    }
  }

  /**
   * Handle memory warnings
   */
  private handleMemoryWarning(warning: MemoryWarning): void {
    console.warn(`Memory warning: ${warning.level} (${(warning.usage * 100).toFixed(1)}%)`);

    switch (warning.level) {
      case 'critical':
        this.triggerMemoryCleanup();
        break;
      case 'high':
        this.optimizeMemoryUsage();
        break;
      case 'medium':
        this.suggestMemoryOptimization();
        break;
    }
  }

  /**
   * Trigger aggressive memory cleanup
   */
  private triggerMemoryCleanup(): void {
    console.log('Triggering memory cleanup...');
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    // Clear image caches (if using react-native-fast-image)
    if ((global as any).FastImage) {
      (global as any).FastImage.clearMemoryCache();
    }

    // Emit memory pressure event
    this.emitMemoryPressureEvent('critical');
  }

  /**
   * Optimize memory usage
   */
  private optimizeMemoryUsage(): void {
    console.log('Optimizing memory usage...');
    
    // Clear old metrics
    this.metrics = this.metrics.slice(-50);
    this.memoryWarnings = this.memoryWarnings.slice(-25);

    // Emit memory pressure event
    this.emitMemoryPressureEvent('high');
  }

  /**
   * Suggest memory optimization
   */
  private suggestMemoryOptimization(): void {
    console.log('Memory usage is elevated, consider optimizing');
    this.emitMemoryPressureEvent('medium');
  }

  /**
   * Emit memory pressure event for components to handle
   */
  private emitMemoryPressureEvent(level: MemoryWarning['level']): void {
    // This would typically emit an event that components can listen to
    // For now, we'll just log it
    console.log(`Memory pressure event: ${level}`);
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  /**
   * Get memory warnings
   */
  getMemoryWarnings(): MemoryWarning[] {
    return [...this.memoryWarnings];
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): {
    averageMemoryUsage: number;
    peakMemoryUsage: number;
    memoryWarningsCount: number;
    lastWarning?: MemoryWarning;
  } {
    const memoryMetrics = this.metrics
      .filter(m => m.memoryUsage !== undefined)
      .map(m => m.memoryUsage!);

    return {
      averageMemoryUsage: memoryMetrics.length > 0 
        ? memoryMetrics.reduce((sum, usage) => sum + usage, 0) / memoryMetrics.length 
        : 0,
      peakMemoryUsage: memoryMetrics.length > 0 ? Math.max(...memoryMetrics) : 0,
      memoryWarningsCount: this.memoryWarnings.length,
      lastWarning: this.memoryWarnings[this.memoryWarnings.length - 1],
    };
  }

  /**
   * Clear all metrics and warnings
   */
  clearMetrics(): void {
    this.metrics = [];
    this.memoryWarnings = [];
    console.log('Performance metrics cleared');
  }
}

/**
 * Performance measurement utilities
 */
export class PerformanceMeasurement {
  private startTime: number;
  private label: string;

  constructor(label: string) {
    this.label = label;
    this.startTime = performance.now();
  }

  /**
   * End measurement and log result
   */
  end(): number {
    const duration = performance.now() - this.startTime;
    console.log(`Performance: ${this.label} took ${duration.toFixed(2)}ms`);
    return duration;
  }

  /**
   * End measurement and return result without logging
   */
  endSilent(): number {
    return performance.now() - this.startTime;
  }
}

/**
 * Memory management utilities
 */
export const MemoryManager = {
  /**
   * Get tablet-specific memory limits
   */
  getMemoryLimits() {
    if (isLargeTablet()) {
      return {
        maxImageCacheSize: 200 * 1024 * 1024, // 200MB
        maxListItems: 1000,
        maxCachedScreens: 10,
      };
    } else if (isTablet()) {
      return {
        maxImageCacheSize: 150 * 1024 * 1024, // 150MB
        maxListItems: 500,
        maxCachedScreens: 8,
      };
    } else {
      return {
        maxImageCacheSize: 100 * 1024 * 1024, // 100MB
        maxListItems: 200,
        maxCachedScreens: 5,
      };
    }
  },

  /**
   * Optimize list rendering for large datasets
   */
  getOptimalListConfig(itemCount: number) {
    const limits = this.getMemoryLimits();
    
    return {
      windowSize: Math.min(itemCount, isTablet() ? 20 : 10),
      initialNumToRender: isTablet() ? 15 : 10,
      maxToRenderPerBatch: isTablet() ? 10 : 5,
      updateCellsBatchingPeriod: isTablet() ? 50 : 100,
      removeClippedSubviews: itemCount > limits.maxListItems / 2,
      getItemLayout: itemCount > 100 ? (data: any, index: number) => ({
        length: isTablet() ? 120 : 80,
        offset: (isTablet() ? 120 : 80) * index,
        index,
      }) : undefined,
    };
  },

  /**
   * Create memory-efficient image loading configuration
   */
  getImageLoadingConfig() {
    const limits = this.getMemoryLimits();
    
    return {
      cache: 'memory',
      priority: 'normal',
      resizeMode: 'cover',
      fadeDuration: isTablet() ? 300 : 200,
      cacheKeyPolicy: 'urlAndCacheKey',
      loadingIndicatorSource: undefined, // Disable for better performance
      progressiveRenderingEnabled: true,
      borderRadius: 0, // Avoid expensive border radius calculations
    };
  },

  /**
   * Clean up resources
   */
  cleanup() {
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    // Clear React Navigation cache
    if ((global as any).__REACT_NAVIGATION_CACHE__) {
      (global as any).__REACT_NAVIGATION_CACHE__.clear();
    }

    console.log('Memory cleanup completed');
  },
};

/**
 * Performance optimization utilities
 */
export const PerformanceOptimizer = {
  /**
   * Debounce function calls for better performance
   */
  debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number,
    immediate?: boolean
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout | null = null;
    
    return function executedFunction(...args: Parameters<T>) {
      const later = () => {
        timeout = null;
        if (!immediate) func(...args);
      };
      
      const callNow = immediate && !timeout;
      
      if (timeout) clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      
      if (callNow) func(...args);
    };
  },

  /**
   * Throttle function calls
   */
  throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    
    return function executedFunction(...args: Parameters<T>) {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  /**
   * Run task after interactions complete
   */
  runAfterInteractions(task: () => void): void {
    InteractionManager.runAfterInteractions(task);
  },

  /**
   * Batch multiple state updates
   */
  batchUpdates(updates: (() => void)[]): void {
    InteractionManager.runAfterInteractions(() => {
      updates.forEach(update => update());
    });
  },
};

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();

// Auto-start monitoring in development
if (__DEV__) {
  performanceMonitor.startMonitoring();
}

export default {
  PerformanceMeasurement,
  MemoryManager,
  PerformanceOptimizer,
  performanceMonitor,
};