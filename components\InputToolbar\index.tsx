import React, {memo, useCallback, useRef, useState} from 'react';
import {Platform, StyleSheet, TextInput, View, ViewStyle} from 'react-native';
import Animated from 'react-native-reanimated';
import ButtonIcon from '@/components/Buttons/ButtonIcon';
import Colors from '@/constants/Colors';

interface Props {
  style?: ViewStyle | object;
  inputToolBarStyle?: ViewStyle | object;
  placeholder?: string;
  linearButton?: boolean;
  onScrollToEnd: () => void;
  onSendMessage: (text: string) => void;
  onShare: () => void;
  onSendFile: () => void;
  onFocusInput?: () => void;
  onBlur?: () => void;
}

export default memo(
  ({
    style,
    onSendMessage,
    onScrollToEnd,
    placeholder,
    inputToolBarStyle,
    onFocusInput,
  }: Props) => {
    const [chatContent, setContent] = useState('');
    const inputRef = useRef<any>();

    const _onSendMessage = useCallback(() => {
      onSendMessage && onSendMessage(chatContent);
      inputRef.current.clear();
    }, [chatContent, onSendMessage]);

    const _onFocusInput = useCallback(() => {
      onScrollToEnd && onScrollToEnd();
      onFocusInput && onFocusInput();
    }, []);

    return (
      <Animated.View style={[styles.container, style && style]}>
        <View
          style={[
            styles.inputToolBarStyle,
            inputToolBarStyle,
            {backgroundColor: Colors.LightChatBox},
          ]}>
          <TextInput
            ref={inputRef}
            placeholder={placeholder ? placeholder : ''}
            style={[styles.textInput, {color: Colors.Black}]}
            value={chatContent}
            onChangeText={setContent}
            onFocus={_onFocusInput}
          />
        </View>
        <ButtonIcon
          tintColor={Colors.White}
          style={styles.buttonSend}
          icon={'send'}
          onPress={_onSendMessage}
        />
      </Animated.View>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  inputToolBarStyle: {
    borderRadius: 8,
    paddingHorizontal: 20,
    height: 50,
    flex: 1,
    ...Platform.select({
      ios: {
        paddingVertical: 12,
      },
    }),
  },
  textInput: {
    fontSize: 16,
  },
  buttonAttack: {
    marginHorizontal: 16,
  },
  buttonSend: {
    marginLeft: 16,
  },
});
