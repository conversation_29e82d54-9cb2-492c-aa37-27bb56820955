import React from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams } from 'expo-router';
import { Stack } from 'expo-router';

import EditTaskScreen from '@/screens/Tasks/editTask';

export default function EditTaskRoute() {
  const { id } = useLocalSearchParams();

  return (
    <>
      <Stack.Screen options={{ title: 'Edit Task' }} />
      <SafeAreaView style={styles.container}>
        <EditTaskScreen taskId={id as string} />
      </SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});