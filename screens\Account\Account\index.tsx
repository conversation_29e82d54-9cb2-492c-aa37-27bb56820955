import {useFocusEffect, useNavigation} from '@react-navigation/native';
import React, {memo, useCallback, useState} from 'react';
import {Alert, Image, ImageSourcePropType, StyleSheet, View} from 'react-native';
import {getBottomSpace, getStatusBarHeight} from 'react-native-iphone-x-helper';
import AccountItem from '@/components/AccountItem/Index';
import ButtonIcon from '@/components/Buttons/ButtonIcon';
import ButtonText from '@/components/Buttons/ButtonText';
import Content from '@/components/Layout/Content';
import Layout from '@/components/Layout/Layout';
import Text from '@/components/Text';
import {Colors, Routes} from '@/constants';
import {useTheme} from '@/constants/Theme';
import {SOURCE_ICON} from '@/assets/images';
import {ACCOUNT_IMAGE} from '@/assets/images/Account';
import {AVATAR} from '@/assets/images/avatar';
import Theme from '@/constants/Theme';

interface AccountData {
  id: number;
  name: string;
  email: string;
  accountType: string;
}

interface AccountScreenProps {
  navigation?: any;
}

export const ACCOUNT_SAMPLE: AccountData = {
  id: 1,
  name: '<PERSON>',
  email: '<EMAIL>',
  accountType: 'Prime Account',
};

/**
 * Account Screen
 * Main account settings and information screen
 */
const Account = memo((props: AccountScreenProps) => {
  const {theme} = useTheme();
  const navigation = useNavigation<any>();
  const [account, setAccount] = useState<AccountData>(ACCOUNT_SAMPLE);
  // Dark mode has been removed from the app
  useFocusEffect(
    useCallback(() => {
      setAccount(ACCOUNT_SAMPLE);
    }, []),
  );

  const onBankDeposit = useCallback(() => {
    navigation.navigate(Routes.BankDeposit);
  }, [navigation]);

  const onInviteExpert = useCallback(() => {
    navigation.navigate(Routes.AccountInviteExpert);
  }, [navigation]);

  const onEditButton = useCallback(() => {
    Alert.alert('Edit Profile', 'This feature will allow you to edit your profile information.');
  }, []);

  return (
    <Content
      style={styles.container}
      scrollEventThrottle={16}
      bounces={false}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.contentContainerStyle}>
      <View style={styles.infoView}>
        <View style={Theme.flexRow}>
          <Image source={AVATAR.wallace as ImageSourcePropType} style={styles.avatar} />
          <View>
            <Text size={15} bold marginBottom={4}>
              {account.name}
            </Text>
            <Text style={styles.email}>{account.email}</Text>
            <Text style={styles.type}>{account.accountType}</Text>
          </View>
        </View>
        <ButtonIcon
          icon={require('@/assets/images/ic_edit.png')}
          style={styles.icon}
          size={24}
          onPress={onEditButton}
        />
      </View>
      <Layout style={styles.content}>
        <AccountItem
          onPress={onBankDeposit}
          style={styles.firstView}
          icon={SOURCE_ICON.payment}
          name="Bank Deposit"
          lineBottom
        />
        <AccountItem
          style={styles.middleView}
          icon={SOURCE_ICON.setting}
          name="Setting"
          lineBottom
        />
        {/* Dark mode toggle removed */}
      </Layout>
      <Layout style={styles.content}>
        <AccountItem
          style={styles.firstView}
          icon={SOURCE_ICON.home}
          name="About Doctor Plus"
          lineBottom
        />
        <AccountItem
          style={styles.middleView}
          icon={SOURCE_ICON.help}
          name="Help & Support"
          lineBottom
        />
        <AccountItem
          style={styles.middleView}
          icon={SOURCE_ICON.term}
          name="Privacy and Policy"
          lineBottom
        />
        <View style={styles.lastView}>
          <View style={Theme.flexRow}>
            <Image style={styles.imageInvite} source={ACCOUNT_IMAGE.invite as ImageSourcePropType} />
            <View>
              <Text style={styles.text}>
                Invite experts and get $100 referral for you!
              </Text>
              <ButtonText
                borderColor={theme.colors.Snow || '#f8f8f8'}
                white
                title="Invite Now!"
                style={styles.button}
                onPress={onInviteExpert}
              />
            </View>
          </View>
        </View>
      </Layout>
    </Content>
  );
});

export default Account;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: getStatusBarHeight() + 52,
  },
  infoView: {
    ...Theme.flexRowSpace,
    marginHorizontal: 24,
    marginBottom: 32,
  },
  avatar: {
    width: 64,
    height: 64,
    marginRight: 16,
  },
  icon: {
    tintColor: Colors.White,
    backgroundColor: Colors.DodgerBlue,
    alignSelf: 'center',
  },
  name: {
    fontSize: 15,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  email: {
    fontSize: 13,
    marginBottom: 8,
  },
  type: {
    fontSize: 13,
    color: Colors.GrayBlue,
  },
  content: {
    marginHorizontal: 24,
    marginVertical: 8,
    borderRadius: 16,
  },
  firstView: {
    ...Theme.flexRowSpace,
    paddingTop: 24,
    paddingHorizontal: 24,
    paddingBottom: 16,
  },
  middleView: {
    ...Theme.flexRowSpace,
    paddingTop: 16,
    paddingHorizontal: 24,
    paddingBottom: 16,
  },
  lastView: {
    ...Theme.flexRowSpace,
    paddingTop: 16,
    paddingHorizontal: 24,
    paddingBottom: 24,
    paddingRight: 16,
  },
  button: {
    width: 90,
    height: 30,
    borderRadius: 8,
    backgroundColor: Colors.DodgerBlue,
  },
  text: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: 200,
    lineHeight: 25,
  },
  contentContainerStyle: {
    paddingBottom: getBottomSpace() + 100,
  },
  imageInvite: {
    marginRight: 18,
  },
});
