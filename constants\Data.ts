import {SOURCE_ICON} from '@/assets/images';
import {AVATAR} from '@/assets/images/avatar';
import {IMAGE} from '@/assets/images/Images';
import Routes from './Routes';

export const phonesAreaCodes = [
  {
    id: 0,
    img: require('images/country/img_flag.png'),
    name: 'America',
    code: '+1',
  },
  {
    id: 1,
    img: require('images/country/img_flag.png'),
    name: 'Afghanistan',
    code: '+93',
  },
  {
    id: 2,
    img: require('images/country/img_flag.png'),
    name: 'Albania',
    code: '+355',
  },
  {
    id: 3,
    img: require('images/country/img_flag.png'),
    name: 'Algeria',
    code: '+213',
  },
  {
    id: 4,
    img: require('images/country/img_flag.png'),
    name: 'American Samoa',
    code: '******',
  },
  {
    id: 5,
    img: require('images/country/img_flag.png'),
    name: 'Andorra',
    code: '+376',
  },
  {
    id: 6,
    img: require('images/country/img_flag.png'),
    name: 'Angola',
    code: '+244',
  },
  {
    id: 7,
    img: require('images/country/img_flag.png'),
    name: 'Anguilla',
    code: '******',
  },
];

export const CATEGORY_LIST_EXAMPLE = [
  {
    id: 1,
    name: 'Case Manager',
    displayName: 'CASEMANAGER',
  },
  {
    id: 2,
    name: 'Caregiver',
    displayName: 'CAREGIVER',
  },
  {
    id: 3,
    name: 'Physician',
    displayName: 'PHYSICIAN',
  },
];

export const FREQUENCY_LIST_EXAMPLE = [
  {
    id: 0,
    name: 'Select',
  },
  {
    id: 1,
    name: 'Frequency 1',
  },
  {
    id: 2,
    name: 'Frequency 2',
  },
  {
    id: 3,
    name: 'Frequency 3',
  },
];

export const TRENDING_TOPICS_DATA = [
  {
    id: 0,
    image: require('images/TrendingTopics/img_topic_1.png'),
    doctor: {
      name: 'Jesse Arnold',
      avatar: require('images/avatar/Jesse.png'),
      quantity: 'Integrative Medicine',
    },
    name: 'Air Pollution',
    description:
      'Air pollution is the introduction of chemicals and other materials into the atmosphere that cause harm to living organisms and the natural environment.',
  },
  {
    id: 1,
    image: require('images/TrendingTopics/img_topic_2.png'),
    doctor: {
      name: 'Blake Gomez',
      avatar: require('images/avatar/Blake.png'),
      quantity: 'Integrative Medicine',
    },
    name: 'Taminflu',
    description:
      'Air pollution is the introduction of chemicals and other materials into the atmosphere that cause harm to living organisms and the natural environment.',
  },
];

export const HEALTH_FEED_DATA = [
  {
    id: 0,
    subTitle: 'Health Tip',
    title: 'Quitting smoking',
    name: 'Dr.Sarah Conner',
    avatar: AVATAR.sarah,
    image: IMAGE.cigarette,
    action: 'shared',
    thanks: 12500,
    shares: 1200,
    quantity: 'Integrative Medicine',
    shareOn: 'Air Pollotion',
    subDescription:
      'Quitting smoking is the process of discontinuing the use of inhaled tobacco products.',
    description:
      'Quitting smoking is the process of discontinuing the use of inhaled tobacco products.\n\nInteresting Fact: Smoking is the number one cause of preventable death in the United States.',
  },
];

export const NEW_CONSULTS_DATA = [
  {
    id: 0,
    question: `Please provide some do's and don'ts for people who are sick with the flu or a cold.`,
    numberOfAnswers: 12,
    doctor: {
      name: 'Sandra Klevins',
      avatar: require('images/avatar/doctor-free-consults-avatar-1.png'),
    },
    image: require('images/Consults/consults-1.png'),
    answer: `Always use your own pen at the doctor's office and not the pen 100's of infected patients touched.`,
  },
  {
    id: 1,
    question: `What does a tender abdomen after painful stomach flu mean?`,
    numberOfAnswers: 5,
    doctor: {
      name: 'Francisco Webster',
      avatar: require('images/avatar/doctor-free-consults-avatar-2.png'),
    },
    image: require('images/Consults/consults-2.png'),
    answer: `Always use your own pen at the doctor's office and not the pen 100's of infected patients touched...`,
  },
];

export const FEATURE = [
  {
    icon: SOURCE_ICON.whiteAdditional,
    title: 'Overview',
    //route: Routes.
  },
  {
    icon: SOURCE_ICON.whiteCondition,
    title: 'Conditions & Symptoms',
    route: Routes.TopicDetailConditions,
  },
  {
    icon: SOURCE_ICON.whiteMedication,
    title: 'Medications',
    //route: Routes.
  },
  {
    icon: SOURCE_ICON.procedure,
    title: 'Procedures',
    //route: Routes.
  },
  {
    icon: SOURCE_ICON.healthGuide,
    title: 'Health Guide',
    //route: Routes.
  },
];

export const DATA_CONDITIONS = [
  'Muscle Pain',
  'Heart diseases',
  'Asthma',
  'Low back pain, better with bending forward',
];

export const LIST_DOCTOR_DATA = [
  {
    id: 0,
    name: 'Dr. Margaret Wells',
    avatar: AVATAR.doctor1,
    online: true,
  },
  {
    id: 1,
    name: 'Dr. Christine Bradley',
    avatar: AVATAR.doctor2,
    online: true,
  },
  {
    id: 2,
    name: 'Dr. Dylan Oliver',
    avatar: AVATAR.doctor5,
    online: true,
  },
  {
    id: 3,
    name: 'Dr. Marguerite Sutton',
    avatar: AVATAR.doctor7,
    online: true,
  },
];

export const PRIVATE_CHAT = [
  {
    id: 0,
    nameGroup: 'NYC Hospital',
    avatar: AVATAR.doctor3,
    //members: [AVATAR.doctor1, AVATAR.doctor2],
    message: 'I sent your patient contact...',
    online: false,
    read: false,
    time: '09:21',
  },
  {
    id: 1,
    name: 'Dr. Mattie Harper',
    avatar: AVATAR.doctor3,
    message: 'I sent your patient contact...',
    online: true,
    read: false,
    time: '07:56',
  },
  {
    id: 2,
    name: 'Dr. Lina Thompson',
    avatar: AVATAR.doctor4,
    message: 'How do you think?',
    online: false,
    read: true,
    time: '06:23',
  },
  {
    id: 3,
    name: 'Dr. Dylan Oliver',
    avatar: AVATAR.doctor4,
    message: 'Yes!',
    online: true,
    read: true,
    time: 'Tue',
  },
  {
    id: 4,
    name: 'Dr. Dylan Oliver',
    avatar: AVATAR.doctor5,
    message: 'Ok. I got it. Thank you',
    online: false,
    read: true,
    time: 'Sat',
  },
  {
    id: 5,
    nameGroup: 'Bio Genetics',
    avatar: AVATAR.doctor3,
    // members: [AVATAR.doctor6, AVATAR.doctor7],
    message: "That's good idea! I think so.",
    online: false,
    read: false,
    time: 'Jan 02',
  },
];

export const TASK_STATUS = [
  {
    id: 1,
    name: 'Open',
    displayName: 'Open',
  },
  {
    id: 2,
    name: 'Inprogress',
    displayName: 'Inprogress',
  },
  {
    id: 3,
    name: 'Closed',
    displayName: 'Closed',
  },
  {
    id: 4,
    name: 'Due',
    displayName: 'Due',
  },
  {
    id: 5,
    name: 'Over Due',
    displayName: 'Over Due',
  },
];

export const TASK_PRIORITY = [
  {
    id: 1,
    name: 'High',
    displayName: 'High',
  },
  {
    id: 2,
    name: 'Medium',
    displayName: 'Medium',
  },
  {
    id: 3,
    name: 'Low',
    displayName: 'Low',
  },
];
