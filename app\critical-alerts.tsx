import React from 'react';
import { Stack } from 'expo-router';

import CriticalAlertsScreen from '@/screens/CriticalAlerts';

export default function CriticalAlertsRoute() {
  return (
    <>
      <Stack.Screen 
        options={{ 
          title: 'Critical Alerts',
          headerShown: false // Hide the default header since we're using our custom NavigationHeader
        }} 
      />
      <CriticalAlertsScreen />
    </>
  );
}
