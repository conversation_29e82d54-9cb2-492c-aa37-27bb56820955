import { StackActions, useNavigation } from '@react-navigation/native';
import React, { memo, useCallback, useEffect, useState } from 'react';
import {
  Modal,
  StyleSheet,
  View,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Image,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { getStatusBarHeight } from 'react-native-iphone-x-helper';
import ButtonLinear from '@/components/Buttons/ButtonLinear';
import HeaderButton from '@/components/HeaderButton';
import InputApp from '@/components/InputApp';
import Loader from '@/components/Loader/Loader';
import SuccessFailAlert from '@/components/SuccessFailModal';
import Text from '@/components/Text';
import { Colors, Routes } from '@/constants';
import { SOURCE_ICON } from '@/assets/images';
import { apiPost } from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import scale from '@/utils/scale';
import validationEmail from '@/utils/validation/email';

const { width } = Dimensions.get('window');

const ForgetPassword = memo(() => {
  const [email, setEmail] = useState('');
  const [isValidEmail, setIsValidEmail] = useState(false);
  const [loader, setLoader] = useState(false);
  const [visibleModal, setVisibleModal] = useState(false);
  const [messasge, setMessage] = useState('');
  const [successOrFail, setSuccessOrFail] = useState(false);

  useEffect(() => {
    const validation = validationEmail(email);
    setIsValidEmail(validation);
  }, [email]);

  const navigation = useNavigation();

  const onSendEmail = async () => {
    setLoader(true);
    let user = {
      username: email,
    };
    const loginResponse = await apiPost(
      URLS.authUrl + 'caregiver/resentOTP',
      user,
    );
    if (loginResponse?.status == 200) {
      setLoader(false);
      if (loginResponse?.data?.responseMessage === 'Success') {
        setMessage('Password reset code has been sent to your email.');
        setVisibleModal(true);
        setSuccessOrFail(true);
      } else {
        setVisibleModal(true);
        setMessage(
          loginResponse?.data?.responseMessage === 'Failed'
            ? 'Failed to send password reset code. Please check your email.'
            : loginResponse?.data?.responseMessage,
        );
        setSuccessOrFail(false);
      }
    } else {
      setLoader(false);
      const errorMessage = loginResponse?.response?.data?.responseMessage
        ? loginResponse?.response?.data?.responseMessage
        : loginResponse.message === 'Network Error'
        ? 'Network error. Please check your data connection.'
        : loginResponse.message;
      setVisibleModal(true);
      setMessage(errorMessage);
      setSuccessOrFail(false);
    }
  };

  const close = useCallback(() => {
    setVisibleModal(false);
  }, []);

  const open = () => {
    setVisibleModal(false);
    console.log('Email-->', email);
    navigation.dispatch(StackActions.replace(Routes.RecoveryPassword, { email }));
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <Loader modalVisible={loader} />
      
      {/* Background Gradient */}
      <LinearGradient
        colors={['#f8fffe', '#e8f8f7', '#ffffff']}
        style={styles.background}
      />

      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Header */}
        <View style={styles.headerContainer}>
          <HeaderButton style={styles.headerButton} />
        </View>

        {/* Main Content */}
        <View style={styles.mainContent}>
          {/* Icon Section */}
          <View style={styles.iconContainer}>
            <View style={styles.iconWrapper}>
              <Image 
                source={require('@/assets/images/ic_accept.png')} 
                style={styles.lockIcon}
                resizeMode="contain"
              />
            </View>
          </View>

          {/* Text Section */}
          <View style={styles.textSection}>
            <Text style={styles.title}>Forgot Password?</Text>
            <Text style={styles.subtitle}>
              Don't worry! It happens. Please enter the email address associated with your account.
            </Text>
          </View>

          {/* Form Section */}
          <View style={styles.formSection}>
            <InputApp
              title="Email Address"
              value={email}
              onChangeText={setEmail}
              placeholder="Enter your email address"
              borderColor={isValidEmail ? Colors.TiffanyBlue : Colors.InnearColor}
              autoFocus
              keyboardType="email-address"
              icon={
                validationEmail(email) ? (
                  <Image
                    source={require('@/assets/images/ic_accept.png')}
                    style={styles.inputIcon}
                  />
                ) : (
                  <Image source={SOURCE_ICON['reset-search']} style={styles.inputIcon} />
                )
              }
              isShowIcon={true}
              style={styles.input}
            />

            <ButtonLinear
              title="Send Reset Code"
              onPress={onSendEmail}
              disabled={email?.length === 0 || !isValidEmail}
              style={styles.sendButton}
            />
          </View>

          {/* Footer Section */}
          <View style={styles.footerSection}>
            <Text style={styles.footerText}>
              Remember your password?{' '}
              <Text 
                style={styles.footerLink} 
                onPress={() => navigation.goBack()}
              >
                Back to Sign In
              </Text>
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Modal */}
      <Modal
        visible={visibleModal}
        onRequestClose={close}
        transparent
        animationType="fade"
      >
        <SuccessFailAlert
          close={close}
          open={open}
          primaryText="Okay"
          showPrimaryButton={successOrFail}
          secondaryText="Close"
          message={messasge}
          successOrFail={successOrFail}
          showSecondaryButton={!successOrFail}
        />
      </Modal>
    </KeyboardAvoidingView>
  );
});

export default ForgetPassword;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.White,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: getStatusBarHeight(),
    paddingBottom: 30,
  },
  headerContainer: {
    marginBottom: 20,
  },
  headerButton: {
    marginTop: 0,
  },
  mainContent: {
    flex: 1,
    justifyContent: 'center',
    paddingVertical: 40,
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  iconWrapper: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.TiffanyBlueOpacity,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.TiffanyBlue,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  lockIcon: {
    width: 40,
    height: 40,
    tintColor: Colors.TiffanyBlue,
  },
  textSection: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.DarkJungleGreen,
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: Colors.GrayBlue,
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
  },
  formSection: {
    marginBottom: 40,
  },
  input: {
    borderRadius: 12,
    backgroundColor: Colors.White,
    shadowColor: Colors.Black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  inputIcon: {
    width: 20,
    height: 20,
    tintColor: Colors.TiffanyBlue,
  },
  sendButton: {
    marginTop: 32,
    height: 56,
    borderRadius: 16,
    shadowColor: Colors.TiffanyBlue,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  footerSection: {
    alignItems: 'center',
    marginTop: 'auto',
  },
  footerText: {
    fontSize: 16,
    color: Colors.GrayBlue,
    textAlign: 'center',
  },
  footerLink: {
    color: Colors.TiffanyBlue,
    fontWeight: '600',
  },
});
