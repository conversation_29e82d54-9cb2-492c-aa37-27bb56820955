import { Text, type TextProps, StyleSheet } from 'react-native';

import { useThemeColor } from '@/hooks/useThemeColor';
import { Typography, FontFamily } from '@/constants/Fonts';

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string; // Kept for backward compatibility but not used
  type?: 'default' | 'title' | 'defaultSemiBold' | 'subtitle' | 'link' | 'h1' | 'h2' | 'h3' | 'bodyLarge' | 'bodyMedium' | 'bodySmall' | 'labelLarge' | 'labelMedium' | 'labelSmall' | 'button';
};

export function ThemedText({
  style,
  lightColor,
  darkColor,
  type = 'default',
  ...rest
}: ThemedTextProps) {
  // Only use lightColor, ignore darkColor as we only support light mode
  const color = useThemeColor({ light: lightColor }, 'text');

  return (
    <Text
      style={[
        { color },
        // Legacy styles
        type === 'default' ? styles.default : undefined,
        type === 'title' ? styles.title : undefined,
        type === 'defaultSemiBold' ? styles.defaultSemiBold : undefined,
        type === 'subtitle' ? styles.subtitle : undefined,
        type === 'link' ? styles.link : undefined,
        // New typography styles
        type === 'h1' ? Typography.h1 : undefined,
        type === 'h2' ? Typography.h2 : undefined,
        type === 'h3' ? Typography.h3 : undefined,
        type === 'bodyLarge' ? Typography.bodyLarge : undefined,
        type === 'bodyMedium' ? Typography.bodyMedium : undefined,
        type === 'bodySmall' ? Typography.bodySmall : undefined,
        type === 'labelLarge' ? Typography.labelLarge : undefined,
        type === 'labelMedium' ? Typography.labelMedium : undefined,
        type === 'labelSmall' ? Typography.labelSmall : undefined,
        type === 'button' ? Typography.button : undefined,
        style,
      ]}
      {...rest}
    />
  );
}

const styles = StyleSheet.create({
  // Legacy styles with updated font family
  default: {
    fontSize: 16,
    lineHeight: 24,
    fontFamily: FontFamily.primary.regular,
  },
  defaultSemiBold: {
    fontSize: 16,
    lineHeight: 24,
    fontFamily: FontFamily.primary.semiBold,
  },
  title: {
    fontSize: 32,
    lineHeight: 32,
    fontFamily: FontFamily.primary.bold,
  },
  subtitle: {
    fontSize: 20,
    fontFamily: FontFamily.primary.bold,
  },
  link: {
    lineHeight: 30,
    fontSize: 16,
    color: '#0a7ea4',
    fontFamily: FontFamily.primary.medium,
  },
});
