import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';
import { initializeFirebaseApp } from './firebaseConfig';
import { getMessagingToken, setBackgroundMessageHandler } from './firebaseMessaging';

// Set notification handler to determine how notifications are handled when app is in foreground
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

// Storage keys
const FCM_TOKEN_STORAGE_KEY = '@FCMToken';
const BACKGROUND_NOTIFICATIONS_KEY = '@BackgroundNotifications';

let isRegistrationInProgress = false;
let lastRegistrationTime = 0;
const REGISTRATION_COOLDOWN = 60000;

let firebaseInitialized = false;

const initializeFirebase = () => {
  if (!firebaseInitialized) {
    try {
      initializeFirebaseApp();
      firebaseInitialized = true;
      console.log('Firebase initialized for messaging');
    } catch (error) {
      console.error('Error initializing Firebase for messaging:', error);
    }
  }
};

setBackgroundMessageHandler(async remoteMessage => {
  console.log('Background FCM message received:', JSON.stringify(remoteMessage));
  
  try {
    initializeFirebase();
    
    await storeBackgroundNotification(remoteMessage);
    console.log('Background notification stored successfully');
  } catch (error) {
    console.error('Error storing background notification:', error);
  }
});

export async function storeBackgroundNotification(remoteMessage: any) {
  try {
    const storedNotifications = await AsyncStorage.getItem(BACKGROUND_NOTIFICATIONS_KEY);
    let backgroundNotifications: any[] = [];
    
    if (storedNotifications) {
      backgroundNotifications = JSON.parse(storedNotifications);
    }
    
    const messageData = remoteMessage.data || {};
    
    const extractedType = messageData.type || messageData.notificationType || messageData.category || 'general';
    const extractedPatientId = messageData.patientId || messageData.patient_id || messageData.id || messageData.userId;
    const extractedPatientName = messageData.patientName || messageData.patient_name || messageData.name;
    const extractedRoute = messageData.route || messageData.screen || messageData.target;
    
    const notificationItem = {
      id: `bg-fcm-${Date.now()}-${Math.random()}`,
      title: remoteMessage.notification?.title || 'WatchRx Caregiver',
      body: remoteMessage.notification?.body || '',
      data: {
        ...messageData,
        type: extractedType,
        patientId: extractedPatientId,
        patientName: extractedPatientName,
        route: extractedRoute,
        originalAndroidData: messageData
      },
      timestamp: Date.now(),
      read: false,
      type: extractedType,
      receivedInBackground: true,
      messageId: remoteMessage.messageId || null,
    };
    
    console.log('Storing background notification with data:', {
      type: extractedType,
      patientId: extractedPatientId,
      patientName: extractedPatientName,
      route: extractedRoute,
      title: notificationItem.title
    });
    
    backgroundNotifications.unshift(notificationItem);
    
    if (backgroundNotifications.length > 50) {
      backgroundNotifications = backgroundNotifications.slice(0, 50);
    }
    
    await AsyncStorage.setItem(BACKGROUND_NOTIFICATIONS_KEY, JSON.stringify(backgroundNotifications));
    console.log('Background notification stored:', notificationItem.title);
  } catch (error) {
    console.error('Error storing background notification:', error);
  }
}

export async function getAndClearBackgroundNotifications(): Promise<any[]> {
  try {
    const storedNotifications = await AsyncStorage.getItem(BACKGROUND_NOTIFICATIONS_KEY);
    
    if (storedNotifications) {
      const backgroundNotifications = JSON.parse(storedNotifications);
      
      await AsyncStorage.removeItem(BACKGROUND_NOTIFICATIONS_KEY);
      
      return backgroundNotifications;
    }
    
    return [];
  } catch (error) {
    console.error('Error retrieving background notifications:', error);
    return [];
  }
}

export async function getBackgroundNotifications(): Promise<any[]> {
  try {
    const storedNotifications = await AsyncStorage.getItem(BACKGROUND_NOTIFICATIONS_KEY);
    
    if (storedNotifications) {
      const backgroundNotifications = JSON.parse(storedNotifications);
      return backgroundNotifications;
    }
    
    return [];
  } catch (error) {
    console.error('Error getting background notifications:', error);
    return [];
  }
}

export async function clearBackgroundNotifications() {
  try {
    await AsyncStorage.removeItem(BACKGROUND_NOTIFICATIONS_KEY);
    console.log('🧹 Background notifications cleared');
  } catch (error) {
    console.error('Error clearing background notifications:', error);
  }
}

export async function registerForPushNotificationsAsync() {
  let token;

  if (isRegistrationInProgress) {
    console.log('Registration already in progress, returning cached token');
    return await getFCMToken();
  }

  const now = Date.now();
  if (now - lastRegistrationTime < REGISTRATION_COOLDOWN) {
    console.log('Registration in cooldown period, returning cached token');
    return await getFCMToken();
  }

  isRegistrationInProgress = true;

  try {
    if (!Device.isDevice) {
      console.log('Not a physical device, skipping push registration');
      isRegistrationInProgress = false;
      return null;
    }

    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('default', {
        name: 'Default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }

    console.log('Checking existing notification permissions...');
    const existingPermissions = await Notifications.getPermissionsAsync();
    console.log('Existing notification permission status:', existingPermissions.status);

    let permissionStatus = existingPermissions;

    if (existingPermissions.status !== 'granted') {
      console.log('Requesting notification permissions...');
      
      permissionStatus = await Notifications.requestPermissionsAsync({
        ios: {
          allowAlert: true,
          allowBadge: true,
          allowSound: true,
          allowProvisional: false,
          allowAnnouncements: true,
          allowCriticalAlerts: false,
        },
        android: {
          allowAlert: true,
          allowBadge: true,
          allowSound: true,
        },
      });

      console.log('Permission request result:', permissionStatus.status);
      console.log('Full permission details:', JSON.stringify(permissionStatus, null, 2));
    }

    if (permissionStatus.status !== 'granted') {
      console.log('Permission not granted for notifications');
      isRegistrationInProgress = false;
      return null;
    }

    try {
      initializeFirebase();
      
      console.log(`${Platform.OS}: Getting FCM token...`);
      
      const fcmToken = await getMessagingToken();
      
      if (fcmToken) {
        console.log(`${Platform.OS}: FCM token received:`, fcmToken);
        token = fcmToken;
      } else {
        console.error('Failed to get FCM token');
        isRegistrationInProgress = false;
        return null;
      }

      if (token) {
        console.log('Storing token:', token);
        await setFCMToken(token);
      }
    } catch (error) {
      console.error('Error getting FCM token:', error);
    }

    lastRegistrationTime = Date.now();

    isRegistrationInProgress = false;
    console.log('Push notification registration complete, token:', token);

    return token;
  } catch (error) {
    console.error('Unexpected error in registerForPushNotificationsAsync:', error);
    isRegistrationInProgress = false;
    throw error;
  }
}

export async function setFCMToken(token: string) {
  try {
    await AsyncStorage.setItem(FCM_TOKEN_STORAGE_KEY, token);
  } catch (error) {
  }
}

export async function getFCMToken() {
  try {
    return await AsyncStorage.getItem(FCM_TOKEN_STORAGE_KEY);
  } catch (error) {
    return null;
  }
}

export async function resetFCMToken() {
  try {
    await AsyncStorage.removeItem(FCM_TOKEN_STORAGE_KEY);
  } catch (error) {
  }
}

export function registerNotificationListeners() {
  const notificationListener = Notifications.addNotificationReceivedListener(_ => {
  });

  const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
    const { title, body } = response.notification.request.content;

    if (title && body) {
      Alert.alert(title, body);
    }
  });

  return { notificationListener, responseListener };
}

export function removeNotificationListeners(listeners: {
  notificationListener?: Notifications.Subscription,
  responseListener?: Notifications.Subscription
}) {
  if (listeners.notificationListener) {
    Notifications.removeNotificationSubscription(listeners.notificationListener);
  }
  if (listeners.responseListener) {
    Notifications.removeNotificationSubscription(listeners.responseListener);
  }
}

let isBackendRegistrationInProgress = false;
let lastBackendRegistrationTime = 0;
const BACKEND_REGISTRATION_COOLDOWN = 60000;

export async function registerDeviceWithBackend(apiPostWithToken: Function, caregiverId: string, token: string, FCMURL: string) {
  if (!token) {
    console.log('No token provided for backend registration');
    return null;
  }

  if (isBackendRegistrationInProgress) {
    console.log('Backend registration already in progress');
    return null;
  }

  const now = Date.now();
  if (now - lastBackendRegistrationTime < BACKEND_REGISTRATION_COOLDOWN) {
    console.log('Backend registration in cooldown period');
    return { status: 200, message: 'Registration skipped (cooldown)' };
  }

  isBackendRegistrationInProgress = true;

  try {
    console.log(`Registering ${Platform.OS} device with backend for caregiver ID: ${caregiverId}`);
    console.log('Token being registered:', token);

    const formattedToken = token;
    console.log(`${Platform.OS}: Using FCM token for backend:`, formattedToken);

    const response = await apiPostWithToken(
      {
        deviceType: Platform.OS,
        nurseId: '' + caregiverId,
        registerId: formattedToken,
      },
      FCMURL
    );

    console.log('Backend registration response:', response);

    lastBackendRegistrationTime = Date.now();

    isBackendRegistrationInProgress = false;

    return response;
  } catch (error) {
    console.error('Error registering device with backend:', error);
    isBackendRegistrationInProgress = false;
    return null;
  }
}