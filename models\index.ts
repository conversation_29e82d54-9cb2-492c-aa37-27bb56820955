export interface ServerError {
  errorMessage: string;
  status?: number;
  data?: any;
}

export interface PatientItemProps {
  patientId: number;
  image: any;
  patientName: string;
  address: string;
  phone: string;
  patientAlertsCount: number;
  imeiNo: string;
  gpsStatus: string;
  trackingStatus: string;
  radius: string;
  latLong: string;
  reachableStatus: Boolean;
  programs: WatchRxPrograms;
  dob?: string; // Date of birth
  mrn?: string; // Medical Record Number
}

export interface WatchRxPrograms {
  selectedPrograms: Program[];
  availablePrograms: Program[];
}
export interface Program {
  mins: number;
  programName: string;
  programActivated: boolean;
  patientProgramId?: string;
  programId: string;
}

export interface MedicationsProps {
  medicineId: number;
  image: any;
  medicineName: string;
  timeSlots: string;
  daysOfWeek: string;
  color: string;
  strength: string;
  quantities: string;
  medtime_rel_food: string;
  beforeOrAfterFood: string;
  medicineForm: string;
}

export interface VitalReportsProps {
  vitalType: number;
  vitalData: any;
  vitalDate: string;
}

export interface UserProfileProps {
  phoneNumber: string;
  firstName: string;
  lastName: string;
  picPath: string;
  name: string;
  userName: string;
  address1: string;
  address2: string;
  state: string;
  city: string;
  zip: string;
  country: string;
  addresId: string;
}

export interface AlertsProps {
  patientId: string;
  patientName: string;
  gcmRegistrationId: string;
  missedTime: string;
  alertDescription: string;
  missedTimeSlot: string;
  missedBeforeOrAfterFood: string;
  missedMedicationIds: string;
  alertType: string;
  alertId: string;
  mrn: string;
  createdDate: string;
  updatedOn: string;
  color: string;
}

export interface CustomAlertsProps {
  alertType: string;
  alertTime: string;
  alertId: string;
  detail: string;
  startDate: string;
  endDate: string;
  type: string;
}

export interface TaskItemProps {
  taskId: string;
  taskTitle: string;
  taskDesc: string;
  taskPriority: string;
  taskStartDate: string;
  taskEndDate: string;
  taskStatus: string;
  patientId: string;
  patientName: string;
}

export interface TodayTaskItem {
  id: string;
  title: string;
  description: string;
  start: string; // ISO date string
  end: string; // ISO date string
  className: string;
  patientId: string;
  userId: string;
  priority: string;
  patinetName: string; // Note: API has typo in field name
}

export interface TodayTasksResponse {
  data: TodayTaskItem[];
  success: boolean;
}
