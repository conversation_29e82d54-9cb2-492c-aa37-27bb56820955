
import {Platform, StyleSheet, ViewStyle} from 'react-native';
import Constants from 'expo-constants';
import React from 'react';

// Replacement for react-native-iphone-x-helper functions
const getBottomSpace = () => {
  return Platform.OS === 'ios' ? 34 : 0;
};

const getStatusBarHeight = () => {
  return Constants.statusBarHeight || 20;
};

// Import from local constants
import Colors from './Colors';
import {HEADER_HEIGHT} from './Const';
import { 
  isTablet, 
  isLargeTablet, 
  spacing as responsiveSpacing, 
  typography as responsiveTypography,
  iconSizes,
  buttonDimensions,
  tabletLayout,
  getOrientation 
} from '@/utils/responsive';

// Define theme mode type - only light mode is supported
export type TMode = 'light';

// Create Theme Context
export const ThemeContext = React.createContext<{
  theme: typeof defaultTheme;
  toggleTheme: () => void; // Kept for compatibility
  colorScheme: TMode;
}>({
  theme: {} as any,
  toggleTheme: () => {}, // No-op function
  colorScheme: 'light',
});

// Create a hook for theme usage
export const useTheme = () => {
  return React.useContext(ThemeContext);
};

// Enhanced theme object with tablet-specific configurations
export const defaultTheme = {
  colors: Colors,
  spacing: responsiveSpacing,
  typography: responsiveTypography,
  iconSizes,
  buttonDimensions,
  
  // Legacy spacing for backward compatibility
  legacySpacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32
  },
  
  // Tablet-specific theme properties
  tablet: {
    maxContentWidth: tabletLayout.maxContentWidth,
    sidebarWidth: tabletLayout.sidebarWidth,
    masterDetailSplit: tabletLayout.masterDetailSplit,
    gridColumns: tabletLayout.gridColumns,
    touchTargets: tabletLayout.touchTargets,
    spacingMultiplier: tabletLayout.spacingMultiplier,
    
    // Enhanced spacing for tablets
    enhancedSpacing: {
      xs: isTablet() ? responsiveSpacing.xs * 1.2 : responsiveSpacing.xs,
      sm: isTablet() ? responsiveSpacing.sm * 1.2 : responsiveSpacing.sm,
      md: isTablet() ? responsiveSpacing.md * 1.2 : responsiveSpacing.md,
      lg: isTablet() ? responsiveSpacing.lg * 1.2 : responsiveSpacing.lg,
      xl: isTablet() ? responsiveSpacing.xl * 1.2 : responsiveSpacing.xl,
      xxl: isTablet() ? responsiveSpacing.xxl * 1.2 : responsiveSpacing.xxl,
    },
    
    // Tablet-specific component sizing
    components: {
      card: {
        minHeight: isLargeTablet() ? 140 : isTablet() ? 120 : 100,
        padding: isLargeTablet() ? 24 : isTablet() ? 20 : 16,
        borderRadius: isTablet() ? 16 : 12,
        margin: isLargeTablet() ? 20 : isTablet() ? 16 : 12,
      },
      button: {
        height: buttonDimensions.height,
        padding: buttonDimensions.padding,
        borderRadius: buttonDimensions.borderRadius,
        minWidth: isTablet() ? 120 : 100,
      },
      input: {
        height: isTablet() ? 52 : 48,
        padding: isTablet() ? 16 : 12,
        borderRadius: isTablet() ? 12 : 8,
        fontSize: isTablet() ? 18 : 16,
      },
      modal: {
        borderRadius: isTablet() ? 20 : 16,
        padding: isTablet() ? 32 : 24,
        maxWidth: isTablet() ? 600 : '90%',
      },
    },
    
    // Orientation-specific adjustments
    orientation: {
      landscape: {
        containerPadding: isLargeTablet() ? 48 : isTablet() ? 32 : 20,
        contentSpacing: isTablet() ? 24 : 16,
        sidebarVisible: isTablet(),
      },
      portrait: {
        containerPadding: isLargeTablet() ? 32 : isTablet() ? 24 : 16,
        contentSpacing: isTablet() ? 20 : 16,
        sidebarVisible: false,
      },
    },
  },
  
  // Additional properties needed by components (legacy)
  innearColor: Colors.Platinum,
  searchBox: Colors.Snow,
  text: Colors.DarkJungleGreen
};

// Define themes object - only light mode is supported
export const themes = {
  light: defaultTheme
};

export default StyleSheet.create({
  flexRowSpace: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  flexRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  flexOne: {
    flex: 1,
  },
  flexDirection: {
    flexDirection: 'row',
  },
  container: {
    flex: 1,
    backgroundColor: Colors.Snow,
    paddingHorizontal: 24,
    paddingBottom: getBottomSpace(),
    paddingTop: getStatusBarHeight(),
  },
  icons: {
    width: 24,
    height: 24,
  },
  icons32: {
    width: 32,
    height: 32,
  },
  icons16: {
    width: 16,
    height: 16,
  },
  alignSelfCenter: {
    alignSelf: 'center',
  },
  shadow: {
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3,
      },
      android: {
        elevation: 2,
        // No background color to preserve original component colors
      },
    }),
  },
  buttonSlider: {
    width: 48,
    height: 6,
    backgroundColor: Colors.Platinum,
    marginTop: 12,
    borderRadius: 3,
    alignSelf: 'center',
  },
  headerStyle: {
    elevation: 0,
    shadowOpacity: 0,
    borderBottomWidth: 0,
  },
  headerBackGround: {
    flex: 1,
    // backgroundColor: Colors.Snow,
  },
  headerNavigationStyle: {
    shadowColor: 'transparent',
    height: HEADER_HEIGHT,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 108 - getStatusBarHeight(),
    paddingTop: getStatusBarHeight(),
  },
  updateProfileContentScrollStyle: {
    paddingHorizontal: 24,
    paddingBottom: getBottomSpace() + 16,
    paddingTop: 15, // Consistent padding for all screens
  },
  headerComponent: {
    height: HEADER_HEIGHT,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    ...Platform.select({
      ios: {
        paddingTop: getStatusBarHeight(),
      },
      android: {},
    }),
  },
});
