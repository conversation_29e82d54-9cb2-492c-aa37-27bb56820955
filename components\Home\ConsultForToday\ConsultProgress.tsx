import React, {memo, useMemo} from 'react';
import {StyleSheet, View, ViewStyle} from 'react-native';
import Animated, {
  add,
  block,
  Clock,
  Extrapolate,
  interpolateNode,
  multiply,
  set,
  useCode,
  Value,
} from 'react-native-reanimated';
import Svg, {Defs, LinearGradient, Path, Stop} from 'react-native-svg';
import Text from '@/components/Text';
import {Colors} from '@/constants';
import Theme from '@/constants/Theme';
import {runTiming} from '@/utils/runTiming';

const width = 101;
const height = 106;
const viewBox = '0 0 101 106';
const size = 86;
const half = 86 / 2;
function circleD(firstPoint: {x: number; y: number}, radius: number) {
  const moveToFirstPoint = `M${firstPoint.x - 2} ${firstPoint.y}`;
  const _1stArc = `a${radius} ${radius} 0 0 1 ${radius} ${radius} `;
  const _2ndArc = `a${radius} ${radius} 0 0 1 ${-radius} ${radius} `;
  const _3rdArc = `a${radius} ${radius} 0 0 1 ${-radius} ${-radius} `;
  const _4thArc = `a${radius} ${radius} 0 0 1 ${radius} ${-radius} `;
  return [moveToFirstPoint, _1stArc, _2ndArc, _3rdArc, _4thArc].join(' ');
}

const AnimatedPath = Animated.createAnimatedComponent(Path);

interface ConsultsProgressProps {
  current: number;
  total: number;
  strokeWidth?: number;
  style?: ViewStyle;
}

const ConsultsProgress = memo(
  ({
    current = 60,
    total = 100,
    strokeWidth = 5,
    style,
  }: ConsultsProgressProps) => {
    const path =
      'M48 10C24.2518 10 5 29.2518 5 53C5 76.7482 24.2518 96 48 96C71.7482 96 91 76.7482 91 53C91 29.2518 71.7482 10 48 10ZM48 16C68.4345 16 85 32.5655 85 53C85 73.4345 68.4345 90 48 90C27.5655 90 11 73.4345 11 53C11 32.5655 27.5655 16 48 16Z';

    const animation = useMemo(() => new Value(0), []);
    const progress = useMemo(() => new Value(0), []);
    const clock = new Clock();
    useCode(
      () =>
        block([
          set(animation, runTiming(clock)),
          set(
            progress,
            interpolateNode(animation, {
              inputRange: [0, 1],
              outputRange: [1, (total - current) / total],
              extrapolate: Extrapolate.CLAMP,
            }),
          ),
        ]),
      [current, total],
    );

    // const strokeDashoffset = Math.PI * size * progress - 2;
    const strokeDashoffset = add(multiply(Math.PI, size, progress), -2);
    const r = size - strokeWidth;
    return (
      <View style={[styles.container, style]}>
        <View
          style={{
            width: 96,
            height: 96,
            borderRadius: 96 / 2,
            backgroundColor: Colors.White,
            position: 'absolute',
            top: (height - 96) / 2,
            left: 0,
            ...Theme.center,
          }}>
          <Text
            hilight
            size={32}
            lineHeight={48}
            center
            marginTop={-strokeWidth / 2}
            marginLeft={-strokeWidth / 2}
            bold
            color={Colors.TiffanyBlue}
            textDecorationLine={'underline'}>
            {current}
          </Text>
        </View>
        <Svg width={width} height={height} viewBox={viewBox}>
          <Path d={path} fill={Colors.Isabelline} />
          <AnimatedPath
            d={circleD({x: width / 2, y: (height - r) / 2}, r / 2)}
            fill="transparent"
            strokeWidth={6}
            strokeDasharray={Math.PI * size}
            strokeDashoffset={strokeDashoffset}
            stroke="url(#grad)"
            strokeLinecap="round"
          />
          <Defs>
            <LinearGradient id="grad" x1="0" y1="0" x2="1" y2="0">
              <Stop offset="0" stopColor="#38F399" stopOpacity="1" />
              <Stop offset="1" stopColor={Colors.TiffanyBlue} stopOpacity="1" />
            </LinearGradient>
          </Defs>
        </Svg>
      </View>
    );
  },
);

export default ConsultsProgress;

const styles = StyleSheet.create({
  container: {
    width: width,
    height: height,
    // borderRadius: 96 / 2,
    ...Theme.center,
  },
});
