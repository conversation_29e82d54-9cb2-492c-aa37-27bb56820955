import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, Platform, Settings, View } from 'react-native';
import { useRouteContext } from '@/context/RouteContext';
import * as SplashScreen from 'expo-splash-screen';
import { useDispatch } from 'react-redux';
import PushController from '@/utils/ExpoPushController';
import Loader from '@/components/Loader/Loader';
import { logInSuccess } from '@/services/actions/loginActions';
import { fetchUserProfile } from '@/services/actions/userProfileActions';
import { apiPost } from '@/services/apis/apiManager';
import URLS from '@/services/config/config';

import {
  fetchCredentialsSInfo,
  getStoredUserRole,
  resetCredentials,
  setAuthToken
} from '@/services/secure-storage';

// Define interfaces for credentials and authentication
interface Credentials {
  username: string;
  password: string;
  userRole?: 'caregiver' | 'physician';
}

interface AuthResponse {
  responseMessage: string;
  token: string;
  userId: string;
}

// Prevent the splash screen from auto-hiding
SplashScreen.preventAutoHideAsync().catch(() => {
  /* reloading the app might trigger some race conditions, ignore them */
});

/**
 * Splash Screen
 * Handles app initialization, authentication, and redirection
 */
function Splash() {
  const [loader, setLoader] = useState(true);
  const dispatch = useDispatch();
  const router = useRouter();
  const { setHasSelectedOrganization } = useRouteContext();


  /**
   * Initialize the app, check for stored credentials, and navigate accordingly
   */
  const initApp = async () => {

    if (Platform.OS == 'ios') {
      if (!Settings.get('hasRunBefore')) {
        resetCredentials();
        Settings.set({hasRunBefore: true});
      }
    }

    return fetchCredentialsSInfo().then(async cred => {
      if (cred && cred.username && cred.password) {
        setLoader(true);
        
        // Get stored role information
        const storedRole = await getStoredUserRole();
        const userRole = cred.userRole || storedRole?.userType || 'caregiver';
        
        let user = {
          username: cred.username,
          password: cred.password,
          userType: userRole,
        };
        
        const loginResponse = await apiPost(
          URLS.authUrl + 'authenticate',
          user,
        );
        if (loginResponse?.status == 200) {
          setLoader(false);
          if (loginResponse?.data?.responseMessage === 'Success') {
            let data = {
              ...user,
              token: loginResponse?.data?.token,
              userId: loginResponse?.data?.userId,
              userRole: userRole,
              roleId: userRole === 'physician' ? 3 : 5,
            };
            dispatch(logInSuccess(data));
            setAuthToken(loginResponse?.data?.token);
            dispatch(
              fetchUserProfile({careGiverId: loginResponse?.data?.userId}),
            );
            await SplashScreen.hideAsync();
            // Navigate to the organization screen
            navigateAfterLogin();
          } else {
            doCleanUp();
            await SplashScreen.hideAsync();
            router.replace('/onboarding');
          }
        } else {
          setLoader(false);
          doCleanUp();
          await SplashScreen.hideAsync();
          router.replace('/onboarding');
        }
        } else {
        setLoader(false);
        doCleanUp();
        await SplashScreen.hideAsync();
        router.replace('/onboarding');
      }
    });
  };

  const doCleanUp = async () => {
    resetCredentials();
  };

  /**
   * Navigate to the appropriate screen after login
   */
  const navigateAfterLogin = () => {
    // Reset organization selection state
    setHasSelectedOrganization(false);

    try {
      router.replace('/organization');
    } catch (error) {
      // If navigation fails, show error and fallback to tabs
      Alert.alert(
        'Navigation Error',
        'Unable to navigate to organization screen. Going to home screen instead.',
        [{ text: 'OK' }]
      );
      router.replace('/(tabs)');
    }
  };

  useEffect(() => {
    initApp();
  }, [dispatch, router]); // Add dependencies

  return (
    <View style={{flex: 1, backgroundColor: 'white'}}>
      <Loader modalVisible={loader} />
      <PushController />
    </View>
  );
}

export default Splash;