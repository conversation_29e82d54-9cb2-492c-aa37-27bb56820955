import React from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams } from 'expo-router';
import { useDispatch } from 'react-redux';

import CCMReportsScreen from '@/screens/PatientDetails/CCMReports';
import { setCurrentPatientId } from '@/services/actions/patientActions';

export default function CCMReportsRoute() {
  const { id } = useLocalSearchParams();
  const dispatch = useDispatch();

  React.useEffect(() => {
    if (id) {
      dispatch(setCurrentPatientId(id as string));
    }
  }, [id, dispatch]);

  return (
    <SafeAreaView style={styles.container}>
      <CCMReportsScreen />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
