import React from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  Platform,
} from 'react-native';
import {Colors} from '@/constants';
import {useTheme} from '@/constants/Theme';
import Theme from '@/constants/Theme';
import LinearColors from '../../LinearColors';
import Text from '../../Text';
import ButtonBorder from '../ButtonBorder';
import { createShadow } from '@/utils/shadowStyles';

interface ButtonLinearProps {
  title: string;
  style?: ViewStyle;
  onPress?: () => void;
  children?: any;
  leftChildren?: any;
  styleButton?: ViewStyle;
  disabled?: boolean;
  white?: boolean;
  black?: boolean;
  colors?: string[];
  loader?: boolean;
}

const ButtonLinear = ({
  title,
  style,
  onPress,
  children,
  styleButton,
  leftChildren,
  black,
  disabled,
  colors,
  loader,
}: ButtonLinearProps) => {
  const {theme} = useTheme();
  if (disabled) {
    return (
      <ButtonBorder
        black={black}
        title={title}
        style={{...styleButton, ...styles.container, marginTop: 15}}
        backgroundColor={theme.placeholder}
        borderColor={Colors.Isabelline}
      />
    );
  }
  return (
    <TouchableOpacity
      style={[styleButton, Platform.OS === 'android' && styles.androidFix]}
      activeOpacity={0.8}
      onPress={onPress}>
      <LinearColors
        style={{...styles.container, ...style}}
        vertical
        locations={[0, 0.75]}
        colors={colors || [Colors.TurquoiseBlue, Colors.TealBlue]}>
        {leftChildren}
        <Text white color={Colors.White} type="H5" bold>
          {title}
        </Text>
        {loader && <ActivityIndicator size="large" color="red" />}
        {children}
      </LinearColors>
    </TouchableOpacity>
  );
};

export default ButtonLinear;

const styles = StyleSheet.create({
  container: {
    height: 50,
    marginTop: 24,
    borderRadius: 12,
    ...Theme.center,
    ...Theme.flexRow,
  },
  androidFix: {
    // Fix for Android shadow/ripple effect issues
    overflow: 'hidden',
    ...createShadow(0), // No elevation to prevent shadow artifacts
  },
});
