import Moment from 'moment';
import React, { memo, useEffect, useRef } from 'react';
import { StyleSheet, View, Animated, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Text from '@/components/Text';
import { Colors } from '@/constants';

interface Props {
  answer: string;
  timestamp: string;
  patientName?: string;
  isLastMessage?: boolean;
  connectingDevice?: string;
}

const ReceivedBubble = memo(({ answer, timestamp, patientName, isLastMessage = false, connectingDevice = 'watch' }: Props) => {
  const slideAnim = useRef(new Animated.Value(-50)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  
  const isMobileDevice = connectingDevice === 'mobile';

  useEffect(() => {
    Animated.parallel([
      Animated.spring(slideAnim, {
        toValue: 0,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const formatTimestamp = (timestamp: string) => {
    if (!timestamp || timestamp.trim() === '') {
      return '';
    }

    try {
      // Try multiple timestamp formats that might be used
      const formats = [
        'MM-DD-YYYY hh:mm:ss',
        'MM-DD-YYYY HH:mm:ss',
        'YYYY-MM-DD hh:mm:ss',
        'YYYY-MM-DD HH:mm:ss',
        'YYYY-MM-DD hh:mm A',
        'YYYY-MM-DD HH:mm A',
        'MM/DD/YYYY hh:mm:ss',
        'MM/DD/YYYY HH:mm:ss',
        'DD-MM-YYYY hh:mm:ss',
        'DD-MM-YYYY HH:mm:ss'
      ];

      let messageTime = Moment();
      let formatWorked = false;

      // Try each format until one works
      for (const format of formats) {
        const testTime = Moment(timestamp, format, true);
        if (testTime.isValid()) {
          messageTime = testTime;
          formatWorked = true;
          break;
        }
      }

      // If no specific format worked, try Moment's default parsing
      if (!formatWorked) {
        const testTime = Moment(timestamp);
        if (testTime.isValid()) {
          messageTime = testTime;
        } else {
          console.warn('Unable to parse timestamp:', timestamp);
          return 'Recently';
        }
      }

      const now = Moment();
      const today = now.clone().startOf('day');
      const yesterday = now.clone().subtract(1, 'day').startOf('day');

      if (messageTime.isSame(today, 'day')) {
        return messageTime.format('h:mm A');
      } else if (messageTime.isSame(yesterday, 'day')) {
        return `Yesterday ${messageTime.format('h:mm A')}`;
      } else if (messageTime.isAfter(now.clone().subtract(7, 'days'))) {
        return messageTime.format('ddd h:mm A');
      } else {
        return messageTime.format('MMM DD, h:mm A');
      }
    } catch (error) {
      console.warn('Error formatting timestamp:', timestamp, error);
      return 'Recently';
    }
  };

  const getAvatarText = () => {
    if (patientName && patientName.trim().length > 0) {
      return patientName.trim().charAt(0).toUpperCase();
    }
    return 'P';
  };

  return (
    <Animated.View 
      style={[
        styles.container,
        {
          transform: [
            { translateX: slideAnim },
            { scale: scaleAnim }
          ],
          opacity: opacityAnim,
        }
      ]}
    >
      <View style={styles.rowContainer}>
        <View style={styles.avatarContainer}>
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>
              {getAvatarText()}
            </Text>
          </View>
          <View style={styles.onlineIndicator} />
        </View>
        
        <View style={styles.contentContainer}>
          <View style={[
            styles.bubbleContainer,
            Platform.OS === 'ios' && styles.bubbleContainerIOS,
            Platform.OS === 'android' && styles.bubbleContainerAndroid,
          ]}>
            <Text style={styles.messageText}>
              {answer}
            </Text>
          </View>
          <Text style={styles.timestampText}>
            {formatTimestamp(timestamp)}
          </Text>
        </View>
      </View>
    </Animated.View>
  );
});

export default ReceivedBubble;

const styles = StyleSheet.create({
  container: {
    paddingRight: 64,
    marginBottom: 16,
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.TealBlue,
    justifyContent: 'center',
    alignItems: 'center',
    ...Platform.select({
      ios: {
        shadowColor: Colors.Black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.15,
        shadowRadius: 4,
      },
      android: {
        elevation: 3,
      },
    }),
  },
  avatarText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.White,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#4AE54A',
    borderWidth: 1.5,
    borderColor: Colors.White,
  },
  contentContainer: {
    maxWidth: '85%',
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  bubbleContainer: {
    backgroundColor: Colors.White,
    paddingHorizontal: 18,
    paddingVertical: 14,
    borderRadius: 22,
    borderBottomLeftRadius: 8,
    minHeight: 46,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  bubbleContainerIOS: {
    shadowColor: Colors.Black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  bubbleContainerAndroid: {
    elevation: 2,
  },
  messageText: {
    fontSize: 16,
    color: Colors.DarkJungleGreen,
    lineHeight: 22,
    fontWeight: '400',
  },
  timestampText: {
    fontSize: 11,
    color: Colors.GrayBlue,
    marginTop: 4,
    marginLeft: 8,
  }
});
