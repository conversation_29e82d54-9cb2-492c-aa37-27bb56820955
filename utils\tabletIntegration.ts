/**
 * Tablet Integration Utilities
 * 
 * This file provides utilities to integrate tablet-optimized components
 * throughout the app and ensure proper tablet experience
 */

import { isTablet, isLargeTablet } from './responsive';

/**
 * Screen mapping for tablet-optimized components
 */
export const TABLET_SCREEN_MAPPING = {
  // Main tab screens
  'app/(tabs)/patients.tsx': 'components/Patients/PatientListTablet',
  'app/(tabs)/index.tsx': 'components/Home/TabletHome', // Need to create
  'app/(tabs)/schedule.tsx': 'components/Schedule/TabletSchedule', // Need to create
  'app/(tabs)/tasks.tsx': 'components/Tasks/TabletTasks', // Need to create
  'app/(tabs)/profile.tsx': 'components/Profile/TabletProfile', // Need to create
  
  // Patient detail screens
  'app/patient/[id].tsx': 'components/PatientDetails/PatientDetailTablet',
  'app/patient/chat/': 'components/Chat/TabletChatInterface',
  'app/patient/video-call/': 'components/VideoCall/TabletVideoCallInterface',
  
  // Auth screens
  'app/auth/login.tsx': 'components/Auth/TabletLogin', // Need to create
  'app/auth/signup.tsx': 'components/Auth/TabletSignup', // Need to create
  
  // Other screens
  'app/alerts.tsx': 'components/Alerts/TabletAlerts', // Need to create
  'app/billing.tsx': 'components/Billing/TabletBilling', // Need to create
  'app/onboarding.tsx': 'components/OnBoarding/TabletOnBoarding', // Need to create
};

/**
 * Screens that still need tablet optimization
 */
export const REMAINING_TABLET_SCREENS = [
  'TabletBilling',
  'TabletCriticalAlerts',
  'TabletPatientSummary',
  'TabletValidation',
  'TabletMedications',
  'TabletVitals',
  'TabletCarePlan',
  'TabletCCMReports',
  'TabletUpdateProfile',
];

/**
 * Get the appropriate component for a screen based on device type
 */
export const getScreenComponent = (screenName: string, mobileComponent: any, tabletComponent?: any) => {
  if (isTablet() && tabletComponent) {
    return tabletComponent;
  }
  return mobileComponent;
};

/**
 * Check if a screen has tablet optimization
 */
export const hasTabletOptimization = (screenPath: string): boolean => {
  return Object.keys(TABLET_SCREEN_MAPPING).includes(screenPath);
};

/**
 * Get tablet-specific props for a screen
 */
export const getTabletScreenProps = (screenName: string) => {
  const baseProps = {
    isTablet: isTablet(),
    isLargeTablet: isLargeTablet(),
  };
  
  // Add screen-specific props based on screen type
  switch (screenName) {
    case 'PatientList':
      return {
        ...baseProps,
        columns: isLargeTablet() ? 3 : 2,
        cardSize: 'large',
      };
    case 'PatientDetail':
      return {
        ...baseProps,
        layout: 'sideBySide',
        showTabs: true,
      };
    case 'VideoCall':
      return {
        ...baseProps,
        controlsPosition: 'bottom',
        showParticipantList: true,
      };
    case 'Chat':
      return {
        ...baseProps,
        showSidebar: isLargeTablet(),
        messageSpacing: 'large',
      };
    default:
      return baseProps;
  }
};

/**
 * Initialize tablet-specific configurations
 */
export const initializeTabletSupport = () => {
  // Set up tablet-specific configurations
  if (isTablet()) {
    // Enable tablet-specific features
    console.log('Tablet mode enabled');
    
    // Set up tablet-specific navigation
    // Set up tablet-specific themes
    // Set up tablet-specific performance optimizations
  }
};

export default {
  TABLET_SCREEN_MAPPING,
  REMAINING_TABLET_SCREENS,
  getScreenComponent,
  hasTabletOptimization,
  getTabletScreenProps,
  initializeTabletSupport,
};