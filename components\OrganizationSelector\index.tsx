import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  FlatList,
  Modal,
  Dimensions,
  Platform,
  Image,
  Keyboard,
  KeyboardAvoidingView,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { useTheme } from '@/constants/Theme';
import { Colors } from '@/constants';
import useModalAnimation from '@/hooks/useModalAnimation';
import Animated, { useAnimatedStyle, interpolate, withTiming, useSharedValue } from 'react-native-reanimated';
import { SearchIcon, ChevronDownIcon, CheckmarkIcon, CloseIcon } from '@/components/Icons';
import { createShadow } from '@/utils/shadowStyles';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

interface OrganizationItem {
  id: number | string;
  name: string;
  displayName?: string;
  icon?: string; // Optional icon URL
}

interface OrganizationSelectorProps {
  value: OrganizationItem;
  onChange: (item: OrganizationItem) => void;
  items: OrganizationItem[];
  placeholder?: string;
  label?: string;
  disabled?: boolean;
  error?: string;
}

/**
 * A modern organization selector component with a bottom sheet dropdown
 */
const OrganizationSelector: React.FC<OrganizationSelectorProps> = ({
  value,
  onChange,
  items,
  placeholder = 'Select organization',
  label = 'Organization',
  disabled = false,
  error,
}) => {
  // State to track keyboard visibility
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  // Modal animation hook
  const {
    open: openDropdown,
    close: closeDropdown,
    visible: dropdownVisible,
    transY: transYDropdown,
  } = useModalAnimation();

  // Search functionality
  const [searchQuery, setSearchQuery] = useState('');
  const filteredItems = items.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Animation values
  const rotateAnim = useSharedValue(0);

  // Theme
  const { theme } = useTheme();

  // Handle dropdown open/close animations
  const handleOpenDropdown = () => {
    rotateAnim.value = withTiming(1, { duration: 300 });
    openDropdown();
  };

  const handleCloseDropdown = () => {
    rotateAnim.value = withTiming(0, { duration: 300 });
    // Dismiss keyboard first on Android to prevent animation issues
    if (Platform.OS === 'android' && keyboardVisible) {
      Keyboard.dismiss();
      // Small delay to ensure keyboard is dismissed before animation starts
      setTimeout(() => {
        closeDropdown();
      }, 100);
    } else {
      closeDropdown();
    }
  };

  // Handle item selection
  const handleSelectItem = useCallback((item: OrganizationItem) => {
    // Dismiss keyboard first on Android
    if (Platform.OS === 'android' && keyboardVisible) {
      Keyboard.dismiss();
    }
    onChange(item);
    handleCloseDropdown();
  }, [onChange, keyboardVisible, handleCloseDropdown]);

  // Generate initials from organization name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Animated chevron style
  const chevronStyle = useAnimatedStyle(() => {
    const rotate = interpolate(
      rotateAnim.value,
      [0, 1],
      [0, 180]
    );

    return {
      transform: [{ rotate: `${rotate}deg` }],
    };
  });

  // Render organization item
  const renderItem = useCallback(({ item }: { item: OrganizationItem }) => {
    const isSelected = value?.id === item.id;
    const initials = getInitials(item.name);

    return (
      <TouchableOpacity
        style={[
          styles.itemContainer,
          isSelected && { backgroundColor: Colors.TealBlue + '20' }
        ]}
        onPress={() => handleSelectItem(item)}
        activeOpacity={0.7}
      >
        <View style={[
          styles.iconContainer,
          {
            backgroundColor: isSelected ? Colors.TealBlue + '30' : Colors.Platinum + '90',
            borderWidth: isSelected ? 2 : 0,
            borderColor: isSelected ? Colors.TealBlue : 'transparent'
          }
        ]}>
          {item.icon ? (
            <Image source={{ uri: item.icon }} style={styles.icon} />
          ) : (
            <Text style={[
              styles.initials,
              { color: isSelected ? Colors.TealBlue : Colors.GrayBlue }
            ]}>
              {initials}
            </Text>
          )}
        </View>
        <View style={styles.itemTextContainer}>
          <Text style={[
            styles.itemText,
            {
              color: theme.text,
              fontWeight: isSelected ? '600' : '400'
            }
          ]}>
            {item.name}
          </Text>
        </View>
        {isSelected && (
          <View style={styles.checkmarkContainer}>
            <CheckmarkIcon color={Colors.TealBlue} size={20} />
          </View>
        )}
      </TouchableOpacity>
    );
  }, [value, handleSelectItem, theme]);

  // Add keyboard listeners
  React.useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (e) => {
        setKeyboardVisible(true);
        setKeyboardHeight(e.endCoordinates.height);
      }
    );

    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardVisible(false);
        setKeyboardHeight(0);
      }
    );

    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
    };
  }, []);

  // Animated style for the modal that accounts for keyboard height
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: SCREEN_HEIGHT - transYDropdown.value }],
      maxHeight: Platform.OS === 'android' && keyboardVisible
        ? SCREEN_HEIGHT * 0.5 // Limit height more on Android when keyboard is visible
        : SCREEN_HEIGHT * 0.7,
    };
  }, [keyboardVisible]);

  return (
    <View style={styles.container}>
      {/* Label with enhanced styling */}
      {label && (
        <Text style={[
          styles.label,
          {
            color: error ? Colors.Tomato : theme.text
          }
        ]}>
          {label}
        </Text>
      )}

      {/* Enhanced Selector Button */}
      <TouchableOpacity
        style={[
          styles.selectorButton,
          {
            backgroundColor: theme.searchBox,
            borderColor: error ? Colors.Tomato : theme.innearColor,
            ...createShadow(1),
          },
          disabled && styles.disabledButton
        ]}
        onPress={handleOpenDropdown}
        disabled={disabled}
        activeOpacity={0.7}
      >
        <View style={styles.selectorContent}>
          {/* Display selected org icon/initials if available */}
          {value?.id ? (
            <View style={[styles.smallIconContainer, { backgroundColor: Colors.TealBlue + '30' }]}>
              {value.icon ? (
                <Image source={{ uri: value.icon }} style={styles.smallIcon} />
              ) : (
                <Text style={styles.smallInitials}>{getInitials(value.name)}</Text>
              )}
            </View>
          ) : (
            <View style={[styles.placeholderIcon, { backgroundColor: Colors.Platinum }]}>
              <SearchIcon color={Colors.GrayBlue} size={16} />
            </View>
          )}

          <Text
            style={[
              styles.selectorText,
              {
                color: value?.id ? theme.text : Colors.GrayBlue,
                ...(value?.id ? styles.selectedText : {})
              }
            ]}
            numberOfLines={1}
          >
            {value?.name || placeholder}
          </Text>
        </View>

        <Animated.View style={chevronStyle}>
          <ChevronDownIcon color={Colors.GrayBlue} size={16} />
        </Animated.View>
      </TouchableOpacity>

      {/* Error message with improved styling */}
      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}

      {/* Enhanced Dropdown Modal */}
      <Modal
        visible={dropdownVisible}
        transparent
        animationType="none"
        onRequestClose={handleCloseDropdown}
      >
        <View style={styles.modalContainer}>
          {/* Backdrop with blur effect */}
          {Platform.OS === 'ios' && (
            <BlurView intensity={20} style={StyleSheet.absoluteFill} tint="light" />
          )}

          {/* Close backdrop */}
          <TouchableOpacity
            style={StyleSheet.absoluteFill}
            onPress={() => {
              if (Platform.OS === 'android' && keyboardVisible) {
                Keyboard.dismiss();
              }
              handleCloseDropdown();
            }}
            activeOpacity={1}
          />

          {/* Dropdown content with enhanced styling and keyboard avoidance */}
          <Animated.View
            style={[
              styles.dropdownContainer,
              {
                backgroundColor: Colors.White,
                bottom: keyboardVisible ? keyboardHeight : 0,
              },
              animatedStyle,
              createShadow(8),
            ]}
          >
            {/* Handle bar */}
            <View style={styles.handleBar} />

            {/* Header */}
            <View style={styles.dropdownHeader}>
              <Text style={[styles.dropdownTitle, { color: theme.text }]}>
                Select Organization
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => {
                  if (Platform.OS === 'android' && keyboardVisible) {
                    Keyboard.dismiss();
                  }
                  handleCloseDropdown();
                }}
              >
                <CloseIcon color={Colors.DarkJungleGreen} size={16} />
              </TouchableOpacity>
            </View>

            {/* Search input with improved styling */}
            <View style={[styles.searchContainer, { backgroundColor: theme.searchBox }]}>
              <View style={styles.searchIcon}>
                <SearchIcon color={Colors.GrayBlue} size={16} />
              </View>
              <TextInput
                style={[styles.searchInput, { color: theme.text }]}
                placeholder="Search organizations..."
                placeholderTextColor={Colors.GrayBlue}
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoCapitalize="none"
                autoCorrect={false}
                onSubmitEditing={() => {
                  if (Platform.OS === 'android') {
                    Keyboard.dismiss();
                  }
                }}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity
                  style={styles.clearButton}
                  onPress={() => setSearchQuery('')}
                >
                  <CloseIcon color={Colors.DarkJungleGreen} size={12} />
                </TouchableOpacity>
              )}
            </View>

            {/* Organization list with improved styling */}
            <FlatList
              data={filteredItems}
              renderItem={renderItem}
              keyExtractor={(item) => item.id.toString()}
              contentContainerStyle={styles.listContainer}
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled"
              onScrollBeginDrag={() => {
                if (Platform.OS === 'android' && keyboardVisible) {
                  Keyboard.dismiss();
                }
              }}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={[styles.emptyText, { color: theme.text }]}>
                    {searchQuery.length > 0
                      ? 'No organizations found'
                      : 'No organizations available'}
                  </Text>
                </View>
              }
              // Performance optimizations
              initialNumToRender={8}
              maxToRenderPerBatch={10}
              removeClippedSubviews={true}
            />
          </Animated.View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    letterSpacing: 0.2,
  },
  selectorButton: {
    height: 58,
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  disabledButton: {
    opacity: 0.6,
  },
  selectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  selectorText: {
    fontSize: 16,
    flex: 1,
    marginLeft: 8,
  },
  selectedText: {
    fontWeight: '500',
  },
  smallIconContainer: {
    width: 30,
    height: 30,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  smallIcon: {
    width: 18,
    height: 18,
    borderRadius: 9,
  },
  smallInitials: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.TealBlue,
  },
  placeholderIcon: {
    width: 30,
    height: 30,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    color: Colors.Tomato,
    fontSize: 13,
    marginTop: 6,
    fontWeight: '500',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: Platform.OS === 'ios' ? 'transparent' : 'rgba(0, 0, 0, 0.5)',
  },
  dropdownContainer: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingBottom: Platform.OS === 'ios' ? 34 : 24, // Account for bottom safe area
    maxHeight: SCREEN_HEIGHT * 0.7,
  },
  handleBar: {
    width: 48,
    height: 6,
    backgroundColor: Colors.Platinum,
    borderRadius: 3,
    alignSelf: 'center',
    marginTop: 12,
    marginBottom: 8,
  },
  dropdownHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.Platinum,
  },
  dropdownTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.Platinum,
    alignItems: 'center',
    justifyContent: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 48,
    borderRadius: 12,
    marginHorizontal: 24,
    marginVertical: 16,
    paddingHorizontal: 12,
    ...createShadow(1),
  },
  searchIcon: {
    width: 24,
    height: 24,
    marginRight: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: 16,
    fontWeight: '400',
  },
  clearButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.Platinum,
    alignItems: 'center',
    justifyContent: 'center',
  },
  listContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    borderRadius: 12,
    marginBottom: 8,
    paddingHorizontal: 16,
  },
  iconContainer: {
    width: 46,
    height: 46,
    borderRadius: 23,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  icon: {
    width: 28,
    height: 28,
    borderRadius: 14,
  },
  initials: {
    fontSize: 18,
    fontWeight: '600',
  },
  itemTextContainer: {
    flex: 1,
  },
  itemText: {
    fontSize: 16,
  },
  checkmarkContainer: {
    width: 28,
    height: 28,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32,
    paddingHorizontal: 16,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.7,
  },
});

export default OrganizationSelector;
