import React, {Dispatch, memo, SetStateAction, useRef} from 'react';
import {
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {Colors} from '@/constants';
import {useTheme} from '@/constants/Theme';
import Theme from '@/constants/Theme';

interface SearchBoxProps {
  value?: string;
  onChangeText?: Dispatch<SetStateAction<string>>;
  placeholder: string;
  borderColor?: string;
  backgroundColor?: string;
  style?: ViewStyle;
  onSubmitEditing?: () => void;
  shadow?: boolean;
  onFocus?: () => void;
  autoFocus?: boolean;
}

const SearchBox = memo(
  ({
    value,
    onChangeText,
    onSubmitEditing,
    placeholder,
    borderColor,
    style,
    backgroundColor,
    shadow = true,
    onFocus,
    autoFocus,
    ...props
  }: SearchBoxProps) => {
    const {theme} = useTheme();
    const ref = useRef<any>();
    return (
      <View
        style={[
          styles.container,
          style && style,
          {
            backgroundColor: backgroundColor || Colors.White,
          },
        ]}>
        <TextInput
          {...props}
          placeholder={placeholder}
          placeholderTextColor={Colors.GrayBlue}
          style={[
            styles.input,
            {
              color: theme.text,
            }
          ]}
          onFocus={onFocus}
          value={value}
          returnKeyType={'search'}
          onSubmitEditing={() => {
            if (value && value.length > 0) {
              onSubmitEditing && onSubmitEditing();
            }
          }}
          onChangeText={onChangeText}
          autoFocus={autoFocus}
          ref={ref}
        />
        {/* {!!value && value.length > 0 && (
          <TouchableOpacity
            onPress={() => {
              ref.current.clear();
            }}>
            <Image source={SOURCE_ICON['reset-search']} />
          </TouchableOpacity>
        )} */}
      </View>
    );
  },
);

export default SearchBox;

// Export tablet-optimized search component
export { default as TabletSearchBox } from './TabletSearchBox';
export type { FilterOption, FilterGroup } from './TabletSearchBox';

const styles = StyleSheet.create({
  container: {
    ...Theme.flexRow,
    height: 40,
    flex: 1,
  },
  input: {
    flex: 1,
    fontSize: 14,
    paddingVertical: 0,
  },
});
