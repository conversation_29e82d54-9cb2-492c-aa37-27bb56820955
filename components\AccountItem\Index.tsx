import React, {memo} from 'react';
import {
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {Switch} from 'react-native-gesture-handler';
import Line from '@/components/Layout/Line';
import LinearColors from '@/components/LinearColors';
import Text from '@/components/Text';
import {Colors} from '@/constants';
import {SOURCE_ICON} from '@assets/images';
import Theme from '@/constants/Theme';

interface AccountItemProps {
  icon?: any;
  name?: string;
  isToggle?: boolean;
  style?: ViewStyle;
  onPress?: () => void;
  switchValue?: boolean;
  onValueChange?: () => void;
  lineBottom?: boolean;
}

const AccountItem = memo(
  ({
    icon,
    name,
    isToggle,
    style,
    onPress,
    switchValue,
    lineBottom,
    onValueChange,
  }: AccountItemProps) => {
    return (
      <View>
        <TouchableOpacity style={style} onPress={onPress} activeOpacity={0.54}>
          <View style={Theme.flexRow}>
            <LinearColors
              colors={[Colors.TealBlue, Colors.TurquoiseBlue]}
              style={styles.contentIcon}>
              <Image source={icon} style={styles.icon} />
            </LinearColors>
            <Text size={15} marginLeft={16}>
              {name}
            </Text>
          </View>
          <View>
            {isToggle === true ? (
              <Switch value={switchValue} onValueChange={onValueChange} />
            ) : (
              <Image source={SOURCE_ICON.arrowRight} />
            )}
          </View>
        </TouchableOpacity>
        {lineBottom ? <Line /> : null}
      </View>
    );
  },
);

export default AccountItem;

const styles = StyleSheet.create({
  icon: {
    tintColor: Colors.White,
    alignSelf: 'center',
  },
  contentIcon: {
    width: 40,
    height: 40,
    backgroundColor: Colors.DodgerBlue,
    borderRadius: 8,
    justifyContent: 'center',
  },
});
