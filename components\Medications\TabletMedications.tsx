import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { TabletAwareContainer } from '@/components/Layout/TabletAwareContainer';
import { AdaptiveLayout } from '@/components/Layout/AdaptiveLayout';
import { ResponsiveGrid } from '@/components/Layout/ResponsiveGrid';
import { AccessibleText } from '@/components/Accessibility/AccessibleText';
import { AccessibleButton } from '@/components/Accessibility/AccessibleButton';
import { isTablet, isLargeTablet, spacing } from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';

interface Medication {
  id: string;
  name: string;
  genericName?: string;
  dosage: string;
  frequency: string;
  route: 'oral' | 'injection' | 'topical' | 'inhaled' | 'other';
  startDate: string;
  endDate?: string;
  prescribedBy: string;
  instructions: string;
  sideEffects?: string[];
  status: 'active' | 'discontinued' | 'completed';
  adherence: number; // percentage
  lastTaken?: string;
  nextDue?: string;
}

interface TabletMedicationsProps {
  medications?: Medication[];
  patientName?: string;
  onMedicationPress?: (medication: Medication) => void;
  onAddMedication?: () => void;
  onEditMedication?: (medication: Medication) => void;
  onDiscontinue?: (medicationId: string) => void;
}

/**
 * Tablet-optimized Medications screen component
 */
const TabletMedications: React.FC<TabletMedicationsProps> = ({
  medications = [],
  patientName = 'Patient',
  onMedicationPress,
  onAddMedication,
  onEditMedication,
  onDiscontinue,
}) => {
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'active' | 'discontinued'>('active');
  const [selectedMedication, setSelectedMedication] = useState<Medication | null>(null);

  // Sample medications data
  const sampleMedications: Medication[] = [
    {
      id: '1',
      name: 'Lisinopril',
      genericName: 'Lisinopril',
      dosage: '10mg',
      frequency: 'Once daily',
      route: 'oral',
      startDate: '2024-01-01',
      prescribedBy: 'Dr. Smith',
      instructions: 'Take with or without food. Monitor blood pressure regularly.',
      status: 'active',
      adherence: 95,
      lastTaken: '2024-01-15T08:00:00Z',
      nextDue: '2024-01-16T08:00:00Z',
    },
    {
      id: '2',
      name: 'Metformin',
      genericName: 'Metformin HCl',
      dosage: '500mg',
      frequency: 'Twice daily',
      route: 'oral',
      startDate: '2023-12-15',
      prescribedBy: 'Dr. Johnson',
      instructions: 'Take with meals to reduce stomach upset.',
      sideEffects: ['Nausea', 'Diarrhea', 'Stomach upset'],
      status: 'active',
      adherence: 88,
      lastTaken: '2024-01-15T19:00:00Z',
      nextDue: '2024-01-16T08:00:00Z',
    },
    {
      id: '3',
      name: 'Atorvastatin',
      genericName: 'Atorvastatin Calcium',
      dosage: '20mg',
      frequency: 'Once daily at bedtime',
      route: 'oral',
      startDate: '2023-11-01',
      endDate: '2024-01-10',
      prescribedBy: 'Dr. Wilson',
      instructions: 'Take at bedtime. Avoid grapefruit juice.',
      status: 'discontinued',
      adherence: 92,
    },
  ];

  const medsToShow = medications.length > 0 ? medications : sampleMedications;

  const filteredMedications = medsToShow.filter(med => {
    if (selectedFilter === 'all') return true;
    return med.status === selectedFilter;
  });

  const getRouteIcon = (route: Medication['route']) => {
    switch (route) {
      case 'oral':
        return '💊';
      case 'injection':
        return '💉';
      case 'topical':
        return '🧴';
      case 'inhaled':
        return '🫁';
      case 'other':
      default:
        return '💊';
    }
  };

  const getStatusColor = (status: Medication['status']) => {
    switch (status) {
      case 'active':
        return CoreColors.TealBlue;
      case 'discontinued':
        return CoreColors.RedOrange;
      case 'completed':
        return CoreColors.SlateGray;
      default:
        return CoreColors.SlateGray;
    }
  };

  const getAdherenceColor = (adherence: number) => {
    if (adherence >= 90) return CoreColors.TealBlue;
    if (adherence >= 70) return CoreColors.YellowOrange;
    return CoreColors.RedOrange;
  };

  const formatDateTime = (dateTime: string) => {
    return new Date(dateTime).toLocaleString();
  };

  const renderMedicationCard = ({ item }: { item: Medication }) => (
    <TouchableOpacity
      style={[
        styles.medicationCard,
        selectedMedication?.id === item.id && styles.selectedCard,
      ]}
      onPress={() => {
        setSelectedMedication(item);
        onMedicationPress?.(item);
      }}
      accessible={true}
      accessibilityLabel={`Medication: ${item.name} ${item.dosage}`}
      accessibilityRole="button"
    >
      <View style={styles.cardHeader}>
        <View style={styles.medicationRoute}>
          <AccessibleText variant="body" style={styles.routeIcon}>
            {getRouteIcon(item.route)}
          </AccessibleText>
          <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(item.status) }]} />
        </View>
        <View style={[styles.adherenceBadge, { backgroundColor: getAdherenceColor(item.adherence) }]}>
          <AccessibleText variant="caption" style={styles.adherenceText}>
            {item.adherence}%
          </AccessibleText>
        </View>
      </View>

      <View style={styles.cardContent}>
        <AccessibleText variant="h3" style={styles.medicationName}>
          {item.name}
        </AccessibleText>
        {item.genericName && item.genericName !== item.name && (
          <AccessibleText variant="bodySmall" style={styles.genericName}>
            Generic: {item.genericName}
          </AccessibleText>
        )}
        <AccessibleText variant="body" style={styles.dosageInfo}>
          {item.dosage} • {item.frequency}
        </AccessibleText>
        <AccessibleText variant="bodySmall" style={styles.prescriber}>
          Prescribed by {item.prescribedBy}
        </AccessibleText>
      </View>

      <View style={styles.cardFooter}>
        {item.nextDue && item.status === 'active' && (
          <AccessibleText variant="caption" style={styles.nextDue}>
            Next: {new Date(item.nextDue).toLocaleTimeString()}
          </AccessibleText>
        )}
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <AccessibleText variant="caption" style={styles.statusText}>
            {item.status.toUpperCase()}
          </AccessibleText>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderMedicationFilters = () => (
    <View style={styles.filtersContainer}>
      <AccessibleText variant="h3" style={styles.filtersTitle}>
        Filter Medications
      </AccessibleText>
      <View style={styles.filterButtons}>
        {(['all', 'active', 'discontinued'] as const).map((filter) => (
          <TouchableOpacity
            key={filter}
            style={[
              styles.filterButton,
              selectedFilter === filter && styles.activeFilterButton,
            ]}
            onPress={() => setSelectedFilter(filter)}
            accessible={true}
            accessibilityLabel={`Filter by ${filter}`}
            accessibilityRole="button"
          >
            <AccessibleText
              variant="bodySmall"
              style={[
                styles.filterButtonText,
                selectedFilter === filter && styles.activeFilterButtonText,
              ]}
            >
              {filter.toUpperCase()}
            </AccessibleText>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderMedicationsList = () => (
    <View style={styles.medicationsContainer}>
      <View style={styles.listHeader}>
        <AccessibleText variant="h2" style={styles.listTitle}>
          {patientName}'s Medications ({filteredMedications.length})
        </AccessibleText>
        <AccessibleButton
          title="Add Medication"
          onPress={onAddMedication}
          size="small"
          variant="primary"
        />
      </View>

      <FlatList
        data={filteredMedications}
        renderItem={renderMedicationCard}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.medicationsList}
      />
    </View>
  );

  const renderMedicationDetail = () => {
    if (!selectedMedication) {
      return (
        <View style={styles.detailEmpty}>
          <AccessibleText variant="body" style={styles.emptyText}>
            Select a medication to view details
          </AccessibleText>
        </View>
      );
    }

    return (
      <ScrollView style={styles.medicationDetail} showsVerticalScrollIndicator={false}>
        <AccessibleText variant="h2" style={styles.detailTitle}>
          Medication Details
        </AccessibleText>

        <View style={styles.detailCard}>
          <View style={styles.detailHeader}>
            <AccessibleText variant="body" style={styles.detailRouteIcon}>
              {getRouteIcon(selectedMedication.route)}
            </AccessibleText>
            <View style={styles.detailMedicationInfo}>
              <AccessibleText variant="h3" style={styles.detailMedicationName}>
                {selectedMedication.name}
              </AccessibleText>
              {selectedMedication.genericName && selectedMedication.genericName !== selectedMedication.name && (
                <AccessibleText variant="bodySmall" style={styles.detailGenericName}>
                  Generic: {selectedMedication.genericName}
                </AccessibleText>
              )}
            </View>
          </View>

          <View style={styles.detailSection}>
            <AccessibleText variant="h3" style={styles.detailSectionTitle}>
              Dosage Information
            </AccessibleText>
            <View style={styles.detailRow}>
              <AccessibleText variant="bodySmall" style={styles.detailLabel}>
                Dosage:
              </AccessibleText>
              <AccessibleText variant="body" style={styles.detailValue}>
                {selectedMedication.dosage}
              </AccessibleText>
            </View>
            <View style={styles.detailRow}>
              <AccessibleText variant="bodySmall" style={styles.detailLabel}>
                Frequency:
              </AccessibleText>
              <AccessibleText variant="body" style={styles.detailValue}>
                {selectedMedication.frequency}
              </AccessibleText>
            </View>
            <View style={styles.detailRow}>
              <AccessibleText variant="bodySmall" style={styles.detailLabel}>
                Route:
              </AccessibleText>
              <AccessibleText variant="body" style={styles.detailValue}>
                {selectedMedication.route}
              </AccessibleText>
            </View>
          </View>

          <View style={styles.detailSection}>
            <AccessibleText variant="h3" style={styles.detailSectionTitle}>
              Instructions
            </AccessibleText>
            <AccessibleText variant="body" style={styles.detailInstructions}>
              {selectedMedication.instructions}
            </AccessibleText>
          </View>

          {selectedMedication.sideEffects && selectedMedication.sideEffects.length > 0 && (
            <View style={styles.detailSection}>
              <AccessibleText variant="h3" style={styles.detailSectionTitle}>
                Possible Side Effects
              </AccessibleText>
              {selectedMedication.sideEffects.map((effect, index) => (
                <AccessibleText key={index} variant="bodySmall" style={styles.sideEffect}>
                  • {effect}
                </AccessibleText>
              ))}
            </View>
          )}

          <View style={styles.detailSection}>
            <AccessibleText variant="h3" style={styles.detailSectionTitle}>
              Prescription Details
            </AccessibleText>
            <View style={styles.detailRow}>
              <AccessibleText variant="bodySmall" style={styles.detailLabel}>
                Prescribed by:
              </AccessibleText>
              <AccessibleText variant="body" style={styles.detailValue}>
                {selectedMedication.prescribedBy}
              </AccessibleText>
            </View>
            <View style={styles.detailRow}>
              <AccessibleText variant="bodySmall" style={styles.detailLabel}>
                Start Date:
              </AccessibleText>
              <AccessibleText variant="body" style={styles.detailValue}>
                {new Date(selectedMedication.startDate).toLocaleDateString()}
              </AccessibleText>
            </View>
            {selectedMedication.endDate && (
              <View style={styles.detailRow}>
                <AccessibleText variant="bodySmall" style={styles.detailLabel}>
                  End Date:
                </AccessibleText>
                <AccessibleText variant="body" style={styles.detailValue}>
                  {new Date(selectedMedication.endDate).toLocaleDateString()}
                </AccessibleText>
              </View>
            )}
          </View>

          {selectedMedication.status === 'active' && (
            <View style={styles.detailSection}>
              <AccessibleText variant="h3" style={styles.detailSectionTitle}>
                Adherence & Schedule
              </AccessibleText>
              <View style={styles.detailRow}>
                <AccessibleText variant="bodySmall" style={styles.detailLabel}>
                  Adherence:
                </AccessibleText>
                <View style={[styles.adherenceBadge, { backgroundColor: getAdherenceColor(selectedMedication.adherence) }]}>
                  <AccessibleText variant="caption" style={styles.adherenceText}>
                    {selectedMedication.adherence}%
                  </AccessibleText>
                </View>
              </View>
              {selectedMedication.lastTaken && (
                <View style={styles.detailRow}>
                  <AccessibleText variant="bodySmall" style={styles.detailLabel}>
                    Last Taken:
                  </AccessibleText>
                  <AccessibleText variant="body" style={styles.detailValue}>
                    {formatDateTime(selectedMedication.lastTaken)}
                  </AccessibleText>
                </View>
              )}
              {selectedMedication.nextDue && (
                <View style={styles.detailRow}>
                  <AccessibleText variant="bodySmall" style={styles.detailLabel}>
                    Next Due:
                  </AccessibleText>
                  <AccessibleText variant="body" style={styles.detailValue}>
                    {formatDateTime(selectedMedication.nextDue)}
                  </AccessibleText>
                </View>
              )}
            </View>
          )}
        </View>

        <View style={styles.detailActions}>
          <AccessibleButton
            title="Edit Medication"
            onPress={() => onEditMedication?.(selectedMedication)}
            variant="primary"
            style={styles.detailActionButton}
          />
          {selectedMedication.status === 'active' && (
            <AccessibleButton
              title="Discontinue"
              onPress={() => onDiscontinue?.(selectedMedication.id)}
              variant="outline"
              style={styles.detailActionButton}
            />
          )}
          <AccessibleButton
            title="View History"
            onPress={() => {}}
            variant="text"
            style={styles.detailActionButton}
          />
        </View>
      </ScrollView>
    );
  };

  return (
    <TabletAwareContainer style={styles.container}>
      <AdaptiveLayout
        mobileLayout={() => (
          <ScrollView style={styles.mobileLayout}>
            {renderMedicationFilters()}
            {renderMedicationsList()}
          </ScrollView>
        )}
        tabletLayout={() => (
          <View style={styles.tabletLayout}>
            <View style={styles.leftPanel}>
              {renderMedicationFilters()}
              {renderMedicationsList()}
            </View>
            <View style={styles.rightPanel}>
              {renderMedicationDetail()}
            </View>
          </View>
        )}
      />
    </TabletAwareContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  mobileLayout: {
    flex: 1,
  },
  tabletLayout: {
    flex: 1,
    flexDirection: 'row',
    padding: spacing.md,
  },
  leftPanel: {
    flex: 1,
    marginRight: spacing.md,
  },
  rightPanel: {
    width: isLargeTablet() ? 400 : 320,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: spacing.lg,
  },
  filtersContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: isTablet() ? 12 : 8,
    padding: spacing.lg,
    marginBottom: spacing.md,
  },
  filtersTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.md,
  },
  filterButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  filterButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  activeFilterButton: {
    backgroundColor: CoreColors.TurquoiseBlue,
    borderColor: CoreColors.TurquoiseBlue,
  },
  filterButtonText: {
    color: CoreColors.SlateGray,
  },
  activeFilterButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  medicationsContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: isTablet() ? 12 : 8,
    padding: spacing.lg,
    flex: 1,
  },
  listHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  listTitle: {
    color: CoreColors.DarkJungleGreen,
    flex: 1,
  },
  medicationsList: {
    paddingBottom: spacing.lg,
  },
  medicationCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  selectedCard: {
    backgroundColor: '#EBF8FF',
    borderColor: CoreColors.TurquoiseBlue,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  medicationRoute: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  routeIcon: {
    fontSize: 20,
    marginRight: spacing.xs,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  adherenceBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: 4,
    borderRadius: 12,
  },
  adherenceText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  cardContent: {
    marginBottom: spacing.md,
  },
  medicationName: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.xs,
  },
  genericName: {
    color: CoreColors.SlateGray,
    marginBottom: spacing.xs,
    fontStyle: 'italic',
  },
  dosageInfo: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.xs,
    fontWeight: '500',
  },
  prescriber: {
    color: CoreColors.SlateGray,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  nextDue: {
    color: CoreColors.TurquoiseBlue,
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  detailEmpty: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    color: CoreColors.SlateGray,
    textAlign: 'center',
  },
  medicationDetail: {
    flex: 1,
  },
  detailTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.lg,
  },
  detailCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: spacing.lg,
    marginBottom: spacing.xl,
  },
  detailHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  detailRouteIcon: {
    fontSize: 32,
    marginRight: spacing.md,
  },
  detailMedicationInfo: {
    flex: 1,
  },
  detailMedicationName: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.xs,
  },
  detailGenericName: {
    color: CoreColors.SlateGray,
    fontStyle: 'italic',
  },
  detailSection: {
    marginBottom: spacing.lg,
  },
  detailSectionTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.md,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  detailLabel: {
    color: CoreColors.SlateGray,
    flex: 1,
  },
  detailValue: {
    color: CoreColors.DarkJungleGreen,
    flex: 1,
    textAlign: 'right',
  },
  detailInstructions: {
    color: CoreColors.SlateGray,
    lineHeight: 24,
  },
  sideEffect: {
    color: CoreColors.SlateGray,
    marginBottom: spacing.xs,
  },
  detailActions: {
    marginTop: 'auto',
    paddingTop: spacing.lg,
  },
  detailActionButton: {
    marginBottom: spacing.sm,
  },
});

export default TabletMedications;