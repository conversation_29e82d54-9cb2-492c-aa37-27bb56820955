import React from 'react';
import {
  ColorValue,
  PressableStateCallbackType,
  ScrollView as DefaultScrollView,
  ScrollViewProps,
  ViewStyle,
} from 'react-native';
interface Props extends ScrollViewProps {
  children?:
    | React.ReactNode
    | ((state: PressableStateCallbackType) => React.ReactNode);
  style?: ViewStyle;
  contentStyle?: ViewStyle;
  backgroundColor?: ColorValue | string;
  contentContainerStyle?: ViewStyle;
  horizontal?: boolean;
}

const Content = (props: Props) => {
  return (
    <DefaultScrollView
      horizontal={props.horizontal}
      contentContainerStyle={props.contentContainerStyle}
      bounces
      style={[props.style]}>
      {props.children}
    </DefaultScrollView>
  );
};
export default Content;
