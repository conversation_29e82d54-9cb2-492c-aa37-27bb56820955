import { useMemo } from 'react';
import { useTheme } from '@/constants/Theme';
import { useOrientation } from './useOrientation';
import {
  getTabletSpacing,
  getTabletTypography,
  getTabletComponentSize,
  getOrientationTheme,
  createTabletStyles,
  createOrientationStyles,
  createResponsiveStyles,
  getTabletTouchTarget,
  getTabletShadow,
  getTabletBorderRadius,
  getTabletGridConfig,
  getTabletModalConfig,
  createTabletPadding,
  createTabletFontSize,
} from '@/utils/themeUtils';
import { 
  isTablet, 
  isLargeTablet, 
  getDeviceType 
} from '@/utils/responsive';

/**
 * Enhanced theme hook with tablet-specific utilities
 * Provides easy access to responsive theme values and utilities
 */
export const useTabletTheme = () => {
  const { theme } = useTheme();
  const { orientation, isLandscape, isPortrait } = useOrientation();
  
  const tabletTheme = useMemo(() => ({
    // Base theme
    ...theme,
    
    // Device detection
    isTablet: isTablet(),
    isLargeTablet: isLargeTablet(),
    deviceType: getDeviceType(),
    
    // Orientation
    orientation,
    isLandscape,
    isPortrait,
    
    // Tablet-specific theme utilities
    spacing: {
      ...theme.spacing,
      get: (size: keyof typeof theme.spacing) => getTabletSpacing(size),
    },
    
    typography: {
      ...theme.typography,
      get: (variant: keyof typeof theme.typography) => getTabletTypography(variant),
    },
    
    components: {
      get: (component: keyof typeof theme.tablet.components) => getTabletComponentSize(component),
    },
    
    orientation: {
      get: () => getOrientationTheme(orientation),
    },
    
    // Style creation utilities
    createStyles: {
      tablet: createTabletStyles,
      orientation: createOrientationStyles,
      responsive: createResponsiveStyles,
    },
    
    // Utility functions
    touchTarget: (size?: 'minimum' | 'comfortable' | 'spacious') => getTabletTouchTarget(size),
    shadow: (intensity?: 'light' | 'medium' | 'heavy') => getTabletShadow(intensity),
    borderRadius: (size?: 'small' | 'medium' | 'large') => getTabletBorderRadius(size),
    gridConfig: () => getTabletGridConfig(orientation),
    modalConfig: () => getTabletModalConfig(),
    
    // Value creation utilities
    padding: (base: number, tabletMultiplier?: number, largeTabletMultiplier?: number) => 
      createTabletPadding(base, tabletMultiplier, largeTabletMultiplier),
    fontSize: (base: number, tabletMultiplier?: number, largeTabletMultiplier?: number) => 
      createTabletFontSize(base, tabletMultiplier, largeTabletMultiplier),
  }), [theme, orientation, isLandscape, isPortrait]);
  
  return tabletTheme;
};

/**
 * Hook for getting tablet-aware spacing values
 */
export const useTabletSpacing = () => {
  const { theme } = useTheme();
  
  return useMemo(() => ({
    xs: getTabletSpacing('xs'),
    sm: getTabletSpacing('sm'),
    md: getTabletSpacing('md'),
    lg: getTabletSpacing('lg'),
    xl: getTabletSpacing('xl'),
    xxl: getTabletSpacing('xxl'),
  }), []);
};

/**
 * Hook for getting tablet-aware typography values
 */
export const useTabletTypography = () => {
  const { theme } = useTheme();
  
  return useMemo(() => ({
    h1: getTabletTypography('h1'),
    h2: getTabletTypography('h2'),
    h3: getTabletTypography('h3'),
    body: getTabletTypography('body'),
    bodySmall: getTabletTypography('bodySmall'),
    caption: getTabletTypography('caption'),
  }), []);
};

/**
 * Hook for creating responsive styles based on current device and orientation
 */
export const useResponsiveStyles = <T extends Record<string, any>>(styleConfig: {
  phone: T;
  phonePortrait?: Partial<T>;
  phoneLandscape?: Partial<T>;
  tablet?: Partial<T>;
  tabletPortrait?: Partial<T>;
  tabletLandscape?: Partial<T>;
  largeTablet?: Partial<T>;
}) => {
  const { orientation } = useOrientation();
  
  return useMemo(() => 
    createResponsiveStyles(styleConfig), 
    [styleConfig, orientation]
  );
};

export default useTabletTheme;