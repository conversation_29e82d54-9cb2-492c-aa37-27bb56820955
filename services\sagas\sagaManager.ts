import {call, put, takeLatest, select} from 'redux-saga/effects';
import {
  FETCH_ALERTS_LIST,
  FETCH_ALERTS_LIST_FAILURE,
  FETCH_ALERTS_LIST_SUCCESS,
  <PERSON>ET<PERSON>_DTASKS_LIST,
  <PERSON>ET<PERSON>_DTASKS_LIST_FAILURE,
  <PERSON>ETCH_DTASKS_LIST_SUCCESS,
  FETCH_FTASKS_LIST,
  FETCH_FTASKS_LIST_FAILURE,
  FETCH_FTASKS_LIST_SUCCESS,
  FETCH_PATIENTS_LIST,
  FETCH_PATIENTS_LIST_FAILURE,
  FETCH_PATIENTS_LIST_SUCCESS,
  FETCH_MORE_PATIENTS_LIST,
  FETCH_MORE_PATIENTS_LIST_FAILURE,
  FETCH_MORE_PATIENTS_LIST_SUCCESS,
  FETCH_USER_PROFILE,
  FETCH_USER_PROFILE_FAILURE,
  FETCH_USER_PROFILE_SUCCESS,
} from '../actions/type';
import {
  getAll<PERSON>lertsCall,
  getPatient<PERSON>ist<PERSON>all,
  getTasksApiCall,
  getUserProfiletCall,
} from '../apis/sagaApiManager';

// Selector to get user role information from Redux store
const getUserRole = (state: any) => ({
  userRole: state?.loginReducer?.userRole || 'caregiver',
  roleId: state?.loginReducer?.roleId || 5,
});

function* getPatientsList(payload: any): any {
  try {
    // Get user role information
    const roleInfo = yield select(getUserRole);
    
    const startPage = payload.payload.pageNumber || 0;
    const pageSize = payload.payload.pageSize || 10;
    
    console.log('Saga - Fetching initial patient page:', {
      careGiverId: payload.payload.careGiverId,
      orgId: payload.payload.orgId,
      roleType: roleInfo.userRole,
      pageNumber: startPage,
      pageSize,
      isReset: payload.payload.resetPagination
    });
    
    const updatedPayload = {
      careGiverId: payload.payload.careGiverId,
      orgId: payload.payload.orgId,
      roleType: roleInfo.userRole,
      pageNumber: startPage,
      pageSize: pageSize,
    };
    
    const response = yield call(getPatientListCall, updatedPayload);
    if (response && response.data) {
      const responseData = response.data;
      const patientsReceived = responseData.patientInfo?.length || 0;
      const totalCount = parseInt(responseData.count) || patientsReceived;
      
      console.log('Saga - Initial patients API Response:', {
        totalCount: responseData.count,
        patientsReceived,
        pageNumber: startPage,
        pageSize,
        responseStatus: response.status,
        hasPatients: patientsReceived > 0,
      });
      
      yield put({
        type: FETCH_PATIENTS_LIST_SUCCESS,
        data: responseData.patientInfo || [],
        orgId: payload.payload.orgId,
        totalCount: totalCount,
        pageNumber: startPage,
        pageSize: pageSize,
        isLoadingMore: false,
      });
    } else {
      console.error('Saga - Invalid API response:', response);
      yield put({type: FETCH_PATIENTS_LIST_FAILURE, error: 'Invalid server response'});
    }
  } catch (error: any) {
    console.error('Saga - Error fetching patients:', error);
    yield put({type: FETCH_PATIENTS_LIST_FAILURE, error: error.errorMessage || 'Network Error'});
  }
}

function* getMorePatientsList(payload: any): any {
  try {
    // Get user role information
    const roleInfo = yield select(getUserRole);
    
    const pageNumber = payload.payload.pageNumber;
    const pageSize = payload.payload.pageSize || 10;
    
    console.log('Saga - Fetching more patients page:', {
      careGiverId: payload.payload.careGiverId,
      orgId: payload.payload.orgId,
      roleType: roleInfo.userRole,
      pageNumber,
      pageSize,
    });
    
    const updatedPayload = {
      careGiverId: payload.payload.careGiverId,
      orgId: payload.payload.orgId,
      roleType: roleInfo.userRole,
      pageNumber: pageNumber,
      pageSize: pageSize,
    };
    
    const response = yield call(getPatientListCall, updatedPayload);
    if (response && response.data) {
      const responseData = response.data;
      const patientsReceived = responseData.patientInfo?.length || 0;
      const totalCount = parseInt(responseData.count) || patientsReceived;
      
      console.log('Saga - More patients API Response:', {
        totalCount: responseData.count,
        patientsReceived,
        pageNumber: pageNumber,
        pageSize,
        responseStatus: response.status,
        hasPatients: patientsReceived > 0,
      });
      
      yield put({
        type: FETCH_MORE_PATIENTS_LIST_SUCCESS,
        data: responseData.patientInfo || [],
        orgId: payload.payload.orgId,
        totalCount: totalCount,
        pageNumber: pageNumber,
        pageSize: pageSize,
      });
    } else {
      console.error('Saga - Invalid API response for more patients:', response);
      yield put({type: FETCH_MORE_PATIENTS_LIST_FAILURE, error: 'Invalid server response'});
    }
  } catch (error: any) {
    console.error('Saga - Error fetching more patients:', error);
    yield put({type: FETCH_MORE_PATIENTS_LIST_FAILURE, error: error.errorMessage || 'Network Error'});
  }
}

function* getUserProfile(payload: any): any {
  try {
    const roleInfo = yield select(getUserRole);

    const updatedPayload = {
      ...payload.payload,
      roleType: payload.payload.roleType || roleInfo.roleId.toString()
    };
    
    const response = yield call(getUserProfiletCall, updatedPayload);
    if (response?.data) {
      yield put({
        type: FETCH_USER_PROFILE_SUCCESS,
        data: response?.data?.clinicianDetails,
      });
    } else {
      yield put({type: FETCH_USER_PROFILE_FAILURE, error: 'Server Error!!'});
    }
  } catch (error: any) {
    yield put({type: FETCH_USER_PROFILE_FAILURE, error: error.errorMessage});
  }
}

function* getAllAlerts(payload: any): any {
  try {
    yield put({
      type: FETCH_ALERTS_LIST_SUCCESS,
      data: [],
    });
    const response = yield call(getAllAlertsCall, payload.payload);

    if (response) {
      yield put({
        type: FETCH_ALERTS_LIST_SUCCESS,
        data: response?.data?.data,
      });
    } else {
      yield put({type: FETCH_ALERTS_LIST_FAILURE, error: 'Server Error!!'});
    }
  } catch (error: any) {
    yield put({type: FETCH_ALERTS_LIST_FAILURE, error: error.errorMessage});
  }
}

function* getDueTasks(payload: any): any {
  try {
    const roleInfo = yield select(getUserRole);

    const updatedPayload = {
      ...payload.payload,
      roleId: roleInfo.roleId.toString()
    };
    
    const response = yield call(getTasksApiCall, updatedPayload);

    if (response) {
      yield put({
        type: FETCH_DTASKS_LIST_SUCCESS,
        data: response?.data?.data,
      });
    } else {
      yield put({type: FETCH_DTASKS_LIST_FAILURE, error: 'Server Error!!'});
    }
  } catch (error: any) {
    yield put({type: FETCH_DTASKS_LIST_FAILURE, error: error.errorMessage});
  }
}

function* getFutureTaks(payload: any): any {
  try {
    const roleInfo = yield select(getUserRole);

    const updatedPayload = {
      ...payload.payload,
      roleId: roleInfo.roleId.toString()
    };
    
    const response = yield call(getTasksApiCall, updatedPayload);

    if (response) {
      yield put({
        type: FETCH_FTASKS_LIST_SUCCESS,
        data: response?.data?.data,
      });
    } else {
      yield put({type: FETCH_FTASKS_LIST_FAILURE, error: 'Server Error!!'});
    }
  } catch (error: any) {
    yield put({type: FETCH_FTASKS_LIST_FAILURE, error: error.errorMessage});
  }
}

export default function* sagaManager() {
  yield takeLatest(FETCH_PATIENTS_LIST, getPatientsList);
  yield takeLatest(FETCH_MORE_PATIENTS_LIST, getMorePatientsList);
  yield takeLatest(FETCH_USER_PROFILE, getUserProfile);
  yield takeLatest(FETCH_ALERTS_LIST, getAllAlerts);
  yield takeLatest(FETCH_DTASKS_LIST, getDueTasks);
  yield takeLatest(FETCH_FTASKS_LIST, getFutureTaks);
}
