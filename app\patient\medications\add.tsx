import React from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams } from 'expo-router';

import AddMedicationScreen from '@/screens/PatientDetails/Medications/addMedication';

export default function AddMedicationRoute() {
  return (
    // Explicitly exclude the top and bottom edges to prevent double padding with the header and remove bottom safe area
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      <AddMedicationScreen />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
