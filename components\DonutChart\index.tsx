import React from 'react';
import { View, Text } from 'react-native';
import Svg, { G, Circle, Path, Text as SvgText } from 'react-native-svg';

interface DonutChartProps {
  data: Array<{
    name: string;
    count: number;
    color: string;
  }>;
  width: number;
  height: number;
  innerRadius?: number;
  outerRadius?: number;
}

const DonutChart: React.FC<DonutChartProps> = ({
  data,
  width,
  height,
  innerRadius = 35,
  outerRadius = 70,
}) => {
  // Calculate total value of all data points
  const total = data.reduce((sum, item) => sum + (item.count || 0), 0);
  
  // Don't render if no data or total is zero
  if (total === 0) {
    return (
      <View style={{ width, height, justifyContent: 'center', alignItems: 'center' }}>
        <Text>No data available</Text>
      </View>
    );
  }

  const centerX = width / 2;
  const centerY = height / 2;
  
  // Track the accumulated angle for drawing each segment
  let accumulatedAngle = 0;
  
  return (
    <View style={{ width, height, justifyContent: 'center', alignItems: 'center' }}>
      <Svg width={width} height={height} viewBox={`0 0 ${width} ${height}`}>
        <G x={centerX} y={centerY}>
          {/* Draw a white circle in the center for the donut hole */}
          <Circle r={innerRadius} fill="white" />
          
          {data.map((item, index) => {
            // Skip zero values
            if (item.count === 0) return null;
            
            // Calculate the angle this slice takes up
            const angle = (item.count / total) * 360;
            
            // Generate the SVG arc path
            const startAngle = accumulatedAngle;
            const endAngle = startAngle + angle;
            
            // Convert angles to radians
            const startRadians = (startAngle * Math.PI) / 180;
            const endRadians = (endAngle * Math.PI) / 180;
            
            // Calculate arc points
            const startX = outerRadius * Math.cos(startRadians);
            const startY = outerRadius * Math.sin(startRadians);
            const endX = outerRadius * Math.cos(endRadians);
            const endY = outerRadius * Math.sin(endRadians);
            
            // Large arc flag is 1 if angle > 180 degrees
            const largeArcFlag = angle > 180 ? 1 : 0;
            
            // Create path for this slice
            const pathData = `
              M ${innerRadius * Math.cos(startRadians)} ${innerRadius * Math.sin(startRadians)}
              L ${startX} ${startY}
              A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${endX} ${endY}
              L ${innerRadius * Math.cos(endRadians)} ${innerRadius * Math.sin(endRadians)}
              A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${innerRadius * Math.cos(startRadians)} ${innerRadius * Math.sin(startRadians)}
            `;
            
            // Update accumulated angle for next segment
            accumulatedAngle += angle;
            
            return (
              <Path
                key={index}
                d={pathData}
                fill={item.color}
                stroke="white"
                strokeWidth={1.5}
                opacity={0.9}
              />
            );
          })}
        </G>
      </Svg>
    </View>
  );
};

export default DonutChart;