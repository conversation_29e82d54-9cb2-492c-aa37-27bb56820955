import { useRouter } from 'expo-router';
import React, { memo, useCallback } from 'react';
import { 
  Image, 
  StyleSheet, 
  View,
  Dimensions 
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import ButtonLinear from '@/components/Buttons/ButtonLinear';
import Text from '@/components/Text';
import { Colors } from '@/constants';
import scale from '@/utils/scale';
import { SafeAreaView } from 'react-native-safe-area-context';

const { width } = Dimensions.get('window');

interface SignUpSuccessfulProps {}

const SignUpSuccessful = memo((props: SignUpSuccessfulProps) => {
  const router = useRouter();

  const onPressLogin = useCallback(() => {
    router.replace('/auth/login');
  }, [router]);

  return (
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      {/* Background Gradient */}
      <LinearGradient
        colors={['#f8fffe', '#e8f8f7', '#ffffff']}
        style={styles.background}
      />

      <View style={styles.contentContainer}>
        {/* Success Icon */}
        <View style={styles.iconContainer}>
          <View style={styles.successIconWrapper}>
            <Image
              source={require('@/assets/images/ic_accept.png')}
              style={styles.successIcon}
              resizeMode="contain"
            />
          </View>
          
          {/* Celebration Effects */}
          <View style={styles.celebrationContainer}>
            <Text style={styles.celebrationEmoji}>🎉</Text>
            <Text style={[styles.celebrationEmoji, styles.celebrationEmojiRight]}>
              ✨
            </Text>
          </View>
        </View>

        {/* Success Content */}
        <View style={styles.textContainer}>
          <Text style={styles.title}>Welcome to Care Manager!</Text>
          <Text style={styles.subtitle}>
            Thank you for registering with us. We're excited to have you on board! 
            We'll notify you once your account is activated and ready to use.
          </Text>
        </View>

        {/* Features Preview */}
        <View style={styles.featuresContainer}>
          <View style={styles.featureItem}>
            <Text style={styles.featureEmoji}>👩‍⚕️</Text>
            <Text style={styles.featureText}>Professional Care Management</Text>
          </View>
          
          <View style={styles.featureItem}>
            <Text style={styles.featureEmoji}>📱</Text>
            <Text style={styles.featureText}>Easy Mobile Access</Text>
          </View>
          
          <View style={styles.featureItem}>
            <Text style={styles.featureEmoji}>🔐</Text>
            <Text style={styles.featureText}>Secure & Private</Text>
          </View>
        </View>

        {/* Action Button */}
        <View style={styles.buttonContainer}>
          <ButtonLinear
            title="Continue to Sign In"
            onPress={onPressLogin}
            style={styles.buttonLinear}
          />
        </View>

        {/* Footer */}
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>
            Questions? Contact our support team for assistance
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
});

export default SignUpSuccessful;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.White,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 40,
  },
  iconContainer: {
    position: 'relative',
    marginBottom: 40,
  },
  successIconWrapper: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: Colors.Malachite,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.Malachite,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  successIcon: {
    width: 60,
    height: 60,
    tintColor: Colors.White,
  },
  celebrationContainer: {
    position: 'absolute',
    top: -20,
    left: -20,
    right: -20,
    bottom: -20,
  },
  celebrationEmoji: {
    position: 'absolute',
    fontSize: 30,
    top: 0,
    left: 0,
  },
  celebrationEmojiRight: {
    top: 20,
    right: 0,
    left: 'auto',
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 48,
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    color: Colors.DarkJungleGreen,
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: Colors.GrayBlue,
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 16,
  },
  featuresContainer: {
    width: '100%',
    marginBottom: 48,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: Colors.White,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: Colors.Black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  featureEmoji: {
    fontSize: 24,
    marginRight: 16,
  },
  featureText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.DarkJungleGreen,
    flex: 1,
  },
  buttonContainer: {
    width: '100%',
    marginBottom: 32,
  },
  buttonLinear: {
    height: 56,
    borderRadius: 16,
    shadowColor: Colors.TiffanyBlue,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  footerContainer: {
    paddingHorizontal: 24,
  },
  footerText: {
    fontSize: 14,
    color: Colors.GrayBlue,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
