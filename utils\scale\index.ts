import { Dimensions } from 'react-native';

/**
 * Design specifications for scaling calculations
 */
const DESIGN_SPECS = {
  // Original design dimensions (typically from design tools like Figma)
  widthDesign: 375,
  heightDesign: 812
};

/**
 * Get current screen dimensions
 */
const getScreenDimensions = () => {
  const { width, height } = Dimensions.get('window');
  return { width, height };
};

/**
 * Scales a size based on the device's screen size relative to the design specifications
 * 
 * @param size The original size from the design
 * @param accordingHeight Whether to scale according to height instead of width (default: false)
 * @returns The scaled size appropriate for the current device
 */
function scale(size: number, accordingHeight: boolean = false): number {
  // Get current dimensions
  const { width, height } = getScreenDimensions();
  
  // Calculate scale factor based on width or height
  if (accordingHeight) {
    return (size * height) / DESIGN_SPECS.heightDesign;
  }
  return (size * width) / DESIGN_SPECS.widthDesign;
}

/**
 * Scale specifically for vertical dimensions (height, margins, etc.)
 * 
 * @param size The original size from the design
 * @returns The scaled size appropriate for the current device
 */
export function verticalScale(size: number): number {
  return scale(size, true);
}

/**
 * Scale specifically for horizontal dimensions (width, paddings, etc.)
 * 
 * @param size The original size from the design
 * @returns The scaled size appropriate for the current device
 */
export function horizontalScale(size: number): number {
  return scale(size, false);
}

/**
 * Moderately scales a size based on the device's screen width
 * Less aggressive scaling for font sizes to maintain readability
 *
 * @param size The original size from the design
 * @param factor The factor to moderate the scaling (default: 0.5)
 * @returns The moderately scaled size
 */
export function moderateScale(size: number, factor: number = 0.5): number {
  const { width } = getScreenDimensions();
  const scaleFactor = 1 + ((width / DESIGN_SPECS.widthDesign) - 1) * factor;
  return size * scaleFactor;
}

export default scale;
