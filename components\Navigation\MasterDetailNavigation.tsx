import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  Dimensions,
  ViewStyle,
  Modal,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import {
  isTablet,
  isLargeTablet,
  getOrientation,
  spacing,
  tabletLayout,
} from '@/utils/responsive';
import { useOrientation } from '@/hooks/useOrientation';
import { CoreColors } from '@/constants/Colors';

interface MasterDetailNavigationProps {
  masterComponent: React.ReactNode;
  detailComponent: React.ReactNode;
  masterWidth?: number;
  showMasterInPortrait?: boolean;
  splitThreshold?: number;
  masterTitle?: string;
  detailTitle?: string;
  onMasterVisibilityChange?: (visible: boolean) => void;
  style?: ViewStyle;
}

/**
 * Master-Detail Navigation component optimized for tablet layouts
 * Provides adaptive navigation patterns based on screen size and orientation
 */
const MasterDetailNavigation: React.FC<MasterDetailNavigationProps> = ({
  masterComponent,
  detailComponent,
  masterWidth,
  showMasterInPortrait = false,
  splitThreshold = 768,
  masterTitle = 'Master',
  detailTitle = 'Detail',
  onMasterVisibilityChange,
  style,
}) => {
  const { orientation, screenWidth } = useOrientation();
  const [masterVisible, setMasterVisible] = useState(false);
  const [slideAnim] = useState(new Animated.Value(0));

  // Determine if we should show master panel based on screen size and orientation
  const shouldShowMaster = screenWidth >= splitThreshold && 
    (orientation === 'landscape' || showMasterInPortrait);

  // Calculate master panel width
  const getMasterWidth = () => {
    if (masterWidth) return masterWidth;
    
    if (isLargeTablet()) {
      return tabletLayout.sidebarWidth || 400;
    } else if (isTablet()) {
      return tabletLayout.sidebarWidth || 320;
    }
    
    return 280;
  };

  const calculatedMasterWidth = getMasterWidth();

  // Handle master panel visibility changes
  useEffect(() => {
    onMasterVisibilityChange?.(shouldShowMaster || masterVisible);
  }, [shouldShowMaster, masterVisible, onMasterVisibilityChange]);

  // Animate master panel for modal presentation
  const showMasterModal = () => {
    setMasterVisible(true);
    Animated.timing(slideAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const hideMasterModal = () => {
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setMasterVisible(false);
    });
  };

  // Master panel component
  const MasterPanel = ({ isModal = false }: { isModal?: boolean }) => (
    <View style={[
      styles.masterPanel,
      {
        width: calculatedMasterWidth,
        backgroundColor: '#FAFBFC',
        borderRightWidth: isModal ? 0 : 1,
        borderRightColor: '#E5E7EB',
      },
      isModal && styles.masterPanelModal,
    ]}>
      {isModal && (
        <View style={styles.modalHeader}>
          <TouchableOpacity
            onPress={hideMasterModal}
            style={styles.closeButton}
            accessibilityLabel="Close master panel"
          >
            <Ionicons name="close" size={24} color={CoreColors.DarkJungleGreen} />
          </TouchableOpacity>
        </View>
      )}
      {masterComponent}
    </View>
  );

  // Detail panel component
  const DetailPanel = () => (
    <View style={[
      styles.detailPanel,
      {
        flex: shouldShowMaster ? 1 : 1,
        marginLeft: shouldShowMaster ? 0 : 0,
      },
    ]}>
      {!shouldShowMaster && (
        <View style={styles.detailHeader}>
          <TouchableOpacity
            onPress={showMasterModal}
            style={styles.menuButton}
            accessibilityLabel="Show master panel"
          >
            <Ionicons name="menu" size={24} color={CoreColors.DarkJungleGreen} />
          </TouchableOpacity>
        </View>
      )}
      {detailComponent}
    </View>
  );

  // Split view layout for tablets in landscape
  if (shouldShowMaster) {
    return (
      <View style={[styles.container, style]}>
        <MasterPanel />
        <DetailPanel />
      </View>
    );
  }

  // Single panel with modal master for phones and portrait tablets
  return (
    <View style={[styles.container, style]}>
      <DetailPanel />
      
      {/* Master panel modal */}
      <Modal
        visible={masterVisible}
        animationType="none"
        transparent={true}
        onRequestClose={hideMasterModal}
      >
        <View style={styles.modalOverlay}>
          <TouchableOpacity
            style={styles.modalBackdrop}
            activeOpacity={1}
            onPress={hideMasterModal}
          />
          <Animated.View
            style={[
              styles.modalContainer,
              {
                transform: [{
                  translateX: slideAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [-calculatedMasterWidth, 0],
                  }),
                }],
              },
            ]}
          >
            <MasterPanel isModal={true} />
          </Animated.View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
  },
  
  masterPanel: {
    backgroundColor: '#FAFBFC',
    borderRightWidth: 1,
    borderRightColor: '#E5E7EB',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 2, height: 0 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  
  masterPanelModal: {
    borderRightWidth: 0,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 4, height: 0 },
        shadowOpacity: 0.2,
        shadowRadius: 12,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  
  detailPanel: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  
  detailHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  
  menuButton: {
    padding: spacing.xs,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  
  modalOverlay: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  
  modalBackdrop: {
    flex: 1,
  },
  
  modalContainer: {
    height: '100%',
    backgroundColor: '#FAFBFC',
  },
  
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  
  closeButton: {
    padding: spacing.xs,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
});

export default MasterDetailNavigation;