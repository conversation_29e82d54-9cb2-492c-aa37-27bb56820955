import React from 'react';
import { Stack } from 'expo-router';
import { View, Dimensions } from 'react-native';

import BillingInfoScreen from '@/screens/BillingInfo';
import NavigationHeader from '@/components/NavigationHeader';

export default function BillingRoute() {
  const screenHeight = Dimensions.get('window').height;

  return (
    <View style={{ flex: 1, height: screenHeight }}>
      <Stack.Screen options={{
        headerShown: false
      }} />
      <NavigationHeader
        title="Billing Information"
        showBackButton={true}
        showLogout={false}
        showRightLogo={true}
      />
      <BillingInfoScreen />
    </View>
  );
}
