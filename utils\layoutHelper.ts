import { Platform, StatusBar } from 'react-native';
import { getStatusBarHeight, getBottomSpace, isIphoneX } from 'react-native-iphone-x-helper';
import Constants from '@/constants/Const';

/**
 * Helper functions for layout calculations
 */

// Calculate the header height including status bar
export const getHeaderHeight = (): number => {
  if (Platform.OS === 'android') {
    // For Android, use actual status bar height and header content
    const statusBarHeight = StatusBar.currentHeight || 0;
    const headerContentHeight = Constants.heightScale(56);
    return statusBarHeight + headerContentHeight + 16; // Extra padding for proper spacing
  } else {
    // For iOS, keep existing calculation
    const statusBarHeight = getStatusBarHeight();
    const headerContentHeight = Constants.heightScale(56);
    const notchPadding = isIphoneX() ? 10 : 0;
    const platformBuffer = 5;
    return statusBarHeight + headerContentHeight + notchPadding + platformBuffer;
  }
};

// Calculate safe padding for content to avoid going under header
export const getHeaderSafePadding = (): number => {
  return getHeaderHeight(); // Use exact header height for consistent positioning
};

// Get standardized content padding that should be used across all screens
export const getStandardContentPadding = (): number => {
  return 24; // Increased standard padding to use under the header for consistent spacing
};

// Get bottom tab bar height including safe area
export const getBottomTabHeight = (): number => {
  const bottomSpace = Platform.OS === 'ios' ? getBottomSpace() : 0;
  return Constants.heightScale(60) + bottomSpace;
};

// Calculate safe padding for content to avoid going under bottom tab bar
export const getBottomTabSafePadding = (): number => {
  return getBottomTabHeight() + 10; // Add 10px extra for safety margin
};
