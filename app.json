{"expo": {"name": "WatchRx Care Team", "slug": "WatchRxCaregiver", "version": "2.1.0", "orientation": "default", "icon": "./assets/images/app_icon.png", "scheme": "WatchRxCaregiver", "userInterfaceStyle": "automatic", "newArchEnabled": false, "ios": {"supportsTablet": true, "requireFullScreen": false, "bundleIdentifier": "com.watchrxhealth.caregiver", "googleServicesFile": "./firebase/GoogleService-Info.plist", "config": {"googleMapsApiKey": "AIzaSyB2LvSZUp_CDSLfY_tyRCbuy_6UvzKvGio"}, "infoPlist": {"NSLocationWhenInUseUsageDescription": "WatchRx Care Team needs your location to display your position on the map relative to your patients, allowing you to coordinate care visits efficiently.", "NSLocationAlwaysAndWhenInUseUsageDescription": "WatchRx Care Team needs your location to track your position relative to patients on the map, enabling you to receive proximity alerts and optimize care routes even when the app is in the background.", "NSLocationAlwaysUsageDescription": "WatchRx Care Team needs your location to monitor your position relative to patients on the map, enabling timely care coordination and emergency response when needed.", "NSCameraUsageDescription": "WatchRx Care Team needs camera access to conduct secure video calls with patients for remote health assessments and medication adherence checks.", "NSMicrophoneUsageDescription": "WatchRx Care Team needs microphone access to enable clear audio communication during video calls with patients for remote health consultations and care instructions.", "NSUserNotificationsUsageDescription": "WatchRx Care Team needs notification access to send you important alerts about patient emergencies, medication reminders, and care updates to help you provide timely healthcare support.", "ITSAppUsesNonExemptEncryption": false, "UIBackgroundModes": ["voip", "audio", "remote-notification", "fetch"], "NSSpeechRecognitionUsageDescription": "WatchRx Care Team needs speech recognition to enable voice-to-text features for quick note-taking during patient consultations and for hands-free operation during care activities.", "UISupportedInterfaceOrientations": ["UIInterfaceOrientationPortrait", "UIInterfaceOrientationPortraitUpsideDown", "UIInterfaceOrientationLandscapeLeft", "UIInterfaceOrientationLandscapeRight"], "UISupportedInterfaceOrientations~ipad": ["UIInterfaceOrientationPortrait", "UIInterfaceOrientationPortraitUpsideDown", "UIInterfaceOrientationLandscapeLeft", "UIInterfaceOrientationLandscapeRight"], "UIRequiresFullScreen": false, "UIStatusBarHidden": false, "UIViewControllerBasedStatusBarAppearance": true}, "appleTeamId": "WZUTS95U37"}, "android": {"versionCode": 28, "adaptiveIcon": {"foregroundImage": "./assets/images/app_icon.png", "backgroundColor": "#ffffff"}, "package": "com.watchrxhealth.caregiver", "googleServicesFile": "./firebase/google-services.json", "config": {"googleMaps": {"apiKey": "AIzaSyDpkdfpIrIAcU7iFw5-e-PqTZ1LmnGQkjc"}}, "softwareKeyboardLayoutMode": "pan", "permissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.FOREGROUND_SERVICE", "android.permission.ACCESS_BACKGROUND_LOCATION", "android.permission.ACCESS_NETWORK_STATE", "android.permission.CAMERA", "android.permission.INTERNET", "android.permission.MODIFY_AUDIO_SETTINGS", "android.permission.RECORD_AUDIO", "android.permission.SYSTEM_ALERT_WINDOW", "android.permission.WAKE_LOCK", "android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.BLUETOOTH_CONNECT"], "permissionsNotificationText": "WatchRx Care Team uses location services to help you coordinate care with patients.", "intentFilters": [{"action": "android.intent.action.VIEW", "category": ["android.intent.category.DEFAULT", "android.intent.category.BROWSABLE"], "data": {"scheme": "WatchRxCaregiver"}}]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/screen.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-notifications", {"icon": "./assets/images/notification-icon.png", "color": "#ffffff", "sounds": ["./assets/sounds/notification.wav"], "defaultChannel": "default", "enableBackgroundRemoteNotifications": true, "iosDisplayInForeground": true}], ["expo-camera", {"cameraPermission": "WatchRx Care Team needs camera access to conduct secure video calls with patients for remote health assessments and medication adherence checks."}], ["expo-av", {"microphonePermission": "WatchRx Care Team needs microphone access to enable clear audio communication during video calls with patients for remote health consultations and care instructions."}], ["expo-image-picker", {"photosPermission": "WatchRx Care Team needs access to your photos to upload and share important health documents and images with your care team.", "cameraPermission": "WatchRx Care Team needs camera access to capture photos for patient documentation and remote assessments."}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "WatchRx Care Team needs your location to track your position relative to patients on the map, enabling you to receive proximity alerts and optimize care routes even when the app is in the background.", "locationAlwaysPermission": "WatchRx Care Team needs your location to monitor your position relative to patients on the map, enabling timely care coordination and emergency response when needed.", "locationWhenInUsePermission": "WatchRx Care Team needs your location to display your position on the map relative to your patients, allowing you to coordinate care visits efficiently."}], ["expo-build-properties", {"android": {"enableJetifier": true, "minSdkVersion": 26, "compileSdkVersion": 35, "targetSdkVersion": 35}, "ios": {"useFrameworks": "static"}}], ["expo-speech-recognition", {"speechRecognitionPermission": "WatchRx Care Team needs speech recognition to enable voice-to-text features for quick note-taking during patient consultations and for hands-free operation during care activities."}], "expo-secure-store", ["@react-native-firebase/app", {"android": {"notification": {"default_channel_id": "default", "default_color": "@color/notification_icon_color"}}}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "fac69da2-5d38-4708-8ea1-c5779f488435"}}}}