export const SOURCE_ICON: any = {
  'home-outline': require('./ic_home_normal.png'),
  home: require('./ic_home_active.png'),
  'notification-outline': require('./ic_chat_circle.png'),
  notification: require('./Home/img_consult_history.png'),
  'new-feed-outline': require('./ic_feed_normal.png'),
  'new-feed': require('./ic_feed_active.png'),
  'my-work-outline': require('./ic_my_work_normal.png'),
  'my-work': require('./ic_my_work_active.png'),
  'account-outline': require('./ic_account_normal.png'),
  account: require('./ic_account_active.png'),
  back: require('./ic_back.png'),
  whiteArrow: require('./ic_white_arrow.png'),
  search: require('./ic_search_normal.png'),
  option: require('./ic_option.png'),
  greyOption: require('./ic_option_grey.png'),
  edit: require('./ic_edit.png'),
  share: require('./ic_share.png'),
  attack: require('./ic_attack.png'),
  send: require('./ic_send.png'),
  atoz: require('./ic_atoz.png'),
  medication: require('./ic_medication.png'),
  plus: require('./ic_plus.png'),
  whitePlus: require('./ic_white_plus.png'),
  whiteDoctor: require('./ic_white_doctor.png'),
  hospital: require('./ic_hospital.png'),
  healthGuide: require('./ic_health_guide.png'),
  whiteMedication: require('./ic_white_medication.png'),
  rateFull: require('./ic_rate_full.png'),
  addtional: require('./ic_addtional.png'),
  arrowRight: require('./ic_arrow_right.png'),
  'Video Call': require('./ic_video.png'),
  'Voice Call': require('./ic_voice_call.png'),
  'Live Chat': require('./ic_chat_live.png'),
  Message: require('./ic_message.png'),
  additionalInformation: require('./ic_additional_information.png'),
  condition: require('./ic_condition.png'),
  procedure: require('./ic_procedure.png'),
  whiteCondition: require('./ic_white_condition.png'),
  whiteAdditional: require('./ic_white_additional.png'),
  close: require('./ic_close.png'),
  whiteClose: require('./ic_close_16.png'),
  accountNormal: require('./ic_account_normal1.png'),
  followed: require('./ic_followed.png'),
  recommended: require('./ic_recommended.png'),
  checkMark: require('./ic_checkmark.png'),
  backWhite: require('./ic_back_white.png'),
  help: require('./ic_help_white.png'),
  payment: require('./ic_payment.png'),
  term: require('./ic_topic.png'),
  themeMode: require('./ic_theme_mode.png'),
  setting: require('./ic_setting.png'),
  radioActive: require('./ic_radio_active.png'),
  myNetwork: require('./ic_my_network.png'),
  onlineState: require('./ic_online_state.png'),
  filter: require('./ic_filter.png'),
  add: require('./ic_add_16.png'),
  pencil: require('./ic_pencil.png'),
  doctor: require('./ic_doctor.png'),
  typeCall: require('./ic_type_call.png'),
  healthNormal: require('./ic_health_normal.png'),
  allergies: require('./ic_allergies.png'),
  heart: require('./ic_heart.png'),
  exp: require('./ic_exp.png'),
  edu: require('./ic_edu.png'),
  certification: require('./ic_certification.png'),
  percent: require('./ic_percent.png'),
  likeComment: require('./ic_like_comment.png'),
  typeMessage: require('./ic_type_message.png'),
  typeVideo: require('./ic_type_video.png'),
  typeAppointment: require('./ic_appointment.png'),
  calendar: require('./Home/img_schedule.png'),
  time: require('./ic_time.png'),
  history: require('./ic_history.png'),
  'reset-search': require('./ic_reset_search.png'),
  'in-network': require('./In-Network.png'),
  nearby: require('./ic_nearby.png'),
  sideEffect: require('./ic_side_effect.png'),
  thanks: require('./ic_thanks.png'),
  call: require('./ic_call.png'),
  'hospital-bed': require('./ic_hospital_bed.png'),
  'pin-map': require('./ic_pin_map.png'),
  follow: require('./ic_follow.png'),
  patients: require('./Home/img_patient_management.png'),
  careTeam: require('./MyWork/img_care_team.png'),
  appLogo: require('./watchrx.png'),
  whome: require('./w_home.png'),
  whomeactive: require('./active_home.png'),
  logout: require('./ic_logout.png'),
  logo: require('./logo.png'),
  logoHeader: require('./logo.png'),
  warnings: require('./ic_notification.png')
};
