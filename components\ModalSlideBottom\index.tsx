import React from 'react';
import {StyleSheet, TouchableOpacity, View, Dimensions} from 'react-native';
import Animated, {useAnimatedStyle} from 'react-native-reanimated';
import {Colors} from '@/constants';
import {useTheme} from '@/constants/Theme';

const {height: SCREEN_HEIGHT} = Dimensions.get('window');

interface ModalSlideBottomProps {
  children: React.ReactNode;
  onClose: () => void;
  transY: Animated.SharedValue<number>;
  pointerEvents?: 'box-none' | 'none' | 'box-only' | 'auto'; // Add support for pointerEvents
}

const ModalSlideBottom = (props: ModalSlideBottomProps) => {
  const {theme} = useTheme();

  // Create animated style using the modern Reanimated 2 API
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: SCREEN_HEIGHT - props.transY.value }],
    };
  });

  // Use the pointerEvents prop if provided, otherwise default to 'auto'
  const pointerEventsValue = props.pointerEvents || 'auto';

  return (
    <View style={styles.container} pointerEvents={pointerEventsValue}>
      <TouchableOpacity
        onPress={props.onClose}
        activeOpacity={1}
        style={StyleSheet.absoluteFillObject}
      />
      <Animated.View
        style={[
          styles.modal,
          {backgroundColor: theme.modalColor},
          animatedStyle,
        ]}
        pointerEvents="box-none">
        <View style={styles.buttonSlider} />
        {props.children}
      </Animated.View>
    </View>
  );
};

export default ModalSlideBottom;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.ModalBackground,
    justifyContent: 'flex-end',
  },
  modal: {
    position: 'absolute',
    bottom: 0,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    left: 0,
    right: 0,
    zIndex: 100,
    // Default position is offscreen
    transform: [{ translateY: SCREEN_HEIGHT }],
  },
  buttonSlider: {
    width: 48,
    height: 6,
    backgroundColor: Colors.Platinum,
    marginTop: 12,
    borderRadius: 3,
    alignSelf: 'center',
  },
});
