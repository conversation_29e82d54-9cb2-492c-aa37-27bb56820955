import React, { memo, useMemo, useCallback, useState, useEffect, useRef } from 'react';
import { 
  View, 
  FlatList, 
  VirtualizedList, 
  Dimensions, 
  InteractionManager,
  LayoutAnimation,
  Platform,
} from 'react-native';
import { isTablet, isLargeTablet, getOrientation } from './responsive';

/**
 * High-performance FlatList optimized for tablets
 */
export interface OptimizedFlatListProps<T> {
  data: T[];
  renderItem: ({ item, index }: { item: T; index: number }) => React.ReactElement;
  keyExtractor?: (item: T, index: number) => string;
  onEndReached?: () => void;
  onEndReachedThreshold?: number;
  refreshing?: boolean;
  onRefresh?: () => void;
  ListHeaderComponent?: React.ComponentType<any> | React.ReactElement | null;
  ListFooterComponent?: React.ComponentType<any> | React.ReactElement | null;
  ListEmptyComponent?: React.ComponentType<any> | React.ReactElement | null;
  numColumns?: number;
  horizontal?: boolean;
  style?: any;
  contentContainerStyle?: any;
}

export function OptimizedFlatList<T>({
  data,
  renderItem,
  keyExtractor,
  onEndReached,
  onEndReachedThreshold = 0.5,
  refreshing = false,
  onRefresh,
  ListHeaderComponent,
  ListFooterComponent,
  ListEmptyComponent,
  numColumns = 1,
  horizontal = false,
  style,
  contentContainerStyle,
}: OptimizedFlatListProps<T>) {
  // Tablet-optimized performance settings
  const performanceConfig = useMemo(() => {
    const itemCount = data.length;
    const isTabletDevice = isTablet();
    const isLargeTabletDevice = isLargeTablet();
    
    return {
      // Window size - how many items to keep rendered outside visible area
      windowSize: isLargeTabletDevice ? 25 : isTabletDevice ? 20 : 10,
      
      // Initial number of items to render
      initialNumToRender: isLargeTabletDevice ? 20 : isTabletDevice ? 15 : 10,
      
      // Maximum items to render per batch
      maxToRenderPerBatch: isLargeTabletDevice ? 15 : isTabletDevice ? 10 : 5,
      
      // Batching period for rendering updates
      updateCellsBatchingPeriod: isTabletDevice ? 50 : 100,
      
      // Remove clipped subviews for large lists
      removeClippedSubviews: itemCount > (isTabletDevice ? 100 : 50),
      
      // Enable scroll performance optimizations
      scrollEventThrottle: isTabletDevice ? 16 : 32,
      
      // Disable nested scrolling for better performance
      nestedScrollEnabled: false,
      
      // Optimize for tablet memory
      legacyImplementation: false,
    };
  }, [data.length]);

  // Memoized render item to prevent unnecessary re-renders
  const memoizedRenderItem = useCallback(
    ({ item, index }: { item: T; index: number }) => {
      return renderItem({ item, index });
    },
    [renderItem]
  );

  // Optimized key extractor
  const optimizedKeyExtractor = useCallback(
    (item: T, index: number) => {
      if (keyExtractor) {
        return keyExtractor(item, index);
      }
      // Fallback to index-based key
      return `item-${index}`;
    },
    [keyExtractor]
  );

  return (
    <FlatList
      data={data}
      renderItem={memoizedRenderItem}
      keyExtractor={optimizedKeyExtractor}
      onEndReached={onEndReached}
      onEndReachedThreshold={onEndReachedThreshold}
      refreshing={refreshing}
      onRefresh={onRefresh}
      ListHeaderComponent={ListHeaderComponent}
      ListFooterComponent={ListFooterComponent}
      ListEmptyComponent={ListEmptyComponent}
      numColumns={numColumns}
      horizontal={horizontal}
      style={style}
      contentContainerStyle={contentContainerStyle}
      
      // Performance optimizations
      windowSize={performanceConfig.windowSize}
      initialNumToRender={performanceConfig.initialNumToRender}
      maxToRenderPerBatch={performanceConfig.maxToRenderPerBatch}
      updateCellsBatchingPeriod={performanceConfig.updateCellsBatchingPeriod}
      removeClippedSubviews={performanceConfig.removeClippedSubviews}
      scrollEventThrottle={performanceConfig.scrollEventThrottle}
      nestedScrollEnabled={performanceConfig.nestedScrollEnabled}
      legacyImplementation={performanceConfig.legacyImplementation}
      
      // Additional optimizations
      getItemLayout={
        // Only provide getItemLayout for uniform item heights
        numColumns === 1 && !horizontal ? (data, index) => ({
          length: isTablet() ? 120 : 80,
          offset: (isTablet() ? 120 : 80) * index,
          index,
        }) : undefined
      }
    />
  );
}

/**
 * Virtualized grid component optimized for tablets
 */
interface VirtualizedGridProps<T> {
  data: T[];
  renderItem: (item: T, index: number) => React.ReactElement;
  itemHeight: number;
  numColumns?: number;
  spacing?: number;
  onEndReached?: () => void;
  style?: any;
}

export function VirtualizedGrid<T>({
  data,
  renderItem,
  itemHeight,
  numColumns,
  spacing = 8,
  onEndReached,
  style,
}: VirtualizedGridProps<T>) {
  const [screenData, setScreenData] = useState(() => Dimensions.get('window'));
  
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenData(window);
    });
    return () => subscription?.remove?.();
  }, []);

  // Calculate optimal columns based on screen size
  const calculatedColumns = useMemo(() => {
    if (numColumns) return numColumns;
    
    const orientation = getOrientation();
    if (isLargeTablet()) {
      return orientation === 'landscape' ? 4 : 3;
    } else if (isTablet()) {
      return orientation === 'landscape' ? 3 : 2;
    } else {
      return 1;
    }
  }, [numColumns, screenData]);

  // Calculate item width
  const itemWidth = useMemo(() => {
    const availableWidth = screenData.width - (spacing * 2);
    const totalSpacing = (calculatedColumns - 1) * spacing;
    return (availableWidth - totalSpacing) / calculatedColumns;
  }, [screenData.width, calculatedColumns, spacing]);

  // Group data into rows
  const rowData = useMemo(() => {
    const rows = [];
    for (let i = 0; i < data.length; i += calculatedColumns) {
      rows.push(data.slice(i, i + calculatedColumns));
    }
    return rows;
  }, [data, calculatedColumns]);

  const renderRow = useCallback(({ item: rowItems, index }: { item: T[]; index: number }) => (
    <View style={{
      flexDirection: 'row',
      paddingHorizontal: spacing,
      marginBottom: spacing,
    }}>
      {rowItems.map((item, itemIndex) => (
        <View
          key={`${index}-${itemIndex}`}
          style={{
            width: itemWidth,
            height: itemHeight,
            marginRight: itemIndex < rowItems.length - 1 ? spacing : 0,
          }}
        >
          {renderItem(item, index * calculatedColumns + itemIndex)}
        </View>
      ))}
      {/* Fill empty spaces in the last row */}
      {rowItems.length < calculatedColumns && 
        Array.from({ length: calculatedColumns - rowItems.length }).map((_, emptyIndex) => (
          <View
            key={`empty-${index}-${emptyIndex}`}
            style={{
              width: itemWidth,
              marginRight: emptyIndex < calculatedColumns - rowItems.length - 1 ? spacing : 0,
            }}
          />
        ))
      }
    </View>
  ), [itemWidth, itemHeight, spacing, calculatedColumns, renderItem]);

  return (
    <OptimizedFlatList
      data={rowData}
      renderItem={renderRow}
      keyExtractor={(_, index) => `row-${index}`}
      onEndReached={onEndReached}
      style={style}
      getItemLayout={(data, index) => ({
        length: itemHeight + spacing,
        offset: (itemHeight + spacing) * index,
        index,
      })}
    />
  );
}

/**
 * Lazy loading component for heavy content
 */
interface LazyComponentProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  delay?: number;
  threshold?: number;
}

export const LazyComponent: React.FC<LazyComponentProps> = ({
  children,
  fallback = null,
  delay = 0,
  threshold = 0.1,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);
  const elementRef = useRef<View>(null);

  useEffect(() => {
    if (isVisible && !shouldRender) {
      const timer = setTimeout(() => {
        setShouldRender(true);
      }, delay);
      
      return () => clearTimeout(timer);
    }
  }, [isVisible, shouldRender, delay]);

  // Intersection observer simulation for React Native
  useEffect(() => {
    const checkVisibility = () => {
      if (elementRef.current) {
        // Simple visibility check - in a real implementation,
        // you might want to use a more sophisticated approach
        setIsVisible(true);
      }
    };

    const timer = setTimeout(checkVisibility, 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <View ref={elementRef}>
      {shouldRender ? children : fallback}
    </View>
  );
};

/**
 * Optimized image component for tablets
 */
interface OptimizedImageProps {
  source: { uri: string } | number;
  style?: any;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  placeholder?: React.ReactNode;
  onLoad?: () => void;
  onError?: () => void;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = memo(({
  source,
  style,
  resizeMode = 'cover',
  placeholder,
  onLoad,
  onError,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setHasError(true);
    onError?.();
  }, [onError]);

  // Use React Native's Image component with optimizations
  const ImageComponent = require('react-native').Image;

  if (hasError) {
    return placeholder || <View style={[style, { backgroundColor: '#f0f0f0' }]} />;
  }

  return (
    <View style={style}>
      {!isLoaded && placeholder}
      <ImageComponent
        source={source}
        style={[
          style,
          !isLoaded && { position: 'absolute', opacity: 0 }
        ]}
        resizeMode={resizeMode}
        onLoad={handleLoad}
        onError={handleError}
        // Performance optimizations
        fadeDuration={isTablet() ? 300 : 200}
        progressiveRenderingEnabled={true}
        cache="memory"
      />
    </View>
  );
});

/**
 * Smooth layout animation utilities
 */
export const LayoutAnimations = {
  /**
   * Configure smooth layout transitions for tablets
   */
  configureNext: (duration?: number) => {
    const animationDuration = duration || (isTablet() ? 300 : 250);
    
    LayoutAnimation.configureNext({
      duration: animationDuration,
      create: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
      update: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.scaleXY,
      },
      delete: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
    });
  },

  /**
   * Smooth orientation change animation
   */
  orientationChange: () => {
    if (Platform.OS === 'ios') {
      LayoutAnimation.configureNext({
        duration: isTablet() ? 400 : 300,
        create: {
          type: LayoutAnimation.Types.easeInEaseOut,
          property: LayoutAnimation.Properties.scaleXY,
        },
        update: {
          type: LayoutAnimation.Types.easeInEaseOut,
          property: LayoutAnimation.Properties.scaleXY,
        },
      });
    }
  },

  /**
   * Smooth list item animation
   */
  listItemChange: () => {
    LayoutAnimation.configureNext({
      duration: isTablet() ? 250 : 200,
      create: {
        type: LayoutAnimation.Types.spring,
        property: LayoutAnimation.Properties.opacity,
        springDamping: 0.7,
      },
      update: {
        type: LayoutAnimation.Types.spring,
        property: LayoutAnimation.Properties.scaleXY,
        springDamping: 0.7,
      },
      delete: {
        type: LayoutAnimation.Types.easeOut,
        property: LayoutAnimation.Properties.opacity,
      },
    });
  },
};

/**
 * Performance monitoring hook for components
 */
export const useRenderPerformance = (componentName: string) => {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(Date.now());

  useEffect(() => {
    renderCount.current += 1;
    const now = Date.now();
    const timeSinceLastRender = now - lastRenderTime.current;
    lastRenderTime.current = now;

    if (__DEV__ && renderCount.current > 1) {
      console.log(
        `${componentName} render #${renderCount.current} (${timeSinceLastRender}ms since last)`
      );
    }
  });

  return {
    renderCount: renderCount.current,
    resetCount: () => { renderCount.current = 0; },
  };
};

/**
 * Batch state updates for better performance
 */
export const useBatchedUpdates = () => {
  const pendingUpdates = useRef<(() => void)[]>([]);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const batchUpdate = useCallback((update: () => void) => {
    pendingUpdates.current.push(update);

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      InteractionManager.runAfterInteractions(() => {
        const updates = pendingUpdates.current;
        pendingUpdates.current = [];
        updates.forEach(update => update());
      });
    }, isTablet() ? 16 : 32); // 60fps for tablets, 30fps for phones
  }, []);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return batchUpdate;
};

export default {
  OptimizedFlatList,
  VirtualizedGrid,
  LazyComponent,
  OptimizedImage,
  LayoutAnimations,
  useRenderPerformance,
  useBatchedUpdates,
};