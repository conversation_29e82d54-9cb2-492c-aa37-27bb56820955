import React from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams } from 'expo-router';

import PasswordExpireScreen from '@/screens/LoginAndSignUp/PasswordExpire';

export default function PasswordExpireRoute() {
  const { userName } = useLocalSearchParams();

  return (
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      <PasswordExpireScreen userName={userName as string} />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});