import {combineReducers} from 'redux';
import currentOrgIdReducer from './currentOrgIdReducer';
import currentPatientIdReducer from './currentPatientIdReducer';
import currentPatientNameReducer from './currentPatientNameReducer';
import currentTaskReducer from './currentTaskReducer';
import dueTasksListReducer from './dueTasksListReducer';
import alertsListReducer from './fetchAlertsListReducer';
import patientsListReducer from './fetchPatientsListReducer';
import futureTasksListReducer from './futureTasksListReducer';
import loginReducer from './loginReducer';
import userProfileReducer from './userProfileReducer';

const appreducer = combineReducers({
  loginReducer,
  patientsListReducer,
  userProfileReducer,
  alertsListReducer,
  currentPatientIdReducer,
  dueTasksListReducer,
  futureTasksListReducer,
  currentPatientNameReducer,
  currentTaskReducer,
  currentOrgIdReducer,
});

const rootReducer = (state: any, action: any) => {
  if (action.type === 'USER_LOGOUT') {
    state = undefined;
  }
  return appreducer(state, action);
};
export default rootReducer;
