import {useNavigation} from '@react-navigation/native';
import React, {memo} from 'react';
import {
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {Colors} from '@/constants';
import {useTheme} from '@/constants/Theme';
import Theme from '@/constants/Theme';

interface HeaderButtonProps {
  style?: ViewStyle;
}

const HeaderButton = memo((props: HeaderButtonProps) => {
  const {theme} = useTheme();
  const {goBack} = useNavigation();
  return (
    <View style={[styles.container, props.style]}>
      <TouchableOpacity
        style={[styles.backButton, {borderColor: theme.innearColor}]}
        onPress={goBack}>
        <Image
          source={require('images/ic_back.png')}
          style={{tintColor: theme.activeTincolor}}
        />
      </TouchableOpacity>
    </View>
  );
});

export default HeaderButton;

const styles = StyleSheet.create({
  container: {
    ...Theme.flexRowSpace,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.Platinum,
    ...Theme.center,
  },
});
