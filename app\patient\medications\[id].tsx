import React from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams } from 'expo-router';
import { useDispatch } from 'react-redux';

import MedicationPageScreen from '@/screens/PatientDetails/Medications';
import { setCurrentPatientId } from '@/services/actions/patientActions';

export default function MedicationPageRoute() {
  const { id } = useLocalSearchParams();
  const dispatch = useDispatch();

  React.useEffect(() => {
    if (id) {
      dispatch(setCurrentPatientId(id as string));
    }
  }, [id, dispatch]);

  return (
    // Explicitly exclude the top and bottom edges to prevent double padding with the header and remove bottom safe area
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      <MedicationPageScreen />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
