import React from 'react';
import { ZoomVideoSdkProvider, RawDataMemoryMode } from '@zoom/react-native-videosdk';
import { Platform } from 'react-native';

interface ZoomProviderWrapperProps {
  children: React.ReactNode;
}

const ZoomProviderWrapper: React.FC<ZoomProviderWrapperProps> = ({ children }) => {
  // Configuration for Zoom Video SDK based on official documentation
  // See: https://developers.zoom.us/docs/video-sdk/react-native/
  const zoomConfig = {
    // Use the correct app group ID format for iOS
    appGroupId: Platform.OS === 'ios' ? 'group.watchrx.io' : '',
    domain: 'zoom.us',
    enableLog: true, // Enable logs for debugging
    
    // Video settings
    videoRawDataMemoryMode: RawDataMemoryMode.Stack, // Use stack memory mode
    enableFullHD: false, // Set to false for better performance
    enableHardwareAcceleration: true, // Enable hardware acceleration
    
    // Audio settings
    audioRawDataMemoryMode: RawDataMemoryMode.Stack, // Use stack memory mode
    speakerFilePath: '', // Leave empty for default
    
    // Feature settings
    enableFaceBeauty: false, // Disable face beauty for better performance
    enableRawDataDelegate: false, // Disable raw data delegate
    
    // Additional recommended settings
    enableAlwaysAudioEnable: true, // Ensure audio can be enabled
    enableCustomizedMeetingUI: true, // Use customized UI
    enableCustomizedMeetingUITitle: true, // Allow custom title
    useExternalVideoSource: false, // Don't use external video source
  };

  return (
    <ZoomVideoSdkProvider config={zoomConfig}>
      {children}
    </ZoomVideoSdkProvider>
  );
};

export default ZoomProviderWrapper;
