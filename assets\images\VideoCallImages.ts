/**
 * Modern VideoCallIcons using @expo/vector-icons
 * This ensures consistent and modern icon appearance across the app
 * 
 * Using specific literal types to ensure proper TypeScript validation
 */
import { MaterialIcons, MaterialCommunityIcons } from '@expo/vector-icons';

// Define icon name types based on the actual icon libraries
type MaterialIconName = keyof typeof MaterialIcons.glyphMap;
type MaterialCommunityIconName = keyof typeof MaterialCommunityIcons.glyphMap;

// Icon constants with proper typings
export const VIDEO_CALL_ICONS = {
  // Microphone icons (MaterialIcons)
  micMute: 'mic-off' as MaterialIconName,
  micUnmute: 'mic' as MaterialIconName,
  
  // Video icons (MaterialIcons)
  video: 'videocam' as MaterialIconName,
  videoOff: 'videocam-off' as MaterialIconName,
  
  // Call control icons (MaterialIcons)
  call: 'call' as MaterialIconName,
  callEnd: 'call-end' as MaterialIconName,
  
  // Camera control icons (MaterialCommunityIcons)
  flipCamera: 'camera-flip' as MaterialCommunityIconName,
};
