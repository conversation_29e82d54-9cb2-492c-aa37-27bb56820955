import { useIsFocused } from '@react-navigation/native';
import { useRouter } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import { useRouteContext } from '@/context/RouteContext';
import {
  <PERSON>ert,
  BackHandler,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Modal,
} from 'react-native';
import { useDispatch } from 'react-redux';
import { LinearGradient } from 'expo-linear-gradient';
import ButtonLinear from '@/components/Buttons/ButtonLinear';
import InputApp from '@/components/InputApp';
import Loader from '@/components/Loader/Loader';
import Text from '@/components/Text';
import { Colors } from '@/constants';
import { useTheme } from '@/constants/Theme';
import { SOURCE_ICON } from '@/assets/images';
import { isTablet } from '@/utils/responsive';
import { TabletLogin } from '@/components/Auth';
import {
  logInFailure,
  logInSuccess,
} from '@/services/actions/loginActions';
import { fetchUserProfile } from '@/services/actions/userProfileActions';
import { apiPost } from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import {
  setAuthToken,
  setCredentialsSInfo,
} from '@/services/secure-storage';
import { isInternetAvailable } from '@/utils/internetCheck/connectivityCheck';
import scale from '@/utils/scale';
import validationEmail from '@/utils/validation/email';
import { determineUserRole } from '@/utils/roleHelper';
import { Ionicons } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

interface LoginCredentials {
  username: string;
  password: string;
  userType: string;
}

interface RoleOption {
  label: string;
  value: 'caregiver' | 'physician';
  icon: string;
}

const roleOptions: RoleOption[] = [
  {
    label: 'Case Manager',
    value: 'caregiver',
    icon: 'people-outline'
  },
  {
    label: 'Physician',
    value: 'physician',
    icon: 'medical-outline'
  }
];

const Login = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const { setHasSelectedOrganization } = useRouteContext();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [visiblePassword, setVisiblePassword] = useState(false);
  const [isValidEmail, setIsValidEmail] = useState(false);
  const { theme } = useTheme();
  const [loader, setLoader] = useState(false);
  
  // Role selection state
  const [selectedRole, setSelectedRole] = useState<'caregiver' | 'physician'>('caregiver');
  const [showRoleDropdown, setShowRoleDropdown] = useState(false);

  // Use tablet-optimized component for tablets
  if (isTablet()) {
    return (
      <TabletLogin
        onLogin={(loginEmail, loginPassword) => {
          setEmail(loginEmail);
          setPassword(loginPassword);
          onLogin();
        }}
        onForgotPassword={() => router.push('/auth/forgot-password')}
        onSignUp={() => router.push('/auth/signup')}
        loading={loader}
      />
    );
  }

  const onShowHidePassword = useCallback(() => {
    setVisiblePassword(prev => !prev);
  }, []);

  const isFocused = useIsFocused();
  useEffect(() => {
    const exitApp = () => {
      BackHandler.exitApp();
      return true;
    };
    if (isFocused) {
      BackHandler.addEventListener('hardwareBackPress', exitApp);
    }
    return () => {
      BackHandler.removeEventListener('hardwareBackPress', exitApp);
    };
  }, [isFocused]);

  const onSignUp = useCallback(() => {
    router.push('/auth/signup');
  }, [router]);

  const onLogin = async () => {
    setLoader(true);

    // Use the selected role instead of auto-detection
    const userRole = determineUserRole(email.trim(), selectedRole);

    const user: LoginCredentials = {
      username: email.trim(),
      password: password.trim(),
      userType: userRole.userType,
    };

    const userName = email.trim();

    try {
      const loginResponse = await apiPost(URLS.authUrl + 'authenticate', user);

      if (loginResponse?.status === 200) {
        setLoader(false);

        if (loginResponse?.data?.responseMessage === 'Success') {
          if (loginResponse.data?.passwordExpired) {
            Alert.alert(
              'Warning',
              'Your password has expired because you haven\'t changed it in a while. You will need to create a new one.',
              [
                {
                  text: 'Ok',
                  onPress: () => {
                    router.replace({
                      pathname: '/auth/password-expire',
                      params: { userName }
                    });
                  },
                },
              ],
            );
          } else {
            const userData = {
              ...user,
              token: loginResponse?.data?.token,
              userId: loginResponse?.data?.userId,
              userRole: userRole.userType,
              roleId: userRole.roleId,
            };

            dispatch(logInSuccess(userData));
            setAuthToken(loginResponse?.data?.token);
            setCredentialsSInfo(email, password, selectedRole);
            dispatch(
              fetchUserProfile({
                careGiverId: loginResponse?.data?.userId,
                roleType: userRole.roleId.toString()
              }),
            );

            navigateAfterLogin();
          }
        } else {
          Alert.alert(
            'Error',
            loginResponse?.data?.responseMessage || 'Login failed',
            [{text: 'Dismiss'}]
          );
        }
      } else {
        setLoader(false);
        const errorMessage = loginResponse?.response?.data?.responseMessage
          ? loginResponse?.response?.data?.responseMessage
          : loginResponse.message === 'Network Error'
          ? 'Network error. Please check your data connection.'
          : loginResponse.message || 'Unknown error';

        dispatch(logInFailure(errorMessage));
        Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
      }
    } catch (error) {
      setLoader(false);
      const errorMessage = 'Unexpected error during login. Please try again.';
      dispatch(logInFailure(errorMessage));
      Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
    }
  };

  const navigateAfterLogin = useCallback(() => {
    setHasSelectedOrganization(false);

    try {
      router.replace('/organization');
    } catch (error) {
      router.replace('/(tabs)');
    }
  }, [router, setHasSelectedOrganization]);

  const onForgotPassword = useCallback(() => {
    router.push('/auth/forgot-password');
  }, [router]);

  useEffect(() => {
    setIsValidEmail(validationEmail(email));
  }, [email]);

  const renderRoleSelector = () => {
    const selectedRoleOption = roleOptions.find(option => option.value === selectedRole);
    
    return (
      <View style={styles.roleSelectorContainer}>
        <Text style={styles.roleSelectorLabel}>Select Your Role</Text>
        <TouchableOpacity 
          style={styles.roleDropdownButton}
          onPress={() => setShowRoleDropdown(!showRoleDropdown)}
          activeOpacity={0.8}
        >
          <View style={styles.roleDropdownContent}>
            <Ionicons name={selectedRoleOption?.icon as any} size={20} color={Colors.TiffanyBlue} />
            <Text style={styles.roleDropdownText}>{selectedRoleOption?.label}</Text>
          </View>
          <Ionicons 
            name={showRoleDropdown ? "chevron-up" : "chevron-down"} 
            size={20} 
            color={Colors.GrayBlue} 
          />
        </TouchableOpacity>
        
        {/* Role Selection Modal */}
        <Modal
          visible={showRoleDropdown}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowRoleDropdown(false)}
        >
          <View style={styles.modalOverlay}>
            <TouchableOpacity 
              style={styles.modalBackground}
              activeOpacity={1}
              onPress={() => setShowRoleDropdown(false)}
            />
            <View style={styles.modalContainer}>
              <View style={styles.modalContent}>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>Select Your Role</Text>
                  <TouchableOpacity 
                    onPress={() => setShowRoleDropdown(false)}
                    style={styles.modalCloseButton}
                    hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                  >
                    <Ionicons name="close" size={24} color={Colors.GrayBlue} />
                  </TouchableOpacity>
                </View>
                
                <View style={styles.optionsContainer}>
                  {roleOptions.map((option, index) => (
                    <TouchableOpacity
                      key={option.value}
                      style={[
                        styles.optionItem,
                        selectedRole === option.value && styles.optionItemSelected,
                        index === roleOptions.length - 1 && styles.optionItemLast
                      ]}
                      onPress={() => {
                        setSelectedRole(option.value);
                        setShowRoleDropdown(false);
                      }}
                      activeOpacity={0.7}
                    >
                      <View style={styles.optionLeft}>
                        <View style={[
                          styles.optionIconContainer,
                          { 
                            backgroundColor: option.value === 'physician' 
                              ? Colors.BlueCrayola + '15' 
                              : Colors.TiffanyBlue + '15' 
                          }
                        ]}>
                          <Ionicons 
                            name={option.icon as any} 
                            size={28} 
                            color={option.value === 'physician' ? Colors.BlueCrayola : Colors.TiffanyBlue} 
                          />
                        </View>
                        <View style={styles.optionTextContainer}>
                          <Text style={styles.optionLabel}>{option.label}</Text>
                        </View>
                      </View>
                      
                      <View style={styles.optionRight}>
                        {selectedRole === option.value ? (
                          <View style={styles.selectedCheckmark}>
                            <Ionicons name="checkmark-circle" size={28} color={Colors.TiffanyBlue} />
                          </View>
                        ) : (
                          <View style={styles.unselectedCircle}>
                            <View style={styles.unselectedCircleInner} />
                          </View>
                        )}
                      </View>
                    </TouchableOpacity>
                  ))}
                </View>
                
                <View style={styles.modalFooter}>
                  <TouchableOpacity 
                    style={styles.continueButton}
                    onPress={() => setShowRoleDropdown(false)}
                  >
                    <Text style={styles.continueButtonText}>Continue</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    );
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <Loader modalVisible={loader} />
      
      {/* Background Gradient */}
      <LinearGradient
        colors={['#f8fffe', '#e8f8f7', '#ffffff']}
        style={styles.background}
      />
      
      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Header Section */}
        <View style={styles.headerSection}>
          <View style={styles.logoContainer}>
            <Image 
              source={require('@/assets/images/logo_1.png')} 
              style={styles.logo}
              resizeMode="contain"
            />
          </View>
          
          <View style={styles.welcomeTextContainer}>
            <Text style={styles.welcomeTitle}>Welcome Back</Text>
            <Text style={styles.welcomeSubtitle}>
              Sign in to continue to your account
            </Text>
          </View>
        </View>

        {/* Form Section */}
        <View style={styles.formSection}>
          {/* Role Selection */}
          {renderRoleSelector()}

          <View style={styles.inputContainer}>
            <InputApp
              borderColor={isValidEmail ? Colors.TiffanyBlue : Colors.InnearColor}
              title="Email Address"
              value={email}
              onChangeText={setEmail}
              placeholder="Enter your email"
              icon={
                validationEmail(email) ? (
                  <Image
                    source={require('@/assets/images/ic_accept.png')}
                    style={styles.inputIcon}
                  />
                ) : (
                  <Image source={SOURCE_ICON['reset-search']} style={styles.inputIcon} />
                )
              }
              isShowIcon={true}
              keyboardType="email-address"
              style={styles.input}
            />
            
            <InputApp
              borderColor={password.length > 0 ? Colors.TiffanyBlue : Colors.InnearColor}
              title="Password"
              value={password}
              onChangeText={setPassword}
              placeholder="Enter your password"
              secureTextEntry={!visiblePassword}
              marginTop={20}
              icon={
                <Image
                  source={require('@/assets/images/ic_eye_on.png')}
                  style={styles.inputIcon}
                />
              }
              isShowIcon
              iconPress={onShowHidePassword}
              style={styles.input}
            />
          </View>

          {/* Forgot Password */}
          <TouchableOpacity style={styles.forgotPasswordContainer} onPress={onForgotPassword}>
            <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
          </TouchableOpacity>

          {/* Login Button */}
          <View style={styles.loginButtonContainer}>
            <ButtonLinear
              title="Sign In"
              onPress={async () => {
                if (await isInternetAvailable()) {
                  onLogin();
                }
              }}
              disabled={!(validationEmail(email) && password)}
              loader={loader}
              style={styles.loginButton}
            />
          </View>
        </View>

        {/* Footer Section */}
        <View style={styles.footerSection}>
          <View style={styles.dividerContainer}>
            <View style={styles.dividerLine} />
            <Text style={styles.dividerText}>or</Text>
            <View style={styles.dividerLine} />
          </View>

          <View style={styles.signUpContainer}>
            <Text style={styles.signUpText}>
              Don't have an account?{' '}
              <Text style={styles.signUpLink} onPress={onSignUp}>
                Sign Up
              </Text>
            </Text>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default Login;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.White,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 30,
  },
  headerSection: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoContainer: {
    marginBottom: 30,
    shadowColor: Colors.TiffanyBlue,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  logo: {
    width: scale(120),
    height: scale(120),
  },
  welcomeTextContainer: {
    alignItems: 'center',
  },
  welcomeTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.DarkJungleGreen,
    marginBottom: 8,
    textAlign: 'center',
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: Colors.GrayBlue,
    textAlign: 'center',
    lineHeight: 22,
  },
  formSection: {
    marginBottom: 40,
  },
  inputContainer: {
    marginBottom: 16,
  },
  input: {
    borderRadius: 12,
    backgroundColor: Colors.White,
    shadowColor: Colors.Black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  inputIcon: {
    width: 20,
    height: 20,
    tintColor: Colors.TiffanyBlue,
  },
  forgotPasswordContainer: {
    alignSelf: 'flex-end',
    marginTop: 12,
    marginBottom: 32,
  },
  forgotPasswordText: {
    fontSize: 14,
    color: Colors.TiffanyBlue,
    fontWeight: '600',
  },
  loginButtonContainer: {
    marginTop: 8,
  },
  loginButton: {
    height: 56,
    borderRadius: 16,
    shadowColor: Colors.TiffanyBlue,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  footerSection: {
    marginTop: 'auto',
    paddingTop: 20,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 32,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: Colors.InnearColor,
  },
  dividerText: {
    marginHorizontal: 16,
    fontSize: 14,
    color: Colors.GrayBlue,
  },
  signUpContainer: {
    alignItems: 'center',
  },
  signUpText: {
    fontSize: 16,
    color: Colors.GrayBlue,
    textAlign: 'center',
  },
  signUpLink: {
    color: Colors.TiffanyBlue,
    fontWeight: '600',
  },
  roleSelectorContainer: {
    marginBottom: 24,
  },
  roleSelectorLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.DarkJungleGreen,
    marginBottom: 8,
  },
  roleDropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderWidth: 1,
    borderColor: Colors.InnearColor,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  roleDropdownContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  roleDropdownText: {
    fontSize: 16,
    color: Colors.DarkJungleGreen,
    marginLeft: 12,
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    width: width * 0.9,
    maxWidth: 400,
    backgroundColor: Colors.White,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  modalContent: {
    padding: 24,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.DarkJungleGreen,
  },
  modalCloseButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F5F5F5',
  },
  optionsContainer: {
    marginBottom: 24,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 12,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: '#FAFAFA',
  },
  optionItemSelected: {
    backgroundColor: Colors.TiffanyBlue + '10',
    borderColor: Colors.TiffanyBlue + '40',
  },
  optionItemLast: {
    marginBottom: 0,
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionTextContainer: {
    flex: 1,
  },
  optionLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.DarkJungleGreen,
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
    color: Colors.GrayBlue,
    lineHeight: 18,
  },
  optionRight: {
    marginLeft: 16,
  },
  selectedCheckmark: {
    
  },
  unselectedCircle: {
    width: 28,
    height: 28,
    borderRadius: 14,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  unselectedCircleInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'transparent',
  },
  modalFooter: {
    alignItems: 'center',
    paddingTop: 8,
  },
  continueButton: {
    backgroundColor: Colors.TiffanyBlue,
    paddingVertical: 14,
    paddingHorizontal: 32,
    borderRadius: 12,
    minWidth: 120,
    alignItems: 'center',
    shadowColor: Colors.TiffanyBlue,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.White,
  },
});
