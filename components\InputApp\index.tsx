import React, {Dispatch, memo, SetStateAction} from 'react';
import {
  ColorValue,
  KeyboardTypeOptions,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import {useTheme} from '@/constants/Theme';
import Text from '../Text';
import TextInput from '../TextInput';

interface InputAppProps {
  value: string;
  onChangeText?: (text: string) => void | Dispatch<SetStateAction<string>>;
  placeholder?: string;
  isShowIcon?: boolean;
  icon?: any;
  secureTextEntry?: boolean;
  style?: ViewStyle;
  styleView?: ViewStyle;
  title: string;
  borderColor?: ColorValue | string;
  iconPress?: () => void;
  autoFocus?: boolean;
  isShowIconLeft?: boolean;
  iconLeft?: any;
  iconPressLeft?: () => void;
  marginTop?: number;
  multiline?: boolean;
  editable?: boolean;
  onPress?: () => void;
  styleInput?: ViewStyle;
  keyboardType?: KeyboardTypeOptions;
}

const InputApp = memo(
  ({
    value,
    placeholder,
    onChangeText,
    isShowIcon,
    icon,
    secureTextEntry,
    style,
    styleView,
    title,
    borderColor,
    iconPress,
    autoFocus,
    isShowIconLeft,
    iconLeft,
    iconPressLeft,
    marginTop,
    multiline,
    editable = true,
    onPress,
    styleInput,
    keyboardType,
  }: InputAppProps) => {
    const {theme} = useTheme();
    return (
      <TouchableOpacity
        style={[styleView, {marginTop: marginTop}]}
        onPress={onPress}
        disabled={!onPress}
        activeOpacity={0.7}>
        <Text type="H6" semibold>
          {title}
        </Text>
        <TextInput
          {...{
            value,
            placeholder,
            onChangeText,
            isShowIcon,
            icon,
            secureTextEntry,
            borderColor: borderColor || theme.innearColor,
            iconPress,
            autoFocus,
            isShowIconLeft,
            iconLeft,
            iconPressLeft,
            multiline,
            editable,
            keyboardType,
          }}
          style={{marginTop: 4, ...style}}
        />
      </TouchableOpacity>
    );
  },
);

export default InputApp;

const styles = StyleSheet.create({
  container: {},
});
