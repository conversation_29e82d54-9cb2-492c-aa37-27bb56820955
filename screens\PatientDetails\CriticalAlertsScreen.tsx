import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Alert, StatusBar } from 'react-native';
import { useRoute } from '@react-navigation/native';
import Container from '@/components/Layout/Container';
import { Colors } from '@/constants';
import { Ionicons } from '@expo/vector-icons';
import Loader from '@/components/Loader/Loader';
import AlertsDatePicker from '@/components/DatePickerButtons/alerts-date-picker';
import Moment from 'moment';
import AlertsPage from '@/screens/PatientsAlerts/AlertsPage';
import { AlertsProps } from '@/models';
import { apiPostWithToken } from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import { useSelector } from 'react-redux';
import { useIsFocused } from '@react-navigation/native';
import NavigationHeader from '@/components/NavigationHeader';

// Using AlertsProps from models instead of defining a custom interface

interface RouteParams {
  patientId?: string;
  patientName?: string;
}

interface CriticalAlertsScreenProps {
  patientId?: string;
  patientName?: string;
  navigation?: any;
}

/**
 * Critical Alerts Screen
 * Displays only critical alerts for the specific patient
 */
const CriticalAlertsScreen: React.FC<CriticalAlertsScreenProps> = (props) => {
  const route = useRoute();
  const params = route.params as RouteParams;
  const patientId = props.patientId || params.patientId || '';
  const patientName = props.patientName || params.patientName || 'Patient';
  const [loading, setLoading] = useState(true);
  const [alerts, setAlerts] = useState<any[]>([]);
  const isFocused = useIsFocused();

  // Get organization ID and caregiver ID from Redux store using memoization for better performance
  const orgId = useSelector((state: any) => state?.currentOrgIdReducer?.orgId);
  const caregiverId = useSelector((state: any) => state?.loginReducer?.data?.userId);

  // State for date range selection - using the same approach as main alerts screen
  const [fromDate, setFromDate] = useState<Date>();
  const [toDate, setToDate] = useState<Date>();
  const [refreshing, setRefreshing] = useState(false);
  
  // Initialize date range on component mount - only once
  useEffect(() => {
    const today = new Date();
    setToDate(today);
    setFromDate(Moment(today).add(-30, "days").toDate());
  }, []);

  // Memoize API parameters for better performance
  const apiParams = React.useMemo(() => {
    if (!fromDate || !toDate) return null;
    return {
      careGiverId: caregiverId,
      startDate: Moment(fromDate).format("YYYY-MM-DD"),
      endDate: Moment(toDate).format("YYYY-MM-DD"),
      orgId: orgId,
      patientId: patientId
    };
  }, [caregiverId, fromDate, toDate, orgId, patientId]);

  // Fetch data when parameters change or screen is focused
  useEffect(() => {
    if (isFocused && apiParams) {
      fetchCriticalAlerts(apiParams);
    }
  }, [isFocused, apiParams]);

  // Fetch critical alerts based on date range with optimized parameters
  const fetchCriticalAlerts = async (params: any) => {
    if (!params) return;
    
    setLoading(true);
    
    try {
      // Use AbortController for request cancellation
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15-second timeout
      
      // Create a modified version of the params with abort signal
      // We can't directly pass the signal to apiPostWithToken since it only accepts two parameters
      const response = await apiPostWithToken(
        params,
        URLS.caregiverUrl + "getAlertbynurseid"
      );
      
      clearTimeout(timeoutId);
      
      if (response?.status === 200) {
        const alerts = response?.data?.data;
        if (alerts?.length) {
          // Filter only Critical alerts for the specific patient
          const criticalAlerts = alerts.filter((alert: any) => 
            alert.alertType === 'Critical' && 
            alert.patientId.toString() === patientId.toString()
          );
          setAlerts(criticalAlerts);
        } else {
          setAlerts([]);
        }
      } else {
        const errorMessage = response?.response?.data?.responseMessage
          ? response?.response?.data?.responseMessage
          : response.message === "Network Error"
          ? "Network error. Please check your data connection."
          : response.message;
        Alert.alert("Error", errorMessage, [{ text: "Dismiss" }]);
      }
    } catch (error: any) {
      // Don't show error for aborted requests
      if (error.name !== 'AbortError') {
        console.error('Error fetching critical alerts:', error);
        Alert.alert('Error', 'Failed to load critical alerts. Please try again.');
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    if (apiParams) {
      fetchCriticalAlerts(apiParams);
    }
  };

  return (
    <Container style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.TurquoiseBlue} />
      <NavigationHeader 
        title={`${patientName} Critical Alerts`}
        showBackButton={true}
        showLogo={false}
        showRightLogo={true}
      />
      <Loader modalVisible={loading} />
      
      {!loading && (
        <View style={styles.contentContainer}>
          {/* Date picker section - matching main alerts screen */}
          <View style={styles.datePickersContainer}>
            <View style={styles.datePickerWrapper}>
              <AlertsDatePicker
                date={fromDate}
                onDateChange={(date) => setFromDate(date)}
                label="Start Date"
              />
            </View>
            <View style={styles.dateSeparator}>
              <Ionicons name="arrow-forward" size={18} color={Colors.GrayBlue} />
            </View>
            <View style={styles.datePickerWrapper}>
              <AlertsDatePicker
                date={toDate}
                onDateChange={(date) => setToDate(date)}
                label="End Date"
              />
            </View>
          </View>

          {/* Alerts list - reusing the AlertsPage component from main dashboard */}
          <View style={styles.alertsContainer}>
            <AlertsPage
              data={alerts as unknown as AlertsProps[]}
              title="Critical"
              nameDisplay={false} // Patient name is already in the header
              onRefresh={handleRefresh}
              refreshing={refreshing}
            />
          </View>
        </View>
      )}
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
    marginTop: 10, // Matches the main CriticalAlerts screen spacing
  },
  datePickersContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  datePickerWrapper: {
    width: '46%',
  },
  dateSeparator: {
    width: '8%',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 20,
  },
  alertsContainer: {
    flex: 1,
    marginTop: 8,
  }
});

export default CriticalAlertsScreen;
