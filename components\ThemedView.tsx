import { View, type ViewProps } from 'react-native';

import { useThemeColor } from '@/hooks/useThemeColor';

export type ThemedViewProps = ViewProps & {
  lightColor?: string;
  darkColor?: string; // Kept for backward compatibility but not used
};

export function ThemedView({ style, lightColor, darkColor, ...otherProps }: ThemedViewProps) {
  // Only use lightColor, ignore darkColor as we only support light mode
  const backgroundColor = useThemeColor({ light: lightColor }, 'background');

  return <View style={[{ backgroundColor }, style]} {...otherProps} />;
}
