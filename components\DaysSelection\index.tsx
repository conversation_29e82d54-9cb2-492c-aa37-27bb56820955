import React, {memo} from 'react';
import {TouchableOpacity, View} from 'react-native';
import Text from '@/components/Text';
import {Colors} from '@/constants';
import Theme from '@/constants/Theme';
import scale from '@/utils/scale';

interface DaysSelectionItemProps {
  id?: number;
  date: String;
  onPress: () => void;
  selected: boolean;
}

export default memo(({date, onPress, selected}: DaysSelectionItemProps) => {
  return (
    <View>
      {selected ? (
        <TouchableOpacity
          activeOpacity={0.54}
          onPress={onPress}
          style={{
            backgroundColor: Colors.TealBlue,
            height: scale(40),
            width: scale(40),
            marginBottom: 10,
            ...Theme.center,
            borderRadius: 8,
            marginRight: 8,
          }}>
          <Text black center color={Colors.GrayBlue} size={20}>
            {date}
          </Text>
        </TouchableOpacity>
      ) : (
        <TouchableOpacity
          activeOpacity={0.54}
          onPress={onPress}
          style={{
            backgroundColor: Colors.Snow,
            height: 40,
            width: 40,
            marginBottom: 10,
            ...Theme.center,
            borderRadius: 8,
            marginRight: 8,
          }}>
          <Text black center color={Colors.White} size={20}>
            {date}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
});
