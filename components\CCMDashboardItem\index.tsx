import React, {memo, useCallback, useRef} from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
  Animated,
  Easing,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { FontAwesome5, MaterialCommunityIcons, MaterialIcons, Ionicons, AntDesign, Feather } from '@expo/vector-icons';
import {Constants} from '@/constants';
import Theme from '@/constants/Theme';
import Text from '../Text';
import { useCCMContentContext } from '@/context/CCMContentContext';

interface ItemProps {
  iconFamily: string;
  iconName: string;
  title: string;
  style?: ViewStyle;
  color: string;
  index: number;
}

export default memo((props: ItemProps) => {
  // Animation values
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  // Animation configurations
  const handlePressIn = () => {
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 150,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic)
      }),
      Animated.timing(opacityAnim, {
        toValue: 0.9,
        duration: 150,
        useNativeDriver: true
      })
    ]).start();
  };

  const handlePressOut = () => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 5,
        tension: 40,
        useNativeDriver: true
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true
      })
    ]).start();
  };

  const router = useRouter();
  const { setCcmContentTitle } = useCCMContentContext();

  const onPress = useCallback(() => {
    // Set the title in context
    setCcmContentTitle(props.title);
    
    // Navigate to the placeholder screen with the tile index and title
    router.push({
      pathname: '/patient/ccm-content/[id]' as any,
      params: {
        id: props.index.toString(),
        tileIndex: props.index,
        title: props.title
      }
    });
  }, [router, props.index, props.title, setCcmContentTitle]);

  // Render appropriate icon based on icon family
  const renderIcon = () => {
    const commonProps = {
      size: 48,
      color: '#FFFFFF',
    };

    switch (props.iconFamily) {
      case 'FontAwesome5':
        return <FontAwesome5 name={props.iconName as any} {...commonProps} />;
      case 'MaterialCommunityIcons':
        return <MaterialCommunityIcons name={props.iconName as any} {...commonProps} />;
      case 'MaterialIcons':
        return <MaterialIcons name={props.iconName as any} {...commonProps} />;
      case 'Ionicons':
        return <Ionicons name={props.iconName as any} {...commonProps} />;
      case 'AntDesign':
        return <AntDesign name={props.iconName as any} {...commonProps} />;
      case 'Feather':
        return <Feather name={props.iconName as any} {...commonProps} />;
      default:
        return <MaterialCommunityIcons name={props.iconName as any} {...commonProps} />;
    }
  };

  return (
    <TouchableOpacity
      activeOpacity={1}
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}>
      <Animated.View
        style={[
          styles.container,
          props.style,
          {
            transform: [{ scale: scaleAnim }],
            opacity: opacityAnim
          }
        ]}>
        <LinearGradient
          colors={[props.color, props.color]}
          style={styles.gradientContainer}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {renderIcon()}
          <Text 
            marginTop={16} 
            size={18} 
            lineHeight={22} 
            center 
            bold 
            color="#FFFFFF"
            style={{
              fontWeight: '900',
              letterSpacing: -0.3
            }}
          >
            {props.title}
          </Text>
        </LinearGradient>
      </Animated.View>
    </TouchableOpacity>
  );
});

const styles = StyleSheet.create({
  container: {
    width: (Constants.width - Constants.widthScale(80)) / 2,
    height: (Constants.width - Constants.widthScale(140)) / 2,
    marginBottom: Constants.heightScale(16),
    borderRadius: 24,
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 12,
    overflow: 'hidden',
  },
  gradientContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
    ...Theme.center,
    paddingHorizontal: Constants.widthScale(12),
  },
  icon: {
    marginBottom: 10,
  },
});
