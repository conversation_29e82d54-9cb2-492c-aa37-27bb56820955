{"name": "watchrxcaregiver", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "android:clean": "expo run:android --clear", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "postinstall": "node ./scripts/postinstall.js"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-community/netinfo": "11.4.1", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/messaging": "^22.2.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.1.1", "@reduxjs/toolkit": "^1.9.7", "@twilio/voice-react-native-sdk": "^1.6.0", "@zoom/react-native-videosdk": "^2.2.0", "axios": "^1.7.7", "eas-cli": "^16.3.2", "expo": "~52.0.46", "expo-application": "~6.0.2", "expo-av": "~15.0.2", "expo-blur": "~14.0.3", "expo-build-properties": "~0.13.2", "expo-camera": "~16.0.18", "expo-cli": "^6.3.12", "expo-constants": "~17.0.8", "expo-device": "~7.0.3", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-location": "~18.0.10", "expo-notifications": "0.28.3", "expo-router": "~4.0.20", "expo-secure-store": "~14.0.1", "expo-speech-recognition": "^2.0.0", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "graphql": "^16.10.0", "moment": "^2.30.1", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.9", "react-native-calendars": "^1.1312.0", "react-native-chart-kit": "^6.12.0", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "~2.20.2", "react-native-maps": "1.18.0", "react-native-modal-datetime-picker": "^18.0.0", "react-native-paper": "^4.12.2", "react-native-phone-number-input": "^2.1.0", "react-native-reanimated": "~3.16.1", "react-native-redash": "^16.2.4", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-sensitive-info": "^5.5.8", "react-native-svg": "^15.8.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "react-redux": "^8.1.3", "redux": "^4.2.1", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-saga": "^1.2.3", "redux-thunk": "^2.4.2", "toggle-switch-react-native": "^3.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-redux": "^7.1.24", "@types/react-test-renderer": "^18.3.0", "@types/redux-logger": "^3.0.9", "@types/redux-saga": "^0.10.5", "babel-plugin-module-resolver": "^4.1.0", "jest": "^29.2.1", "jest-expo": "~52.0.6", "patch-package": "^8.0.0", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true, "packageManager": "yarn@1.22.22+sha1.ac34549e6aa8e7ead463a7407e1c7390f61a6610", "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-native-sensitive-info", "react-native-chart-kit", "react-native-phone-number-input", "react-native-redash", "toggle-switch-react-native"], "listUnknownPackages": false}}}}