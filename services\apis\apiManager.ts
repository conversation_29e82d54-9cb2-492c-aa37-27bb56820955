import axios, { AxiosError } from "axios";
import { getAuthToken } from "../secure-storage/index";
import baseUrl from "@/services/config/config";

/**
 * Standard API response type
 * Maintains backward compatibility with the previous implementation
 */
export interface ApiResponse<T = any> {
  // New standardized properties
  data?: T;
  error?: {
    message: string;
    status?: number;
    details?: any;
  };
  status?: number;

  // Backward compatibility properties
  response?: {
    status: number;
    data: T;
  };
  message?: string;
}

const BASE_URL = "https://watchrxapp.com";

/**
 * Makes a POST request to the specified URL
 * @param url The URL to make the POST request to
 * @param body The request body
 * @returns Promise with the response or error details
 */
export const apiPost = async <T = any>(
  url: string,
  body: Record<string, any>
): Promise<ApiResponse<T>> => {
  try {
    const response = await axios.post<T>(url, body, {
      headers: {
        "Content-Type": "application/json",
      },
      timeout: 15000, // 15 seconds timeout
    });

    // Return both new format and backward compatible format
    return {
      data: response.data,
      status: response.status,
      // Backward compatibility
      response: {
        status: response.status,
        data: response.data,
      },
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMsg = axiosError.message || "Unknown error";

    // Return both new format and backward compatible format
    return {
      error: {
        message: errorMsg,
        status: axiosError.response?.status,
        details: axiosError.response?.data,
      },
      // Backward compatibility
      message: errorMsg,
    };
  }
};

/**
 * Makes a POST request with authentication token
 * @param params Request body parameters
 * @param url The URL to make the POST request to
 * @returns Promise with the response or error details
 */
/**
 * Makes a POST request with authentication token and retry mechanism
 * @param params Request body parameters
 * @param url The URL to make the POST request to
 * @param retries Number of retries to attempt (default: 2)
 * @returns Promise with the response or error details
 */
export const apiPostWithToken = async <T = any>(
  params: any,
  url: string
): Promise<ApiResponse<T>> => {
  try {
    const token = await getAuthToken();

    if (!token) {
      const errorMsg = "Authentication token not found";
      return {
        error: {
          message: errorMsg,
          status: 401,
        },
        // Backward compatibility
        message: errorMsg,
      };
    }

    console.log(
      "apiPostWithToken",
      url,
      params,
      token ? "Token exists" : "No token"
    );

    const response = await axios.post<T>(url, params, {
      headers: {
        Authorization: "Bearer " + token,
        "Content-Type": "application/json",
      },
      // No timeout to match original implementation
    });

    // Return both new format and backward compatible format
    return {
      data: response.data,
      status: response.status,
      // Backward compatibility
      response: {
        status: response.status,
        data: response.data,
      },
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMsg = axiosError.message || "Unknown error";

    // Return both new format and backward compatible format
    return {
      error: {
        message: errorMsg,
        status: axiosError.response?.status,
        details: axiosError.response?.data,
      },
      // Backward compatibility
      message: errorMsg,
    };
  }
};

/**
 * Uploads an image with authentication token
 * @param params FormData containing the image and other parameters
 * @param url The URL to upload the image to (defaults to config URL if not provided)
 * @returns Promise with the response or error details
 */
export const uploadImage = async <T = any>(
  params: any,
  url: string
): Promise<ApiResponse<T>> => {
  try {
    const token = await getAuthToken();
    if (!token) {
      const errorMsg = "Authentication token not found";
      return {
        error: {
          message: errorMsg,
          status: 401,
        },
        // Backward compatibility
        message: errorMsg,
      };
    }

    const response = await axios.post<T>(url, params, {
      headers: {
        Accept: "application/json",
        "Content-Type": "multipart/form-data",
        Authorization: "Bearer " + token,
      },
      // No timeout to match original implementation
    });

    // Return both new format and backward compatible format
    return {
      data: response.data,
      status: response.status,
      // Backward compatibility
      response: {
        status: response.status,
        data: response.data,
      },
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMsg = axiosError.message || "Unknown error";

    // Return both new format and backward compatible format
    return {
      error: {
        message: errorMsg,
        status: axiosError.response?.status,
        details: axiosError.response?.data,
      },
      // Backward compatibility
      message: errorMsg,
    };
  }
};

/**
 * Interface for prescription upload parameters
 */
export interface PrescriptionUploadParams {
  file: {
    uri: string;
    type: string;
    name: string;
  };
  userId: string | number;
  orgId: string | number;
  patientId: string | number;
}

/**
 * Uploads a prescription image for OCR processing
 * @param params Parameters containing file, userId, orgId, and patientId
 * @param url The URL to upload the prescription to (uses medImageUploadUrl from config if not provided)
 * @returns Promise with the response or error details
 */
export const uploadPrescription = async <T = any>(
  params: PrescriptionUploadParams,
  url?: string
): Promise<ApiResponse<T>> => {
  try {
    const { file, userId, orgId, patientId } = params;

    // Validate required parameters
    if (!file || !file.uri) {
      const errorMsg = "No prescription image file provided";
      return {
        error: {
          message: errorMsg,
          status: 400,
        },
        message: errorMsg,
      };
    }

    if (!userId || !orgId || !patientId) {
      const errorMsg = "Missing required IDs: userId, orgId, or patientId";
      return {
        error: {
          message: errorMsg,
          status: 400,
        },
        message: errorMsg,
      };
    }

    // Use provided URL or default from config
    const uploadUrl = url || baseUrl.medImageUploadUrl;
    if (!uploadUrl) {
      const errorMsg = "Prescription upload URL not configured";
      return {
        error: {
          message: errorMsg,
          status: 500,
        },
        message: errorMsg,
      };
    }

    console.log('Uploading prescription image:', {
      uri: file.uri,
      type: file.type,
      name: file.name,
      userId,
      orgId,
      patientId,
      uploadUrl
    });

    const token = await getAuthToken();
    if (!token) {
      const errorMsg = "Authentication token not found";
      return {
        error: {
          message: errorMsg,
          status: 401,
        },
        message: errorMsg,
      };
    }

    const formData = new FormData();
    formData.append('userId', userId.toString());
    formData.append('orgId', orgId.toString());
    formData.append('patientId', patientId.toString());
    
    // Append image file with proper format
    formData.append('file', {
      uri: file.uri,
      type: file.type || 'image/jpeg',
      name: file.name || `prescription_${Date.now()}.jpg`,
    } as any);

    const response = await axios.post<T>(uploadUrl, formData, {
      headers: {
        Authorization: 'Bearer ' + token,
        'Content-Type': 'multipart/form-data',
      },
      timeout: 30000, // 30 second timeout
    });

    console.log('Prescription upload response:', response.data);

    // Return both new format and backward compatible format
    return {
      data: response.data,
      status: response.status,
      // Backward compatibility
      response: {
        status: response.status,
        data: response.data,
      },
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMsg = axiosError.message || "Unknown error";
    console.error('Prescription upload error:', errorMsg, error);

    // Return both new format and backward compatible format
    return {
      error: {
        message: errorMsg,
        status: axiosError.response?.status,
        details: axiosError.response?.data,
      },
      // Backward compatibility
      message: errorMsg,
    };
  }
};

/**
 * Gets an access token for a patient with retry mechanism
 * @param patientId The patient ID
 * @param retryCount Number of retries to attempt (default: 2)
 * @returns Promise with the response or error details
 */
export const getAccessToken = async <T = any>(
  patientId: number | string,
  retryCount: number = 2
): Promise<ApiResponse<T>> => {
  try {
    console.log("apiManager.getAccessToken: Called with patientId:", patientId);

    // Convert to number if it's a string
    const patientIdNum =
      typeof patientId === "string" ? Number(patientId) : patientId;

    if (isNaN(patientIdNum) || patientIdNum <= 0) {
      const errorMsg = "Invalid patient ID";
      console.error(
        "apiManager.getAccessToken: Invalid patient ID:",
        patientId
      );
      return {
        error: {
          message: errorMsg,
          details: { patientId },
        },
        // Backward compatibility
        message: errorMsg,
      };
    }

    // Use direct URL like in the old implementation
    // This was more reliable with watch devices
    const url = `${BASE_URL}/accessToken/${patientIdNum}`;
    console.log("apiManager.getAccessToken: Requesting token from URL:", url);

    // Use the same configuration as the old implementation
    const configurationObject = {
      method: "get",
      url: url,
      timeout: 15000, // 15 seconds timeout
    };

    const response = await axios(configurationObject);
    console.log(
      "apiManager.getAccessToken: Received response with status:",
      response.status
    );

    // Validate the token data
    if (!response.data) {
      throw new Error("Empty token received from server");
    }

    // Return both new format and backward compatible format
    return {
      data: response.data,
      status: response.status,
      // Backward compatibility
      response: {
        status: response.status,
        data: response.data,
      },
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMsg = axiosError.message || "Unknown error";
    console.error(
      "apiManager.getAccessToken: Error getting token:",
      errorMsg,
      error
    );

    // Retry logic for network errors or server errors (5xx)
    if (
      retryCount > 0 &&
      (errorMsg === "Network Error" ||
        axiosError.code === "ECONNABORTED" ||
        (axiosError.response && axiosError.response.status >= 500))
    ) {
      console.log(
        `apiManager.getAccessToken: Retrying... (${retryCount} attempts left)`
      );
      // Wait for a short time before retrying
      await new Promise((resolve) => setTimeout(resolve, 1000));
      return getAccessToken(patientId, retryCount - 1);
    }

    // Return both new format and backward compatible format
    return {
      error: {
        message: errorMsg,
        status: axiosError.response?.status,
        details: axiosError.response?.data,
      },
      // Backward compatibility
      message: errorMsg,
    };
  }
};

/**
 * Acknowledges that a call has ended
 * @param roomSid The room SID for the call
 * @param patientId The patient ID
 * @returns Promise with the response or error details
 */
export const ackCallEnded = async <T = any>(
  roomSid: string,
  patientId: number | string
): Promise<ApiResponse<T>> => {
  try {
    // Convert patientId to string if it's a number
    const patientIdStr =
      typeof patientId === "number" ? patientId.toString() : patientId;

    if (!roomSid || !patientIdStr) {
      const errorMsg = "Invalid parameters";
      return {
        error: {
          message: errorMsg,
          details: { roomSid, patientId },
        },
        // Backward compatibility
        message: errorMsg,
      };
    }

    // Use direct URL like in the old implementation
    // This was more reliable with watch devices
    const url = `${BASE_URL}/logVideoCallTimer/${roomSid}/${patientIdStr}`;

    // Use the same configuration as the old implementation
    const configurationObject = {
      method: "get",
      url: url,
      timeout: 15000, // 15 seconds timeout
    };

    const response = await axios(configurationObject);

    // Return both new format and backward compatible format
    return {
      data: response.data,
      status: response.status,
      // Backward compatibility
      response: {
        status: response.status,
        data: response.data,
      },
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMsg = axiosError.message || "Unknown error";

    // Return both new format and backward compatible format
    return {
      error: {
        message: errorMsg,
        status: axiosError.response?.status,
        details: axiosError.response?.data,
      },
      // Backward compatibility
      message: errorMsg,
    };
  }
};

/**
 * Interface for Zoom token response
 */
export interface ZoomTokenResponse {
  accessToken: string;
  meetingNumber: string;
  password: string;
  userName: string;
}

/**
 * Gets a Zoom access token for a patient
 * @param patientId The patient ID
 * @param retryCount Number of retries to attempt (default: 2)
 * @param program The program category (default: "rpm")
 * @returns Promise with the response or error details
 */
export const getZoomAccessToken = async (
  patientId: number | string,
  retryCount: number = 2,
  program: string = "rpm"
): Promise<ApiResponse<ZoomTokenResponse>> => {
  try {
    console.log(
      "apiManager.getZoomAccessToken: Called with patientId:",
      patientId
    );

    // Convert to number if it's a string
    const patientIdNum =
      typeof patientId === "string" ? Number(patientId) : patientId;

    if (isNaN(patientIdNum) || patientIdNum <= 0) {
      const errorMsg = "Invalid patient ID";
      console.error(
        "apiManager.getZoomAccessToken: Invalid patient ID:",
        patientId
      );
      return {
        error: {
          message: errorMsg,
          details: { patientId },
        },
        // Backward compatibility
        message: errorMsg,
      };
    }

    // Use the Zoom access token API endpoint
    const url = `${BASE_URL}/_ah/api/caregiverapi/v1/zoomAccessToken`;

    const token = await getAuthToken();
    if (!token) {
      const errorMsg = "Authentication token not found";
      return {
        error: {
          message: errorMsg,
          status: 401,
        },
        // Backward compatibility
        message: errorMsg,
      };
    }

    // Get the current user ID from Redux store
    const { store } = require("@/services/store");
    const state = store.getState();
    const caregiverId = state?.loginReducer?.data?.userId;

    console.log(
      "apiManager.getZoomAccessToken: Using caregiver ID:",
      caregiverId || "Not found, using patient ID as fallback"
    );

    // Create the request configuration
    const configurationObject = {
      method: "post",
      url: url,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: {
        patientId: patientIdNum.toString(),
        program: program,
        userId: caregiverId ? caregiverId.toString() : patientIdNum.toString(), // Use caregiver ID with fallback to patient ID
      },
      timeout: 15000, // 15 seconds timeout
    };
    console.log("configurationObject", configurationObject);
    const response = await axios(configurationObject);
    console.log(
      "apiManager.getZoomAccessToken: Received response with status:",
      response.status
    );

    // Validate the token data
    if (!response.data) {
      throw new Error("Empty token received from server");
    }

    // Map the response to the expected format for the Zoom SDK
    // Based on the API response format: { zoomToken, sessionPasscode, sessionName, userName }
    const formattedData = {
      accessToken: response.data.zoomToken,
      meetingNumber: response.data.sessionName || response.data.sessionPasscode,
      password: response.data.sessionPasscode,
      userName: response.data.userName || "Care Manager",
    };
    console.log(
      "apiManager.getZoomAccessToken: Formatted data:",
      formattedData
    );

    console.log(
      "apiManager.getZoomAccessToken: Formatted response data for Zoom SDK"
    );

    // Return both new format and backward compatible format
    return {
      data: formattedData,
      status: response.status,
      // Backward compatibility
      response: {
        status: response.status,
        data: formattedData,
      },
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMsg = axiosError.message || "Unknown error";
    console.error(
      "apiManager.getZoomAccessToken: Error getting token:",
      errorMsg,
      error
    );

    // Retry logic for network errors or server errors (5xx)
    if (
      retryCount > 0 &&
      (errorMsg === "Network Error" ||
        axiosError.code === "ECONNABORTED" ||
        (axiosError.response && axiosError.response.status >= 500))
    ) {
      console.log(
        `apiManager.getZoomAccessToken: Retrying... (${retryCount} attempts left)`
      );
      // Wait for a short time before retrying
      await new Promise((resolve) => setTimeout(resolve, 1000));
      return getZoomAccessToken(patientId, retryCount - 1);
    }

    // Return both new format and backward compatible format
    return {
      error: {
        message: errorMsg,
        status: axiosError.response?.status,
        details: axiosError.response?.data,
      },
      // Backward compatibility
      message: errorMsg,
    };
  }
};

/**
 * Interface for Twilio VOIP token response
 */
export interface TwilioVOIPTokenResponse {
  token: string;
  identity: string;
  phoneNumber?: string; // Optional since VoIP doesn't require traditional phone numbers
  programs: Array<{
    programId: number;
    programName: string;
    description: string;
  }>;
  status: boolean;
}

/**
 * Gets a Twilio VOIP access token for a patient
 * @param patientId The patient ID
 * @param retryCount Number of retries to attempt (default: 2)
 * @param deviceToken Optional device token for push notifications
 * @param platform Optional platform identifier (iOS/Android)
 * @param edge Optional edge location for connectivity (default: let Twilio choose optimal)
 * @returns Promise with the response or error details
 */
export const getTwilioVOIPToken = async (
  patientId: number | string,
  retryCount: number = 2,
  deviceToken?: string,
  platform?: string,
  edge?: string
): Promise<ApiResponse<TwilioVOIPTokenResponse>> => {
  try {
    console.log(
      "apiManager.getTwilioVOIPToken: Called with patientId:",
      patientId,
      "retryCount:",
      retryCount
    );

    // Convert to number if it's a string
    const patientIdNum =
      typeof patientId === "string" ? Number(patientId) : patientId;

    if (isNaN(patientIdNum) || patientIdNum <= 0) {
      const errorMsg = "Invalid patient ID";
      console.error(
        "apiManager.getTwilioVOIPToken: Invalid patient ID:",
        patientId
      );
      return {
        error: {
          message: errorMsg,
          details: { patientId },
        },
        // Backward compatibility
        message: errorMsg,
      };
    }

    // Use the Twilio VOIP push-token API endpoint with patient ID in URL path
    const url = `${BASE_URL}/_ah/api/caregiverapi/v1/pushToken`;

    const token = await getAuthToken();
    if (!token) {
      const errorMsg = "Authentication token not found";
      return {
        error: {
          message: errorMsg,
          status: 401,
        },
        // Backward compatibility
        message: errorMsg,
      };
    }

    console.log(
      "apiManager.getTwilioVOIPToken: Requesting VOIP token from URL:",
      url
    );

    // Simple request payload matching web implementation
    const requestData: any = {
      patientId: patientIdNum.toString(), // Match your curl request format
    };

    console.log(
      "apiManager.getTwilioVOIPToken: Request payload:",
      requestData
    );

    // Create the request configuration using the caregiver's token (same as other APIs)
    const configurationObject = {
      method: "post",
      url: url,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: requestData,
      timeout: 15000, // 15 seconds timeout
    };

    // Retry logic for network issues
    let response;
    let lastError;
    
    for (let attempt = 0; attempt <= retryCount; attempt++) {
      try {
        console.log(`apiManager.getTwilioVOIPToken: API attempt ${attempt + 1}/${retryCount + 1}`);
        response = await axios(configurationObject);
        break; // Success, exit retry loop
      } catch (error: any) {
        lastError = error;
        console.warn(`apiManager.getTwilioVOIPToken: Attempt ${attempt + 1} failed:`, error.message);
        
        if (attempt < retryCount) {
          // Wait before retry (exponential backoff)
          const delayMs = Math.min(1000 * Math.pow(2, attempt), 5000);
          console.log(`apiManager.getTwilioVOIPToken: Retrying in ${delayMs}ms...`);
          await new Promise(resolve => setTimeout(resolve, delayMs));
        }
      }
    }
    
    if (!response) {
      throw lastError || new Error('Failed to get VOIP token after all retries');
    }
    console.log(
      "apiManager.getTwilioVOIPToken: Received response with status:",
      response.status
    );
    console.log(
      "apiManager.getTwilioVOIPToken: Raw response data:",
      JSON.stringify(response.data, null, 2)
    );
    console.log(
      "apiManager.getTwilioVOIPToken: Response data type:",
      typeof response.data
    );

    // Validate the token data - let's check what we actually got
    if (!response.data) {
      throw new Error("Empty response data received from server");
    }

    // Validate the expected API response structure
    const { identity, token: voipToken, phoneNumber, programs, status } = response.data;
    
    console.log("apiManager.getTwilioVOIPToken: Extracted fields:", {
      identity,
      voipToken,
      phoneNumber,
      programs,
      status
    });
    
    if (!voipToken || typeof voipToken !== 'string' || voipToken.trim() === '') {
      console.error("apiManager.getTwilioVOIPToken: Token validation failed:", {
        voipToken,
        type: typeof voipToken,
        exists: !!voipToken
      });
      throw new Error("Missing or invalid token in VOIP response");
    }
    
    if (!identity || typeof identity !== 'string') {
      throw new Error("Missing or invalid identity in VOIP response");
    }
    
    // Phone number is optional for VoIP calls since they work over internet
    // Only validate if phoneNumber is provided and not empty
    if (phoneNumber && typeof phoneNumber !== 'string') {
      throw new Error("Invalid phoneNumber in VOIP response");
    }
    
    if (!Array.isArray(programs)) {
      throw new Error("Missing or invalid programs array in VOIP response");
    }
    
    if (typeof status !== 'boolean') {
      throw new Error("Missing or invalid status in VOIP response");
    }

    // Return simple response data (matching web implementation)
    return {
      data: response.data,
      status: response.status,
      // Backward compatibility
      response: {
        status: response.status,
        data: response.data,
      },
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMsg = axiosError.message || "Unknown error";
    console.error(
      "apiManager.getTwilioVOIPToken: Error getting VOIP token:",
      errorMsg,
      error
    );

    // Retry logic for network errors or server errors (5xx)
    if (
      retryCount > 0 &&
      (errorMsg === "Network Error" ||
        axiosError.code === "ECONNABORTED" ||
        (axiosError.response && axiosError.response.status >= 500))
    ) {
      console.log(
        `apiManager.getTwilioVOIPToken: Retrying... (${retryCount} attempts left)`
      );
      // Wait for a short time before retrying
      await new Promise((resolve) => setTimeout(resolve, 1000));
      return getTwilioVOIPToken(patientId, retryCount - 1, deviceToken, platform, edge);
    }

    // Return both new format and backward compatible format
    return {
      error: {
        message: errorMsg,
        status: axiosError.response?.status,
        details: axiosError.response?.data,
      },
      // Backward compatibility
      message: errorMsg,
    };
  }
};

/**
 * Interface for alerts count API request
 */
export interface AlertsCountRequest {
  startDate: string;
  endDate: string;
  userId: string;
  orgId: string;
}

/**
 * Interface for alerts count API response
 */
export interface AlertsCountResponse {
  Warning: string;
  messageCount: string;
  Alarm: string;
  Critical: string;
  Info: string;
}

/**
 * Fetches alerts count by date range (device alerts data)
 * @param params Request parameters with date range and user info
 * @returns Promise with the alerts count response or error details
 */
export const getAlertsCountByDate = async (
  params: AlertsCountRequest
): Promise<ApiResponse<AlertsCountResponse>> => {
  try {
    const token = await getAuthToken();
    if (!token) {
      const errorMsg = "Authentication token not found";
      return {
        error: {
          message: errorMsg,
          status: 401,
        },
        // Backward compatibility
        message: errorMsg,
      };
    }

    const url = `${BASE_URL}/_ah/api/caregiverapi/v1/alertsCntByDate`;
    
    const response = await axios.post<AlertsCountResponse>(url, params, {
      headers: {
        Authorization: "Bearer " + token,
        "Content-Type": "application/json",
      },
      timeout: 15000, // 15 seconds timeout
    });

    // Return both new format and backward compatible format
    return {
      data: response.data,
      status: response.status,
      // Backward compatibility
      response: {
        status: response.status,
        data: response.data,
      },
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMsg = axiosError.message || "Unknown error";

    // Return both new format and backward compatible format
    return {
      error: {
        message: errorMsg,
        status: axiosError.response?.status,
        details: axiosError.response?.data,
      },
      // Backward compatibility
      message: errorMsg,
    };
  }
};

/**
 * Interface for clear notification API request
 */
export interface ClearNotificationRequest {
  patientId: string;
}

/**
 * Interface for clear notification API response
 */
export interface ClearNotificationResponse {
  success: boolean;
}

/**
 * Clears a specific notification by patientId
 * @param patientId The patient ID for the notification to clear
 * @returns Promise with the clear notification response or error details
 */
export const clearPatientNotification = async (
  patientId: string
): Promise<ApiResponse<ClearNotificationResponse>> => {
  try {
    const token = await getAuthToken();
    if (!token) {
      const errorMsg = "Authentication token not found";
      return {
        error: {
          message: errorMsg,
          status: 401,
        },
        // Backward compatibility
        message: errorMsg,
      };
    }

    const url = `${BASE_URL}/_ah/api/caregiverapi/v1/clearNotification`;
    const params: ClearNotificationRequest = {
      patientId: patientId.toString()
    };

    const response = await axios.post<ClearNotificationResponse>(url, params, {
      headers: {
        Authorization: "Bearer " + token,
        "Content-Type": "application/json",
      },
      timeout: 15000, // 15 seconds timeout
    });

    // Return both new format and backward compatible format
    return {
      data: response.data,
      status: response.status,
      // Backward compatibility
      response: {
        status: response.status,
        data: response.data,
      },
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMsg = axiosError.message || "Unknown error";

    // Return both new format and backward compatible format
    return {
      error: {
        message: errorMsg,
        status: axiosError.response?.status,
        details: axiosError.response?.data,
      },
      // Backward compatibility
      message: errorMsg,
    };
  }
};

/**
 * Interface for unread messages API request
 */
export interface UnreadMessagesRequest {
  userId: string;
  orgId: string;
}

/**
 * Interface for unread messages API response
 */
export interface UnreadMessagesResponse {
  data: Array<{
    patientName: string;
    patientId: string;
  }>;
  success: boolean;
}

/**
 * Fetches unread messages for the current user
 * @param params Request parameters with userId and orgId
 * @returns Promise with the unread messages response or error details
 */
export const getUnreadMessages = async (
  params: UnreadMessagesRequest
): Promise<ApiResponse<UnreadMessagesResponse>> => {
  try {
    const token = await getAuthToken();
    if (!token) {
      const errorMsg = "Authentication token not found";
      return {
        error: {
          message: errorMsg,
          status: 401,
        },
        // Backward compatibility
        message: errorMsg,
      };
    }

    const url = `${BASE_URL}/_ah/api/caregiverapi/v1/unReadMessages`;
    
    const response = await axios.post<UnreadMessagesResponse>(url, params, {
      headers: {
        Authorization: "Bearer " + token,
        "Content-Type": "application/json",
      },
      timeout: 15000, // 15 seconds timeout
    });

    // Return both new format and backward compatible format
    return {
      data: response.data,
      status: response.status,
      // Backward compatibility
      response: {
        status: response.status,
        data: response.data,
      },
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    const errorMsg = axiosError.message || "Unknown error";

    // Return both new format and backward compatible format
    return {
      error: {
        message: errorMsg,
        status: axiosError.response?.status,
        details: axiosError.response?.data,
      },
      // Backward compatibility
      message: errorMsg,
    };
  }
};
