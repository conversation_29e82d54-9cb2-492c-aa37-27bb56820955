import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  View,
  FlatList,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import moment from 'moment';

import Text from '@/components/Text';
import { AdaptiveLayout, TabletAwareContainer } from '@/components/Layout';
import { useOrientation } from '@/hooks/useOrientation';
import { useTabletTheme } from '@/hooks/useTabletTheme';
import {
  isTablet,
  isLargeTablet,
  spacing,
  typography,
  iconSizes,
} from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';

export interface ChatMessage {
  id: string;
  text: string;
  timestamp: Date;
  sender: {
    id: string;
    name: string;
    avatar?: string;
    isCurrentUser: boolean;
  };
  type?: 'text' | 'image' | 'file' | 'system';
  status?: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  attachments?: {
    id: string;
    type: 'image' | 'file';
    url: string;
    name: string;
    size?: number;
  }[];
}

export interface ChatParticipant {
  id: string;
  name: string;
  avatar?: string;
  isOnline: boolean;
  lastSeen?: Date;
  role?: string;
}

interface TabletChatInterfaceProps {
  // Chat data
  messages: ChatMessage[];
  participants: ChatParticipant[];
  currentUserId: string;
  
  // Chat info
  chatTitle?: string;
  chatSubtitle?: string;
  isGroup?: boolean;
  
  // Actions
  onSendMessage?: (text: string, attachments?: any[]) => void;
  onLoadMoreMessages?: () => void;
  onTypingStart?: () => void;
  onTypingStop?: () => void;
  onAttachFile?: () => void;
  onAttachImage?: () => void;
  onMessagePress?: (message: ChatMessage) => void;
  onParticipantPress?: (participant: ChatParticipant) => void;
  
  // State
  isLoading?: boolean;
  hasMoreMessages?: boolean;
  typingUsers?: string[];
  
  // Layout
  showParticipants?: boolean;
  showChatInfo?: boolean;
  
  // Styling
  style?: ViewStyle;
}

/**
 * Chat Message Component
 */
const ChatMessageItem: React.FC<{
  message: ChatMessage;
  isCurrentUser: boolean;
  showAvatar: boolean;
  onPress?: (message: ChatMessage) => void;
}> = ({ message, isCurrentUser, showAvatar, onPress }) => {
  const tabletTheme = useTabletTheme();

  const handlePress = useCallback(() => {
    onPress?.(message);
  }, [message, onPress]);

  const getStatusIcon = useCallback(() => {
    if (!isCurrentUser) return null;
    
    switch (message.status) {
      case 'sending':
        return <Ionicons name="time" size={iconSizes.xs} color={CoreColors.GrayBlue} />;
      case 'sent':
        return <Ionicons name="checkmark" size={iconSizes.xs} color={CoreColors.GrayBlue} />;
      case 'delivered':
        return <Ionicons name="checkmark-done" size={iconSizes.xs} color={CoreColors.GrayBlue} />;
      case 'read':
        return <Ionicons name="checkmark-done" size={iconSizes.xs} color={CoreColors.TurquoiseBlue} />;
      case 'failed':
        return <Ionicons name="alert-circle" size={iconSizes.xs} color={CoreColors.Red} />;
      default:
        return null;
    }
  }, [isCurrentUser, message.status]);

  return (
    <TouchableOpacity
      style={[
        styles.messageContainer,
        isCurrentUser ? styles.messageContainerSent : styles.messageContainerReceived,
      ]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      {/* Avatar (for received messages) */}
      {!isCurrentUser && showAvatar && (
        <View style={styles.avatarContainer}>
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>
              {message.sender.name.charAt(0).toUpperCase()}
            </Text>
          </View>
        </View>
      )}

      {/* Message Content */}
      <View style={[
        styles.messageBubble,
        isCurrentUser ? styles.messageBubbleSent : styles.messageBubbleReceived,
        {
          maxWidth: isTablet() ? '70%' : '80%',
          borderRadius: tabletTheme.borderRadius('medium'),
        }
      ]}>
        {/* Sender name (for group chats) */}
        {!isCurrentUser && showAvatar && (
          <Text style={[
            styles.senderName,
            { fontSize: tabletTheme.typography.get('caption').fontSize }
          ]}>
            {message.sender.name}
          </Text>
        )}

        {/* Message text */}
        <Text style={[
          styles.messageText,
          isCurrentUser ? styles.messageTextSent : styles.messageTextReceived,
          { fontSize: tabletTheme.typography.get('body').fontSize }
        ]}>
          {message.text}
        </Text>

        {/* Attachments */}
        {message.attachments && message.attachments.length > 0 && (
          <View style={styles.attachmentsContainer}>
            {message.attachments.map((attachment) => (
              <View key={attachment.id} style={styles.attachment}>
                <Ionicons
                  name={attachment.type === 'image' ? 'image' : 'document'}
                  size={iconSizes.sm}
                  color={isCurrentUser ? '#FFFFFF' : CoreColors.TurquoiseBlue}
                />
                <Text style={[
                  styles.attachmentName,
                  isCurrentUser ? styles.attachmentNameSent : styles.attachmentNameReceived,
                ]}>
                  {attachment.name}
                </Text>
              </View>
            ))}
          </View>
        )}

        {/* Message footer */}
        <View style={styles.messageFooter}>
          <Text style={[
            styles.messageTime,
            isCurrentUser ? styles.messageTimeSent : styles.messageTimeReceived,
            { fontSize: tabletTheme.typography.get('caption').fontSize }
          ]}>
            {moment(message.timestamp).format('HH:mm')}
          </Text>
          {getStatusIcon()}
        </View>
      </View>
    </TouchableOpacity>
  );
};

/**
 * Chat Input Component
 */
const ChatInput: React.FC<{
  onSendMessage?: (text: string) => void;
  onAttachFile?: () => void;
  onAttachImage?: () => void;
  onTypingStart?: () => void;
  onTypingStop?: () => void;
  disabled?: boolean;
}> = ({
  onSendMessage,
  onAttachFile,
  onAttachImage,
  onTypingStart,
  onTypingStop,
  disabled = false,
}) => {
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const tabletTheme = useTabletTheme();

  const handleTextChange = useCallback((text: string) => {
    setInputText(text);
    
    if (!isTyping && text.length > 0) {
      setIsTyping(true);
      onTypingStart?.();
    }
    
    // Reset typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      onTypingStop?.();
    }, 1000);
  }, [isTyping, onTypingStart, onTypingStop]);

  const handleSend = useCallback(() => {
    if (inputText.trim().length > 0 && !disabled) {
      onSendMessage?.(inputText.trim());
      setInputText('');
      setIsTyping(false);
      onTypingStop?.();
      
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    }
  }, [inputText, disabled, onSendMessage, onTypingStop]);

  const showAttachmentOptions = useCallback(() => {
    Alert.alert(
      'Add Attachment',
      'Choose attachment type',
      [
        { text: 'Camera', onPress: onAttachImage },
        { text: 'File', onPress: onAttachFile },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  }, [onAttachFile, onAttachImage]);

  return (
    <View style={[
      styles.inputContainer,
      {
        paddingHorizontal: tabletTheme.spacing.get('lg'),
        paddingVertical: tabletTheme.spacing.get('md'),
        ...tabletTheme.shadow('light'),
      }
    ]}>
      <View style={[
        styles.inputWrapper,
        {
          borderRadius: tabletTheme.borderRadius('large'),
          minHeight: isTablet() ? 52 : 44,
        }
      ]}>
        {/* Attachment button */}
        <TouchableOpacity
          style={styles.attachButton}
          onPress={showAttachmentOptions}
          disabled={disabled}
        >
          <Ionicons
            name="add"
            size={isTablet() ? iconSizes.lg : iconSizes.md}
            color={disabled ? CoreColors.GrayBlue : CoreColors.TurquoiseBlue}
          />
        </TouchableOpacity>

        {/* Text input */}
        <TextInput
          style={[
            styles.textInput,
            { fontSize: tabletTheme.typography.get('body').fontSize }
          ]}
          value={inputText}
          onChangeText={handleTextChange}
          placeholder="Type a message..."
          placeholderTextColor={CoreColors.GrayBlue}
          multiline={true}
          maxLength={1000}
          editable={!disabled}
          textAlignVertical="center"
        />

        {/* Send button */}
        <TouchableOpacity
          style={[
            styles.sendButton,
            {
              backgroundColor: inputText.trim().length > 0 && !disabled 
                ? CoreColors.TurquoiseBlue 
                : CoreColors.GrayBlue,
            }
          ]}
          onPress={handleSend}
          disabled={inputText.trim().length === 0 || disabled}
        >
          <Ionicons
            name="send"
            size={isTablet() ? iconSizes.md : iconSizes.sm}
            color="#FFFFFF"
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

/**
 * Participants Panel Component
 */
const ParticipantsPanel: React.FC<{
  participants: ChatParticipant[];
  onParticipantPress?: (participant: ChatParticipant) => void;
  onClose?: () => void;
}> = ({ participants, onParticipantPress, onClose }) => {
  const tabletTheme = useTabletTheme();

  const renderParticipant = useCallback(({ item }: { item: ChatParticipant }) => (
    <TouchableOpacity
      style={styles.participantItem}
      onPress={() => onParticipantPress?.(item)}
    >
      <View style={styles.participantAvatar}>
        <Text style={styles.participantAvatarText}>
          {item.name.charAt(0).toUpperCase()}
        </Text>
        {item.isOnline && <View style={styles.onlineIndicator} />}
      </View>
      
      <View style={styles.participantInfo}>
        <Text style={[
          styles.participantName,
          { fontSize: tabletTheme.typography.get('body').fontSize }
        ]}>
          {item.name}
        </Text>
        {item.role && (
          <Text style={[
            styles.participantRole,
            { fontSize: tabletTheme.typography.get('caption').fontSize }
          ]}>
            {item.role}
          </Text>
        )}
        {!item.isOnline && item.lastSeen && (
          <Text style={[
            styles.participantLastSeen,
            { fontSize: tabletTheme.typography.get('caption').fontSize }
          ]}>
            Last seen {moment(item.lastSeen).fromNow()}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  ), [onParticipantPress, tabletTheme]);

  return (
    <View style={styles.participantsPanel}>
      <View style={styles.participantsPanelHeader}>
        <Text style={[
          styles.participantsPanelTitle,
          { fontSize: tabletTheme.typography.get('h6').fontSize }
        ]}>
          Participants ({participants.length})
        </Text>
        {onClose && (
          <TouchableOpacity onPress={onClose} style={styles.participantsPanelClose}>
            <Ionicons name="close" size={iconSizes.md} color={CoreColors.DarkJungleGreen} />
          </TouchableOpacity>
        )}
      </View>
      
      <FlatList
        data={participants}
        renderItem={renderParticipant}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

/**
 * Main Tablet Chat Interface
 */
const TabletChatInterface: React.FC<TabletChatInterfaceProps> = ({
  messages,
  participants,
  currentUserId,
  
  chatTitle,
  chatSubtitle,
  isGroup = false,
  
  onSendMessage,
  onLoadMoreMessages,
  onTypingStart,
  onTypingStop,
  onAttachFile,
  onAttachImage,
  onMessagePress,
  onParticipantPress,
  
  isLoading = false,
  hasMoreMessages = false,
  typingUsers = [],
  
  showParticipants = false,
  showChatInfo = true,
  
  style,
}) => {
  const { orientation } = useOrientation();
  const tabletTheme = useTabletTheme();
  const flatListRef = useRef<FlatList>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages.length]);

  const renderMessage = useCallback(({ item, index }: { item: ChatMessage; index: number }) => {
    const isCurrentUser = item.sender.id === currentUserId;
    const previousMessage = index > 0 ? messages[index - 1] : null;
    const showAvatar = !isCurrentUser && (
      !previousMessage || 
      previousMessage.sender.id !== item.sender.id ||
      moment(item.timestamp).diff(moment(previousMessage.timestamp), 'minutes') > 5
    );

    return (
      <ChatMessageItem
        message={item}
        isCurrentUser={isCurrentUser}
        showAvatar={showAvatar}
        onPress={onMessagePress}
      />
    );
  }, [messages, currentUserId, onMessagePress]);

  // Chat header
  const chatHeader = (
    <View style={[
      styles.chatHeader,
      { paddingHorizontal: tabletTheme.spacing.get('lg') }
    ]}>
      <View style={styles.chatHeaderContent}>
        <Text style={[
          styles.chatTitle,
          { fontSize: tabletTheme.typography.get('h6').fontSize }
        ]}>
          {chatTitle || 'Chat'}
        </Text>
        {chatSubtitle && (
          <Text style={[
            styles.chatSubtitle,
            { fontSize: tabletTheme.typography.get('caption').fontSize }
          ]}>
            {chatSubtitle}
          </Text>
        )}
      </View>
      
      {isGroup && (
        <TouchableOpacity style={styles.participantsButton}>
          <Ionicons name="people" size={iconSizes.md} color={CoreColors.TurquoiseBlue} />
          <Text style={styles.participantsCount}>{participants.length}</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  // Main chat content
  const chatContent = (
    <View style={styles.chatContent}>
      {showChatInfo && chatHeader}
      
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesListContent}
        showsVerticalScrollIndicator={false}
        onEndReached={hasMoreMessages ? onLoadMoreMessages : undefined}
        onEndReachedThreshold={0.1}
      />
      
      {typingUsers.length > 0 && (
        <View style={styles.typingIndicator}>
          <Text style={styles.typingText}>
            {typingUsers.join(', ')} {typingUsers.length === 1 ? 'is' : 'are'} typing...
          </Text>
        </View>
      )}
      
      <ChatInput
        onSendMessage={onSendMessage}
        onAttachFile={onAttachFile}
        onAttachImage={onAttachImage}
        onTypingStart={onTypingStart}
        onTypingStop={onTypingStop}
        disabled={isLoading}
      />
    </View>
  );

  // Layout for different orientations
  const phoneLayout = chatContent;
  const tabletPortraitLayout = chatContent;
  const tabletLandscapeLayout = (
    <View style={styles.tabletLandscapeContainer}>
      <View style={styles.chatMainArea}>
        {chatContent}
      </View>
      
      {showParticipants && (
        <ParticipantsPanel
          participants={participants}
          onParticipantPress={onParticipantPress}
        />
      )}
    </View>
  );

  return (
    <TabletAwareContainer style={[styles.container, style]}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <AdaptiveLayout
          phoneLayout={phoneLayout}
          tabletPortraitLayout={tabletPortraitLayout}
          tabletLandscapeLayout={tabletLandscapeLayout}
        />
      </KeyboardAvoidingView>
    </TabletAwareContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFBFC',
  } as ViewStyle,

  keyboardAvoidingView: {
    flex: 1,
  } as ViewStyle,

  tabletLandscapeContainer: {
    flex: 1,
    flexDirection: 'row',
  } as ViewStyle,

  chatMainArea: {
    flex: 1,
  } as ViewStyle,

  chatContent: {
    flex: 1,
  } as ViewStyle,

  // Chat Header
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.md,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  } as ViewStyle,

  chatHeaderContent: {
    flex: 1,
  } as ViewStyle,

  chatTitle: {
    fontWeight: '600',
    color: CoreColors.DarkJungleGreen,
  } as TextStyle,

  chatSubtitle: {
    color: CoreColors.GrayBlue,
    marginTop: spacing.xs,
  } as TextStyle,

  participantsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    backgroundColor: CoreColors.TurquoiseBlue + '15',
    borderRadius: 16,
  } as ViewStyle,

  participantsCount: {
    marginLeft: spacing.xs,
    fontSize: typography.caption.fontSize,
    color: CoreColors.TurquoiseBlue,
    fontWeight: '600',
  } as TextStyle,

  // Messages List
  messagesList: {
    flex: 1,
  } as ViewStyle,

  messagesListContent: {
    paddingVertical: spacing.md,
  } as ViewStyle,

  // Message Item
  messageContainer: {
    flexDirection: 'row',
    marginVertical: spacing.xs,
    paddingHorizontal: spacing.lg,
  } as ViewStyle,

  messageContainerSent: {
    justifyContent: 'flex-end',
  } as ViewStyle,

  messageContainerReceived: {
    justifyContent: 'flex-start',
  } as ViewStyle,

  avatarContainer: {
    marginRight: spacing.sm,
  } as ViewStyle,

  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: CoreColors.TurquoiseBlue,
    justifyContent: 'center',
    alignItems: 'center',
  } as ViewStyle,

  avatarText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  } as TextStyle,

  messageBubble: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  } as ViewStyle,

  messageBubbleSent: {
    backgroundColor: CoreColors.TurquoiseBlue,
  } as ViewStyle,

  messageBubbleReceived: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  } as ViewStyle,

  senderName: {
    color: CoreColors.TurquoiseBlue,
    fontWeight: '600',
    marginBottom: spacing.xs,
  } as TextStyle,

  messageText: {
    lineHeight: typography.body.lineHeight,
  } as TextStyle,

  messageTextSent: {
    color: '#FFFFFF',
  } as TextStyle,

  messageTextReceived: {
    color: CoreColors.DarkJungleGreen,
  } as TextStyle,

  attachmentsContainer: {
    marginTop: spacing.sm,
  } as ViewStyle,

  attachment: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  } as ViewStyle,

  attachmentName: {
    marginLeft: spacing.xs,
    fontSize: typography.caption.fontSize,
  } as TextStyle,

  attachmentNameSent: {
    color: 'rgba(255, 255, 255, 0.9)',
  } as TextStyle,

  attachmentNameReceived: {
    color: CoreColors.TurquoiseBlue,
  } as TextStyle,

  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: spacing.xs,
  } as ViewStyle,

  messageTime: {
    marginRight: spacing.xs,
  } as TextStyle,

  messageTimeSent: {
    color: 'rgba(255, 255, 255, 0.7)',
  } as TextStyle,

  messageTimeReceived: {
    color: CoreColors.GrayBlue,
  } as TextStyle,

  // Typing Indicator
  typingIndicator: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
  } as ViewStyle,

  typingText: {
    color: CoreColors.GrayBlue,
    fontSize: typography.caption.fontSize,
    fontStyle: 'italic',
  } as TextStyle,

  // Chat Input
  inputContainer: {
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  } as ViewStyle,

  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  } as ViewStyle,

  attachButton: {
    padding: spacing.sm,
    marginRight: spacing.xs,
  } as ViewStyle,

  textInput: {
    flex: 1,
    maxHeight: 100,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.xs,
    color: CoreColors.DarkJungleGreen,
  } as TextStyle,

  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: spacing.xs,
  } as ViewStyle,

  // Participants Panel
  participantsPanel: {
    width: isLargeTablet() ? 320 : 280,
    backgroundColor: '#FFFFFF',
    borderLeftWidth: 1,
    borderLeftColor: '#E5E7EB',
  } as ViewStyle,

  participantsPanelHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  } as ViewStyle,

  participantsPanelTitle: {
    fontWeight: '600',
    color: CoreColors.DarkJungleGreen,
  } as TextStyle,

  participantsPanelClose: {
    padding: spacing.xs,
  } as ViewStyle,

  participantItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F5F9',
  } as ViewStyle,

  participantAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: CoreColors.TurquoiseBlue,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
    position: 'relative',
  } as ViewStyle,

  participantAvatarText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  } as TextStyle,

  onlineIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: CoreColors.Green,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  } as ViewStyle,

  participantInfo: {
    flex: 1,
  } as ViewStyle,

  participantName: {
    fontWeight: '600',
    color: CoreColors.DarkJungleGreen,
  } as TextStyle,

  participantRole: {
    color: CoreColors.TurquoiseBlue,
    marginTop: spacing.xs,
  } as TextStyle,

  participantLastSeen: {
    color: CoreColors.GrayBlue,
    marginTop: spacing.xs,
  } as TextStyle,
});

export default TabletChatInterface;