import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useDispatch } from 'react-redux';
import moment from 'moment';

import Text from '@/components/Text';
import { ResponsiveGrid } from '@/components/Layout';
import { PatientItemProps } from '@/models';
import { setCurentPatientId } from '@/services/actions/currentPatientId';
import { setCurrentPatientName } from '@/services/actions/currentPatientName';
import { formatPatientDOB } from '@/utils/dateFormatter';
import { useOrientation } from '@/hooks/useOrientation';
import { useTabletTheme } from '@/hooks/useTabletTheme';
import {
  isTablet,
  isLargeTablet,
  getOrientation,
  spacing,
  typography,
  iconSizes,
} from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';

interface PatientListTabletProps {
  data: PatientItemProps[];
  onRefresh?: () => void;
  refreshing?: boolean;
  onLoadMore?: () => void;
  isLoadingMore?: boolean;
  onPatientPress?: (patient: PatientItemProps) => void;
  style?: ViewStyle;
  contentContainerStyle?: ViewStyle;
}

interface PatientCardTabletProps extends PatientItemProps {
  index: number;
  onPress: (patient: PatientItemProps) => void;
}

/**
 * Enhanced Patient Card optimized for tablet layouts
 */
const PatientCardTablet: React.FC<PatientCardTabletProps> = ({
  index,
  onPress,
  ...patient
}) => {
  const router = useRouter();
  const dispatch = useDispatch();
  const { orientation } = useOrientation();
  const tabletTheme = useTabletTheme();

  // Enhanced action handlers for tablets
  const handleVideoCall = useCallback(() => {
    try {
      const patientIdStr = String(patient.patientId);
      router.push(`/patient/video-call/${patientIdStr}`);
    } catch (error) {
      console.error('Error navigating to video call:', error);
    }
  }, [patient.patientId, router]);

  const handlePhoneCall = useCallback(() => {
    try {
      const patientIdStr = String(patient.patientId);
      router.push(`/patient/voip-call/${patientIdStr}`);
    } catch (error) {
      console.error('Error navigating to VOIP call:', error);
    }
  }, [patient.patientId, router]);

  const handleChat = useCallback(() => {
    try {
      router.push(`/patient/chat/${patient.patientId}`);
    } catch (error) {
      console.error('Error navigating to chat:', error);
    }
  }, [patient.patientId, router]);

  const handleSummary = useCallback(() => {
    try {
      router.push({
        pathname: '/patient-summary',
        params: {
          patientId: patient.patientId.toString(),
          patientName: patient.patientName,
        },
      });
    } catch (error) {
      console.error('Error navigating to summary:', error);
    }
  }, [patient.patientId, patient.patientName, router]);

  const onCardPress = useCallback(() => {
    dispatch(setCurentPatientId(patient.patientId));
    dispatch(setCurrentPatientName(patient.patientName));
    onPress(patient);
  }, [dispatch, patient, onPress]);

  // Enhanced formatting helpers
  const formatDate = useCallback((dateString: string | undefined) => {
    if (!dateString) return 'Not provided';
    return moment(dateString, 'MM-DD-YYYY').format('MMM D, YYYY');
  }, []);

  const formatPhone = useCallback((phone: string | undefined) => {
    if (!phone) return 'Not provided';
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return phone.length > 15 ? phone.slice(0, 15) + '...' : phone;
  }, []);

  const formatMRN = useCallback(
    (mrn: string | number | undefined) => {
      if (!mrn) return patient.patientId?.toString() || 'N/A';
      return mrn.toString();
    },
    [patient.patientId]
  );

  // Tablet-optimized action buttons
  const actionButtons = useMemo(() => [
    {
      id: 'video',
      icon: 'videocam' as const,
      color: CoreColors.TealBlue,
      backgroundColor: CoreColors.TealBlue + '15',
      action: handleVideoCall,
      label: 'Video Call',
    },
    {
      id: 'call',
      icon: 'call' as const,
      color: CoreColors.TurquoiseBlue,
      backgroundColor: CoreColors.TurquoiseBlue + '15',
      action: handlePhoneCall,
      label: 'Voice Call',
    },
    {
      id: 'chat',
      icon: 'chatbubbles' as const,
      color: CoreColors.Orange,
      backgroundColor: CoreColors.Orange + '15',
      action: handleChat,
      label: 'Messages',
    },
    {
      id: 'summary',
      icon: 'document-text' as const,
      color: CoreColors.Purple,
      backgroundColor: CoreColors.Purple + '15',
      action: handleSummary,
      label: 'Summary',
    },
  ], [handleVideoCall, handlePhoneCall, handleChat, handleSummary]);

  // Dynamic styles based on tablet configuration
  const cardStyles = useMemo(() => {
    const baseCardStyle = {
      ...styles.patientCard,
      padding: tabletTheme.spacing.get('lg'),
      borderRadius: tabletTheme.borderRadius('medium'),
      minHeight: isLargeTablet() ? 180 : isTablet() ? 160 : 140,
      ...tabletTheme.shadow('light'),
    };

    return baseCardStyle;
  }, [tabletTheme]);

  return (
    <TouchableOpacity
      style={cardStyles}
      onPress={onCardPress}
      activeOpacity={0.95}
      accessible={true}
      accessibilityLabel={`Patient ${patient.patientName}, MRN ${formatMRN(patient.mrn)}`}
      accessibilityRole="button"
    >
      {/* Header Section */}
      <View style={styles.cardHeader}>
        <View style={styles.patientInfo}>
          <Text style={[
            styles.patientName,
            { fontSize: tabletTheme.typography.get('h3').fontSize }
          ]} numberOfLines={1}>
            {patient.patientName}
          </Text>
          <View style={[styles.mrnBadge, { backgroundColor: CoreColors.TurquoiseBlue }]}>
            <Text style={styles.mrnText}>MRN: {formatMRN(patient.mrn)}</Text>
          </View>
        </View>
      </View>

      {/* Patient Details Section */}
      <View style={styles.detailsSection}>
        <View style={styles.detailRow}>
          <View style={styles.detailItem}>
            <MaterialCommunityIcons
              name="calendar-outline"
              size={isTablet() ? iconSizes.md : iconSizes.sm}
              color={CoreColors.GrayBlue}
              style={styles.detailIcon}
            />
            <Text style={[
              styles.detailText,
              { fontSize: tabletTheme.typography.get('bodySmall').fontSize }
            ]}>
              {formatDate(patient.dob)}
            </Text>
          </View>
        </View>

        <View style={styles.detailRow}>
          <View style={styles.detailItem}>
            <Ionicons
              name="call-outline"
              size={isTablet() ? iconSizes.md : iconSizes.sm}
              color={CoreColors.GrayBlue}
              style={styles.detailIcon}
            />
            <Text style={[
              styles.detailText,
              { fontSize: tabletTheme.typography.get('bodySmall').fontSize }
            ]} numberOfLines={1}>
              {formatPhone(patient.phone)}
            </Text>
          </View>
        </View>

        {patient.address && (
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <Ionicons
                name="location-outline"
                size={isTablet() ? iconSizes.md : iconSizes.sm}
                color={CoreColors.GrayBlue}
                style={styles.detailIcon}
              />
              <Text style={[
                styles.detailText,
                { fontSize: tabletTheme.typography.get('bodySmall').fontSize }
              ]} numberOfLines={2}>
                {patient.address}
              </Text>
            </View>
          </View>
        )}
      </View>

      {/* Actions Section */}
      <View style={styles.actionsSection}>
        {actionButtons.map((button) => (
          <TouchableOpacity
            key={button.id}
            style={[
              styles.actionButton,
              {
                backgroundColor: button.backgroundColor,
                width: tabletTheme.touchTarget('comfortable'),
                height: tabletTheme.touchTarget('comfortable'),
              },
            ]}
            onPress={(e) => {
              e.stopPropagation();
              button.action();
            }}
            activeOpacity={0.7}
            accessible={true}
            accessibilityLabel={button.label}
            accessibilityRole="button"
          >
            <Ionicons
              name={button.icon}
              size={isTablet() ? iconSizes.md : iconSizes.sm}
              color={button.color}
            />
          </TouchableOpacity>
        ))}
      </View>
    </TouchableOpacity>
  );
};

/**
 * Tablet-optimized Patient List component with responsive grid layout
 */
const PatientListTablet: React.FC<PatientListTabletProps> = ({
  data,
  onRefresh,
  refreshing = false,
  onLoadMore,
  isLoadingMore = false,
  onPatientPress,
  style,
  contentContainerStyle,
}) => {
  const { orientation } = useOrientation();
  const tabletTheme = useTabletTheme();
  const router = useRouter();
  const dispatch = useDispatch();

  // Default patient press handler
  const handlePatientPress = useCallback((patient: PatientItemProps) => {
    if (onPatientPress) {
      onPatientPress(patient);
    } else {
      // Default navigation to patient details
      try {
        const params = {
          id: patient.patientId,
          patientName: patient.patientName,
          phone: patient.phone || '',
          address: patient.address || '',
          image: patient.image || '',
          dob: patient.dob || '',
          mrn: patient.mrn || '',
        };

        router.push({
          pathname: `/patient/${patient.patientId}` as any,
          params: params,
        });
      } catch (error) {
        console.error('Error navigating to patient details:', error);
      }
    }
  }, [onPatientPress, router]);

  // Render patient item for grid
  const renderPatientItem = useCallback(({ item, index }: { item: PatientItemProps; index: number }) => (
    <PatientCardTablet
      {...item}
      index={index}
      onPress={handlePatientPress}
    />
  ), [handlePatientPress]);

  // Loading footer component
  const renderFooter = useCallback(() => {
    if (!isLoadingMore) return null;
    
    return (
      <View style={styles.loadingFooter}>
        <ActivityIndicator size="small" color={CoreColors.TurquoiseBlue} />
        <Text style={[
          styles.loadingText,
          { fontSize: tabletTheme.typography.get('caption').fontSize }
        ]}>
          Loading more patients...
        </Text>
      </View>
    );
  }, [isLoadingMore, tabletTheme]);

  // Grid configuration based on device and orientation
  const gridConfig = useMemo(() => {
    if (isLargeTablet()) {
      return {
        phoneColumns: 1,
        tabletPortraitColumns: 2,
        tabletLandscapeColumns: 3,
        largeTabletColumns: orientation === 'landscape' ? 4 : 3,
      };
    } else if (isTablet()) {
      return {
        phoneColumns: 1,
        tabletPortraitColumns: 2,
        tabletLandscapeColumns: 3,
        largeTabletColumns: 3,
      };
    } else {
      return {
        phoneColumns: 1,
        tabletPortraitColumns: 1,
        tabletLandscapeColumns: 1,
        largeTabletColumns: 1,
      };
    }
  }, [orientation]);

  return (
    <ResponsiveGrid
      data={data}
      renderItem={renderPatientItem}
      {...gridConfig}
      spacing={tabletTheme.spacing.get('md')}
      style={[styles.container, style]}
      contentContainerStyle={[styles.contentContainer, contentContainerStyle]}
      refreshControl={
        onRefresh ? (
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[CoreColors.TurquoiseBlue]}
            tintColor={CoreColors.TurquoiseBlue}
          />
        ) : undefined
      }
      onEndReached={onLoadMore}
      onEndReachedThreshold={0.5}
      ListFooterComponent={renderFooter}
      showsVerticalScrollIndicator={false}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFBFC',
  } as ViewStyle,

  contentContainer: {
    paddingVertical: spacing.lg,
  } as ViewStyle,

  patientCard: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    marginBottom: spacing.sm,
  } as ViewStyle,

  cardHeader: {
    marginBottom: spacing.md,
  } as ViewStyle,

  patientInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  } as ViewStyle,

  patientName: {
    fontWeight: '700',
    color: CoreColors.DarkJungleGreen,
    flex: 1,
    marginRight: spacing.sm,
  } as TextStyle,

  mrnBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 6,
  } as ViewStyle,

  mrnText: {
    fontSize: typography.caption.fontSize,
    fontWeight: '600',
    color: '#FFFFFF',
  } as TextStyle,

  detailsSection: {
    flex: 1,
    marginBottom: spacing.md,
  } as ViewStyle,

  detailRow: {
    marginBottom: spacing.sm,
  } as ViewStyle,

  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  } as ViewStyle,

  detailIcon: {
    marginRight: spacing.sm,
  },

  detailText: {
    color: CoreColors.GrayBlue,
    flex: 1,
    lineHeight: typography.bodySmall.lineHeight,
  } as TextStyle,

  actionsSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: '#F1F5F9',
  } as ViewStyle,

  actionButton: {
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  } as ViewStyle,

  loadingFooter: {
    padding: spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
  } as ViewStyle,

  loadingText: {
    color: CoreColors.GrayBlue,
    marginTop: spacing.sm,
  } as TextStyle,
});

export default PatientListTablet;