import React from 'react';
import {
  View,
  ViewStyle,
  Platform,
  StyleSheet,
  StatusBar,
  Dimensions,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { getStatusBarHeight } from 'react-native-iphone-x-helper';
import { usePathname } from 'expo-router';

import { getHeaderHeight } from '@/utils/layoutHelper';
import {
  isTablet,
  isLargeTablet,
  getOrientation,
  tabletLayout,
  spacing,
  grid,
} from '@/utils/responsive';

interface TabletAwareContainerProps {
  children?: React.ReactNode;
  style?: ViewStyle;
  disableHeaderPadding?: boolean;
  maxWidth?: number;
  centerContent?: boolean;
  tabletPadding?: number;
  orientation?: 'portrait' | 'landscape';
}

/**
 * Enhanced Container component with tablet-specific optimizations
 * Provides responsive padding, max-width constraints, and orientation-aware layouts
 */
const TabletAwareContainer = (props: TabletAwareContainerProps) => {
  const pathname = usePathname();
  const insets = useSafeAreaInsets();
  const currentOrientation = props.orientation || getOrientation();
  
  // Determine if we should apply header padding
  const shouldApplyHeaderPadding = !props.disableHeaderPadding &&
    !pathname.startsWith('/auth/') &&
    pathname !== '/splash' &&
    pathname !== '/onboarding';

  // Calculate tablet-aware padding
  const getTabletPadding = () => {
    if (props.tabletPadding !== undefined) {
      return props.tabletPadding;
    }
    
    if (isLargeTablet()) {
      return currentOrientation === 'landscape' ? spacing.xxl : spacing.xl;
    } else if (isTablet()) {
      return currentOrientation === 'landscape' ? spacing.xl : spacing.lg;
    }
    
    return grid.container.paddingHorizontal;
  };

  // Calculate content padding top with tablet considerations
  const getContentPaddingTop = () => {
    if (!shouldApplyHeaderPadding) return 0;

    const basePadding = Platform.OS === 'android' 
      ? (StatusBar.currentHeight || insets.top) + 72 + 16
      : getHeaderHeight() + 40;

    // Add extra padding for tablets to provide better visual hierarchy
    if (isTablet()) {
      return basePadding + (isLargeTablet() ? spacing.lg : spacing.md);
    }
    
    return basePadding;
  };

  // Calculate max width for tablet layouts
  const getMaxWidth = () => {
    if (props.maxWidth !== undefined) {
      return props.maxWidth;
    }
    
    return tabletLayout.maxContentWidth;
  };

  const paddingTop = getContentPaddingTop();
  const horizontalPadding = getTabletPadding();
  const maxWidth = getMaxWidth();

  // Create tablet-aware container styles
  const containerStyles: ViewStyle = {
    flex: 1,
    backgroundColor: '#FFFFFF',
    paddingTop: paddingTop,
    paddingHorizontal: horizontalPadding,
    paddingBottom: isTablet() ? spacing.lg : 0,
    position: 'relative',
    zIndex: 1,
    marginTop: 0,
    overflow: 'visible',
  };

  // Add max-width and centering for tablets if needed
  if (isTablet() && (props.centerContent || maxWidth < Dimensions.get('window').width)) {
    containerStyles.maxWidth = maxWidth;
    containerStyles.alignSelf = 'center';
    containerStyles.width = '100%';
  }

  // Content wrapper for additional tablet optimizations
  const contentWrapperStyles: ViewStyle = {
    flex: 1,
    width: '100%',
  };

  // Add orientation-specific adjustments
  if (isTablet() && currentOrientation === 'landscape') {
    contentWrapperStyles.paddingVertical = spacing.sm;
  }

  return (
    <View style={[containerStyles, props.style]}>
      <View style={contentWrapperStyles}>
        {props.children}
      </View>
    </View>
  );
};

export default TabletAwareContainer;