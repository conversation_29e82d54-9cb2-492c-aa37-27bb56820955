import React, {memo, useCallback} from 'react';
import {StyleSheet, TouchableOpacity, View, ViewProps} from 'react-native';
import Layout from '@/components/Layout/Layout';
import Text from '@/components/Text';
import {Colors} from '@/constants';
import {useTheme} from '@/constants/Theme';
import Theme from '@/constants/Theme';

interface Props {
  tabs?: string[];
  style?: ViewProps;
  activeTintColor?: string;
  inactiveTintColor?: string;
  activeBackgroundColor?: string;
  inactiveBackgroundColor?: string;
  tabStyle?: ViewProps;
  onChangeTab?: (index: number) => void;
}

export default memo(
  ({
    tabs,
    onChangeTab,
    tabStyle,
    activeTintColor = Colors.White,
    inactiveTintColor = Colors.GrayBlue,
    activeBackgroundColor = Colors.TealBlue,
  }: Props) => {
    // Use internal state for preview, but defer to parent component if onChangeTab is provided
    const [internalTabActive, setInternalTabActive] = React.useState<number>(3);
    const {theme} = useTheme();

    // If the parent component provides an active tab index, use it
    React.useEffect(() => {
      if (onChangeTab) {
        // This is just to initialize the indicator
        setInternalTabActive(3);
      }
    }, []);

    const _onChangeTab = useCallback(
      (number: number) => {
        setInternalTabActive(number);
        onChangeTab && onChangeTab(number);
      },
      [onChangeTab],
    );

    return (
      <Layout style={styles.container}>
        {tabs?.map((item, index) => {
          // Use the internal state for preview, but defer to parent component if onChangeTab is provided
          const isActive = index === internalTabActive;

          // Get the color for this tab
          const tabColor =
            index === 0 ? Colors.RedNeonFuchsia :
            index === 1 ? Colors.PinkOrange :
            index === 2 ? Colors.Malachite :
            activeBackgroundColor;

          return (
            <TouchableOpacity
              onPress={() => _onChangeTab(index)}
              key={index}
              style={[styles.tabStyle, tabStyle]}
              activeOpacity={0.7}
            >
              <View style={styles.tabContent}>
                <Text
                  color={isActive ? tabColor : Colors.DarkJungleGreen}
                  size={14}
                  lineHeight={16}
                  bold
                  style={styles.tabText}
                >
                  {item}
                </Text>
                {/* Indicator line at the bottom of active tab */}
                <View
                  style={[styles.indicator, {
                    backgroundColor: tabColor,
                    opacity: isActive ? 1 : 0
                  }]}
                />
              </View>
            </TouchableOpacity>
          );
        })}
      </Layout>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    ...Theme.flexDirection,
    justifyContent: 'space-between',
    borderRadius: 0,
    backgroundColor: Colors.White,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  tabStyle: {
    height: 45,
    flex: 1,
    ...Theme.center,
  },
  tabContent: {
    flex: 1,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  tabText: {
    textAlign: 'center',
  },
  indicator: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 3,
    borderTopLeftRadius: 3,
    borderTopRightRadius: 3,
  },
});
