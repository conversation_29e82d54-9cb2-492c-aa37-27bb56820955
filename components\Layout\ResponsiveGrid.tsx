import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  FlatList,
  Dimensions,
  ViewStyle,
  ListRenderItem,
  FlatListProps,
} from 'react-native';

import {
  isTablet,
  isLargeTablet,
  getOrientation,
  getDeviceType,
  spacing,
  tabletLayout,
} from '@/utils/responsive';

interface ResponsiveGridProps<T> extends Omit<FlatListProps<T>, 'renderItem' | 'numColumns'> {
  data: T[];
  renderItem: ListRenderItem<T>;
  phoneColumns?: number;
  tabletPortraitColumns?: number;
  tabletLandscapeColumns?: number;
  largeTabletColumns?: number;
  spacing?: number;
  itemAspectRatio?: number;
  minItemWidth?: number;
  maxItemWidth?: number;
  style?: ViewStyle;
  contentContainerStyle?: ViewStyle;
  onOrientationChange?: (orientation: 'portrait' | 'landscape', columns: number) => void;
}

/**
 * ResponsiveGrid component that provides a flexible grid system
 * Automatically adjusts columns based on device type and orientation
 */
function ResponsiveGrid<T>({
  data,
  renderItem,
  phoneColumns = 1,
  tabletPortraitColumns = 2,
  tabletLandscapeColumns = 3,
  largeTabletColumns = 4,
  spacing: gridSpacing = spacing.md,
  itemAspectRatio,
  minItemWidth,
  maxItemWidth,
  style,
  contentContainerStyle,
  onOrientationChange,
  ...flatListProps
}: ResponsiveGridProps<T>) {
  const [screenData, setScreenData] = useState(() => {
    const { width, height } = Dimensions.get('window');
    return { width, height };
  });

  // Listen for dimension changes
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenData({ width: window.width, height: window.height });
    });

    return () => subscription?.remove?.();
  }, []);

  // Calculate optimal number of columns
  const calculateColumns = useMemo(() => {
    const deviceType = getDeviceType();
    const orientation = getOrientation();
    
    let columns: number;
    
    // Determine columns based on device type and orientation
    if (deviceType === 'extra-large-tablet' || deviceType === 'large-tablet') {
      columns = largeTabletColumns;
    } else if (deviceType === 'tablet') {
      columns = orientation === 'landscape' ? tabletLandscapeColumns : tabletPortraitColumns;
    } else {
      columns = phoneColumns;
    }

    // If minItemWidth is specified, calculate optimal columns based on available width
    if (minItemWidth) {
      const availableWidth = screenData.width - (gridSpacing * 2); // Account for container padding
      const maxPossibleColumns = Math.floor((availableWidth + gridSpacing) / (minItemWidth + gridSpacing));
      columns = Math.min(columns, Math.max(1, maxPossibleColumns));
    }

    // If maxItemWidth is specified, ensure we have enough columns
    if (maxItemWidth) {
      const availableWidth = screenData.width - (gridSpacing * 2);
      const minRequiredColumns = Math.ceil((availableWidth + gridSpacing) / (maxItemWidth + gridSpacing));
      columns = Math.max(columns, minRequiredColumns);
    }

    return columns;
  }, [
    screenData,
    phoneColumns,
    tabletPortraitColumns,
    tabletLandscapeColumns,
    largeTabletColumns,
    minItemWidth,
    maxItemWidth,
    gridSpacing,
  ]);

  // Notify parent of orientation/column changes
  useEffect(() => {
    const orientation = getOrientation();
    onOrientationChange?.(orientation, calculateColumns);
  }, [calculateColumns, onOrientationChange]);

  // Calculate item dimensions
  const getItemDimensions = useMemo(() => {
    const availableWidth = screenData.width - (gridSpacing * 2); // Container padding
    const totalSpacing = (calculateColumns - 1) * gridSpacing;
    const itemWidth = (availableWidth - totalSpacing) / calculateColumns;
    
    let itemHeight = itemWidth;
    if (itemAspectRatio) {
      itemHeight = itemWidth / itemAspectRatio;
    }

    return { width: itemWidth, height: itemHeight };
  }, [screenData.width, calculateColumns, gridSpacing, itemAspectRatio]);

  // Enhanced render item with proper sizing
  const enhancedRenderItem: ListRenderItem<T> = ({ item, index }) => {
    const isLastInRow = (index + 1) % calculateColumns === 0;
    const isLastRow = Math.floor(index / calculateColumns) === Math.floor((data.length - 1) / calculateColumns);
    
    const itemStyle: ViewStyle = {
      width: getItemDimensions.width,
      marginRight: isLastInRow ? 0 : gridSpacing,
      marginBottom: isLastRow ? 0 : gridSpacing,
    };

    if (itemAspectRatio) {
      itemStyle.height = getItemDimensions.height;
    }

    return (
      <View style={itemStyle}>
        {renderItem({ item, index, separators: {} as any })}
      </View>
    );
  };

  // Grid container styles
  const gridContainerStyle: ViewStyle = {
    paddingHorizontal: gridSpacing,
    paddingVertical: isTablet() ? spacing.lg : spacing.md,
    ...contentContainerStyle,
  };

  const gridStyle: ViewStyle = {
    flex: 1,
    ...style,
  };

  return (
    <FlatList
      {...flatListProps}
      data={data}
      renderItem={enhancedRenderItem}
      numColumns={calculateColumns}
      key={`grid-${calculateColumns}-${screenData.width}`} // Force re-render on column change
      style={gridStyle}
      contentContainerStyle={gridContainerStyle}
      showsVerticalScrollIndicator={false}
      showsHorizontalScrollIndicator={false}
    />
  );
}

export default ResponsiveGrid;