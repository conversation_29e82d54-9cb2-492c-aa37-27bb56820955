import Text from "@/components/Text";
import { Constants } from "@/constants";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import React, { useCallback, useRef } from "react";
import {
  Animated,
  Easing,
  Image,
  ImageSourcePropType,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";

import Theme from "@/constants/Theme";

interface ItemProps {
  img: ImageSourcePropType;
  title: string;
  numberNew?: number;
  route?: string;
  style?: ViewStyle;
  color: string;
}

const FeatureItem = (props: ItemProps) => {
  const router = useRouter();
  const onPress = useCallback(() => {
    if (props.route) {
      // Get the current patient ID from the URL if possible
      let patientId = "";
      if (typeof window !== "undefined" && window.location) {
        try {
          // Try to extract from URL path
          const match = window.location.pathname.match(/\/patient\/([^\/]+)/);
          if (match && match[1]) {
            patientId = match[1];
          }

          // If not found in path, try query params
          if (!patientId && window.location.search) {
            const urlParams = new URLSearchParams(window.location.search);
            const idParam = urlParams.get("id");
            if (idParam) {
              patientId = idParam;
            }
          }
        } catch (error) {
          // Silent error handling for URL parsing
        }
      }

      // Map old routes to new Expo Router paths
      const routeMap: Record<string, string> = {
        PatientsStack: "/(tabs)/patients",
        PatientAlerts: "/patient/alerts",
        PatientDetails: "/patient/[id]",
        VitalRepors: "/patient/vitals/[id]",
        MedicationPage: patientId
          ? `/patient/medications/index?patientId=${patientId}`
          : "/patient/medications/index",
        PatientMapView: "/patient/map/[id]",
        CustomAlerts: patientId
          ? `/patient/custom-alerts?patientId=${patientId}`
          : "/patient/custom-alerts",
        CCMReports: patientId
          ? `/patient/ccm/index?patientId=${patientId}`
          : "/patient/ccm/index",
        CarePlanDetails: "/patient/care-plan/[id]",
        PatientVideoCall: "/patient/video-call/[id]",
        Consult: "/patient/tasks",
        Validation: "/validation",
        BillingInfo: "/billing",
      };

      const path = routeMap[props.route] || props.route;

      console.log("=======path", path);

      // Special handling for medication routes
      if (path === "/patient/medications/index" && patientId) {
        try {
          // For medications, always use the direct URL with query parameters
          const medicationsUrl = `/patient/medications/index?patientId=${patientId}`;

          // Use router navigation instead of direct window.location manipulation
          // This works in both web and React Native environments
          try {
            router.push(medicationsUrl as any);
            return; // Stop execution after redirect
          } catch (routerError) {
            // Continue with normal navigation as fallback
          }
        } catch (medicationError) {
          // Continue with normal navigation as fallback
        }
      }

      if (path === "/patient/alerts" && props.title === "Warnings") {
        router.push({
          pathname: "/patient/alerts",
          params: {
            index: 1,
          },
        });
        return;
      }

      // Normal navigation for other routes
      router.push(path as any);
    }
  }, [props.route, router]);

  // Animation values
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  // Animation configurations
  const handlePressIn = () => {
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 0.96,
        duration: 150,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
      Animated.timing(opacityAnim, {
        toValue: 0.9,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 5,
        tension: 40,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // Determine gradient colors based on props.color
  const baseColor = props.color || "#FFFFFF";
  // Use tuple type for LinearGradient colors prop which expects readonly [string, string, ...string[]]
  const gradientColors = [
    baseColor,
    Platform.OS === "ios" ? `${baseColor}F0` : baseColor,
  ] as const;

  return (
    <TouchableOpacity
      activeOpacity={1}
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
    >
      <Animated.View
        style={[
          styles.container,
          props.style,
          {
            transform: [{ scale: scaleAnim }],
            opacity: opacityAnim,
          },
        ]}
      >
        <LinearGradient
          colors={gradientColors}
          style={styles.gradientContainer}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <Image source={props.img} style={styles.icon} />
          <Text style={styles.title}>{props.title}</Text>
        </LinearGradient>
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: (Constants.width - Constants.widthScale(64)) / 2,
    height: (Constants.width - Constants.widthScale(170)) / 2,
    marginBottom: Constants.heightScale(16),
    borderRadius: 16, // Reduced from 22 for a more modern look
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 3, // Reduced from 6
    },
    shadowOpacity: 0.12, // Reduced from 0.18
    shadowRadius: 8, // Reduced from 12
    elevation: 4, // Reduced from 8
  },
  gradientContainer: {
    flex: 1,
    width: "100%",
    height: "100%",
    ...Theme.center,
    paddingHorizontal: Constants.widthScale(16),
  },
  icon: {
    height: 50,
    width: 50,
    resizeMode: "contain",
    marginBottom: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333333",
    textAlign: "center",
  },
});

export default FeatureItem;
