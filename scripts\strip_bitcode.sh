#!/bin/bash

# <PERSON><PERSON>t to strip bitcode from iOS frameworks
# This script should be run from the project root directory

echo "Starting bitcode stripping process..."

# Check if we're in the project root directory
if [ ! -d "ios" ]; then
  echo "Error: This script must be run from the project root directory."
  echo "Please run: cd /path/to/your/project && ./scripts/strip_bitcode.sh"
  exit 1
fi

# Find the bitcode_strip tool
BITCODE_STRIP_PATH=$(xcrun --find bitcode_strip)
if [ -z "$BITCODE_STRIP_PATH" ]; then
  echo "Error: Could not find bitcode_strip tool."
  exit 1
fi

echo "Found bitcode_strip at: $BITCODE_STRIP_PATH"

# Function to strip bitcode from a framework
strip_bitcode() {
  local framework_path="$1"
  if [ -f "$framework_path" ]; then
    echo "Stripping bitcode from: $framework_path"
    "$BITCODE_STRIP_PATH" "$framework_path" -r -o "$framework_path"
    if [ $? -eq 0 ]; then
      echo "✅ Successfully stripped bitcode from: $framework_path"
    else
      echo "❌ Failed to strip bitcode from: $framework_path"
    fi
  else
    echo "⚠️ Framework binary not found: $framework_path"
  fi
}

# Change to the iOS directory
cd ios

# List of frameworks to strip bitcode from
FRAMEWORKS=(
  "Pods/TwilioVideo/TwilioVideo.xcframework/ios-arm64_armv7/TwilioVideo.framework/TwilioVideo"
  "Pods/GoogleMaps/Maps/Frameworks/GoogleMaps.xcframework/ios-arm64/GoogleMaps.framework/GoogleMaps"
  "Pods/GoogleMaps/Maps/Frameworks/GoogleMapsCore.xcframework/ios-arm64/GoogleMapsCore.framework/GoogleMapsCore"
  "Pods/GoogleMaps/Base/Frameworks/GoogleMapsBase.xcframework/ios-arm64/GoogleMapsBase.framework/GoogleMapsBase"
  "Pods/hermes-engine/destroot/Library/Frameworks/universal/hermes.xcframework/ios-arm64/hermes.framework/hermes"
)

# Strip bitcode from each framework
for framework in "${FRAMEWORKS[@]}"; do
  strip_bitcode "$framework"
done

# Auto-detect other frameworks that might need bitcode stripping
echo "Searching for additional frameworks..."
ADDITIONAL_FRAMEWORKS=$(find Pods -path "*/ios-arm64*/**.framework/**" -type f -not -path "*/dSYMs/*" | grep -v simulator)

for framework in $ADDITIONAL_FRAMEWORKS; do
  # Skip frameworks we've already processed
  skip=false
  for known_framework in "${FRAMEWORKS[@]}"; do
    if [[ "$framework" == "$known_framework" ]]; then
      skip=true
      break
    fi
  done
  
  if [ "$skip" = false ]; then
    echo "Found additional framework: $framework"
    strip_bitcode "$framework"
  fi
done

echo "Bitcode stripping process completed!"
echo "If you encounter any issues during build, please check the Xcode logs for more details."
echo "You may need to run 'pod install' again after this process."
