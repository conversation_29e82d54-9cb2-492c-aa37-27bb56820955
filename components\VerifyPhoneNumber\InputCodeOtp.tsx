import React, {Dispatch, SetStateAction, useCallback, useRef} from 'react';
import {
  Image,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import Text from '@/components/Text';
import {Colors, Constants} from '@/constants';
import Theme from '@/constants/Theme';
import fillNumberLength from '@/utils/convert/fillNumberLength';
interface InputCodeOtpProps {
  style?: ViewStyle;
  codeLength?: number;
  code: string;
  setCode: Dispatch<SetStateAction<string>>;
  autoFocus?: boolean;
}

const InputCodeOtp = ({
  style,
  codeLength = 5,
  code,
  autoFocus,
  setCode,
}: InputCodeOtpProps) => {
  const _code = fillNumberLength(code, codeLength);
  const inputRef: any = useRef();
  const renderInputBox = useCallback(() => {
    let arrBox = [];
    for (let i = 0; i < _code.length; i++) {
      arrBox.push(
        <View
          key={i.toString()}
          style={[
            styles.box,
            i !== codeLength - 1 && styles.space,
            _code.charAt(i) !== '#' && styles.alreadyEnter,
          ]}>
          {_code.charAt(i) !== '#' && (
            <Text size={32} semibold center lineHeight={40}>
              {_code.charAt(i)}
            </Text>
          )}
        </View>,
      );
    }
    return arrBox;
  }, [_code]);

  const onPressInput = useCallback(() => {
    inputRef.current.focus();
  }, [inputRef]);

  const onChangeText = useCallback(
    (text: string) => {
      let _text = text;
      if (text.length > codeLength) {
        _text = text.substring(0, codeLength);
      }
      setCode(_text);
    },
    [codeLength],
  );

  return (
    <TouchableOpacity style={[styles.container, style]} onPress={onPressInput}>
      {renderInputBox()}
      {code.length === codeLength && (
        <Image
          source={require('images/ic_accept.png')}
          style={styles.iconAccept}
        />
      )}
      <TextInput
        autoFocus={autoFocus}
        value={code}
        onChangeText={onChangeText}
        style={styles.fakeInput}
        maxLength={codeLength}
        ref={inputRef}
      />
    </TouchableOpacity>
  );
};

export default InputCodeOtp;

const styles = StyleSheet.create({
  container: {
    ...Theme.flexRow,
    alignSelf: 'center',
  },
  box: {
    width: 48,
    height: 56,
    borderColor: Colors.DarkJungleGreen,
    borderWidth: 1,
    borderRadius: 12,
    ...Theme.center,
  },
  space: {
    marginRight: 8,
  },
  iconAccept: {
    position: 'absolute',
    right: -32,
    width: 24,
    height: 24,
  },
  fakeInput: {
    position: 'absolute',
    right: -Constants.width * 2,
  },
  alreadyEnter: {
    borderColor: Colors.Malachite,
  },
});
