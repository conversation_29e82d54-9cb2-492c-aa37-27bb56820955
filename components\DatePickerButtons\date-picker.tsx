import DatePickerBottomIcon from "@/components/DatePickerButtons/date-picker-bottom-arrow";
import scale from "@/utils/scale";
import Moment from "moment";
import React from "react";
import {
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import DateTimePickerModal from "react-native-modal-datetime-picker";

type State = {
  isVisible: boolean;
};

type Props = {
  date?: Date;
  onDateChange: (date: Date) => void;
  maxDate?: Date;
};

export default class WatchRxDatePicker extends React.PureComponent<
  Props,
  State
> {
  constructor(props: Props) {
    super(props);
    this.state = { isVisible: false };
  }

  showDatePicker = () => {
    this.setState({ isVisible: true });
  };

  hideDatePicker = () => {
    this.setState({ isVisible: false });
  };

  handleConfirm = (date: Date) => {
    this.setState({ isVisible: false }, () => {
      this.props.onDateChange(date);
    });
  };

  render() {
    return (
      <View style={style.container}>
        <View style={style.leftContainer}>
          <Text>{Moment(this.props.date).format("MMM YYYY")}</Text>
        </View>
        <TouchableOpacity
          style={style.rightContainer}
          onPress={() => this.setState({ isVisible: !this.state.isVisible })}
        >
          <DateTimePickerModal
            isVisible={this.state.isVisible}
            mode="date"
            date={this.props.date}
            onConfirm={this.handleConfirm}
            onCancel={this.hideDatePicker}
            maximumDate={this.props.maxDate || new Date()}
            display={Platform.OS === "android" ? "default" : "spinner"}
            themeVariant="light"
            isDarkModeEnabled={false}
            textColor="#000000"
            accentColor="#4ECDC4"
            pickerContainerStyleIOS={{
              backgroundColor: '#FFFFFF'
            }}
          />
          <DatePickerBottomIcon />
        </TouchableOpacity>
      </View>
    );
  }
}

const style = StyleSheet.create({
  container: {
    backgroundColor: "white",
    borderRadius: 20,
    height: "100%",
    flexDirection: "row",
    shadowOpacity: 0.25,
    shadowRadius: 1,
    shadowColor: "black",
    shadowOffset: { height: 1, width: 1 },
    elevation: 3,
  },
  leftContainer: {
    width: "70%",
    justifyContent: "center",
    alignItems: "flex-end",
  },
  rightContainer: {
    justifyContent: "center",
    alignItems: "center",
    width: "30%",
  },
  bottomIcon: {
    fontSize: scale(16),
    color: "#838383",
  },
});
