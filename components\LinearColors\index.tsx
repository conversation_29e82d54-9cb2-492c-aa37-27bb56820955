import React, {memo} from 'react';
import {ViewStyle, Platform} from 'react-native';
import {LinearGradient} from 'expo-linear-gradient';

interface Props {
  children?: React.ReactNode;
  style?: ViewStyle;
  colors?: string[];
  vertical?: boolean;
  locations?: number[] | null;
  start?: {x: number; y: number};
  end?: {x: number; y: number};
}

// Workaround for TypeScript issues with expo-linear-gradient
const LinearColors = memo((props: Props) => {
  const {
    children,
    style,
    colors = ['#D5D1E3', '#F0AAB4'],
    vertical = false,
    locations,
    start,
    end
  } = props;

  // Default start and end points based on vertical prop
  const defaultStart = vertical ? {x: 0.5, y: 0} : {x: 0, y: 0.5};
  const defaultEnd = vertical ? {x: 0.5, y: 1} : {x: 1, y: 0.5};

  // Use provided values or defaults
  const startPoint = start || defaultStart;
  const endPoint = end || defaultEnd;

  // Ensure we have at least 2 colors
  const safeColors = colors.length >= 2 ? colors : ['#D5D1E3', '#F0AAB4'];

  // Using any to bypass TypeScript issues
  const LinearGradientAny = LinearGradient as any;

  // Add Android-specific style adjustments
  const androidStyle = Platform.OS === 'android' ? {
    // Prevent shadow artifacts on Android
    elevation: 0,
    // Ensure proper rendering on Android
    overflow: 'hidden',
  } : {};

  return (
    <LinearGradientAny
      colors={safeColors}
      start={startPoint}
      end={endPoint}
      locations={locations || undefined}
      style={[style, androidStyle]}
    >
      {children}
    </LinearGradientAny>
  );
});

export default LinearColors;
