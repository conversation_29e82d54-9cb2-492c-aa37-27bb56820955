import React, {useEffect, useMemo, useRef, memo, useCallback} from 'react';
import {StyleSheet, View, Dimensions, TouchableOpacity, Animated, Easing, ScrollView} from 'react-native';
import {useSelector} from 'react-redux';
import Container from '@/components/Layout/Container';
import Text from '@/components/Text';
import {Routes} from '@/constants';
import { FontAwesome5, Ionicons, MaterialCommunityIcons, Feather, MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface VitalFeature {
  id: number;
  icon: {
    family: string;
    name: string;
    size: number;
  };
  title: string;
  subtitle: string;
  route: string;
  color: string;
  gradientColors: string[];
  priority: string;
  animation: string;
}

// Enhanced features with modern gradients and subtitles matching PatientDetails style
const VitalsDashboardGridItem = memo(({ item, patientId, style }: { item: VitalFeature; patientId: string; style?: any }) => {
  const router = useRouter();
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.timing(scaleAnim, {
      toValue: 0.95,
      duration: 150,
      useNativeDriver: true,
      easing: Easing.bezier(0.4, 0.0, 0.2, 1),
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 4,
      tension: 100,
      useNativeDriver: true,
    }).start();
  };

  const handlePress = () => {
    // Navigate to vital report item with specific vital type
    try {
      router.push({
        pathname: '/patient/vitals/[id]' as any,
        params: {
          id: item.id.toString(),
          title: item.title,
          patientId: patientId
        }
      });
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  const renderIcon = () => {
    const iconProps = {
      size: 32,
      color: "#FFFFFF",
    };

    try {
      switch (item.icon.family) {
        case 'MaterialIcons':
          return <MaterialIcons name={item.icon.name as any} {...iconProps} />;
        case 'MaterialCommunityIcons':
          return <MaterialCommunityIcons name={item.icon.name as any} {...iconProps} />;
        case 'Ionicons':
          return <Ionicons name={item.icon.name as any} {...iconProps} />;
        case 'FontAwesome5':
          return <FontAwesome5 name={item.icon.name as any} {...iconProps} />;
        case 'Feather':
          return <Feather name={item.icon.name as any} {...iconProps} />;
        default:
          return <MaterialIcons name="trending-up" {...iconProps} />;
      }
    } catch (error) {
      // Fallback icon if there's an error rendering the specific icon
      return <MaterialIcons name="trending-up" {...iconProps} />;
    }
  };

  return (
    <TouchableOpacity
      activeOpacity={1}
      onPress={handlePress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      style={style}
    >
      <Animated.View
        style={[
          styles.gridItem,
          {
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <LinearGradient
          colors={[item.gradientColors[0], item.gradientColors[0]]}
          style={styles.gridItemGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {/* Icon container with enhanced styling */}
          <View style={styles.gridIconContainer}>
            <View style={styles.iconBackground}>
              {renderIcon()}
            </View>
          </View>

          {/* Content with improved typography */}
          <View style={styles.gridItemContent}>
            <Text style={styles.gridItemTitle} numberOfLines={1}>
              {item.title}
            </Text>
            <Text style={styles.gridItemSubtitle} numberOfLines={1}>
              {item.subtitle}
            </Text>
          </View>
        </LinearGradient>
      </Animated.View>
    </TouchableOpacity>
  );
});

const VitalReports = (props: any) => {
  // Enhanced features with gradient colors matching main dashboard behavior
  const FEATURES: VitalFeature[] = useMemo(() => [
    {
      id: 1,
      icon: {
        family: 'MaterialIcons',
        name: 'speed',
        size: 28
      },
      title: 'Blood Pressure',
      subtitle: 'Systolic & Diastolic',
      route: Routes.VitalReportItem,
      color: '#F25F5C',
      gradientColors: ["#F25F5C", "#EF4444"], // Critical health metric - red like Critical Alerts
      priority: "high",
      animation: "pulse"
    },
    {
      id: 2,
      icon: {
        family: 'MaterialCommunityIcons',
        name: 'water',
        size: 28
      },
      title: 'Blood Sugar',
      subtitle: 'Glucose levels',
      route: Routes.VitalReportItem,
      color: '#4ECDC4',
      gradientColors: ["#4ECDC4", "#44A08D"], // Primary health monitoring - teal like main Vitals
      priority: "high",
      animation: "scale"
    },
    {
      id: 3,
      icon: {
        family: 'MaterialCommunityIcons',
        name: 'heart-pulse',
        size: 28
      },
      title: 'Heart Rate',
      subtitle: 'Beats per minute',
      route: Routes.VitalReportItem,
      color: '#FF6B6B',
      gradientColors: ["#FF6B6B", "#EE5A52"], // Heart-related - red like GPS (urgent monitoring)
      priority: "high",
      animation: "bounce"
    },
    {
      id: 4,
      icon: {
        family: 'FontAwesome5',
        name: 'walking',
        size: 28
      },
      title: 'Pedometer',
      subtitle: 'Steps & activity',
      route: Routes.VitalReportItem,
      color: '#5FB49C',
      gradientColors: ["#5FB49C", "#10B981"], // Activity/wellness - green like Check In
      priority: "medium",
      animation: "rotate"
    },
    {
      id: 5,
      icon: {
        family: 'FontAwesome5',
        name: 'lungs',
        size: 28
      },
      title: 'Oxygen Saturation',
      subtitle: 'SpO2 levels',
      route: Routes.VitalReportItem,
      color: '#36B5E0',
      gradientColors: ["#36B5E0", "#3490DC"], // Respiratory - blue like Alerts (important monitoring)
      priority: "high",
      animation: "pulse"
    },
    {
      id: 6,
      icon: {
        family: 'FontAwesome5',
        name: 'thermometer',
        size: 28
      },
      title: 'Temperature',
      subtitle: 'Body temperature',
      route: Routes.VitalReportItem,
      color: '#FF9F1C',
      gradientColors: ["#FF9F1C", "#F59E0B"], // Temperature monitoring - orange like Custom Reminders
      priority: "medium",
      animation: "shake"
    },
    {
      id: 7,
      icon: {
        family: 'FontAwesome5',
        name: 'bed',
        size: 28
      },
      title: 'Sleep',
      subtitle: 'Rest patterns',
      route: Routes.VitalReportItem,
      color: '#9381FF',
      gradientColors: ["#9381FF", "#8B5CF6"], // Sleep/rest - purple like Care Team Notes
      priority: "low",
      animation: "scale"
    },
    {
      id: 8,
      icon: {
        family: 'FontAwesome5',
        name: 'weight',
        size: 28
      },
      title: 'Weight',
      subtitle: 'Body mass tracking',
      route: Routes.VitalReportItem,
      color: '#7A77FF',
      gradientColors: ["#7A77FF", "#6366F1"], // Body metrics - blue-purple like Medications
      priority: "low",
      animation: "pulse"
    },
  ], []);

  const patientName = useSelector(
    (state: any) => state?.currentPatientNameReducer?.patientName,
  );

  const patientId = useSelector(
    (state: any) => state?.currentPatientIdReducer?.patientId,
  );

  // Animation references for the grid items
  const gridAnimationRefs = FEATURES.map(() => useRef(new Animated.Value(0)).current);
  
  // Animation timing function for staggered grid animation
  useEffect(() => {
    const animations = gridAnimationRefs.map((anim, index) => {
      return Animated.timing(anim, {
        toValue: 1,
        duration: 300,
        delay: 50 + index * 30,
        useNativeDriver: true,
      });
    });
    
    Animated.stagger(30, animations).start();
  }, []);

  useEffect(() => {
    // Only use this with React Navigation, not with Expo Router
    if (props.navigation && props.navigation.setOptions) {
      props.navigation.setOptions({
        headerTitle: () => (
          <View style={styles.headerContainer}>
            <Text medium size={16} color={'#000'}>
              {patientName}
              {' Vital Reports'}
            </Text>
          </View>
        ),
      });
    }
  }, [patientName, props.navigation]);



  // Fixed grid layout that ensures all 8 tiles are visible
  const renderDashboardGrid = () => {
    const pairs = [];
    for (let i = 0; i < FEATURES.length; i += 2) {
      pairs.push(FEATURES.slice(i, i + 2));
    }

    return pairs.map((pair, rowIndex) => (
      <View key={rowIndex} style={styles.gridRow}>
        {pair.map((item, itemIndex) => (
          <VitalsDashboardGridItem
            key={item.id}
            item={item}
            patientId={String(patientId)}
            style={styles.gridItemContainer}
          />
        ))}
        {/* If odd number of items, add spacer for last row */}
        {pair.length === 1 && <View style={styles.gridItemContainer} />}
      </View>
    ));
  };

  // Render the grid with ScrollView for better small screen support
  return (
    <Container style={styles.container}>
      <ScrollView 
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        bounces={true}
      >
        <View style={styles.contentWrapper}>
          <View style={styles.featuresContainer}>
            {renderDashboardGrid()}
          </View>
        </View>
      </ScrollView>
    </Container>
  );
};

export default VitalReports;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FAFBFC",
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  contentWrapper: {
    flex: 1,
    paddingTop: 20,
  },
  headerContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  featuresContainer: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  gridRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  gridItemContainer: {
    flex: 1,
    marginHorizontal: 6,
    height: 160,
  },
  gridItem: {
    width: "100%",
    height: "100%",
    borderRadius: 12,
    backgroundColor: "#FFFFFF",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    overflow: "hidden",
  },
  gridItemGradient: {
    flex: 1,
    padding: 18,
    justifyContent: "space-between",
  },
  gridIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "flex-start",
  },
  gridItemContent: {
    marginTop: 14,
    flex: 1,
    justifyContent: "flex-end",
  },
  gridItemTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 3,
    letterSpacing: -0.1,
    lineHeight: 20,
  },
  gridItemSubtitle: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.85)",
    fontWeight: "500",
    lineHeight: 16,
  },
  iconBackground: {
    backgroundColor: "rgba(255, 255, 255, 0.25)",
    borderRadius: 20,
    width: 50,
    height: 50,
    justifyContent: "center",
    alignItems: "center",
  },
});