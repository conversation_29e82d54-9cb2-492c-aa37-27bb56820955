import NavigationHeader from "@/components/NavigationHeader";
import React from "react";
import { Stack } from "expo-router";

export default function ValidationLayout() {
  return (
    <>
      <NavigationHeader
        title="Monthly Summary"
        showBackButton={true}
        showLogout={false}
        showLogo={false}
        showRightLogo={true}
      />
      <Stack
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen name="index" />
      </Stack>
    </>
  );
}
