import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { TabletAwareContainer, AdaptiveLayout, ResponsiveGrid } from '@/components/Layout';
import { AccessibleText, AccessibleButton } from '@/components/Accessibility';
import { isTablet, isLargeTablet, spacing } from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';

interface Task {
  id: string;
  title: string;
  description: string;
  patientName: string;
  priority: 'high' | 'medium' | 'low';
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  dueDate: string;
  category: 'medication' | 'follow_up' | 'documentation' | 'assessment' | 'other';
  assignedTo?: string;
}

interface TabletTasksProps {
  tasks?: Task[];
  onTaskPress?: (task: Task) => void;
  onTaskComplete?: (taskId: string) => void;
  onAddTask?: () => void;
}

/**
 * Tablet-optimized Tasks screen component
 */
const TabletTasks: React.FC<TabletTasksProps> = ({
  tasks = [],
  onTaskPress,
  onTaskComplete,
  onAddTask,
}) => {
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'pending' | 'overdue' | 'completed'>('all');
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);

  // Sample tasks data
  const sampleTasks: Task[] = [
    {
      id: '1',
      title: 'Medication Review',
      description: 'Review and update medication list for patient',
      patientName: 'John Smith',
      priority: 'high',
      status: 'overdue',
      dueDate: '2024-01-15',
      category: 'medication',
      assignedTo: 'Dr. Johnson',
    },
    {
      id: '2',
      title: 'Follow-up Call',
      description: 'Schedule follow-up call to check on patient progress',
      patientName: 'Sarah Wilson',
      priority: 'medium',
      status: 'pending',
      dueDate: '2024-01-18',
      category: 'follow_up',
    },
    {
      id: '3',
      title: 'Care Plan Update',
      description: 'Update care plan based on recent assessment',
      patientName: 'Mike Davis',
      priority: 'medium',
      status: 'in_progress',
      dueDate: '2024-01-20',
      category: 'documentation',
    },
    {
      id: '4',
      title: 'Health Assessment',
      description: 'Conduct comprehensive health assessment',
      patientName: 'Emily Brown',
      priority: 'low',
      status: 'completed',
      dueDate: '2024-01-12',
      category: 'assessment',
    },
  ];

  const tasksToShow = tasks.length > 0 ? tasks : sampleTasks;

  const filteredTasks = tasksToShow.filter(task => {
    if (selectedFilter === 'all') return true;
    return task.status === selectedFilter;
  });

  const getPriorityColor = (priority: Task['priority']) => {
    switch (priority) {
      case 'high':
        return CoreColors.RedOrange;
      case 'medium':
        return CoreColors.YellowOrange;
      case 'low':
        return CoreColors.TealBlue;
      default:
        return CoreColors.SlateGray;
    }
  };

  const getStatusColor = (status: Task['status']) => {
    switch (status) {
      case 'completed':
        return CoreColors.TealBlue;
      case 'in_progress':
        return CoreColors.TurquoiseBlue;
      case 'overdue':
        return CoreColors.RedOrange;
      case 'pending':
      default:
        return CoreColors.SlateGray;
    }
  };

  const getCategoryIcon = (category: Task['category']) => {
    switch (category) {
      case 'medication':
        return '💊';
      case 'follow_up':
        return '📞';
      case 'documentation':
        return '📋';
      case 'assessment':
        return '🩺';
      case 'other':
      default:
        return '📝';
    }
  };

  const renderTaskCard = ({ item }: { item: Task }) => (
    <TouchableOpacity
      style={[
        styles.taskCard,
        selectedTask?.id === item.id && styles.selectedTaskCard,
      ]}
      onPress={() => {
        setSelectedTask(item);
        onTaskPress?.(item);
      }}
      accessible={true}
      accessibilityLabel={`Task: ${item.title} for ${item.patientName}`}
      accessibilityRole=\"button\"
    >
      <View style={styles.taskHeader}>
        <View style={styles.taskPriority}>
          <View style={[styles.priorityIndicator, { backgroundColor: getPriorityColor(item.priority) }]} />
          <AccessibleText variant=\"caption\" style={styles.priorityText}>
            {item.priority.toUpperCase()}
          </AccessibleText>
        </View>
        <AccessibleText variant=\"caption\" style={styles.categoryIcon}>
          {getCategoryIcon(item.category)}
        </AccessibleText>
      </View>

      <View style={styles.taskContent}>
        <AccessibleText variant=\"h3\" style={styles.taskTitle}>
          {item.title}
        </AccessibleText>
        <AccessibleText variant=\"bodySmall\" style={styles.taskDescription}>
          {item.description}
        </AccessibleText>
        <AccessibleText variant=\"body\" style={styles.patientName}>
          Patient: {item.patientName}
        </AccessibleText>
      </View>

      <View style={styles.taskFooter}>
        <AccessibleText variant=\"caption\" style={styles.dueDate}>
          Due: {new Date(item.dueDate).toLocaleDateString()}
        </AccessibleText>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <AccessibleText variant=\"caption\" style={styles.statusText}>
            {item.status.replace('_', ' ').toUpperCase()}
          </AccessibleText>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderTaskFilters = () => (
    <View style={styles.filtersContainer}>
      <AccessibleText variant=\"h3\" style={styles.filtersTitle}>
        Filter Tasks
      </AccessibleText>
      <View style={styles.filterButtons}>
        {(['all', 'pending', 'overdue', 'completed'] as const).map((filter) => (
          <TouchableOpacity
            key={filter}
            style={[
              styles.filterButton,
              selectedFilter === filter && styles.activeFilterButton,
            ]}
            onPress={() => setSelectedFilter(filter)}
            accessible={true}
            accessibilityLabel={`Filter by ${filter}`}
            accessibilityRole=\"button\"
          >
            <AccessibleText
              variant=\"bodySmall\"
              style={[
                styles.filterButtonText,
                selectedFilter === filter && styles.activeFilterButtonText,
              ]}
            >
              {filter.replace('_', ' ').toUpperCase()}
            </AccessibleText>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderTasksList = () => (
    <View style={styles.tasksListContainer}>
      <View style={styles.tasksHeader}>
        <AccessibleText variant=\"h2\" style={styles.tasksTitle}>
          Tasks ({filteredTasks.length})
        </AccessibleText>
        <AccessibleButton
          title=\"Add Task\"
          onPress={onAddTask}
          size=\"small\"
          variant=\"primary\"
        />
      </View>

      <ResponsiveGrid
        data={filteredTasks}
        renderItem={renderTaskCard}
        numColumns={isLargeTablet() ? 2 : 1}
        spacing={spacing.md}
      />
    </View>
  );

  const renderTaskDetail = () => {
    if (!selectedTask) {
      return (
        <View style={styles.taskDetailEmpty}>
          <AccessibleText variant=\"body\" style={styles.emptyText}>
            Select a task to view details
          </AccessibleText>
        </View>
      );
    }

    return (
      <ScrollView style={styles.taskDetail} showsVerticalScrollIndicator={false}>
        <AccessibleText variant=\"h2\" style={styles.detailTitle}>
          Task Details
        </AccessibleText>

        <View style={styles.detailSection}>
          <AccessibleText variant=\"h3\" style={styles.detailTaskTitle}>
            {selectedTask.title}
          </AccessibleText>
          <AccessibleText variant=\"body\" style={styles.detailDescription}>
            {selectedTask.description}
          </AccessibleText>
        </View>

        <View style={styles.detailSection}>
          <AccessibleText variant=\"h3\" style={styles.detailSectionTitle}>
            Patient Information
          </AccessibleText>
          <AccessibleText variant=\"body\" style={styles.detailPatientName}>
            {selectedTask.patientName}
          </AccessibleText>
        </View>

        <View style={styles.detailSection}>
          <AccessibleText variant=\"h3\" style={styles.detailSectionTitle}>
            Task Details
          </AccessibleText>
          <View style={styles.detailRow}>
            <AccessibleText variant=\"bodySmall\" style={styles.detailLabel}>
              Priority:
            </AccessibleText>
            <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(selectedTask.priority) }]}>
              <AccessibleText variant=\"caption\" style={styles.priorityBadgeText}>
                {selectedTask.priority.toUpperCase()}
              </AccessibleText>
            </View>
          </View>
          <View style={styles.detailRow}>
            <AccessibleText variant=\"bodySmall\" style={styles.detailLabel}>
              Status:
            </AccessibleText>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(selectedTask.status) }]}>
              <AccessibleText variant=\"caption\" style={styles.statusText}>
                {selectedTask.status.replace('_', ' ').toUpperCase()}
              </AccessibleText>
            </View>
          </View>
          <View style={styles.detailRow}>
            <AccessibleText variant=\"bodySmall\" style={styles.detailLabel}>
              Due Date:
            </AccessibleText>
            <AccessibleText variant=\"body\" style={styles.detailValue}>
              {new Date(selectedTask.dueDate).toLocaleDateString()}
            </AccessibleText>
          </View>
          <View style={styles.detailRow}>
            <AccessibleText variant=\"bodySmall\" style={styles.detailLabel}>
              Category:
            </AccessibleText>
            <AccessibleText variant=\"body\" style={styles.detailValue}>
              {getCategoryIcon(selectedTask.category)} {selectedTask.category.replace('_', ' ')}
            </AccessibleText>
          </View>
          {selectedTask.assignedTo && (
            <View style={styles.detailRow}>
              <AccessibleText variant=\"bodySmall\" style={styles.detailLabel}>
                Assigned To:
              </AccessibleText>
              <AccessibleText variant=\"body\" style={styles.detailValue}>
                {selectedTask.assignedTo}
              </AccessibleText>
            </View>
          )}
        </View>

        <View style={styles.detailActions}>
          {selectedTask.status !== 'completed' && (
            <AccessibleButton
              title=\"Mark Complete\"
              onPress={() => onTaskComplete?.(selectedTask.id)}
              variant=\"primary\"
              style={styles.detailActionButton}
            />
          )}
          <AccessibleButton
            title=\"Edit Task\"
            onPress={() => {}}
            variant=\"outline\"
            style={styles.detailActionButton}
          />
          <AccessibleButton
            title=\"Delete Task\"
            onPress={() => {}}
            variant=\"text\"
            style={styles.detailActionButton}
          />
        </View>
      </ScrollView>
    );
  };

  return (
    <TabletAwareContainer style={styles.container}>
      <AdaptiveLayout
        mobileLayout={() => (
          <ScrollView style={styles.mobileLayout}>
            {renderTaskFilters()}
            {renderTasksList()}
          </ScrollView>
        )}
        tabletLayout={() => (
          <View style={styles.tabletLayout}>
            <View style={styles.leftPanel}>
              {renderTaskFilters()}
              {renderTasksList()}
            </View>
            <View style={styles.rightPanel}>
              {renderTaskDetail()}
            </View>
          </View>
        )}
      />
    </TabletAwareContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  mobileLayout: {
    flex: 1,
  },
  tabletLayout: {
    flex: 1,
    flexDirection: 'row',
    padding: spacing.md,
  },
  leftPanel: {
    flex: 1,
    marginRight: spacing.md,
  },
  rightPanel: {
    width: isLargeTablet() ? 400 : 320,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: spacing.lg,
  },
  filtersContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: isTablet() ? 12 : 8,
    padding: spacing.lg,
    marginBottom: spacing.md,
  },
  filtersTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.md,
  },
  filterButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  filterButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  activeFilterButton: {
    backgroundColor: CoreColors.TurquoiseBlue,
    borderColor: CoreColors.TurquoiseBlue,
  },
  filterButtonText: {
    color: CoreColors.SlateGray,
  },
  activeFilterButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  tasksListContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: isTablet() ? 12 : 8,
    padding: spacing.lg,
    flex: 1,
  },
  tasksHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  tasksTitle: {
    color: CoreColors.DarkJungleGreen,
  },
  taskCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  selectedTaskCard: {
    backgroundColor: '#EBF8FF',
    borderColor: CoreColors.TurquoiseBlue,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  taskPriority: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  priorityIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: spacing.xs,
  },
  priorityText: {
    color: CoreColors.SlateGray,
    fontWeight: '600',
  },
  categoryIcon: {
    fontSize: 16,
  },
  taskContent: {
    marginBottom: spacing.md,
  },
  taskTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.xs,
  },
  taskDescription: {
    color: CoreColors.SlateGray,
    marginBottom: spacing.sm,
  },
  patientName: {
    color: CoreColors.DarkJungleGreen,
    fontWeight: '500',
  },
  taskFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dueDate: {
    color: CoreColors.SlateGray,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  taskDetail: {
    flex: 1,
  },
  taskDetailEmpty: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    color: CoreColors.SlateGray,
    textAlign: 'center',
  },
  detailTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.lg,
  },
  detailSection: {
    marginBottom: spacing.lg,
  },
  detailTaskTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.sm,
  },
  detailDescription: {
    color: CoreColors.SlateGray,
  },
  detailSectionTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.sm,
  },
  detailPatientName: {
    color: CoreColors.DarkJungleGreen,
    fontWeight: '500',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  detailLabel: {
    color: CoreColors.SlateGray,
    flex: 1,
  },
  detailValue: {
    color: CoreColors.DarkJungleGreen,
    flex: 1,
    textAlign: 'right',
  },
  priorityBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityBadgeText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  detailActions: {
    marginTop: 'auto',
    paddingTop: spacing.lg,
  },
  detailActionButton: {
    marginBottom: spacing.sm,
  },
});

export default TabletTasks;