import {useNavigation} from '@react-navigation/native';
import moment from 'moment';
import React, {useState, useRef, useEffect} from 'react';
import {
  Alert,
  Image,
  Modal,
  StyleSheet,
  TouchableOpacity,
  View,
  Animated,
  Pressable,
} from 'react-native';
import {useDispatch} from 'react-redux';
import {Colors, Routes} from '@/constants';
import {width} from '@/constants/Const';
import {TaskItemProps} from '@/models';
import {setCurrentTask} from '@/services/actions/currentTaskAction';
import {apiPostWithToken} from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import Theme from '@/constants/Theme';
import Layout from '../Layout/Layout';
import GenericModal from '../SuccessFailModal/GenericModal';
import Text from '../Text';

interface Props extends TaskItemProps {
  index: number;
  onSuccessDelete(): void;
}

const TasksItem = (props: Props) => {
  // Animation values
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  const checkOverDue = (item: any) => {
    let isBefore = moment(item, 'MM-DD-YYYY').isBefore(moment(), 'day');
    return isBefore;
  };

  const [visibleModal, setVisibleModal] = useState(false);
  const dispatch = useDispatch();
  const navigation = useNavigation() as any;

  // Animation for press feedback
  const onPressIn = () => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 0.98,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const onPressOut = () => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const open = () => {
    setVisibleModal(false);
    deletTasksById();
  };

  const close = () => {
    setVisibleModal(false);
  };

  const editTask = () => {
    dispatch(setCurrentTask(props));
    navigation.navigate(Routes.EditTask, {});
  };

  const deletTasksById = async () => {
    const response = await apiPostWithToken(
      {taskId: props.taskId},
      URLS.caregiverUrl + 'deleteTask',
    );
    if (response?.status == 200) {
      console.log('Deleted Tasks.');
      props.onSuccessDelete();
    } else {
      const errorMessage = response?.response?.data?.responseMessage
        ? response?.response?.data?.responseMessage
        : response.message === 'Network Error'
        ? 'Network error. Please check your data connection.'
        : response.message;
      Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
    }
  };

  return (
    <Animated.View
      style={[styles.container, { transform: [{ scale: scaleAnim }] }]}
    >
      <Pressable
        onPressIn={onPressIn}
        onPressOut={onPressOut}
        style={({ pressed }) => [{ opacity: pressed ? 0.9 : 1 }]}
      >
        <Layout style={styles.content}>
          {/* Task Status and Title */}
          <View style={styles.headerRow}>
            <View style={styles.titleContainer}>
              <View
                style={[styles.statusIndicator, {
                  backgroundColor: checkOverDue(props.taskEndDate) ? Colors.pastelRed : Colors.pastelGreen
                }]}
              />
              <Text
                bold
                size={18}
                color={checkOverDue(props.taskEndDate) ? Colors.pastelRed : Colors.pastelGreen}>
                {props.taskTitle}
              </Text>
            </View>
            <View style={styles.priorityBadge}>
              <Text
                size={12}
                color={Colors.White}
                bold
              >
                {props.taskPriority}
              </Text>
            </View>
          </View>

          {/* Patient Info */}
          <View style={styles.patientSection}>
            <Text size={15} color={Colors.GrayBlue}>
              Patient
            </Text>
            <Text
              bold
              size={16}
              style={{textTransform: 'capitalize'}}>
              {props.patientName}
            </Text>
          </View>

          {/* Task Details */}
          <View style={styles.detailsSection}>
            <View style={styles.detailRow}>
              <View style={styles.detailItem}>
                <Text size={14} color={Colors.GrayBlue}>Start Date</Text>
                <Text bold size={14}>{props.taskStartDate}</Text>
              </View>
              <View style={styles.detailItem}>
                <Text size={14} color={Colors.GrayBlue}>End Date</Text>
                <Text bold size={14}>{props.taskEndDate}</Text>
              </View>
            </View>
            <View style={styles.detailRow}>
              <View style={styles.detailItem}>
                <Text size={14} color={Colors.GrayBlue}>Status</Text>
                <Text bold size={14}>{props.taskStatus}</Text>
              </View>
            </View>
          </View>

          {/* Description */}
          {props.taskDesc ? (
            <View style={styles.descriptionSection}>
              <Text size={14} color={Colors.GrayBlue} marginBottom={4}>Description</Text>
              <Text size={15} lineHeight={22}>
                {props.taskDesc}
              </Text>
            </View>
          ) : null}

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={editTask}>
              <Image source={require('images/edit_t.png')} style={styles.actionIcon} />
              <Text size={14} color={Colors.TealBlue}>Edit</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => setVisibleModal(true)}>
              <Image source={require('images/delete.png')} style={styles.actionIcon} />
              <Text size={14} color={Colors.pastelRed}>Delete</Text>
            </TouchableOpacity>
          </View>
        </Layout>
      </Pressable>

      <Modal
        visible={visibleModal}
        onRequestClose={close}
        transparent
        animationType={'fade'}>
        <GenericModal
          close={close}
          open={open}
          message={
            `Are you sure you want to delete the task "${props.taskTitle}" for patient ${props.patientName}?`
          }
        />
      </Modal>
    </Animated.View>
  );
};

export default TasksItem;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    marginTop: 10,
    marginHorizontal: 16,
    padding: 16,
    borderRadius: 12,
    backgroundColor: Colors.White,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 4,
    marginBottom: 12,
    elevation: 3,
    overflow: 'hidden',
  },
  headerRow: {
    ...Theme.flexRowSpace,
    marginBottom: 12,
  },
  titleContainer: {
    ...Theme.flexRow,
    flex: 1,
    marginRight: 8,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  priorityBadge: {
    backgroundColor: Colors.TealBlue,
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 70,
  },
  patientSection: {
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.WhiteSmoke,
  },
  detailsSection: {
    marginBottom: 16,
  },
  detailRow: {
    ...Theme.flexRowSpace,
    marginBottom: 12,
  },
  detailItem: {
    flex: 1,
    marginRight: 8,
  },
  descriptionSection: {
    padding: 12,
    backgroundColor: Colors.WhiteSmoke,
    borderRadius: 8,
    marginBottom: 16,
  },
  actionButtons: {
    ...Theme.flexRowSpace,
    borderTopWidth: 1,
    borderTopColor: Colors.WhiteSmoke,
    paddingTop: 12,
  },
  actionButton: {
    ...Theme.flexRow,
    padding: 8,
  },
  actionIcon: {
    width: 20,
    height: 20,
    marginRight: 6,
  },
});
