import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  Image,
  FlatList,
  Dimensions,
  Animated,
} from 'react-native';
import { TabletAwareContainer } from '@/components/Layout';
import { AccessibleText, AccessibleButton } from '@/components/Accessibility';
import { isTablet, isLargeTablet, spacing } from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';

interface OnBoardingSlide {
  id: string;
  title: string;
  description: string;
  image: any;
  backgroundColor: string;
}

interface TabletOnBoardingProps {
  onComplete?: () => void;
  onSkip?: () => void;
}

/**
 * Tablet-optimized OnBoarding screen component
 */
const TabletOnBoarding: React.FC<TabletOnBoardingProps> = ({
  onComplete,
  onSkip,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const scrollX = useRef(new Animated.Value(0)).current;

  const slides: OnBoardingSlide[] = [
    {
      id: '1',
      title: 'Welcome to WatchRx Care Team',
      description: 'Comprehensive healthcare management platform designed for care providers and healthcare professionals.',
      image: require('@/assets/images/react-logo.png'),
      backgroundColor: CoreColors.TurquoiseBlue,
    },
    {
      id: '2',
      title: 'Manage Patient Care',
      description: 'Monitor patient health, track medications, and coordinate care plans all in one place.',
      image: require('@/assets/images/react-logo.png'),
      backgroundColor: CoreColors.TealBlue,
    },
    {
      id: '3',
      title: 'Secure Communication',
      description: 'Connect with patients through secure video calls, messaging, and real-time health monitoring.',
      image: require('@/assets/images/react-logo.png'),
      backgroundColor: CoreColors.YellowOrange,
    },
    {
      id: '4',
      title: 'Real-time Alerts',
      description: 'Receive instant notifications for critical health events and medication reminders.',
      image: require('@/assets/images/react-logo.png'),
      backgroundColor: CoreColors.RedOrange,
    },
  ];

  const { width: screenWidth } = Dimensions.get('window');
  const slideWidth = isTablet() ? Math.min(screenWidth * 0.8, 800) : screenWidth;

  const handleNext = () => {
    if (currentIndex < slides.length - 1) {
      const nextIndex = currentIndex + 1;
      setCurrentIndex(nextIndex);
      flatListRef.current?.scrollToIndex({ index: nextIndex, animated: true });
    } else {
      onComplete?.();
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      const prevIndex = currentIndex - 1;
      setCurrentIndex(prevIndex);
      flatListRef.current?.scrollToIndex({ index: prevIndex, animated: true });
    }
  };

  const onViewableItemsChanged = useRef(({ viewableItems }: any) => {
    if (viewableItems.length > 0) {
      setCurrentIndex(viewableItems[0].index);
    }
  }).current;

  const viewabilityConfig = useRef({
    itemVisiblePercentThreshold: 50,
  }).current;

  const renderSlide = ({ item, index }: { item: OnBoardingSlide; index: number }) => (
    <View style={[styles.slide, { width: slideWidth, backgroundColor: item.backgroundColor }]}>
      <View style={styles.slideContent}>
        <View style={styles.imageContainer}>
          <Image
            source={item.image}
            style={styles.slideImage}
            resizeMode="contain"
          />
        </View>
        <View style={styles.textContainer}>
          <AccessibleText variant="h1" style={styles.slideTitle}>
            {item.title}
          </AccessibleText>
          <AccessibleText variant="body" style={styles.slideDescription}>
            {item.description}
          </AccessibleText>
        </View>
      </View>
    </View>
  );

  const renderPagination = () => (
    <View style={styles.pagination}>
      {slides.map((_, index) => {
        const inputRange = [
          (index - 1) * slideWidth,
          index * slideWidth,
          (index + 1) * slideWidth,
        ];

        const dotWidth = scrollX.interpolate({
          inputRange,
          outputRange: [8, 20, 8],
          extrapolate: 'clamp',
        });

        const opacity = scrollX.interpolate({
          inputRange,
          outputRange: [0.3, 1, 0.3],
          extrapolate: 'clamp',
        });

        return (
          <Animated.View
            key={index}
            style={[
              styles.paginationDot,
              {
                width: dotWidth,
                opacity,
              },
            ]}
          />
        );
      })}
    </View>
  );

  return (
    <TabletAwareContainer style={styles.container}>
      <View style={styles.header}>
        <AccessibleButton
          title="Skip"
          onPress={onSkip}
          variant="text"
          style={styles.skipButton}
          accessibilityLabel="Skip onboarding"
        />
      </View>

      <View style={styles.content}>
        <FlatList
          ref={flatListRef}
          data={slides}
          renderItem={renderSlide}
          keyExtractor={(item) => item.id}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={Animated.event(
            [{ nativeEvent: { contentOffset: { x: scrollX } } }],
            { useNativeDriver: false }
          )}
          onViewableItemsChanged={onViewableItemsChanged}
          viewabilityConfig={viewabilityConfig}
          scrollEventThrottle={32}
          contentContainerStyle={styles.flatListContent}
        />

        {renderPagination()}
      </View>

      <View style={styles.footer}>
        <View style={styles.navigationButtons}>
          {currentIndex > 0 && (
            <AccessibleButton
              title="Previous"
              onPress={handlePrevious}
              variant="outline"
              style={styles.navButton}
              accessibilityLabel="Go to previous slide"
            />
          )}
          <View style={styles.spacer} />
          <AccessibleButton
            title={currentIndex === slides.length - 1 ? "Get Started" : "Next"}
            onPress={handleNext}
            variant="primary"
            style={styles.navButton}
            accessibilityLabel={
              currentIndex === slides.length - 1 
                ? "Complete onboarding and get started" 
                : "Go to next slide"
            }
          />
        </View>
      </View>
    </TabletAwareContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.md,
  },
  skipButton: {
    paddingHorizontal: 0,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  flatListContent: {
    alignItems: 'center',
  },
  slide: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.xl,
  },
  slideContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    maxWidth: isTablet() ? 600 : 400,
  },
  imageContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.xl,
  },
  slideImage: {
    width: isLargeTablet() ? 400 : isTablet() ? 300 : 250,
    height: isLargeTablet() ? 400 : isTablet() ? 300 : 250,
  },
  textContainer: {
    alignItems: 'center',
    paddingBottom: spacing.xl,
  },
  slideTitle: {
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: spacing.lg,
    fontWeight: '700',
  },
  slideDescription: {
    color: '#FFFFFF',
    textAlign: 'center',
    opacity: 0.9,
    lineHeight: isTablet() ? 28 : 24,
  },
  pagination: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.lg,
  },
  paginationDot: {
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
    marginHorizontal: 4,
  },
  footer: {
    paddingHorizontal: spacing.xl,
    paddingBottom: spacing.xl,
  },
  navigationButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  navButton: {
    minWidth: isTablet() ? 120 : 100,
  },
  spacer: {
    flex: 1,
  },
});

export default TabletOnBoarding;