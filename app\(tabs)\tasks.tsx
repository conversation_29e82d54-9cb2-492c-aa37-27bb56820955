import React from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import TasksScreen from '@/screens/Tasks';

export default function TasksTabRoute() {
  return (
    <SafeAreaView style={styles.container} edges={['left', 'right']}>
      <TasksScreen />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    marginTop: 0,
    paddingTop: 0,
    paddingBottom: 0,
    position: 'relative',
    zIndex: 1,
  },
}); 