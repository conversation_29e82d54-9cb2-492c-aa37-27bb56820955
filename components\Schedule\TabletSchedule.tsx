import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { TabletAwareContainer } from '@/components/Layout/TabletAwareContainer';
import { AdaptiveLayout } from '@/components/Layout/AdaptiveLayout';
import { AccessibleText } from '@/components/Accessibility/AccessibleText';
import { AccessibleButton } from '@/components/Accessibility/AccessibleButton';
import { isTablet, isLargeTablet, spacing, typography } from '@/utils/responsive';
import { CoreColors } from '@/constants/Colors';

interface ScheduleItem {
  id: string;
  patientName: string;
  type: 'video_call' | 'voice_call' | 'in_person' | 'follow_up';
  time: string;
  duration: number;
  status: 'scheduled' | 'completed' | 'cancelled' | 'in_progress';
  notes?: string;
}

interface TabletScheduleProps {
  scheduleData?: ScheduleItem[];
  selectedDate?: Date;
  onDateChange?: (date: Date) => void;
  onScheduleItem?: (item: ScheduleItem) => void;
}

/**
 * Tablet-optimized Schedule screen component
 */
const TabletSchedule: React.FC<TabletScheduleProps> = ({
  scheduleData = [],
  selectedDate = new Date(),
  onDateChange,
  onScheduleItem,
}) => {
  const [selectedView, setSelectedView] = useState<'day' | 'week' | 'month'>('day');
  const [selectedItem, setSelectedItem] = useState<ScheduleItem | null>(null);

  // Sample schedule data
  const sampleSchedule: ScheduleItem[] = [
    {
      id: '1',
      patientName: 'John Smith',
      type: 'video_call',
      time: '09:00 AM',
      duration: 30,
      status: 'scheduled',
      notes: 'Follow-up consultation',
    },
    {
      id: '2',
      patientName: 'Sarah Johnson',
      type: 'voice_call',
      time: '10:30 AM',
      duration: 15,
      status: 'completed',
      notes: 'Medication review',
    },
    {
      id: '3',
      patientName: 'Mike Davis',
      type: 'video_call',
      time: '02:00 PM',
      duration: 45,
      status: 'in_progress',
      notes: 'Initial consultation',
    },
    {
      id: '4',
      patientName: 'Emily Wilson',
      type: 'follow_up',
      time: '03:30 PM',
      duration: 20,
      status: 'scheduled',
      notes: 'Care plan review',
    },
  ];

  const scheduleToShow = scheduleData.length > 0 ? scheduleData : sampleSchedule;

  const getStatusColor = (status: ScheduleItem['status']) => {
    switch (status) {
      case 'completed':
        return CoreColors.TealBlue;
      case 'in_progress':
        return CoreColors.YellowOrange;
      case 'cancelled':
        return CoreColors.RedOrange;
      case 'scheduled':
      default:
        return CoreColors.TurquoiseBlue;
    }
  };

  const getTypeIcon = (type: ScheduleItem['type']) => {
    switch (type) {
      case 'video_call':
        return '📹';
      case 'voice_call':
        return '📞';
      case 'in_person':
        return '🏥';
      case 'follow_up':
        return '📋';
      default:
        return '📅';
    }
  };

  const renderScheduleItem = ({ item }: { item: ScheduleItem }) => (
    <TouchableOpacity
      style={[
        styles.scheduleItem,
        selectedItem?.id === item.id && styles.selectedScheduleItem,
      ]}
      onPress={() => setSelectedItem(item)}
      accessible={true}
      accessibilityLabel={`${item.patientName} ${item.type} at ${item.time}`}
      accessibilityRole=\"button\"
    >
      <View style={styles.scheduleItemHeader}>
        <View style={styles.scheduleItemTime}>
          <AccessibleText variant=\"body\" style={styles.timeText}>
            {item.time}
          </AccessibleText>
          <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(item.status) }]} />
        </View>
        <AccessibleText variant=\"caption\" style={styles.typeIcon}>
          {getTypeIcon(item.type)}
        </AccessibleText>
      </View>
      
      <View style={styles.scheduleItemContent}>
        <AccessibleText variant=\"h3\" style={styles.patientName}>
          {item.patientName}
        </AccessibleText>
        <AccessibleText variant=\"bodySmall\" style={styles.scheduleType}>
          {item.type.replace('_', ' ').toUpperCase()} • {item.duration} min
        </AccessibleText>
        {item.notes && (
          <AccessibleText variant=\"bodySmall\" style={styles.scheduleNotes}>
            {item.notes}
          </AccessibleText>
        )}
      </View>
      
      <View style={styles.scheduleItemActions}>
        <AccessibleText variant=\"caption\" style={[styles.statusText, { color: getStatusColor(item.status) }]}>
          {item.status.toUpperCase()}
        </AccessibleText>
      </View>
    </TouchableOpacity>
  );

  const renderCalendarView = () => (
    <View style={styles.calendarContainer}>
      <AccessibleText variant=\"h2\" style={styles.calendarTitle}>
        {selectedDate.toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        })}
      </AccessibleText>
      
      {/* View Toggle */}
      <View style={styles.viewToggle}>
        {(['day', 'week', 'month'] as const).map((view) => (
          <TouchableOpacity
            key={view}
            style={[
              styles.viewToggleButton,
              selectedView === view && styles.activeViewToggleButton,
            ]}
            onPress={() => setSelectedView(view)}
            accessible={true}
            accessibilityLabel={`${view} view`}
            accessibilityRole=\"button\"
          >
            <AccessibleText
              variant=\"bodySmall\"
              style={[
                styles.viewToggleText,
                selectedView === view && styles.activeViewToggleText,
              ]}
            >
              {view.toUpperCase()}
            </AccessibleText>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderScheduleList = () => (
    <View style={styles.scheduleListContainer}>
      <View style={styles.scheduleHeader}>
        <AccessibleText variant=\"h2\" style={styles.scheduleTitle}>
          Today's Schedule
        </AccessibleText>
        <AccessibleButton
          title=\"Add Appointment\"
          onPress={() => onScheduleItem?.(selectedItem!)}
          size=\"small\"
          variant=\"outline\"
        />
      </View>
      
      <FlatList
        data={scheduleToShow}
        renderItem={renderScheduleItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scheduleList}
      />
    </View>
  );

  const renderScheduleDetail = () => {
    if (!selectedItem) {
      return (
        <View style={styles.scheduleDetailEmpty}>
          <AccessibleText variant=\"body\" style={styles.emptyText}>
            Select an appointment to view details
          </AccessibleText>
        </View>
      );
    }

    return (
      <View style={styles.scheduleDetail}>
        <AccessibleText variant=\"h2\" style={styles.detailTitle}>
          Appointment Details
        </AccessibleText>
        
        <View style={styles.detailSection}>
          <AccessibleText variant=\"h3\" style={styles.detailPatientName}>
            {selectedItem.patientName}
          </AccessibleText>
          <AccessibleText variant=\"body\" style={styles.detailTime}>
            {selectedItem.time} • {selectedItem.duration} minutes
          </AccessibleText>
          <AccessibleText variant=\"bodySmall\" style={styles.detailType}>
            {selectedItem.type.replace('_', ' ').toUpperCase()}
          </AccessibleText>
        </View>
        
        {selectedItem.notes && (
          <View style={styles.detailSection}>
            <AccessibleText variant=\"h3\" style={styles.detailSectionTitle}>
              Notes
            </AccessibleText>
            <AccessibleText variant=\"body\" style={styles.detailNotes}>
              {selectedItem.notes}
            </AccessibleText>
          </View>
        )}
        
        <View style={styles.detailActions}>
          <AccessibleButton
            title=\"Start Call\"
            onPress={() => {}}
            variant=\"primary\"
            style={styles.detailActionButton}
          />
          <AccessibleButton
            title=\"Reschedule\"
            onPress={() => {}}
            variant=\"outline\"
            style={styles.detailActionButton}
          />
          <AccessibleButton
            title=\"Cancel\"
            onPress={() => {}}
            variant=\"text\"
            style={styles.detailActionButton}
          />
        </View>
      </View>
    );
  };

  return (
    <TabletAwareContainer style={styles.container}>
      <AdaptiveLayout
        mobileLayout={() => (
          <ScrollView style={styles.mobileLayout}>
            {renderCalendarView()}
            {renderScheduleList()}
          </ScrollView>
        )}
        tabletLayout={() => (
          <View style={styles.tabletLayout}>
            <View style={styles.leftPanel}>
              {renderCalendarView()}
              {renderScheduleList()}
            </View>
            <View style={styles.rightPanel}>
              {renderScheduleDetail()}
            </View>
          </View>
        )}
      />
    </TabletAwareContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  mobileLayout: {
    flex: 1,
  },
  tabletLayout: {
    flex: 1,
    flexDirection: 'row',
  },
  leftPanel: {
    flex: 1,
    marginRight: spacing.md,
  },
  rightPanel: {
    width: isLargeTablet() ? 400 : 320,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: spacing.lg,
  },
  calendarContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: isTablet() ? 12 : 8,
    padding: spacing.lg,
    marginBottom: spacing.md,
  },
  calendarTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.md,
  },
  viewToggle: {
    flexDirection: 'row',
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 4,
  },
  viewToggleButton: {
    flex: 1,
    paddingVertical: spacing.sm,
    borderRadius: 6,
    alignItems: 'center',
  },
  activeViewToggleButton: {
    backgroundColor: '#FFFFFF',
  },
  viewToggleText: {
    color: CoreColors.SlateGray,
  },
  activeViewToggleText: {
    color: CoreColors.DarkJungleGreen,
    fontWeight: '600',
  },
  scheduleListContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: isTablet() ? 12 : 8,
    padding: spacing.lg,
    flex: 1,
  },
  scheduleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  scheduleTitle: {
    color: CoreColors.DarkJungleGreen,
  },
  scheduleList: {
    paddingBottom: spacing.lg,
  },
  scheduleItem: {
    flexDirection: 'row',
    padding: spacing.md,
    borderRadius: 8,
    marginBottom: spacing.sm,
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  selectedScheduleItem: {
    backgroundColor: '#EBF8FF',
    borderColor: CoreColors.TurquoiseBlue,
  },
  scheduleItemHeader: {
    width: 80,
    alignItems: 'center',
  },
  scheduleItemTime: {
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  timeText: {
    color: CoreColors.DarkJungleGreen,
    fontWeight: '600',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginTop: 4,
  },
  typeIcon: {
    fontSize: 20,
  },
  scheduleItemContent: {
    flex: 1,
    paddingHorizontal: spacing.md,
  },
  patientName: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.xs,
  },
  scheduleType: {
    color: CoreColors.SlateGray,
    marginBottom: spacing.xs,
  },
  scheduleNotes: {
    color: CoreColors.SlateGray,
    fontStyle: 'italic',
  },
  scheduleItemActions: {
    justifyContent: 'center',
    alignItems: 'flex-end',
  },
  statusText: {
    fontWeight: '600',
    fontSize: 12,
  },
  scheduleDetail: {
    flex: 1,
  },
  scheduleDetailEmpty: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    color: CoreColors.SlateGray,
    textAlign: 'center',
  },
  detailTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.lg,
  },
  detailSection: {
    marginBottom: spacing.lg,
  },
  detailPatientName: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.xs,
  },
  detailTime: {
    color: CoreColors.SlateGray,
    marginBottom: spacing.xs,
  },
  detailType: {
    color: CoreColors.TurquoiseBlue,
    fontWeight: '600',
  },
  detailSectionTitle: {
    color: CoreColors.DarkJungleGreen,
    marginBottom: spacing.sm,
  },
  detailNotes: {
    color: CoreColors.SlateGray,
  },
  detailActions: {
    marginTop: 'auto',
  },
  detailActionButton: {
    marginBottom: spacing.sm,
  },
});

export default TabletSchedule;