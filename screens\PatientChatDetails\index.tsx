import { useFocusEffect } from '@react-navigation/native';
import { useLocalSearchParams } from 'expo-router';
import Moment from 'moment';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { SectionListData, AppState } from 'react-native';
import {
  Alert,
  Animated,
  Image,
  Modal,
  StyleSheet,
  View,
  RefreshControl,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  TextInput,
  TouchableWithoutFeedback,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { Ionicons } from '@expo/vector-icons';
import { useSelector } from 'react-redux';
import * as Notifications from 'expo-notifications';
import messaging from '@react-native-firebase/messaging';
import Loader from '@/components/Loader/Loader';
import SendMessageModal from '@/components/Modals/SendMessage';
import Text from '@/components/Text';
import { Colors } from '@/constants';
import { WatchRxPrograms } from '@/models';
import { apiPostWithToken } from '@/services/apis/apiManager';
import URLS from '@/services/config/config';
import scale from '@/utils/scale';
import { getOrganizationId } from '@/utils/getOrganizationId';
import ReceivedBubble from './ReceivedBubble';
import SentBubble from './SentBubble';
import { useRouter } from 'expo-router';
import NavigationHeader from '@/components/NavigationHeader';
import Container from '@/components/Layout/Container';
import ProgramSelector from '@/components/ProgramSelector';
import { getBottomSpace } from 'react-native-iphone-x-helper';

interface Message {
  message: string;
  time: string;
  type: 'server' | 'mobile';
  date?: string; // Will be added when processing the response
  [key: string]: any;
}

interface MessageSection {
  title: string;
  data: Message[];
}

interface PatientChatDetailsScreenProps {
  validatedDeviceType: string;
}

const ChatDetail = React.memo(({ validatedDeviceType }: PatientChatDetailsScreenProps) => {
  const [messageList, setMessageList] = useState<Message[]>([]);

  const [sendMessageModal, setSendMessageModal] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  
  // Use the validated device type passed from parent
  const connectingDevice = validatedDeviceType;
  
  // Log the validated device type for debugging
  useEffect(() => {
    console.log('PatientChatDetails: Using validated device type:', validatedDeviceType);
  }, [validatedDeviceType]);
  
  const params = useLocalSearchParams();
  const { id, fromNotification, refreshChat, notificationTimestamp, patientName: notificationPatientName } = params;

  const [patientId, setPatientId] = useState(0);
  const [programs, setPrograms] = useState<WatchRxPrograms>();
  const scrollRef = useRef<any>(null);
  const [loader, setLoader] = useState(false);
  
  // For direct mobile message input
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [selectedProgram, setSelectedProgram] = useState('');
  const textInputRef = useRef<TextInput>(null);

  // Simplified animations
  const scrollButtonOpacity = useRef(new Animated.Value(0)).current;
  const fabScale = useRef(new Animated.Value(1)).current;

  // Estimate average item height for getItemLayout
  const ITEM_HEIGHT = 80; // Estimated average height of a message bubble
  const SECTION_HEIGHT = 50; // Estimated height of section headers

  // Handle scroll failures gracefully
  const handleScrollToIndexFailed = useCallback((info: {
    index: number;
    highestMeasuredFrameIndex: number;
    averageItemLength: number;
  }) => {
    // When scrolling fails, try again with a delay
    const wait = new Promise(resolve => setTimeout(resolve, 100));
    wait.then(() => {
      // Try to scroll again but without animation for better reliability
      if (scrollRef.current) {
        scrollRef.current.scrollToEnd({ animated: false });
      }
    });
  }, []);

  const onScrollToEnd = useCallback(() => {
    if (scrollRef.current) {
      // Use getScrollResponder().scrollToEnd to scroll to the bottom of the list
      scrollRef.current.getScrollResponder()?.scrollToEnd({
        animated: true
      });
    }
    hideScrollButton();
    setShowScrollToBottom(false);
    
    if (Platform.OS === 'ios') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  }, []);

  // Simplified scroll button animations
  const showScrollButton = () => {
    Animated.spring(scrollButtonOpacity, {
      toValue: 1,
      tension: 100,
      friction: 8,
      useNativeDriver: true,
    }).start();
  };

  const hideScrollButton = () => {
    Animated.timing(scrollButtonOpacity, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  // FAB animation
  const animateFab = (pressed: boolean) => {
    Animated.spring(fabScale, {
      toValue: pressed ? 0.95 : 1,
      tension: 100,
      friction: 8,
      useNativeDriver: true,
    }).start();
  };

  // Enhanced keyboard listeners with height tracking
  useEffect(() => {
    const keyboardWillShowSubscription = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (event) => {
        setIsKeyboardVisible(true);
        setKeyboardHeight(event.endCoordinates.height);
        
        // When keyboard shows, scroll to bottom with delay to ensure layout is adjusted
        setTimeout(() => {
          if (scrollRef.current && messageList.length > 0) {
            scrollRef.current.getScrollResponder()?.scrollToEnd({
              animated: true
            });
          }
        }, 200);
      }
    );
    
    const keyboardWillHideSubscription = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setIsKeyboardVisible(false);
        setKeyboardHeight(0);
        
        // Force layout update after keyboard is dismissed
        setTimeout(() => {
          if (scrollRef.current && messageList.length > 0) {
            scrollRef.current.getScrollResponder()?.scrollToEnd({
              animated: true
            });
          }
        }, 100);
      }
    );

    return () => {
      keyboardWillShowSubscription.remove();
      keyboardWillHideSubscription.remove();
    };
  }, [messageList]);

  // Scroll to bottom when messages are loaded
  useEffect(() => {
    if (messageList && messageList.length > 0) {
      // Small delay to ensure the list is rendered
      const timer = setTimeout(() => {
        if (scrollRef.current) {
          // Use getScrollResponder().scrollToEnd to scroll to the bottom of the list
          scrollRef.current.getScrollResponder()?.scrollToEnd({
            animated: false
          });
        }
      }, 300);
      
      return () => clearTimeout(timer);
    }
  }, [messageList]);

  const handleScroll = (event: any) => {
    const offsetY = event.nativeEvent.contentOffset.y;
    const contentHeight = event.nativeEvent.contentSize.height;
    const layoutHeight = event.nativeEvent.layoutMeasurement.height;

    const isScrolledUp = contentHeight - layoutHeight - offsetY > 150;

    if (isScrolledUp && !showScrollToBottom) {
      setShowScrollToBottom(true);
      showScrollButton();
    } else if (!isScrolledUp && showScrollToBottom) {
      setShowScrollToBottom(false);
      hideScrollButton();
    }
  };

  // Get patient details from Redux with proper typing
  const patientDetails = useSelector(
    (state: { patientDetailsReducer?: { patientDetails?: any } }) =>
      state?.patientDetailsReducer?.patientDetails
  );

  const patientIdFromRedux = useSelector(
    (state: { currentPatientIdReducer?: { patientId?: string } }) =>
      state?.currentPatientIdReducer?.patientId
  );

  const patientList = useSelector(
    (state: { patientsListReducer?: { data?: any[] } }) =>
      state?.patientsListReducer?.data || []
  );

  // Get user ID and organization ID from Redux
  const caregiverId = useSelector((state: any) => state?.loginReducer?.data?.userId);
  const orgIdFromRedux = useSelector((state: any) => state?.currentOrgIdReducer?.orgId);
  const [orgId, setOrgId] = useState<string>(getOrganizationId());

  // Debug Redux state
  useEffect(() => {
    if (orgIdFromRedux) {
      setOrgId(orgIdFromRedux);
    }
  }, [orgIdFromRedux]);

  // Initialize patient data and set up notification listener (Requirement 1)
  useFocusEffect(
    React.useCallback(() => {
      const effectivePatientId = id || patientIdFromRedux;

      if (effectivePatientId) {
        const patientIdNumber = Number(effectivePatientId);
        setPatientId(patientIdNumber);
        
        // Requirement 1: Pull messages when opening chat screen
        console.log('ChatDetail: Opening chat screen, pulling messages for patient:', patientIdNumber);
        console.log('ChatDetail: caregiverId available:', !!caregiverId, 'orgId available:', !!orgId);
        
        if (caregiverId && orgId) {
          getTextMessageByPatientId(patientIdNumber);
        } else {
          // If caregiverId or orgId is not ready, set up a retry mechanism
          console.log('ChatDetail: caregiverId or orgId not ready, will retry when available');
        }
      }
    }, [id, patientIdFromRedux, patientDetails?.patientName, caregiverId, orgId]),
  );

  // Additional effect to handle delayed loading when caregiverId or orgId becomes available
  useEffect(() => {
    if (patientId && patientId > 0 && caregiverId && orgId && messageList.length === 0) {
      console.log('ChatDetail: caregiverId and orgId now available, loading messages for patient:', patientId);
      getTextMessageByPatientId(patientId);
    }
  }, [caregiverId, orgId, patientId]);

  // Set up real-time notification listener for current patient (Requirement 2)
  useEffect(() => {
    if (!patientId || patientId === 0) return;

    console.log('ChatDetail: Setting up notification listener for patient:', patientId);

    // Listen for FCM messages while on this screen
    const unsubscribeOnMessage = messaging().onMessage(async remoteMessage => {
      console.log('ChatDetail: FCM message received while on chat screen:', JSON.stringify(remoteMessage));
      
      // Check if this is a chat message for the current patient
      if (remoteMessage.data && remoteMessage.data.type === 'chat_message' && remoteMessage.data.patientId) {
        const notificationPatientId = String(remoteMessage.data.patientId);
        
        if (notificationPatientId === patientId.toString()) {
          console.log('ChatDetail: Received notification for current patient, refreshing messages');
          // Requirement 2: Pull messages when receiving notification for same patient
          setTimeout(() => {
            getTextMessageByPatientId(patientId, true);
            
            // Scroll to bottom after refresh
            setTimeout(() => {
              onScrollToEnd();
            }, 300);
          }, 500);
        }
      }
    });

    // Listen for Expo notifications while on this screen
    const notificationListener = Notifications.addNotificationReceivedListener(notification => {
      console.log('ChatDetail: Expo notification received while on chat screen:', notification);
      
      // Check if this is a chat message for the current patient
      const data = notification.request.content.data;
      if (data && data.type === 'chat_message' && data.patientId) {
        const notificationPatientId = String(data.patientId);
        
        if (notificationPatientId === patientId.toString()) {
          console.log('ChatDetail: Received Expo notification for current patient, refreshing messages');
          // Requirement 2: Pull messages when receiving notification for same patient
          setTimeout(() => {
            getTextMessageByPatientId(patientId, true);
            
            // Scroll to bottom after refresh
            setTimeout(() => {
              onScrollToEnd();
            }, 300);
          }, 500);
        }
      }
    });

    // Cleanup listeners when component unmounts or patient changes
    return () => {
      console.log('ChatDetail: Cleaning up notification listeners for patient:', patientId);
      unsubscribeOnMessage();
      notificationListener && Notifications.removeNotificationSubscription(notificationListener);
    };
  }, [patientId]);

  // Handle notification-based navigation and auto-refresh
  useEffect(() => {
    if (fromNotification === 'true' && refreshChat === 'true') {
      console.log('ChatDetail: Opened from notification, auto-refreshing messages');
      
      const effectivePatientId = id || patientIdFromRedux;
      if (effectivePatientId) {
        const patientIdNumber = Number(effectivePatientId);
        setPatientId(patientIdNumber);
        
        // Enhanced retry mechanism for notification-based loading
        const attemptMessageLoad = (retryCount = 0) => {
          console.log('ChatDetail: Attempting to load messages, retry count:', retryCount);
          console.log('ChatDetail: caregiverId available:', !!caregiverId, 'orgId available:', !!orgId);
          
          if (caregiverId && orgId) {
            console.log('ChatDetail: Force refreshing messages for patient:', patientIdNumber);
            getTextMessageByPatientId(patientIdNumber, true);
            
            // Scroll to bottom after messages load
            setTimeout(() => {
              onScrollToEnd();
            }, 500);
          } else if (retryCount < 5) {
            // Retry up to 5 times with increasing delays
            const delay = 500 + (retryCount * 500);
            console.log('ChatDetail: Retrying message load in', delay, 'ms');
            setTimeout(() => attemptMessageLoad(retryCount + 1), delay);
          } else {
            console.warn('ChatDetail: Failed to load messages after 5 retries, missing caregiverId or orgId');
          }
        };
        
        // Start with a small delay to ensure the screen is mounted
        setTimeout(() => attemptMessageLoad(), 300);
      }

      // Log notification interaction for analytics
      console.log('ChatDetail: Notification interaction tracked:', {
        patientId: id,
        timestamp: notificationTimestamp,
        patientName: notificationPatientName
      });
    }
  }, [fromNotification, refreshChat, id, patientIdFromRedux, caregiverId, orgId, notificationTimestamp]);

  // Handle programs initialization
  useFocusEffect(
    React.useCallback(() => {
      const effectivePatientId = id || patientIdFromRedux;

      if (effectivePatientId) {
        const foundPatient = patientList.find(p => p.patientId == effectivePatientId);

        if (foundPatient?.programs) {
          setPrograms(foundPatient.programs);
                      // The connecting device type is already validated and passed from parent
            // No need to set it here as it's controlled by validatedDeviceType prop
          } else if (patientDetails?.programs) {
            setPrograms(patientDetails.programs);
            // The connecting device type is already validated and passed from parent
        } else {
          const defaultPrograms: WatchRxPrograms = {
            selectedPrograms: [
              {
                mins: 0,
                programName: 'RPM',
                programActivated: true,
                patientProgramId: '1',
                programId: '1'
              },
              {
                mins: 0,
                programName: 'CCM',
                programActivated: true,
                patientProgramId: '2',
                programId: '2'
              },
              {
                mins: 0,
                programName: 'PERSONAL',
                programActivated: true,
                patientProgramId: '0',
                programId: '0'
              }
            ],
            availablePrograms: [
              { programName: 'RPM', programId: '1', mins: 0, programActivated: false },
              { programName: 'CCM', programId: '2', mins: 0, programActivated: false },
              { programName: 'PCM', programId: '3', mins: 0, programActivated: false },
              { programName: 'RTM', programId: '4', mins: 0, programActivated: false },
              { programName: 'PERSONAL', programId: '0', mins: 0, programActivated: false }
            ]
          };
          setPrograms(defaultPrograms);
        }
      }
    }, [id, patientIdFromRedux, patientDetails?.programs, patientList]),
  );

  // Auto-select the first program when programs are loaded
  useEffect(() => {
    if (programs?.selectedPrograms && programs.selectedPrograms.length > 0 && selectedProgram === '') {
      setSelectedProgram(programs.selectedPrograms[0].programName);
    }
  }, [programs, selectedProgram]);

  // Handle program selection
  const handleProgramSelection = useCallback((programName: string) => {
    setSelectedProgram(programName);
  }, []);

  const getTextMessageByPatientId = async (patientId: number, isRefreshing = false) => {
    console.log('ChatDetail: getTextMessageByPatientId called with:', { patientId, isRefreshing, caregiverId: !!caregiverId, orgId: !!orgId });
    
    if (patientId !== undefined && patientId > 0) {
      if (!caregiverId || !orgId) {
        console.error('ChatDetail: Missing caregiverId or orgId:', { caregiverId: !!caregiverId, orgId: !!orgId });
        Alert.alert('Error', 'Missing user or organization information. Please try logging in again.', [{ text: 'Dismiss' }]);
        return;
      }

      if (!isRefreshing) {
        setLoader(true);
      }

      try {
        const requestParams = {
          patientId: String(patientId)
        };
        
        const apiUrl = URLS.caregiverUrl + 'getChatMessage';
        
        const response = await apiPostWithToken(requestParams, apiUrl);

        if (response?.status == 200 && response?.data?.success) {
          setLoader(false);
          setRefreshing(false);

          const responseData = response?.data?.data;

          if (responseData && Array.isArray(responseData) && responseData.length > 0) {
            const allMessages: Message[] = [];
            
            responseData.forEach((dateGroup) => {
              if (dateGroup.data && Array.isArray(dateGroup.data)) {
                dateGroup.data.forEach((message: any) => {
                  // Validate message data before processing
                  if (!message || typeof message.message !== 'string') {
                    console.warn('Invalid message data:', message);
                    return;
                  }

                  // Ensure we have valid timestamp data
                  const messageDate = dateGroup.time || new Date().toISOString().split('T')[0];
                  const messageTime = message.time || '00:00:00';
                  
                  const processedMessage: Message = {
                    ...message,
                    date: messageDate,
                    fullTimestamp: `${messageDate} ${messageTime}`,
                    // Ensure message content is properly sanitized
                    message: message.message.trim() || 'Message content unavailable'
                  };
                  
                  allMessages.push(processedMessage);
                });
              }
            });

            const sortedMessages = allMessages.sort((a, b) => {
              const timeA = new Date(`${a.date} ${a.time}`).getTime();
              const timeB = new Date(`${b.date} ${b.time}`).getTime();
              return timeA - timeB;
            });
            
            setMessageList(sortedMessages);
            
            setTimeout(() => {
              onScrollToEnd();
            }, 100);
            
          } else {
            setMessageList([]);
          }
        } else {
          setLoader(false);
          setRefreshing(false);

          let errorMessage = 'Failed to load messages. Please try again.';
          
          if (response?.error?.message) {
            errorMessage = response.error.message;
          } else if (response?.response?.data?.responseMessage) {
            errorMessage = response.response.data.responseMessage;
          } else if (response?.message) {
            errorMessage = response.message === 'Network Error'
              ? 'Network error. Please check your data connection.'
              : response.message;
          }
          
          Alert.alert('Error', errorMessage, [{ text: 'Dismiss' }]);
        }
      } catch (error) {
        setLoader(false);
        setRefreshing(false);
        Alert.alert('Error', 'Failed to load messages. Please try again.', [{ text: 'Dismiss' }]);
      }
    }
  };

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    getTextMessageByPatientId(patientId, true);
  }, [patientId]);

  // Add retry mechanism
  const retryLoadMessages = useCallback(() => {
    if (patientId) {
      setLoader(true);
      getTextMessageByPatientId(patientId, false);
    }
  }, [patientId]);

  const closeModal = useCallback(() => {
    setSendMessageModal(false);
    getTextMessageByPatientId(patientId, true);
  }, [patientId]);

  const handleLoader = (status: boolean | Boolean) => {
    setLoader(Boolean(status));
  };

  const renderBubble = useCallback(({ item }: { item: Message }) => {
    // Display single message bubble based on type
    if (item.type === 'server') {
      // Server messages are sent from caregiver to patient
      return (
        <SentBubble 
          question={item.message} 
          timestamp={item.fullTimestamp || `${item.date} ${item.time}`} 
          connectingDevice={connectingDevice}
        />
      );
    } else {
      // Mobile messages are from patient to caregiver
      return (
        <ReceivedBubble 
          answer={item.message} 
          timestamp={item.fullTimestamp || `${item.date} ${item.time}`} 
          patientName={patientDetails?.patientName}
          connectingDevice={connectingDevice}
        />
      );
    }
  }, [patientDetails?.patientName, validatedDeviceType]);

  const renderSectionHeader = useCallback(({ section }: { section: SectionListData<Message> }) => {
    return (
      <View style={styles.sectionHeader}>
        <View style={styles.dateContainer}>
          <Text style={styles.dateText}>
            {section.title}
          </Text>
        </View>
      </View>
    );
  }, []);

  const groupMessagesByDate = useCallback((messages: Message[]): MessageSection[] => {
    if (!messages || !Array.isArray(messages) || messages.length === 0) return [];
    
    const groupedMessages = messages.reduce((groups: {[key: string]: Message[]}, message) => {
      if (!message || !message.date) {
        console.warn(`⚠️ [CHAT API] Message missing date:`, message);
        return groups;
      }
      
      try {
        // Try to parse the date with multiple formats
        let momentDate = Moment(message.date, 'YYYY-MM-DD', true);
        
        if (!momentDate.isValid()) {
          momentDate = Moment(message.date, 'MM-DD-YYYY', true);
        }
        
        if (!momentDate.isValid()) {
          momentDate = Moment(message.date);
        }
        
        // If still invalid, use today's date as fallback
        if (!momentDate.isValid()) {
          console.warn(`⚠️ [CHAT API] Invalid date format:`, message.date);
          momentDate = Moment();
        }
        
        const dateKey = momentDate.format('MMMM DD, YYYY');
        if (!groups[dateKey]) {
          groups[dateKey] = [];
        }
        groups[dateKey].push(message);
      } catch (error) {
        console.warn(`⚠️ [CHAT API] Error processing message date:`, message.date, error);
        // Use today's date as fallback
        const dateKey = Moment().format('MMMM DD, YYYY');
        if (!groups[dateKey]) {
          groups[dateKey] = [];
        }
        groups[dateKey].push(message);
      }
      
      return groups;
    }, {});

    const sections = Object.keys(groupedMessages)
      .sort((a, b) => Moment(a, 'MMMM DD, YYYY').valueOf() - Moment(b, 'MMMM DD, YYYY').valueOf())
      .map(date => ({
        title: date,
        data: groupedMessages[date].sort((a, b) => {
          // Sort messages within each date group by time
          try {
            const timeA = new Date(`${a.date} ${a.time}`).getTime();
            const timeB = new Date(`${b.date} ${b.time}`).getTime();
            return timeA - timeB;
          } catch (error) {
            console.warn('Error sorting messages by time:', error);
            return 0;
          }
        })
      }));

    return sections;
  }, []);

  // Dismiss keyboard when tapping outside the input
  const dismissKeyboard = useCallback(() => {
    Keyboard.dismiss();
    textInputRef.current?.blur();
  }, []);

  // Handle input focus and scroll to bottom
  const handleInputFocus = useCallback(() => {
    setTimeout(() => {
      if (scrollRef.current) {
        scrollRef.current.getScrollResponder()?.scrollToEnd({
          animated: true
        });
      }
    }, 300);
  }, []);

  // Handle input tap to ensure proper focus and positioning
  const handleInputPress = useCallback(() => {
    textInputRef.current?.focus();
  }, []);

  // Reset keyboard and layout state
  const resetLayoutState = useCallback(() => {
    setIsKeyboardVisible(false);
    Keyboard.dismiss();
    
    // Allow time for layout to update
    setTimeout(() => {
      if (scrollRef.current) {
        scrollRef.current.getScrollResponder()?.scrollToEnd({
          animated: false
        });
      }
    }, 300);
  }, []);

  // Add useEffect to reset state on component mount
  useEffect(() => {
    resetLayoutState();
    
    return () => {
      // Cleanup on unmount
      resetLayoutState();
    };
  }, [resetLayoutState]);

  // Enhanced empty state
  const renderEmptyState = () => (
    <TouchableWithoutFeedback onPress={dismissKeyboard}>
      <View style={styles.emptyContainer}>
        <View style={styles.emptyContent}>
          <View style={styles.emptyImageContainer}>
            <Ionicons name="chatbubble-ellipses-outline" size={80} color={Colors.GrayBlue} />
          </View>
          <Text style={styles.emptyTitle}>
            No Messages Yet
          </Text>
          <Text style={styles.emptySubtitle}>
            Start a conversation with {patientDetails?.patientName || 'this patient'}. They'll receive notifications and can respond anytime.
          </Text>
          
          {/* Debug information */}
          <Text style={[styles.emptySubtitle, { fontSize: 12, marginTop: 16, opacity: 0.7 }]}>
            Debug: PatientID: {patientId || 'Not set'} | CaregiverID: {caregiverId ? 'Available' : 'Missing'} | OrgID: {orgId ? 'Available' : 'Missing'}
          </Text>
          
          {/* Show retry button if there was an error loading messages */}
          <TouchableOpacity
            style={[styles.primaryButton, { marginBottom: 16 }]}
            onPress={() => {
              console.log('ChatDetail: Manual refresh triggered');
              if (patientId && caregiverId && orgId) {
                getTextMessageByPatientId(patientId, true);
              } else {
                console.log('ChatDetail: Manual refresh - missing required data:', { patientId, caregiverId: !!caregiverId, orgId: !!orgId });
                Alert.alert('Unable to Refresh', 'Missing required information. Please try reopening the chat.', [{ text: 'OK' }]);
              }
            }}
            activeOpacity={0.8}
          >
            <View style={styles.buttonContent}>
              <Ionicons name="refresh" size={18} color={Colors.White} />
              <Text style={styles.buttonText}>Refresh Messages</Text>
            </View>
          </TouchableOpacity>
          
          {connectingDevice !== 'mobile' && (
            <TouchableOpacity
              style={styles.primaryButton}
              onPress={() => {
                setSendMessageModal(true);
                if (Platform.OS === 'ios') {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                }
              }}
              activeOpacity={0.8}
            >
              <View style={styles.buttonContent}>
                <Ionicons name="send" size={18} color={Colors.White} />
                <Text style={styles.buttonText}>Send Message</Text>
              </View>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </TouchableWithoutFeedback>
  );

  const router = useRouter();

  // Function to handle sending message directly from the mobile input
  const handleSendMessage = async () => {
    const trimmedMessage = message.trim();
    
    // Enhanced validation
    if (!trimmedMessage || trimmedMessage.length === 0) {
      return;
    }

    // Check for minimum message length
    if (trimmedMessage.length < 1) {
      Alert.alert('Invalid Message', 'Please enter a valid message.', [{ text: 'OK' }]);
      return;
    }

    // Check for maximum message length
    if (trimmedMessage.length > 150) {
      Alert.alert('Message Too Long', 'Please keep your message under 150 characters.', [{ text: 'OK' }]);
      return;
    }

    // Validate required data
    if (!patientId) {
      Alert.alert('Error', 'Patient information is missing. Please try again.', [{ text: 'OK' }]);
      return;
    }

    setIsSending(true);
    
    try {
      // Use the selected program for sending, fallback to first program or 'rpm'
      const programToUse = selectedProgram || 
                          programs?.selectedPrograms?.[0]?.programName || 
                          'RPM';
      
      console.log('ChatDetail: Sending message with selected program:', {
        selectedProgram,
        programToUse,
        allPrograms: programs?.selectedPrograms?.map(p => p.programName)
      });
      
      const requestParams = {
        patientId: patientId?.toString(),
        chatText: trimmedMessage,
        isSenderServer: true,
        isSenderApp: false,
        program: programToUse.toLowerCase(), // ✅ Selected program sent to API
      };

      const apiUrl = URLS.caregiverUrl + 'sendChatMessage';
      const response = await apiPostWithToken(requestParams, apiUrl);

      if (response?.status === 200 && response?.data?.success) {
        setMessage('');
        // Provide user feedback
        if (Platform.OS === 'ios') {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        }
        onRefresh();
        // Dismiss keyboard after sending
        Keyboard.dismiss();
      } else {
        const errorMessage = response?.data?.message || response?.data?.responseMessage || 'Message may not have been sent successfully.';
        Alert.alert('Warning', errorMessage, [{text: 'Dismiss'}]);
      }
    } catch (error: any) {
      console.error('Error sending message:', error);
      const errorMessage = error.response?.data?.responseMessage || 'Network error. Please check your connection.';
      Alert.alert('Error', errorMessage, [{text: 'Dismiss'}]);
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Container style={styles.container}>
      <NavigationHeader
        title={patientDetails?.patientName || "Chat"}
        showBackButton={true}
        showLogo={false}
        showRightLogo={true}
      />
      <Loader modalVisible={loader} />
      
      <View style={styles.conversationContainer}>
        {messageList && messageList.length > 0 ? (
          <>
            <Animated.SectionList
              ref={scrollRef}
              sections={groupMessagesByDate(messageList)}
              renderItem={renderBubble}
              renderSectionHeader={renderSectionHeader}
              keyExtractor={(item, index) => {
                return item?.fullTimestamp ? item.fullTimestamp.toString() + '-' + index : `message-${item.date}-${item.time}-${index}`;
              }}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={onRefresh}
                  colors={[Colors.TealBlue]}
                  tintColor={Colors.TealBlue}
                  progressBackgroundColor={Colors.White}
                />
              }
              contentContainerStyle={[
                styles.conversationContent,
                { 
                  paddingBottom: connectingDevice === 'mobile' ? 
                    (programs?.selectedPrograms && programs.selectedPrograms.length > 0 ? 140 : 100) : 
                    80,
                  flexGrow: 1 
                }
              ]}
              style={styles.sectionList}
              onScroll={handleScroll}
              scrollEventThrottle={16}
              stickySectionHeadersEnabled={false}
              showsVerticalScrollIndicator={true}
              initialNumToRender={20}
              maxToRenderPerBatch={20}
              windowSize={21}
              onScrollToIndexFailed={handleScrollToIndexFailed}
              inverted={false}
              keyboardShouldPersistTaps="handled"
              onContentSizeChange={() => {
                if (!showScrollToBottom) {
                  onScrollToEnd();
                }
              }}
              maintainVisibleContentPosition={{
                minIndexForVisible: 0,
                autoscrollToTopThreshold: 10
              }}
              scrollEnabled={true}
              nestedScrollEnabled={true}
              onScrollBeginDrag={dismissKeyboard}
            />
            
            {/* Enhanced Scroll to Bottom Button */}
            {showScrollToBottom && (
              <Animated.View
                style={[
                  styles.scrollToBottomContainer,
                  {
                    opacity: scrollButtonOpacity,
                    bottom: connectingDevice === 'mobile' ? 
                      (programs?.selectedPrograms && programs.selectedPrograms.length > 0 ? 140 : 100) : 
                      (Platform.OS === 'ios' ? 110 : 100)
                  }
                ]}
              >
                <TouchableOpacity
                  onPress={onScrollToEnd}
                  style={styles.scrollToBottomButton}
                  activeOpacity={0.8}
                >
                  <Ionicons name="chevron-down" size={20} color={Colors.White} />
                </TouchableOpacity>
              </Animated.View>
            )}
          </>
        ) : renderEmptyState()}
        
        {/* WhatsApp-style input for mobile devices */}
        {connectingDevice === 'mobile' && (
          <View style={[
            styles.mobileInputContainerFixed,
            {
              bottom: Platform.OS === 'ios' ? (isKeyboardVisible ? keyboardHeight : 0) : 0,
            }
          ]}>
            {/* Program Selector - Only for mobile devices */}
            {programs?.selectedPrograms && programs.selectedPrograms.length > 0 && (
              <ProgramSelector
                programs={programs.selectedPrograms}
                selectedProgram={selectedProgram}
                onSelectProgram={handleProgramSelection}
                style={styles.programSelectorContainer}
              />
            )}
            <View style={styles.mobileInputWrapper}>
              <TextInput
                ref={textInputRef}
                style={styles.mobileTextInput}
                placeholder={`Send a message to ${patientDetails?.patientName || 'patient'}...`}
                placeholderTextColor="#9DA3B4"
                value={message}
                onChangeText={setMessage}
                multiline
                maxLength={150}
                blurOnSubmit={false}
                returnKeyType="send"
                onSubmitEditing={handleSendMessage}
                textAlignVertical="top"
                enablesReturnKeyAutomatically={true}
                onFocus={handleInputFocus}
                accessible={true}
                accessibilityLabel="Message input field"
                accessibilityHint="Type your message here and tap send"
              />
              
              {/* Character count display */}
              {message.length > 100 && (
                <View style={styles.characterCountContainer}>
                  <Text style={[
                    styles.characterCount,
                    message.length > 140 && styles.characterCountWarning
                  ]}>
                    {150 - message.length}
                  </Text>
                </View>
              )}
              
              <TouchableOpacity 
                style={[
                  styles.mobileSendButton,
                  !message.trim() && styles.mobileSendButtonDisabled
                ]}
                disabled={!message.trim() || isSending}
                onPress={handleSendMessage}
                accessible={true}
                accessibilityLabel="Send message"
                accessibilityRole="button"
              >
                {isSending ? (
                  <Ionicons name="ellipsis-horizontal" size={24} color={Colors.White} />
                ) : (
                  <Ionicons name="send" size={24} color={Colors.White} />
                )}
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
      
      {/* Enhanced Floating Action Button - Only for watch devices */}
      {messageList && messageList.length > 0 && connectingDevice !== 'mobile' && (
        <Animated.View 
          style={[
            styles.fabContainer,
            { 
              transform: [{ scale: fabScale }],
              bottom: Platform.OS === 'ios' ? 30 : 20
            }
          ]}
        >
          <TouchableOpacity
            style={styles.fab}
            onPressIn={() => animateFab(true)}
            onPressOut={() => animateFab(false)}
            onPress={() => {
              setSendMessageModal(true);
              if (Platform.OS === 'ios') {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              }
            }}
            activeOpacity={0.9}
          >
            <Ionicons name="paper-plane" size={22} color={Colors.White} />
          </TouchableOpacity>
        </Animated.View>
      )}

      {/* Enhanced Message Modal - For watch devices */}
      <Modal
        visible={sendMessageModal}
        onRequestClose={closeModal}
        transparent
        animationType={'slide'}
        statusBarTranslucent
      >
        <SendMessageModal
          close={closeModal}
          patientId={patientId}
          loader={handleLoader}
          programs={programs}
          connectingDevice={connectingDevice}
        />
      </Modal>
    </Container>
  );
});

export default ChatDetail;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  conversationContainer: {
    flex: 1,
    position: 'relative',
  },
  conversationContent: {
    paddingTop: 24,
    paddingHorizontal: 20,
    flexGrow: 1,
  },
  sectionList: {
    flex: 1,
    height: '100%',
  },
  messageGroup: {
    marginBottom: 40,
  },
  sectionHeader: {
    alignItems: 'center',
    marginVertical: 24,
  },
  dateContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.08)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  dateText: {
    fontSize: 12,
    color: Colors.GrayBlue,
    fontWeight: '600',
    textAlign: 'center',
  },
  scrollToBottomContainer: {
    position: 'absolute',
    right: 16,
    width: 40,
    height: 40,
    shadowColor: Colors.Black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
    zIndex: 998,
  },
  scrollToBottomButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.TealBlue,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyContent: {
    alignItems: 'center',
    maxWidth: 280,
  },
  emptyImageContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#F0F4F8',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.DarkJungleGreen,
    textAlign: 'center',
    marginBottom: 12,
    lineHeight: 32,
  },
  emptySubtitle: {
    fontSize: 16,
    color: Colors.GrayBlue,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  primaryButton: {
    backgroundColor: Colors.TealBlue,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 24,
    shadowColor: Colors.TealBlue,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.White,
    marginLeft: 8,
  },
  fabContainer: {
    position: 'absolute',
    right: 16,
    bottom: Platform.OS === 'ios' ? 90 : 80,
    zIndex: 999,
    shadowColor: Colors.Black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 12,
  },
  fab: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.TealBlue,
    justifyContent: 'center',
    alignItems: 'center',
  },

  mobileInputContainerFixed: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    backgroundColor: '#f0f2f5',  // WhatsApp-style background
    borderTopWidth: 1,
    borderTopColor: '#E0E4EF',
    paddingHorizontal: 10,
    paddingTop: 10,
    paddingBottom: Platform.OS === 'ios' ? Math.max(getBottomSpace(), 10) + 5 : 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 8,
    zIndex: 999,
    minHeight: Platform.OS === 'ios' ? 70 : 60,
  },
  mobileInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.White,
    borderRadius: 24,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    minHeight: 50,
  },
  mobileTextInput: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 8,
    fontSize: 16,
    maxHeight: 100,
    color: Colors.DarkJungleGreen,
    minHeight: 24,
  },
  mobileSendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.TealBlue,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.TealBlue,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  mobileSendButtonDisabled: {
    backgroundColor: '#E0E4EF',
  },
  characterCountContainer: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: Colors.TealBlue,
    padding: 2,
    borderRadius: 5,
  },
  characterCount: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.White,
  },
  characterCountWarning: {
    color: Colors.Red,
  },
  programSelectorContainer: {
    marginBottom: 8,
  },
});
