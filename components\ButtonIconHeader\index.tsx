import { useRouter } from 'expo-router';
import React, {memo, useCallback} from 'react';
import {
  ColorValue,
  Image,
  ImageSourcePropType,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import {useTheme} from '@/constants/Theme';
import Theme from '@/constants/Theme';

interface ButtonIconHeaderProps {
  onPress?: () => void;
  marginLeft?: number;
  marginRight?: number;
  icon?: ImageSourcePropType;
  tintColor?: string;
  borderColor?: ColorValue | string;
  style?: ViewStyle;
  backgroundColor?: string;
}

const ButtonIconHeader = memo((props: ButtonIconHeaderProps) => {
  const {theme} = useTheme();
  const router = useRouter();
  const onPress = useCallback(() => {
    if (props.onPress) {
      props.onPress();
    } else {
      router.back();
    }
  }, [props.onPress]);
  return (
    <TouchableOpacity
      style={[
        styles.backButton,
        props.style,
        {
          marginLeft: props.marginLeft ? props.marginLeft : 0,
          marginRight: props.marginRight ? props.marginRight : 0,
          borderColor: props.borderColor || theme.innearColor,
          backgroundColor: props.backgroundColor || theme.backgroundItem,
        },
      ]}
      onPress={onPress}>
      <Image
        source={props.icon || require('@/assets/images/ic_back.png')}
        style={{tintColor: props.tintColor || theme.activeTincolor}}
      />
    </TouchableOpacity>
  );
});

export default ButtonIconHeader;

const styles = StyleSheet.create({
  container: {},
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 8,
    borderWidth: 1,
    ...Theme.center,
  },
});
