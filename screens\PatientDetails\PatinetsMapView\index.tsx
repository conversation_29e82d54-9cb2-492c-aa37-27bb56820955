import { BlurView } from "expo-blur";
import { LinearGradient } from "expo-linear-gradient";
import Container from "@/components/Layout/Container";
import Loader from "@/components/Loader/Loader";
import Text from "@/components/Text";
import { apiPostWithToken } from "@/services/apis/apiManager";
import URLS from "@/services/config/config";
import { Ionicons, FontAwesome5, MaterialCommunityIcons } from "@expo/vector-icons";
import * as Location from "expo-location";
import { useLocalSearchParams } from "expo-router";
import React, { memo, useEffect, useRef, useState } from "react";
import {
  Alert,
  Image,
  Platform,
  StyleSheet,
  ToastAndroid,
  TouchableOpacity,
  View,
  Animated,
} from "react-native";
import MapView, { Marker, PROVIDER_GOOGLE } from "react-native-maps";
import { useSelector } from "react-redux";
import ToggleSwitch from "toggle-switch-react-native";

interface PatientMapViewProps {
  patientId?: string;
  navigation?: any;
}

const PatientMapView = memo((props: PatientMapViewProps) => {
  const params = useLocalSearchParams();
  const patientIdFromParams = (params.id as string) || props.patientId;
  const mapViewRef = useRef<MapView | null>(null);
  const [gpsEnabled, setGpsEnabled] = useState(false);
  const [trackingEnabled, setTrackingEnabled] = useState(false);
  const [loader, setLoader] = useState(false);

  const [homeLat, setHomeLat] = useState(0.0);
  const [homeLong, setHomeLong] = useState(0.0);

  const [trackLat, setTrackLat] = useState(0.0);
  const [trackLong, setTrackLong] = useState(0.0);

  // First check props, then URL params, then Redux store
  const patientIdFromRedux = useSelector(
    (state: any) => state?.currentPatientIdReducer?.patientId
  );

  // Use props.patientId first if available, then URL params, then Redux state
  const patientId =
    props.patientId || patientIdFromParams || patientIdFromRedux;

  const caregiverId = useSelector(
    (state: any) => state?.loginReducer?.data?.userId
  );

  // Get patient name from Redux with a fallback for safety
  const patientName = useSelector(
    (state: any) => state?.currentPatientNameReducer?.patientName || "Patient"
  );

  // Get patient image from Redux if available
  const patientImage = useSelector(
    (state: any) => state?.currentPatientProfileReducer?.image || null
  );

  // For animated map movement
  const mapAnim = useRef(new Animated.Value(0)).current;

  // Only set navigation options if using React Navigation
  useEffect(() => {
    if (props.navigation && props.navigation.setOptions) {
      props.navigation.setOptions({
        headerTitle: () => (
          <View
            style={{
              marginBottom: 0,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Text medium size={16} color={"#000"}>
              {patientName}
              {" Location"}
            </Text>
          </View>
        ),
      });
    }
  }, [props.navigation, patientName]);

  useEffect(() => {
    console.log("PatientMapView - Using patient ID:", patientId);
    console.log("Props patientId:", props.patientId);
    console.log("URL params id:", patientIdFromParams);
    console.log("Redux patientId:", patientIdFromRedux);

    // Execute location permission check immediately
    locationPermission();
  }, [patientId, props.patientId, patientIdFromParams, patientIdFromRedux]);

  // Effect to update map when coordinates change
  useEffect(() => {
    if (mapViewRef.current && (homeLat || trackLat)) {
      // Focus map on patient location whenever coordinates change
      const targetLat = trackLat || homeLat;
      const targetLong = trackLong || homeLong;

      if (targetLat && targetLong) {
        console.log("Focusing map on coordinates:", targetLat, targetLong);
        mapViewRef.current.animateToRegion(
          {
            latitude: targetLat,
            longitude: targetLong,
            latitudeDelta: 0.015,
            longitudeDelta: 0.0121,
          },
          1000
        ); // 1 second animation
      }
    }
  }, [homeLat, homeLong, trackLat, trackLong]);

  const locationPermission = async () => {
    try {
      console.log("Requesting location permission...");

      // First check if foreground location is already permitted
      let { status: existingStatus } =
        await Location.getForegroundPermissionsAsync();

      // If not already granted, request it
      if (existingStatus !== "granted") {
        console.log("Permission not granted, requesting...");
        const { status } = await Location.requestForegroundPermissionsAsync();
        existingStatus = status;
      }

      if (existingStatus === "granted") {
        console.log("Location permission granted");

        // Check if location services are enabled
        const isLocationEnabled = await Location.hasServicesEnabledAsync();
        if (!isLocationEnabled) {
          Alert.alert(
            "Location Services Disabled",
            "Please enable location services in your device settings to use the map features."
          );
          return false;
        }

        console.log("Getting GPS data for patient:", patientId);
        getGpsData(patientId);
        return true;
      }

      // Handle permission denied
      if (existingStatus === "denied") {
        if (Platform.OS === "android") {
          ToastAndroid.show(
            "Location access is required for the proper functioning of the app. Please try again.",
            ToastAndroid.LONG
          );
        } else {
          Alert.alert(
            "Permission Denied",
            "Location access is required for the proper functioning of the app. Please try again."
          );
        }
      } else {
        if (Platform.OS === "android") {
          ToastAndroid.show(
            "Location permission revoked by user.",
            ToastAndroid.LONG
          );
        } else {
          Alert.alert(
            "Permission Denied",
            "Location permission revoked by user. Please enable it in your device settings."
          );
        }
      }
    } catch (error) {
      console.error("Error requesting location permission:", error);
      Alert.alert("Error", "Failed to request location permission");
    }
    return false;
  };

  const getGpsData = async (patientId: number | string) => {
    if (!patientId) {
      console.error("Patient ID is undefined or null");
      Alert.alert("Error", "Patient ID is missing");
      return;
    }

    console.log("Fetching GPS data for patient ID:", patientId);
    setLoader(true);

    try {
      const response = await apiPostWithToken(
        { patientId: patientId },
        URLS.caregiverUrl + "getGPSTrackingInfo"
      );
      if (response?.status == 200) {
        setLoader(false);
        if (response?.data?.status === "Success") {
          console.log("GPS data fetched successfully");

          // Convert string values to numbers and handle null/undefined values
          const gpsStatus = !!response?.data?.data?.gpsStatus;
          const trackStatus = !!response?.data?.data?.trackStatus;

          let homeLat = 0.0;
          let homeLong = 0.0;
          let trackLat = 0.0;
          let trackLong = 0.0;

          if (response?.data?.data?.latitude) {
            homeLat = parseFloat(response?.data?.data?.latitude) || 0.0;
          }

          if (response?.data?.data?.longitude) {
            homeLong = parseFloat(response?.data?.data?.longitude) || 0.0;
          }

          if (response?.data?.data?.trackLatitude) {
            trackLat = parseFloat(response?.data?.data?.trackLatitude) || 0.0;
          }

          if (response?.data?.data?.trackLongitude) {
            trackLong = parseFloat(response?.data?.data?.trackLongitude) || 0.0;
          }

          console.log("Setting GPS status:", gpsStatus);
          console.log("Setting tracking status:", trackStatus);
          console.log("Home coordinates:", homeLat, homeLong);
          console.log("Track coordinates:", trackLat, trackLong);

          setGpsEnabled(gpsStatus);
          setTrackingEnabled(trackStatus);
          setHomeLat(homeLat);
          setHomeLong(homeLong);
          setTrackLat(trackLat);
          setTrackLong(trackLong);

          // After setting coordinates, schedule a map centering
          setTimeout(centerMapOnPatient, 500);
        } else {
          Alert.alert("Error", response?.data.responseMessage, [
            { text: "Dismiss" },
          ]);
        }
      } else {
        setLoader(false);
        const errorMessage = response?.response?.data?.responseMessage
          ? response?.response?.data?.responseMessage
          : response.message === "Network Error"
          ? "Network error. Please check your data connection."
          : response.message;
        Alert.alert("Error", errorMessage, [{ text: "Dismiss" }]);
      }
    } catch (error) {
      console.error("Error fetching GPS data:", error);
      setLoader(false);
      Alert.alert("Error", "Failed to fetch GPS data. Please try again.");
    }
  };

  // Function to center the map on patient location (with smooth animation)
  const centerMapOnPatient = () => {
    if (mapViewRef.current) {
      const targetLat = trackLat || homeLat;
      const targetLong = trackLong || homeLong;

      if (targetLat && targetLong) {
        Animated.timing(mapAnim, {
          toValue: 1,
          duration: 1200, // smoother, longer
          useNativeDriver: false,
        }).start(() => mapAnim.setValue(0));
        mapViewRef.current.animateToRegion(
          {
            latitude: targetLat,
            longitude: targetLong,
            latitudeDelta: 0.012,
            longitudeDelta: 0.010,
          },
          1200 // ms
        );
      }
    }
  };

  const changeGpsTrackingStatus = async (url: string) => {
    setLoader(true);

    if (!patientId) {
      setLoader(false);
      Alert.alert("Error", "Patient ID is missing");
      return;
    }

    try {
      console.log("Changing GPS tracking status:", url);
      console.log("For patient ID:", patientId);

      const response = await apiPostWithToken(
        { patientId: patientId, careGiverId: caregiverId },
        url
      );
      if (response?.status == 200) {
        setLoader(false);
        if (response?.data?.status === "Operation Successful") {
          getGpsData(patientId);
          Alert.alert("Success", response?.data?.responseMessage, [
            { text: "Dismiss" },
          ]);
        } else {
          setGpsEnabled(false);
          setTrackingEnabled(false);
          Alert.alert("Error", response?.data?.responseMessage, [
            { text: "Dismiss" },
          ]);
        }
      } else {
        setLoader(false);
        const errorMessage = response?.response?.data?.responseMessage
          ? response?.response?.data?.responseMessage
          : response.message === "Network Error"
          ? "Network error. Please check your data connection."
          : response.message;
        Alert.alert("Error", errorMessage, [{ text: "Dismiss" }]);
      }
    } catch (error) {
      console.error("Error changing GPS tracking status:", error);
      setLoader(false);
      setGpsEnabled(false);
      setTrackingEnabled(false);
      Alert.alert(
        "Error",
        "Failed to change GPS tracking status. Please try again."
      );
    }
  };

  return (
    <Container style={styles.container}>
      <Loader modalVisible={loader} />
      {/* Modern patient info header with clean white card design */}
      <View style={styles.headerCard}>
        <View style={styles.headerLeft}>
          
          <View>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Text size={18} bold color={"#333"} style={{marginRight: 6, fontFamily: 'System', letterSpacing: 0.2}}>
                {patientName}
              </Text>
            </View>
            
          </View>
        </View>
        <TouchableOpacity style={styles.refreshBtn} onPress={() => getGpsData(patientId)} activeOpacity={0.7}>
          <View style={styles.iconBtnBg}>
            <Ionicons name="refresh" size={22} color="#ffffff" />
          </View>
        </TouchableOpacity>
      </View>
      {/* Modern toggle card with clean design */}
      <View style={styles.toggleCard}>
        <View style={styles.togglesRow}>
          <View style={styles.toggleItem}>
            <View style={styles.toggleLabel}>
              <FontAwesome5 name="satellite-dish" size={14} color={gpsEnabled ? "#12b2b3" : "#999"} solid style={{marginRight: 8}} />
              <Text size={14} medium color={"#333"} style={{fontFamily: 'System'}}>
                GPS Status
              </Text>
            </View>
            <ToggleSwitch
              isOn={gpsEnabled}
              onColor="#12b2b3"
              offColor="#e0e0e0"
              thumbOnStyle={{backgroundColor: '#ffffff'}}
              thumbOffStyle={{backgroundColor: '#999999'}}
              size="medium"
              animationSpeed={200}
              onToggle={(isOn) => {
                setGpsEnabled(isOn);
                if (isOn) {
                  changeGpsTrackingStatus(URLS.caregiverUrl + "enableGPS");
                } else {
                  changeGpsTrackingStatus(URLS.caregiverUrl + "disableGPS");
                  if (trackingEnabled) {
                    setTrackingEnabled(false);
                    changeGpsTrackingStatus(
                      URLS.caregiverUrl + "disableGPSTracking"
                    );
                  }
                }
              }}
            />
          </View>
          <View style={styles.divider} />
          <View style={styles.toggleItem}>
            <View style={styles.toggleLabel}>
              <FontAwesome5 name="location-arrow" size={14} color={trackingEnabled ? "#12b2b3" : "#999"} solid style={{marginRight: 8}} />
              <Text size={14} medium color={"#333"} style={{fontFamily: 'System'}}>
                Tracking Status
              </Text>
            </View>
            <ToggleSwitch
              isOn={trackingEnabled}
              onColor="#12b2b3"
              offColor="#e0e0e0"
              thumbOnStyle={{backgroundColor: '#ffffff'}}
              thumbOffStyle={{backgroundColor: '#999999'}}
              size="medium"
              animationSpeed={200}
              onToggle={(isOn) => {
                setTrackingEnabled(isOn);
                if (isOn) {
                  if (gpsEnabled) {
                    changeGpsTrackingStatus(
                      URLS.caregiverUrl + "enableGPSTracking"
                    );
                  } else {
                    setTrackingEnabled(false);
                    Alert.alert("Error", "Please enable GPS first.", [
                      { text: "Dismiss" },
                    ]);
                  }
                } else {
                  changeGpsTrackingStatus(URLS.caregiverUrl + "disableGPSTracking");
                }
              }}
            />
          </View>
        </View>
      </View>
      {/* Modern map card with floating effect and animated markers */}
      <Animated.View style={[styles.mapCard, { transform: [{ scale: mapAnim.interpolate({ inputRange: [0, 1], outputRange: [1, 1.03] }) }] }]}>
        <TouchableOpacity
          style={styles.centerButton}
          onPress={centerMapOnPatient}
          activeOpacity={0.8}
        >
          <View style={styles.floatingButtonBg}>
            <FontAwesome5 name="crosshairs" size={18} color="#ffffff" solid />
          </View>
        </TouchableOpacity>
        <MapView
          provider={PROVIDER_GOOGLE}
          zoomEnabled={true}
          zoomTapEnabled
          zoomControlEnabled
          showsMyLocationButton={false}
          showsUserLocation={false}
          showsPointsOfInterest={true}
          style={styles.map}
          followsUserLocation={false}
          showsCompass={true}
          toolbarEnabled={true}
          showsBuildings={true}
          mapType={"standard"}
          initialRegion={{
            latitude: trackLat || homeLat || 37.78825,
            longitude: trackLong || homeLong || -122.4324,
            latitudeDelta: 0.012,
            longitudeDelta: 0.010,
          }}
          ref={mapViewRef}
          onMapReady={centerMapOnPatient}
        >
          {trackLat && trackLong ? (
            <Marker
              key="tracking-marker"
              coordinate={{ latitude: trackLat, longitude: trackLong }}
              title={"Current Location"}
              description={"Patient's Current Location"}
            >
              <View style={styles.markerContainer}>
                <View style={[styles.markerIconBg, styles.trackingMarker]}>
                  <FontAwesome5 name="map-marker-alt" size={24} color="#ffffff" solid />
                </View>
                <View style={[styles.markerDot, { backgroundColor: '#36B5E0' }]} />
              </View>
            </Marker>
          ) : null}
          {homeLat && homeLong ? (
            <Marker
              key="home-marker"
              coordinate={{ latitude: homeLat, longitude: homeLong }}
              title={"Home"}
              description={"Patient Home Location"}
            >
              <View style={styles.markerContainer}>
                <View style={[styles.markerIconBg, styles.homeMarker]}>
                  <FontAwesome5 name="home" size={20} color="#ffffff" solid />
                </View>
                <View style={[styles.markerDot, { backgroundColor: '#12b2b3' }]} />
              </View>
            </Marker>
          ) : null}
        </MapView>
      </Animated.View>
    </Container>
  );
});

export default PatientMapView;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  headerCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderRadius: 16,
    marginHorizontal: 16,
    marginTop: 18,
    marginBottom: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    backgroundColor: '#ffffff',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  patientIconWrapper: {
    marginRight: 12,
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  avatarPlaceholder: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#12b2b3',
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  locationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#12b2b3',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
    marginTop: 4,
  },
  refreshBtn: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  iconBtnBg: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#12b2b3',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  toggleCard: {
    marginHorizontal: 16,
    borderRadius: 16,
    marginBottom: 16,
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    padding: 6,
  },
  togglesRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  toggleItem: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
  },
  toggleLabel: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  divider: {
    width: 1,
    height: 32,
    backgroundColor: '#e0e0e0',
  },
  mapCard: {
    flex: 1,
    marginHorizontal: 16,
    marginBottom: 18,
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
    backgroundColor: '#fff',
  },
  map: {
    flex: 1,
  },
  centerButton: {
    position: 'absolute',
    right: 16,
    top: 16,
    zIndex: 10,
    overflow: 'hidden',
  },
  floatingButtonBg: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#12b2b3',
    alignItems: 'center',
    justifyContent: 'center',
  },
  markerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 48,
    height: 64,
  },
  markerIconBg: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  trackingMarker: {
    backgroundColor: '#36B5E0',
  },
  homeMarker: {
    backgroundColor: '#12b2b3',
  },
  markerDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginTop: 4,
  },
});
