import { Platform } from 'react-native';
import { Audio } from 'expo-av';

export interface AudioRouteOptions {
  route: 'speaker' | 'earpiece';
}

export class AudioUtils {
  static async setAudioRoute(route: 'speaker' | 'earpiece'): Promise<boolean> {
    try {
      console.log(`AudioUtils: Setting audio route to ${route}`);
      
      // Configure audio session for VoIP calling
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        staysActiveInBackground: true,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: route === 'earpiece',
      });

      console.log(`AudioUtils: Successfully set audio route to ${route}`);
      return true;
    } catch (error: any) {
      console.error('AudioUtils: Failed to set audio route:', error.message || error);
      return false;
    }
  }

  static async resetAudioSession(): Promise<boolean> {
    try {
      console.log('AudioUtils: Resetting audio session');
      
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: false,
        playsInSilentModeIOS: false,
        shouldDuckAndroid: false,
        playThroughEarpieceAndroid: false,
      });

      console.log('AudioUtils: Audio session reset to default');
      return true;
    } catch (error: any) {
      console.error('AudioUtils: Failed to reset audio session:', error.message || error);
      return false;
    }
  }

  static async initializeAudioForCall(): Promise<boolean> {
    try {
      console.log('AudioUtils: Initializing audio for call');
      
      // Set up audio session for VoIP calling
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        staysActiveInBackground: true,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: true, // Start with earpiece
      });

      console.log('AudioUtils: Audio initialized for call (earpiece default)');
      return true;
    } catch (error: any) {
      console.error('AudioUtils: Failed to initialize audio:', error.message || error);
      return false;
    }
  }
} 