import React from 'react';
import {
  PressableStateCallbackType,
  View as DefaultView,
  ViewProps,
  ViewStyle,
} from 'react-native';
import {useTheme} from '@/constants/Theme';
import Theme from '@/constants/Theme';
interface Props extends ViewProps {
  children?:
    | React.ReactNode
    | ((state: PressableStateCallbackType) => React.ReactNode);
  style?: ViewStyle;
}

const Layout = ({children, style}: Props) => {
  const {theme} = useTheme();
  return (
    <DefaultView
      style={[{backgroundColor: theme.backgroundItem}, style, Theme.shadow]}>
      {children}
    </DefaultView>
  );
};
export default Layout;
