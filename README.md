# Care Manager Expo App

This is the Expo version of the Care Manager application, migrated from React Native.

### Twilio Voice Android Build fix (patch)

**Note: These files are now automatically managed by the postinstall script. When you run `npm install` or `yarn install`, the script will automatically verify and update these files if needed.**

The following Android files are required for Twilio VoIP functionality and should be located in `android\app\src\main\java\com\watchrxhealth\caregiver`:

* `MainActivity.kt`

```
package com.watchrxhealth.caregiver

import expo.modules.splashscreen.SplashScreenManager
import android.Manifest
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.widget.Toast
import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.fabricEnabled
import com.facebook.react.defaults.DefaultReactActivityDelegate
import com.twiliovoicereactnative.VoiceActivityProxy
import expo.modules.ReactActivityDelegateWrapper

class MainActivity : ReactActivity() {

  private val activityProxy = VoiceActivityProxy(
        this
    ) { permission: String ->
        if (Manifest.permission.RECORD_AUDIO == permission) {
            Toast.makeText(
                this@MainActivity,
                "Microphone permissions needed. Please allow in your application settings.",
                Toast.LENGTH_LONG
            ).show()
        } else if ((Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) && (Manifest.permission.BLUETOOTH_CONNECT == permission)) {
            Toast.makeText(
                this@MainActivity,
                "Bluetooth permissions needed. Please allow in your application settings.",
                Toast.LENGTH_LONG
            ).show()
        } else if ((Build.VERSION.SDK_INT > Build.VERSION_CODES.S_V2) && (Manifest.permission.POST_NOTIFICATIONS == permission)) {
            Toast.makeText(
                this@MainActivity,
                "Notification permissions needed. Please allow in your application settings.",
                Toast.LENGTH_LONG
            ).show()
        }
    }

  override fun onCreate(savedInstanceState: Bundle?) {
    // Set the theme to AppTheme BEFORE onCreate to support
    // coloring the background, status bar, and navigation bar.
    // This is required for expo-splash-screen.
    // setTheme(R.style.AppTheme);
    // @generated begin expo-splashscreen - expo prebuild (DO NOT MODIFY) sync-f3ff59a738c56c9a6119210cb55f0b613eb8b6af
    SplashScreenManager.registerOnActivity(this)
    // @generated end expo-splashscreen
    super.onCreate(savedInstanceState)
    activityProxy.onCreate(savedInstanceState)
  }

  override fun onDestroy() {
    activityProxy.onDestroy()
    super.onDestroy()
  }

  override fun onNewIntent(intent: Intent?) {
    super.onNewIntent(intent)
    activityProxy.onNewIntent(intent)
  }

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */
  override fun getMainComponentName(): String = "main"

  /**
   * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
   * which allows you to enable New Architecture with a single boolean flags [fabricEnabled]
   */
  override fun createReactActivityDelegate(): ReactActivityDelegate {
    return ReactActivityDelegateWrapper(
          this,
          BuildConfig.IS_NEW_ARCHITECTURE_ENABLED,
          object : DefaultReactActivityDelegate(
              this,
              mainComponentName,
              fabricEnabled
          ){})
  }

  /**
    * Align the back button behavior with Android S
    * where moving root activities to background instead of finishing activities.
    * @see <a href="https://developer.android.com/reference/android/app/Activity#onBackPressed()">onBackPressed</a>
    */
  override fun invokeDefaultOnBackPressed() {
      if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.R) {
          if (!moveTaskToBack(false)) {
              // For non-root activities, use the default implementation to finish them.
              super.invokeDefaultOnBackPressed()
          }
          return
      }

      // Use the default back button implementation on Android S
      // because it's doing more than [Activity.moveTaskToBack] in fact.
      super.invokeDefaultOnBackPressed()
  }
}
```

* `MainApplication.kt`

```
package com.watchrxhealth.caregiver

import android.app.Application
import android.content.res.Configuration
import com.facebook.react.PackageList
import com.facebook.react.ReactApplication
import com.facebook.react.ReactNativeHost
import com.facebook.react.ReactPackage
import com.facebook.react.ReactHost
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.load
import com.facebook.react.defaults.DefaultReactNativeHost
import com.facebook.react.soloader.OpenSourceMergedSoMapping
import com.facebook.soloader.SoLoader
import com.twiliovoicereactnative.VoiceApplicationProxy
import expo.modules.ApplicationLifecycleDispatcher
import expo.modules.ReactNativeHostWrapper

class MainApplication : Application(), ReactApplication {

  private val mReactNativeHost = MainReactNativeHost(this)
  private val voiceApplicationProxy = VoiceApplicationProxy(mReactNativeHost)

  override val reactNativeHost: ReactNativeHost = ReactNativeHostWrapper(
        this,
        mReactNativeHost
  )

  override val reactHost: ReactHost
    get() = ReactNativeHostWrapper.createReactHost(applicationContext, reactNativeHost)

  override fun onCreate() {
    super.onCreate()
    voiceApplicationProxy.onCreate()
    SoLoader.init(this, OpenSourceMergedSoMapping)
    if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
      // If you opted-in for the New Architecture, we load the native entry point for this app.
      load()
    }
    ApplicationLifecycleDispatcher.onApplicationCreate(this)
  }

  override fun onConfigurationChanged(newConfig: Configuration) {
    super.onConfigurationChanged(newConfig)
    ApplicationLifecycleDispatcher.onConfigurationChanged(this, newConfig)
  }

  override fun onTerminate() {
    voiceApplicationProxy.onTerminate()
    super.onTerminate()
  }
}
```

* `MainReactNativeHost.kt`

```
package com.watchrxhealth.caregiver

import android.app.Application
import com.facebook.react.PackageList
import com.facebook.react.ReactPackage
import com.twiliovoicereactnative.VoiceApplicationProxy

class MainReactNativeHost(application: Application) : VoiceApplicationProxy.VoiceReactNativeHost(application) {
    
    override fun getUseDeveloperSupport(): Boolean = BuildConfig.DEBUG

    override fun getPackages(): List<ReactPackage> {
        val packages = PackageList(this).packages
        // Packages that cannot be autolinked yet can be added manually here
        // packages.add(MyReactNativePackage())
        return packages
    }

    override fun getJSMainModuleName(): String = ".expo/.virtual-metro-entry"
}
```
