import {
  FETCH_ALERTS_LIST,
  FETCH_ALERTS_LIST_FAILURE,
  FETCH_ALERTS_LIST_SUCCESS,
} from './type';

export const fetchAlertsList = (payload: any) => {
  return {
    type: FETCH_ALERTS_LIST,
    payload,
  };
};

export const fetchAlertsListSuccess = (data: any) => {
  return {
    type: FETCH_ALERTS_LIST_SUCCESS,
    data,
  };
};

export const fetchAlertsListFailure = (error: any) => {
  return {
    type: FETCH_ALERTS_LIST_FAILURE,
    error,
  };
};